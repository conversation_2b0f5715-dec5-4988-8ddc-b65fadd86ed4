{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/path-has-prefix.ts"], "names": ["parsePath", "pathHasPrefix", "path", "prefix", "pathname", "startsWith"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAc;AAExC;;;;;;CAMC,GACD,OAAO,SAASC,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGJ,UAAUE;IAC/B,OAAOE,aAAaD,UAAUC,SAASC,UAAU,CAACF,SAAS;AAC7D"}