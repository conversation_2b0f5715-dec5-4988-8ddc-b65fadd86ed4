{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["buildAppStaticPaths", "buildStaticPaths", "collectGenerateParams", "loadComponents", "setHttpClientAndAgentOptions", "isAppRouteRouteModule", "loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "cache<PERSON><PERSON><PERSON>", "ppr", "require", "setConfig", "components", "isDev", "getStaticPaths", "Error", "routeModule", "generateParams", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "ComponentMod", "tree", "configFileName"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,sBAAqB;AAE5B,SACEA,mBAAmB,EACnBC,gBAAgB,EAChBC,qBAAqB,QAChB,oBAAmB;AAE1B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,SAASC,qBAAqB,QAAQ,iCAAgC;AAQtE,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,GAAG,EAiBJ;IAKC,oCAAoC;IACpCC,QAAQ,4CAA4CC,SAAS,CAACb;IAC9DN,6BAA6B;QAC3BO;IACF;IAEA,MAAMa,aAAa,MAAMrB,eAAe;QACtCK;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;QACAW,OAAO;IACT;IAEA,IAAI,CAACD,WAAWE,cAAc,IAAI,CAACZ,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIa,MACR,CAAC,uDAAuD,EAAElB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEc,WAAW,EAAE,GAAGJ;QACxB,MAAMK,iBACJD,eAAevB,sBAAsBuB,eACjC;YACE;gBACElB,QAAQ;oBACNoB,YAAYF,YAAYG,QAAQ,CAACD,UAAU;oBAC3CE,SAASJ,YAAYG,QAAQ,CAACC,OAAO;oBACrCC,eAAeL,YAAYG,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBN,YAAYG,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAa1B;YACf;SACD,GACD,MAAMP,sBAAsBsB,WAAWY,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMrC,oBAAoB;YAC/BO;YACAQ,MAAMN;YACNoB;YACAS,gBAAgB5B,OAAO4B,cAAc;YACrC9B;YACAW;YACAC;YACAJ;YACAC;YACAC;YACAG;YACAe,cAAcZ,WAAWY,YAAY;QACvC;IACF;IAEA,OAAO,MAAMnC,iBAAiB;QAC5Bc,MAAMN;QACNiB,gBAAgBF,WAAWE,cAAc;QACzCY,gBAAgB5B,OAAO4B,cAAc;QACrC1B;QACAC;IACF;AACF"}