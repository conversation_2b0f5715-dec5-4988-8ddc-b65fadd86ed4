interface IConsole {
  assert: <PERSON>sol<PERSON>['assert']
  count: <PERSON><PERSON><PERSON>['count']
  debug: <PERSON>sol<PERSON>['debug']
  dir: <PERSON>sole['dir']
  error: <PERSON>sole['error']
  info: Console['info']
  log: Console['log']
  time: Console['time']
  timeEnd: Console['timeEnd']
  timeLog: Console['timeLog']
  trace: Console['trace']
  warn: Console['warn']
}

declare const console: IConsole

export { console };
