import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Feature configuration file path
const FEATURES_CONFIG_PATH = path.join(process.cwd(), 'config', 'features.json');

// Default feature configuration
const DEFAULT_FEATURES = {
  liveChat: {
    enabled: true,
    name: 'Live Chat Support',
    description: 'Real-time customer support with WhatsApp integration',
    category: 'Customer Support',
    settings: {
      whatsappNumber: '+447447186806',
      autoResponse: true,
      businessHours: '9:00-18:00'
    }
  },
  productRecommendations: {
    enabled: true,
    name: 'Smart Product Recommendations',
    description: 'AI-powered "You might also like" suggestions',
    category: 'AI Features',
    settings: {
      algorithm: 'collaborative_filtering',
      maxRecommendations: 6,
      confidenceThreshold: 0.7
    }
  },
  loyaltyProgram: {
    enabled: false,
    name: 'Loyalty & Rewards Program',
    description: 'Points, VIP tiers, and exclusive benefits',
    category: 'Customer Engagement',
    settings: {
      pointsPerPound: 10,
      vipThreshold: 500,
      welcomeBonus: 100
    }
  },
  arTryOn: {
    enabled: false,
    name: 'AR Try-On Experience',
    description: 'Virtual try-on for clothes and accessories',
    category: 'Advanced Features',
    settings: {
      supportedCategories: ['clothing', 'accessories'],
      quality: 'high'
    }
  },
  voiceSearch: {
    enabled: false,
    name: 'Voice Search',
    description: 'Search products using voice commands',
    category: 'Search & Discovery',
    settings: {
      language: 'en-GB',
      sensitivity: 'medium'
    }
  },
  socialCommerce: {
    enabled: true,
    name: 'Social Commerce',
    description: 'Instagram integration and social sharing',
    category: 'Social Features',
    settings: {
      platforms: ['instagram', 'facebook', 'twitter'],
      autoPost: false
    }
  },
  pushNotifications: {
    enabled: true,
    name: 'Push Notifications',
    description: 'Real-time notifications for deals and updates',
    category: 'Engagement',
    settings: {
      orderUpdates: true,
      promotions: true,
      backInStock: true
    }
  },
  oneClickReorder: {
    enabled: true,
    name: 'One-Click Reorder',
    description: 'Quick reorder from purchase history',
    category: 'User Experience',
    settings: {
      maxHistoryItems: 20,
      showInProfile: true
    }
  },
  advancedSearch: {
    enabled: true,
    name: 'Advanced Search & Filters',
    description: 'Enhanced search with filters and suggestions',
    category: 'Search & Discovery',
    settings: {
      autoComplete: true,
      searchHistory: true,
      visualSearch: false
    }
  },
  mobilePayments: {
    enabled: true,
    name: 'Mobile Payment Options',
    description: 'Apple Pay, Google Pay, and other mobile payments',
    category: 'Payments',
    settings: {
      applePay: true,
      googlePay: true,
      paypal: true
    }
  },
  inventoryAlerts: {
    enabled: true,
    name: 'Inventory Alerts',
    description: 'Low stock and out of stock notifications',
    category: 'Inventory Management',
    settings: {
      lowStockThreshold: 10,
      emailAlerts: true,
      customerNotifications: true
    }
  },
  priceDropAlerts: {
    enabled: false,
    name: 'Price Drop Alerts',
    description: 'Notify customers when prices drop on wishlist items',
    category: 'Customer Engagement',
    settings: {
      emailNotifications: true,
      pushNotifications: true,
      threshold: 10
    }
  }
};

// Ensure config directory exists
function ensureConfigDirectory() {
  const configDir = path.dirname(FEATURES_CONFIG_PATH);
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
}

// Load features configuration
function loadFeaturesConfig() {
  try {
    ensureConfigDirectory();
    
    if (fs.existsSync(FEATURES_CONFIG_PATH)) {
      const configData = fs.readFileSync(FEATURES_CONFIG_PATH, 'utf8');
      const config = JSON.parse(configData);
      
      // Merge with defaults to ensure all features exist
      return { ...DEFAULT_FEATURES, ...config };
    } else {
      // Create default config file
      saveFeaturesConfig(DEFAULT_FEATURES);
      return DEFAULT_FEATURES;
    }
  } catch (error) {
    console.error('Error loading features config:', error);
    return DEFAULT_FEATURES;
  }
}

// Save features configuration
function saveFeaturesConfig(config) {
  try {
    ensureConfigDirectory();
    fs.writeFileSync(FEATURES_CONFIG_PATH, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving features config:', error);
    return false;
  }
}

// GET endpoint - Get all features
export async function GET() {
  try {
    const features = loadFeaturesConfig();
    
    return NextResponse.json({
      success: true,
      features,
      message: 'Features configuration loaded successfully'
    });
    
  } catch (error) {
    console.error('Error getting features:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      features: DEFAULT_FEATURES
    }, { status: 500 });
  }
}

// POST endpoint - Toggle feature or update settings
export async function POST(request) {
  try {
    const body = await request.json();
    const { feature, enabled, settings } = body;
    
    if (!feature) {
      return NextResponse.json({
        success: false,
        error: 'Feature name is required'
      }, { status: 400 });
    }
    
    // Load current configuration
    const features = loadFeaturesConfig();
    
    // Check if feature exists
    if (!features[feature]) {
      return NextResponse.json({
        success: false,
        error: `Feature '${feature}' not found`
      }, { status: 404 });
    }
    
    // Update feature
    if (enabled !== undefined) {
      features[feature].enabled = enabled;
      console.log(`🔧 Feature '${feature}' ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    if (settings) {
      features[feature].settings = { ...features[feature].settings, ...settings };
      console.log(`⚙️ Feature '${feature}' settings updated`);
    }
    
    // Save configuration
    const saved = saveFeaturesConfig(features);
    
    if (!saved) {
      return NextResponse.json({
        success: false,
        error: 'Failed to save feature configuration'
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      feature: features[feature],
      message: `Feature '${feature}' updated successfully`
    });
    
  } catch (error) {
    console.error('Error toggling feature:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// PUT endpoint - Update multiple features
export async function PUT(request) {
  try {
    const body = await request.json();
    const { features: updatedFeatures } = body;
    
    if (!updatedFeatures || typeof updatedFeatures !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Features object is required'
      }, { status: 400 });
    }
    
    // Load current configuration
    const features = loadFeaturesConfig();
    
    // Update features
    let updatedCount = 0;
    for (const [featureName, featureData] of Object.entries(updatedFeatures)) {
      if (features[featureName]) {
        features[featureName] = { ...features[featureName], ...featureData };
        updatedCount++;
      }
    }
    
    // Save configuration
    const saved = saveFeaturesConfig(features);
    
    if (!saved) {
      return NextResponse.json({
        success: false,
        error: 'Failed to save features configuration'
      }, { status: 500 });
    }
    
    console.log(`🔧 Updated ${updatedCount} features`);
    
    return NextResponse.json({
      success: true,
      features,
      updatedCount,
      message: `${updatedCount} features updated successfully`
    });
    
  } catch (error) {
    console.error('Error updating features:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// DELETE endpoint - Reset to defaults
export async function DELETE() {
  try {
    const saved = saveFeaturesConfig(DEFAULT_FEATURES);
    
    if (!saved) {
      return NextResponse.json({
        success: false,
        error: 'Failed to reset features configuration'
      }, { status: 500 });
    }
    
    console.log('🔄 Features configuration reset to defaults');
    
    return NextResponse.json({
      success: true,
      features: DEFAULT_FEATURES,
      message: 'Features configuration reset to defaults'
    });
    
  } catch (error) {
    console.error('Error resetting features:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
