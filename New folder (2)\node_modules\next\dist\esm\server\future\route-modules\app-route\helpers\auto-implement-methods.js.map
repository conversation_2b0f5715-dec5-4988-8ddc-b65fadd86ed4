{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/auto-implement-methods.ts"], "names": ["HTTP_METHODS", "handleMethodNotAllowedResponse", "AUTOMATIC_ROUTE_METHODS", "autoImplementMethods", "handlers", "methods", "reduce", "acc", "method", "implemented", "Set", "filter", "missing", "has", "GET", "HEAD", "add", "allow", "push", "headers", "Allow", "sort", "join", "OPTIONS", "Response", "status", "Error"], "mappings": "AAEA,SAASA,YAAY,QAA0B,uBAAsB;AACrE,SAASC,8BAA8B,QAAQ,kCAAiC;AAEhF,MAAMC,0BAA0B;IAAC;IAAQ;CAAU;AAEnD,OAAO,SAASC,qBACdC,QAA0B;IAE1B,0EAA0E;IAC1E,mEAAmE;IACnE,MAAMC,UAAkDL,aAAaM,MAAM,CACzE,CAACC,KAAKC,SAAY,CAAA;YAChB,GAAGD,GAAG;YACN,wEAAwE;YACxE,gCAAgC;YAChC,CAACC,OAAO,EAAEJ,QAAQ,CAACI,OAAO,IAAIP;QAChC,CAAA,GACA,CAAC;IAGH,4EAA4E;IAC5E,sCAAsC;IACtC,MAAMQ,cAAc,IAAIC,IAAIV,aAAaW,MAAM,CAAC,CAACH,SAAWJ,QAAQ,CAACI,OAAO;IAC5E,MAAMI,UAAUV,wBAAwBS,MAAM,CAC5C,CAACH,SAAW,CAACC,YAAYI,GAAG,CAACL;IAG/B,2EAA2E;IAC3E,KAAK,MAAMA,UAAUI,QAAS;QAC5B,iEAAiE;QACjE,oEAAoE;QACpE,WAAW;QACX,IAAIJ,WAAW,QAAQ;YACrB,IAAIJ,SAASU,GAAG,EAAE;gBAChB,uDAAuD;gBACvDT,QAAQU,IAAI,GAAGX,SAASU,GAAG;gBAE3B,0BAA0B;gBAC1BL,YAAYO,GAAG,CAAC;YAClB;YACA;QACF;QAEA,gDAAgD;QAChD,IAAIR,WAAW,WAAW;YACxB,wEAAwE;YAExE,oEAAoE;YACpE,MAAMS,QAAuB;gBAAC;mBAAcR;aAAY;YAExD,yEAAyE;YACzE,8CAA8C;YAC9C,IAAI,CAACA,YAAYI,GAAG,CAAC,WAAWJ,YAAYI,GAAG,CAAC,QAAQ;gBACtDI,MAAMC,IAAI,CAAC;YACb;YAEA,wEAAwE;YACxE,oDAAoD;YACpD,MAAMC,UAAU;gBAAEC,OAAOH,MAAMI,IAAI,GAAGC,IAAI,CAAC;YAAM;YAEjD,oEAAoE;YACpE,kBAAkB;YAClBjB,QAAQkB,OAAO,GAAG,IAAM,IAAIC,SAAS,MAAM;oBAAEC,QAAQ;oBAAKN;gBAAQ;YAElE,mCAAmC;YACnCV,YAAYO,GAAG,CAAC;YAEhB;QACF;QAEA,MAAM,IAAIU,MACR,CAAC,0EAA0E,EAAElB,OAAO,CAAC;IAEzF;IAEA,OAAOH;AACT"}