'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  BarChart3,
  Users,
  Package,
  Settings,
  TrendingUp,
  ShoppingCart,
  MessageCircle,
  Star,
  Eye,
  DollarSign,
  Activity,
  Bell,
  Search,
  Filter,
  Download,
  RefreshCw,
  LogOut,
  Shield,
  Zap,
  Heart,
  Gift,
  Smartphone,
  Headphones,
  Brain
} from 'lucide-react';

export default function AdminDashboard() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [otp, setOtp] = useState('');
  const [generatedOTP, setGeneratedOTP] = useState('');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState({
    totalSales: 0,
    totalOrders: 0,
    totalCustomers: 0,
    totalProducts: 0,
    conversionRate: 0,
    avgOrderValue: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if already authenticated (you can add session storage here)
    const isAuth = sessionStorage.getItem('admin_authenticated');
    if (isAuth === 'true') {
      setIsAuthenticated(true);
      loadDashboardData();
    } else {
      setLoading(false);
    }
  }, []);

  const loadDashboardStats = async () => {
    try {
      // Load sync status
      const syncResponse = await fetch('/api/alidrop-sync?action=status');
      const syncData = await syncResponse.json();
      
      setStats({
        syncStatus: syncData.status || {},
        lastUpdated: new Date().toLocaleString()
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-8 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">🎛️ Deal4U Admin</h1>
              <p className="text-blue-100 text-lg">
                Unified admin control panel - All features in one place
              </p>
            </div>

            <div className="text-right">
              <Link
                href="/admin/master-control"
                className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-gray-50 transition-colors font-semibold text-lg"
              >
                <Settings className="w-5 h-5 mr-2" />
                Open Master Control
              </Link>
            </div>
          </div>
        </div>

        {/* Master Control Redirect */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="w-8 h-8 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              All Admin Features Consolidated!
            </h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We've combined all your admin features into one powerful Master Control Panel.
              No more jumping between multiple pages - everything you need is now in one place!
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="bg-blue-50 rounded-lg p-4">
                <Zap className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-blue-900">Auto Import</div>
                <div className="text-xs text-blue-700">AliExpress products</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <RefreshCw className="w-6 h-6 text-green-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-green-900">Smart Sync</div>
                <div className="text-xs text-green-700">Fix images & branding</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4">
                <Brain className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-purple-900">AI Categories</div>
                <div className="text-xs text-purple-700">Smart categorization</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4">
                <Settings className="w-6 h-6 text-orange-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-orange-900">Job Manager</div>
                <div className="text-xs text-orange-700">Track operations</div>
              </div>
            </div>

            <Link
              href="/admin/master-control"
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold text-lg"
            >
              <Settings className="w-5 h-5 mr-2" />
              Go to Master Control Panel
            </Link>
          </div>
        </div>

        {/* Quick Access */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Access</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/admin/master-control"
              className="flex items-center justify-center p-4 border-2 border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <div className="text-center">
                <Settings className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="font-medium text-blue-900">Master Control</div>
                <div className="text-xs text-blue-700">All admin features</div>
              </div>
            </Link>

            <Link
              href="/"
              className="flex items-center justify-center p-4 border-2 border-green-200 rounded-lg hover:bg-green-50 transition-colors"
            >
              <div className="text-center">
                <ShoppingCart className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-medium text-green-900">View Store</div>
                <div className="text-xs text-green-700">Customer view</div>
              </div>
            </Link>

            <div className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg bg-gray-50">
              <div className="text-center">
                <div className="w-8 h-8 bg-gray-400 rounded-full mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">159</span>
                </div>
                <div className="font-medium text-gray-900">Total Products</div>
                <div className="text-xs text-gray-700">In your store</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
