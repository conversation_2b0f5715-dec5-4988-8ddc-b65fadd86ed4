/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth-1.0a";
exports.ids = ["vendor-chunks/oauth-1.0a"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth-1.0a/oauth-1.0a.js":
/*!***********************************************!*\
  !*** ./node_modules/oauth-1.0a/oauth-1.0a.js ***!
  \***********************************************/
/***/ ((module) => {

eval("if (true) {\n    module.exports = OAuth;\n}\n\n/**\n * Constructor\n * @param {Object} opts consumer key and secret\n */\nfunction OAuth(opts) {\n    if(!(this instanceof OAuth)) {\n        return new OAuth(opts);\n    }\n\n    if(!opts) {\n        opts = {};\n    }\n\n    if(!opts.consumer) {\n        throw new Error('consumer option is required');\n    }\n\n    this.consumer            = opts.consumer;\n    this.nonce_length        = opts.nonce_length || 32;\n    this.version             = opts.version || '1.0';\n    this.parameter_seperator = opts.parameter_seperator || ', ';\n    this.realm               = opts.realm;\n\n    if(typeof opts.last_ampersand === 'undefined') {\n        this.last_ampersand = true;\n    } else {\n        this.last_ampersand = opts.last_ampersand;\n    }\n\n    // default signature_method is 'PLAINTEXT'\n    this.signature_method = opts.signature_method || 'PLAINTEXT';\n\n    if(this.signature_method == 'PLAINTEXT' && !opts.hash_function) {\n        opts.hash_function = function(base_string, key) {\n            return key;\n        }\n    }\n\n    if(!opts.hash_function) {\n        throw new Error('hash_function option is required');\n    }\n\n    this.hash_function = opts.hash_function;\n    this.body_hash_function = opts.body_hash_function || this.hash_function;\n}\n\n/**\n * OAuth request authorize\n * @param  {Object} request data\n * {\n *     method,\n *     url,\n *     data\n * }\n * @param  {Object} key and secret token\n * @return {Object} OAuth Authorized data\n */\nOAuth.prototype.authorize = function(request, token) {\n    var oauth_data = {\n        oauth_consumer_key: this.consumer.key,\n        oauth_nonce: this.getNonce(),\n        oauth_signature_method: this.signature_method,\n        oauth_timestamp: this.getTimeStamp(),\n        oauth_version: this.version\n    };\n\n    if(!token) {\n        token = {};\n    }\n\n    if(token.key !== undefined) {\n        oauth_data.oauth_token = token.key;\n    }\n\n    if(!request.data) {\n        request.data = {};\n    }\n\n    if(request.includeBodyHash) {\n      oauth_data.oauth_body_hash = this.getBodyHash(request, token.secret)\n    }\n\n    oauth_data.oauth_signature = this.getSignature(request, token.secret, oauth_data);\n\n    return oauth_data;\n};\n\n/**\n * Create a OAuth Signature\n * @param  {Object} request data\n * @param  {Object} token_secret key and secret token\n * @param  {Object} oauth_data   OAuth data\n * @return {String} Signature\n */\nOAuth.prototype.getSignature = function(request, token_secret, oauth_data) {\n    return this.hash_function(this.getBaseString(request, oauth_data), this.getSigningKey(token_secret));\n};\n\n/**\n * Create a OAuth Body Hash\n * @param {Object} request data\n */\nOAuth.prototype.getBodyHash = function(request, token_secret) {\n  var body = typeof request.data === 'string' ? request.data : JSON.stringify(request.data)\n\n  if (!this.body_hash_function) {\n    throw new Error('body_hash_function option is required');\n  }\n\n  return this.body_hash_function(body, this.getSigningKey(token_secret))\n};\n\n/**\n * Base String = Method + Base Url + ParameterString\n * @param  {Object} request data\n * @param  {Object} OAuth data\n * @return {String} Base String\n */\nOAuth.prototype.getBaseString = function(request, oauth_data) {\n    return request.method.toUpperCase() + '&' + this.percentEncode(this.getBaseUrl(request.url)) + '&' + this.percentEncode(this.getParameterString(request, oauth_data));\n};\n\n/**\n * Get data from url\n * -> merge with oauth data\n * -> percent encode key & value\n * -> sort\n *\n * @param  {Object} request data\n * @param  {Object} OAuth data\n * @return {Object} Parameter string data\n */\nOAuth.prototype.getParameterString = function(request, oauth_data) {\n    var base_string_data;\n    if (oauth_data.oauth_body_hash) {\n        base_string_data = this.sortObject(this.percentEncodeData(this.mergeObject(oauth_data, this.deParamUrl(request.url))));\n    } else {\n        base_string_data = this.sortObject(this.percentEncodeData(this.mergeObject(oauth_data, this.mergeObject(request.data, this.deParamUrl(request.url)))));\n    }\n\n    var data_str = '';\n\n    //base_string_data to string\n    for(var i = 0; i < base_string_data.length; i++) {\n        var key = base_string_data[i].key;\n        var value = base_string_data[i].value;\n        // check if the value is an array\n        // this means that this key has multiple values\n        if (value && Array.isArray(value)){\n          // sort the array first\n          value.sort();\n\n          var valString = \"\";\n          // serialize all values for this key: e.g. formkey=formvalue1&formkey=formvalue2\n          value.forEach((function(item, i){\n            valString += key + '=' + item;\n            if (i < value.length){\n              valString += \"&\";\n            }\n          }).bind(this));\n          data_str += valString;\n        } else {\n          data_str += key + '=' + value + '&';\n        }\n    }\n\n    //remove the last character\n    data_str = data_str.substr(0, data_str.length - 1);\n    return data_str;\n};\n\n/**\n * Create a Signing Key\n * @param  {String} token_secret Secret Token\n * @return {String} Signing Key\n */\nOAuth.prototype.getSigningKey = function(token_secret) {\n    token_secret = token_secret || '';\n\n    if(!this.last_ampersand && !token_secret) {\n        return this.percentEncode(this.consumer.secret);\n    }\n\n    return this.percentEncode(this.consumer.secret) + '&' + this.percentEncode(token_secret);\n};\n\n/**\n * Get base url\n * @param  {String} url\n * @return {String}\n */\nOAuth.prototype.getBaseUrl = function(url) {\n    return url.split('?')[0];\n};\n\n/**\n * Get data from String\n * @param  {String} string\n * @return {Object}\n */\nOAuth.prototype.deParam = function(string) {\n    var arr = string.split('&');\n    var data = {};\n\n    for(var i = 0; i < arr.length; i++) {\n        var item = arr[i].split('=');\n\n        // '' value\n        item[1] = item[1] || '';\n\n        // check if the key already exists\n        // this can occur if the QS part of the url contains duplicate keys like this: ?formkey=formvalue1&formkey=formvalue2\n        if (data[item[0]]){\n          // the key exists already\n          if (!Array.isArray(data[item[0]])) {\n            // replace the value with an array containing the already present value\n            data[item[0]] = [data[item[0]]];\n          }\n          // and add the new found value to it\n          data[item[0]].push(decodeURIComponent(item[1]));\n        } else {\n          // it doesn't exist, just put the found value in the data object\n          data[item[0]] = decodeURIComponent(item[1]);\n        }\n    }\n\n    return data;\n};\n\n/**\n * Get data from url\n * @param  {String} url\n * @return {Object}\n */\nOAuth.prototype.deParamUrl = function(url) {\n    var tmp = url.split('?');\n\n    if (tmp.length === 1)\n        return {};\n\n    return this.deParam(tmp[1]);\n};\n\n/**\n * Percent Encode\n * @param  {String} str\n * @return {String} percent encoded string\n */\nOAuth.prototype.percentEncode = function(str) {\n    return encodeURIComponent(str)\n        .replace(/\\!/g, \"%21\")\n        .replace(/\\*/g, \"%2A\")\n        .replace(/\\'/g, \"%27\")\n        .replace(/\\(/g, \"%28\")\n        .replace(/\\)/g, \"%29\");\n};\n\n/**\n * Percent Encode Object\n * @param  {Object} data\n * @return {Object} percent encoded data\n */\nOAuth.prototype.percentEncodeData = function(data) {\n    var result = {};\n\n    for(var key in data) {\n        var value = data[key];\n        // check if the value is an array\n        if (value && Array.isArray(value)){\n          var newValue = [];\n          // percentEncode every value\n          value.forEach((function(val){\n            newValue.push(this.percentEncode(val));\n          }).bind(this));\n          value = newValue;\n        } else {\n          value = this.percentEncode(value);\n        }\n        result[this.percentEncode(key)] = value;\n    }\n\n    return result;\n};\n\n/**\n * Get OAuth data as Header\n * @param  {Object} oauth_data\n * @return {String} Header data key - value\n */\nOAuth.prototype.toHeader = function(oauth_data) {\n    var sorted = this.sortObject(oauth_data);\n\n    var header_value = 'OAuth ';\n\n    if (this.realm) {\n        header_value += 'realm=\"' + this.realm + '\"' + this.parameter_seperator;\n    }\n\n    for(var i = 0; i < sorted.length; i++) {\n        if (sorted[i].key.indexOf('oauth_') !== 0)\n            continue;\n\n        header_value += this.percentEncode(sorted[i].key) + '=\"' + this.percentEncode(sorted[i].value) + '\"' + this.parameter_seperator;\n    }\n\n    return {\n        Authorization: header_value.substr(0, header_value.length - this.parameter_seperator.length) //cut the last chars\n    };\n};\n\n/**\n * Create a random word characters string with input length\n * @return {String} a random word characters string\n */\nOAuth.prototype.getNonce = function() {\n    var word_characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var result = '';\n\n    for(var i = 0; i < this.nonce_length; i++) {\n        result += word_characters[parseInt(Math.random() * word_characters.length, 10)];\n    }\n\n    return result;\n};\n\n/**\n * Get Current Unix TimeStamp\n * @return {Int} current unix timestamp\n */\nOAuth.prototype.getTimeStamp = function() {\n    return parseInt(new Date().getTime()/1000, 10);\n};\n\n////////////////////// HELPER FUNCTIONS //////////////////////\n\n/**\n * Merge object\n * @param  {Object} obj1\n * @param  {Object} obj2\n * @return {Object}\n */\nOAuth.prototype.mergeObject = function(obj1, obj2) {\n    obj1 = obj1 || {};\n    obj2 = obj2 || {};\n\n    var merged_obj = obj1;\n    for(var key in obj2) {\n        merged_obj[key] = obj2[key];\n    }\n    return merged_obj;\n};\n\n/**\n * Sort object by key\n * @param  {Object} data\n * @return {Array} sorted array\n */\nOAuth.prototype.sortObject = function(data) {\n    var keys = Object.keys(data);\n    var result = [];\n\n    keys.sort();\n\n    for(var i = 0; i < keys.length; i++) {\n        var key = keys[i];\n        result.push({\n            key: key,\n            value: data[key],\n        });\n    }\n\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth-1.0a/oauth-1.0a.js\n");

/***/ })

};
;