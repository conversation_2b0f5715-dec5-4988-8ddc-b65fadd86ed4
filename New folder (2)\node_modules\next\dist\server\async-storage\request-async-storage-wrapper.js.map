{"version": 3, "sources": ["../../../src/server/async-storage/request-async-storage-wrapper.ts"], "names": ["RequestAsyncStorageWrapper", "getHeaders", "headers", "cleaned", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "param", "FLIGHT_PARAMETERS", "delete", "toString", "toLowerCase", "seal", "getMutableCookies", "onUpdateCookies", "cookies", "RequestCookies", "MutableRequestCookiesAdapter", "wrap", "mergeMiddlewareCookies", "req", "existingCookies", "setCookieValue", "responseHeaders", "Headers", "cookie", "splitCookiesString", "append", "responseCookies", "ResponseCookies", "getAll", "set", "storage", "res", "renderOpts", "callback", "previewProps", "undefined", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "store", "requestCookies", "RequestCookiesAdapter", "mutableCookies", "draftMode", "DraftModeProvider", "reactLoadableManifest", "assetPrefix", "run"], "mappings": ";;;;+BA2EaA;;;eAAAA;;;kCAlEqB;yBAI3B;gCAKA;yBACyC;mCACd;uBACC;AAEnC,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACH;IACpC,KAAK,MAAMI,SAASC,mCAAiB,CAAE;QACrCJ,QAAQK,MAAM,CAACF,MAAMG,QAAQ,GAAGC,WAAW;IAC7C;IAEA,OAAON,uBAAc,CAACO,IAAI,CAACR;AAC7B;AAEA,SAASS,kBACPV,OAAsC,EACtCW,eAA6C;IAE7C,MAAMC,UAAU,IAAIC,uBAAc,CAACX,uBAAc,CAACC,IAAI,CAACH;IACvD,OAAOc,4CAA4B,CAACC,IAAI,CAACH,SAASD;AACpD;AAQA;;;;CAIC,GACD,SAASK,uBACPC,GAA0B,EAC1BC,eAAiD;IAEjD,IACE,6BAA6BD,IAAIjB,OAAO,IACxC,OAAOiB,IAAIjB,OAAO,CAAC,0BAA0B,KAAK,UAClD;QACA,MAAMmB,iBAAiBF,IAAIjB,OAAO,CAAC,0BAA0B;QAC7D,MAAMoB,kBAAkB,IAAIC;QAE5B,KAAK,MAAMC,UAAUC,IAAAA,yBAAkB,EAACJ,gBAAiB;YACvDC,gBAAgBI,MAAM,CAAC,cAAcF;QACvC;QAEA,MAAMG,kBAAkB,IAAIC,wBAAe,CAACN;QAE5C,0DAA0D;QAC1D,KAAK,MAAME,UAAUG,gBAAgBE,MAAM,GAAI;YAC7CT,gBAAgBU,GAAG,CAACN;QACtB;IACF;AACF;AAEO,MAAMxB,6BAGT;IACF;;;;;;;;GAQC,GACDiB,MACEc,OAAwC,EACxC,EAAEZ,GAAG,EAAEa,GAAG,EAAEC,UAAU,EAAkB,EACxCC,QAAyC;QAEzC,IAAIC,eAA8CC;QAElD,IAAIH,cAAc,kBAAkBA,YAAY;YAC9C,yDAAyD;YACzDE,eAAe,AAACF,WAAmBE,YAAY;QACjD;QAEA,SAASE,uBAAuBvB,OAAiB;YAC/C,IAAIkB,KAAK;gBACPA,IAAIM,SAAS,CAAC,cAAcxB;YAC9B;QACF;QAEA,MAAMyB,QAKF,CAAC;QAEL,MAAMC,QAAsB;YAC1B,IAAItC,WAAU;gBACZ,IAAI,CAACqC,MAAMrC,OAAO,EAAE;oBAClB,oEAAoE;oBACpE,8BAA8B;oBAC9BqC,MAAMrC,OAAO,GAAGD,WAAWkB,IAAIjB,OAAO;gBACxC;gBAEA,OAAOqC,MAAMrC,OAAO;YACtB;YACA,IAAIY,WAAU;gBACZ,IAAI,CAACyB,MAAMzB,OAAO,EAAE;oBAClB,4DAA4D;oBAC5D,2DAA2D;oBAC3D,MAAM2B,iBAAiB,IAAI1B,uBAAc,CACvCX,uBAAc,CAACC,IAAI,CAACc,IAAIjB,OAAO;oBAGjCgB,uBAAuBC,KAAKsB;oBAE5B,oEAAoE;oBACpE,8BAA8B;oBAC9BF,MAAMzB,OAAO,GAAG4B,qCAAqB,CAAC/B,IAAI,CAAC8B;gBAC7C;gBAEA,OAAOF,MAAMzB,OAAO;YACtB;YACA,IAAI6B,kBAAiB;gBACnB,IAAI,CAACJ,MAAMI,cAAc,EAAE;oBACzB,MAAMA,iBAAiB/B,kBACrBO,IAAIjB,OAAO,EACX+B,CAAAA,8BAAAA,WAAYpB,eAAe,KACxBmB,CAAAA,MAAMK,yBAAyBD,SAAQ;oBAG5ClB,uBAAuBC,KAAKwB;oBAE5BJ,MAAMI,cAAc,GAAGA;gBACzB;gBACA,OAAOJ,MAAMI,cAAc;YAC7B;YACA,IAAIC,aAAY;gBACd,IAAI,CAACL,MAAMK,SAAS,EAAE;oBACpBL,MAAMK,SAAS,GAAG,IAAIC,oCAAiB,CACrCV,cACAhB,KACA,IAAI,CAACL,OAAO,EACZ,IAAI,CAAC6B,cAAc;gBAEvB;gBAEA,OAAOJ,MAAMK,SAAS;YACxB;YACAE,uBAAuBb,CAAAA,8BAAAA,WAAYa,qBAAqB,KAAI,CAAC;YAC7DC,aAAad,CAAAA,8BAAAA,WAAYc,WAAW,KAAI;QAC1C;QAEA,OAAOhB,QAAQiB,GAAG,CAACR,OAAON,UAAUM;IACtC;AACF"}