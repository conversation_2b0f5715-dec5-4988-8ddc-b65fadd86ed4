{"version": 3, "sources": ["../../../src/build/output/index.ts"], "names": ["ampValidation", "formatAmpMessages", "reportTrigger", "startedDevelopmentServer", "watchCompilers", "appUrl", "bindAddr", "consoleStore", "setState", "amp", "output", "bold", "messages", "chalkError", "red", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "yellow", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "textTable", "align", "stringLength", "str", "stripAnsi", "buildStore", "createStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "url", "getState", "loading", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "undefined", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "formatWebpackMessages", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "COMPILER_NAMES", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAsMgBA,aAAa;eAAbA;;IAxJAC,iBAAiB;eAAjBA;;IAsRAC,aAAa;eAAbA;;IAzTAC,wBAAwB;eAAxBA;;IAqNAC,cAAc;eAAdA;;;4BAhOkB;kEACZ;kEACA;iEACE;8EACU;uBACI;2BAGP;;;;;;AAGxB,SAASD,yBAAyBE,MAAc,EAAEC,QAAgB;IACvEC,YAAY,CAACC,QAAQ,CAAC;QAAEH;QAAQC;IAAS;AAC3C;AAiCO,SAASL,kBAAkBQ,GAAkB;IAClD,IAAIC,SAASC,IAAAA,gBAAI,EAAC,oBAAoB;IACtC,IAAIC,WAAuB,EAAE;IAE7B,MAAMC,aAAaC,IAAAA,eAAG,EAAC;IACvB,SAASC,SAASC,IAAY,EAAEC,KAAgB;QAC9CL,SAASM,IAAI,CAAC;YAACF;YAAMH;YAAYI,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYC,IAAAA,kBAAM,EAAC;IACzB,SAASC,QAAQP,IAAY,EAAEQ,IAAe;QAC5CZ,SAASM,IAAI,CAAC;YAACF;YAAMK;YAAWG,KAAKL,OAAO;YAAEK,KAAKJ,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQP,IAAK;QACtB,IAAI,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGjB,GAAG,CAACO,KAAK;QAEpC,MAAMW,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBhB,SAASC,MAAMS,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDjB,SAAS,IAAIU,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKf,MAAMU,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACApB,SAASM,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACN,SAASmB,MAAM,EAAE;QACpB,OAAO;IACT;IAEArB,UAAUuB,IAAAA,kBAAS,EAACrB,UAAU;QAC5BsB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOC,IAAAA,kBAAS,EAACD,KAAKL,MAAM;QAC9B;IACF;IAEA,OAAOrB;AACT;AAEA,MAAM4B,aAAaC,IAAAA,iBAAW,EAAmB;IAC/C,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BR,WAAWS,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEvC,GAAG,EAAE+B,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAEC,GAAG,EAAE,GAAGF;IAE1D,MAAM,EAAE3C,MAAM,EAAE,GAAGE,YAAY,CAAC4C,QAAQ;IAExC,IAAIX,OAAOY,OAAO,IAAIX,OAAOW,OAAO,KAAIV,8BAAAA,WAAYU,OAAO,GAAE;QAC3D7C,YAAY,CAACC,QAAQ,CACnB;YACE6C,WAAW;YACXhD,QAAQA;YACR,wEAAwE;YACxE+C,SAAS;YACTH;YACAC;QACF,GACA;QAEFN,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOY,OAAO;QACxEP,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOW,OAAO;QACxEN,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWU,OAAO;QAC/DT,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIW,eAAqC;QACvCD,WAAW;QACXhD,QAAQA;QACR+C,SAAS;QACTG,cAAc;QACdC,mBACE,AAACZ,CAAAA,mBAAmBJ,OAAOgB,iBAAiB,GAAG,CAAA,IAC9CX,CAAAA,mBAAmBJ,OAAOe,iBAAiB,GAAG,CAAA,IAC9CV,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYc,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACf;IACnB;IACA,IAAIF,OAAOf,MAAM,IAAImB,kBAAkB;QACrC,0BAA0B;QAC1BrC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG8C,YAAY;YACf7B,QAAQe,OAAOf,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIe,OAAOhB,MAAM,IAAIoB,kBAAkB;QAC5CtC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG8C,YAAY;YACf7B,QAAQgB,OAAOhB,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIgB,WAAWjB,MAAM,IAAIqB,sBAAsB;QACpDvC,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG8C,YAAY;YACf7B,QAAQiB,WAAWjB,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXc,OAAOd,QAAQ,IAAI,EAAE;eACrBe,OAAOf,QAAQ,IAAI,EAAE;eACrBgB,WAAWhB,QAAQ,IAAI,EAAE;SAC9B,CAACgC,MAAM,CAACzD,kBAAkBQ,QAAQ,EAAE;QAErCF,YAAY,CAACC,QAAQ,CACnB;YACE,GAAG8C,YAAY;YACf7B,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEO,SAAS1B,cACdgB,IAAY,EACZS,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEjB,GAAG,EAAE,GAAG6B,WAAWa,QAAQ;IACnC,IAAI,CAAE1B,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCO,WAAW9B,QAAQ,CAAC;YAClBC,KAAKkD,OAAOC,IAAI,CAACnD,KACdqB,MAAM,CAAC,CAAC+B,IAAMA,MAAM7C,MACpB8C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGxD,GAAG,CAACwD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGzD,GAAG;QAAE,CAACO,KAAK,EAAE;YAAES;YAAQC;QAAS;IAAE;IACrEY,WAAW9B,QAAQ,CAAC;QAClBC,KAAKkD,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEO,SAAS5D,eACdoC,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BJ,WAAW9B,QAAQ,CAAC;QAClBgC,QAAQ;YAAEY,SAAS;QAAK;QACxBX,QAAQ;YAAEW,SAAS;QAAK;QACxBV,YAAY;YAAEU,SAAS;QAAK;QAC5BH,SAAS;QACTC,KAAKiB;IACP;IAEA,SAASC,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,IAAI,CAAC,EAAE;YACjDE,QAAQ;gBAAEnB,SAAS;YAAK;QAC1B;QAEAkB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,IAAI,CAAC,EAAE,CAACO;YAC5CtC,WAAW9B,QAAQ,CAAC;gBAAEC,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEgB,MAAM,EAAEC,QAAQ,EAAE,GAAGmD,IAAAA,8BAAqB,EAChDD,MAAME,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACxD,0BAAAA,OAAQM,MAAM;YAClC,MAAMmD,cAAc,CAAC,EAACxD,4BAAAA,SAAUK,MAAM;YAEtCwC,QAAQ;gBACNnB,SAAS;gBACTI,mBAAmBoB,MAAMO,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjD5D,QAAQwD,YAAYxD,SAAS;gBAC7BC,UAAUwD,cAAcxD,WAAW;YACrC;QACF;IACF;IAEA0C,YAAYkB,yBAAc,CAAC9C,MAAM,EAAEA,QAAQ,CAAC+C;QAC1C,IACE,CAACA,OAAOnC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGT,UAAU,CAACU,OAAO,IACzCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAlB,WAAW9B,QAAQ,CAAC;gBAClBgC,QAAQ+C;gBACRtC,SAASkB;gBACTjB,KAAKiB;YACP;QACF,OAAO;YACL7B,WAAW9B,QAAQ,CAAC;gBAClBgC,QAAQ+C;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAC7C,MAAM,EAAEA,QAAQ,CAAC8C;QAC1C,IACE,CAACA,OAAOnC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGT,UAAU,CAACU,OAAO,IACzCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAlB,WAAW9B,QAAQ,CAAC;gBAClBiC,QAAQ8C;gBACRtC,SAASkB;gBACTjB,KAAKiB;YACP;QACF,OAAO;YACL7B,WAAW9B,QAAQ,CAAC;gBAClBiC,QAAQ8C;YACV;QACF;IACF;IACAnB,YAAYkB,yBAAc,CAAC5C,UAAU,EAAEA,YAAY,CAAC6C;QAClD,IACE,CAACA,OAAOnC,OAAO,IACf,CAACd,WAAWa,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrC,CAACd,WAAWa,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAlB,WAAW9B,QAAQ,CAAC;gBAClBkC,YAAY6C;gBACZtC,SAASkB;gBACTjB,KAAKiB;YACP;QACF,OAAO;YACL7B,WAAW9B,QAAQ,CAAC;gBAClBkC,YAAY6C;YACd;QACF;IACF;AACF;AAEO,SAASrF,cAAc+C,OAAe,EAAEC,GAAY;IACzDZ,WAAW9B,QAAQ,CAAC;QAClByC;QACAC;IACF;AACF"}