{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "names": ["BailoutToCSRError", "staticGenerationAsyncStorage", "bailoutToClientRendering", "reason", "staticGenerationStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,OAAO,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,wBAAwBH,6BAA6BI,QAAQ;IAEnE,IAAID,yCAAAA,sBAAuBE,WAAW,EAAE;IAExC,IAAIF,yCAAAA,sBAAuBG,kBAAkB,EAC3C,MAAM,IAAIP,kBAAkBG;AAChC"}