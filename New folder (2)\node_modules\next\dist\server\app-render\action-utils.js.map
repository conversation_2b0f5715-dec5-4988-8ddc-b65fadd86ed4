{"version": 3, "sources": ["../../../src/server/app-render/action-utils.ts"], "names": ["createServerModuleMap", "selectWorkerForForwarding", "serverActionsManifest", "pageName", "Proxy", "get", "_", "id", "process", "env", "NEXT_RUNTIME", "workers", "normalizeWorkerPageName", "name", "chunks", "actionId", "worker<PERSON>ame", "denormalizeWorkerPageName", "Object", "keys", "pathHasPrefix", "bundlePath", "normalizeAppPath", "removePathPrefix"], "mappings": ";;;;;;;;;;;;;;;IASgBA,qBAAqB;eAArBA;;IA2BAC,yBAAyB;eAAzBA;;;0BAnCiB;+BACH;kCACG;AAM1B,SAASD,sBAAsB,EACpCE,qBAAqB,EACrBC,QAAQ,EAIT;IACC,OAAO,IAAIC,MACT,CAAC,GACD;QACEC,KAAK,CAACC,GAAGC;YACP,OAAO;gBACLA,IAAIL,qBAAqB,CACvBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACH,GAAG,CAACI,OAAO,CAACC,wBAAwBT,UAAU;gBAChDU,MAAMN;gBACNO,QAAQ,EAAE;YACZ;QACF;IACF;AAEJ;AAMO,SAASb,0BACdc,QAAgB,EAChBZ,QAAgB,EAChBD,qBAAqC;QAGnCA;IADF,MAAMS,WACJT,mCAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACK,SAAS,qBAFXb,iCAEaS,OAAO;IACtB,MAAMK,aAAaJ,wBAAwBT;IAE3C,oCAAoC;IACpC,IAAI,CAACQ,SAAS;IAEd,6DAA6D;IAC7D,IAAIA,OAAO,CAACK,WAAW,EAAE;QACvB;IACF;IAEA,yEAAyE;IACzE,OAAOC,0BAA0BC,OAAOC,IAAI,CAACR,QAAQ,CAAC,EAAE;AAC1D;AAEA;;;CAGC,GACD,SAASC,wBAAwBT,QAAgB;IAC/C,IAAIiB,IAAAA,4BAAa,EAACjB,UAAU,QAAQ;QAClC,OAAOA;IACT;IAEA,OAAO,QAAQA;AACjB;AAEA;;CAEC,GACD,SAASc,0BAA0BI,UAAkB;IACnD,OAAOC,IAAAA,0BAAgB,EAACC,IAAAA,kCAAgB,EAACF,YAAY;AACvD"}