{"version": 3, "sources": ["../../src/lib/patch-incorrect-lockfile.ts"], "names": ["promises", "Log", "findUp", "nextPkgJson", "isCI", "getRegistry", "registry", "fetchPkgInfo", "pkg", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "patchIncorrectLockfile", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "cwd", "content", "readFile", "endingNewline", "endsWith", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "filter", "startsWith", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "warn", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,YAAY,6BAA4B;AAC/C,2BAA2B;AAC3B,OAAOC,iBAAiB,oBAAmB;AAE3C,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,IAAIC;AAEJ,eAAeC,aAAaC,GAAW;IACrC,IAAI,CAACF,UAAUA,WAAWD;IAC1B,MAAMI,MAAM,MAAMC,MAAM,CAAC,EAAEJ,SAAS,EAAEE,IAAI,CAAC;IAE3C,IAAI,CAACC,IAAIE,EAAE,EAAE;QACX,MAAM,IAAIC,MACR,CAAC,kCAAkC,EAAEJ,IAAI,aAAa,EAAEC,IAAII,MAAM,CAAC,CAAC;IAExE;IACA,MAAMC,OAAO,MAAML,IAAIM,IAAI;IAC3B,MAAMC,cAAcF,KAAKG,QAAQ,CAACd,YAAYe,OAAO,CAAC;IAEtD,OAAO;QACLC,IAAIH,YAAYG,EAAE;QAClBC,KAAKJ,YAAYI,GAAG;QACpBC,SAASL,YAAYK,OAAO;QAC5BC,SAASN,YAAYO,IAAI,CAACD,OAAO;QACjCE,WAAWR,YAAYO,IAAI,CAACC,SAAS;IACvC;AACF;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,uBAAuBC,GAAW;IACtD,IAAIC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C;IACF;IACA,MAAMC,eAAe,MAAM5B,OAAO,qBAAqB;QAAE6B,KAAKL;IAAI;IAElE,IAAI,CAACI,cAAc;QACjB,oDAAoD;QACpD;IACF;IACA,MAAME,UAAU,MAAMhC,SAASiC,QAAQ,CAACH,cAAc;IACtD,+BAA+B;IAC/B,MAAMI,gBAAgBF,QAAQG,QAAQ,CAAC,UACnC,SACAH,QAAQG,QAAQ,CAAC,QACjB,OACA;IAEJ,MAAMC,iBAAiBC,KAAKC,KAAK,CAACN;IAClC,MAAMO,kBAAkBC,SAASJ,kCAAAA,eAAgBG,eAAe,EAAE;IAClE,MAAME,kBAAkBC,OAAOC,IAAI,CACjCxC,WAAW,CAAC,uBAAuB,IAAI,CAAC,GACxCyC,MAAM,CAAC,CAACpC,MAAQA,IAAIqC,UAAU,CAAC;IAEjC,MAAMC,kBAAkB,CACtBtC,KACAuC;QAEAX,eAAeY,YAAY,CAACxC,IAAI,GAAG;YACjCU,SAASf,YAAYe,OAAO;YAC5B+B,UAAUF,QAAQzB,OAAO;YACzBE,WAAWuB,QAAQvB,SAAS;YAC5B0B,UAAU;QACZ;IACF;IAEA,MAAMC,eAAe,CACnB3C,KACAuC;QAEAX,eAAegB,QAAQ,CAAC5C,IAAI,GAAG;YAC7BU,SAASf,YAAYe,OAAO;YAC5B+B,UAAUF,QAAQzB,OAAO;YACzBE,WAAWuB,QAAQvB,SAAS;YAC5BJ,KAAK2B,QAAQ3B,GAAG;YAChB8B,UAAU;YACV/B,IAAI4B,QAAQ5B,EAAE;YACdE,SAAS0B,QAAQ1B,OAAO;QAC1B;IACF;IAEA,IAAI;QACF,MAAMgC,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAEnC,IAAI,CAACA,kBAAkBC,QAAQ,CAACf,kBAAkB;YAChD,8BAA8B;YAC9B;QACF;QACA,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMgB,0BACJhB,oBAAoB,KAAKA,oBAAoB;QAC/C,MAAMiB,sBAAsBjB,oBAAoB,KAAKA,oBAAoB;QAEzE,IACE,AAACgB,2BAA2B,CAACnB,eAAeY,YAAY,IACvDQ,uBAAuB,CAACpB,eAAegB,QAAQ,EAChD;YACA,2BAA2B;YAC3B;QACF;QACA,MAAMK,iBAAiB,EAAE;QACzB,IAAIC;QAEJ,IAAIF,qBAAqB;YACvBE,YAAY;YACZ,KAAK,MAAMlD,OAAOkC,OAAOC,IAAI,CAACP,eAAegB,QAAQ,EAAG;gBACtD,IAAI5C,IAAI2B,QAAQ,CAAC,sBAAsB;oBACrCuB,YAAYlD,IAAImD,SAAS,CAAC,GAAGnD,IAAIoD,MAAM,GAAG;gBAC5C;YACF;YAEA,IAAI,CAACF,WAAW;gBACd,4CAA4C;gBAC5C;YACF;QACF;QAEA,KAAK,MAAMlD,OAAOiC,gBAAiB;YACjC,IACE,AAACc,2BAA2B,CAACnB,eAAeY,YAAY,CAACxC,IAAI,IAC5DgD,uBAAuB,CAACpB,eAAegB,QAAQ,CAAC,CAAC,EAAEM,UAAU,EAAElD,IAAI,CAAC,CAAC,EACtE;gBACAiD,eAAeI,IAAI,CAACrD;YACtB;QACF;QACA,IAAIiD,eAAeG,MAAM,KAAK,GAAG;YAC/B;QACF;QACA3D,IAAI6D,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1C1D,OAAO,4CAA4C;QAGrD,IAAIA,MAAM;YACR,8DAA8D;YAC9D;QACF;QACA,MAAM2D,WAAW,MAAMC,QAAQC,GAAG,CAChCR,eAAeS,GAAG,CAAC,CAAC1D,MAAQD,aAAaC;QAG3C,IAAK,IAAI2D,IAAI,GAAGA,IAAIJ,SAASH,MAAM,EAAEO,IAAK;YACxC,MAAM3D,MAAMiD,cAAc,CAACU,EAAE;YAC7B,MAAMpB,UAAUgB,QAAQ,CAACI,EAAE;YAE3B,IAAIZ,yBAAyB;gBAC3BT,gBAAgBtC,KAAKuC;YACvB;YACA,IAAIS,qBAAqB;gBACvBL,aAAa,CAAC,EAAEO,UAAU,EAAElD,IAAI,CAAC,EAAEuC;YACrC;QACF;QAEA,MAAM/C,SAASoE,SAAS,CACtBtC,cACAO,KAAKgC,SAAS,CAACjC,gBAAgB,MAAM,KAAKF;QAE5CjC,IAAI6D,IAAI,CACN;IAEJ,EAAE,OAAOQ,KAAK;QACZrE,IAAIsE,KAAK,CACP,CAAC,yFAAyF,CAAC;QAE7FC,QAAQD,KAAK,CAACD;IAChB;AACF"}