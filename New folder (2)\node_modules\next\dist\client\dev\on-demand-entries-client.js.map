{"version": 3, "sources": ["../../../src/client/dev/on-demand-entries-client.ts"], "names": ["page", "setInterval", "sendMessage", "JSON", "stringify", "event", "Router", "ready", "notFoundSrcPage", "self", "__NEXT_DATA__", "pathname"], "mappings": ";;;;+BAGA;;;eAAA;;;;iEAHmB;2BACS;MAE5B,WAAe,OAAOA;IACpB,IAAIA,MAAM;QACR,wDAAwD;QACxD,sDAAsD;QACtDC,YAAY;YACVC,IAAAA,sBAAW,EAACC,KAAKC,SAAS,CAAC;gBAAEC,OAAO;gBAAQL;YAAK;QACnD,GAAG;IACL,OAAO;QACLM,eAAM,CAACC,KAAK,CAAC;YACXN,YAAY;gBACV,iEAAiE;gBACjE,gEAAgE;gBAChE,+DAA+D;gBAC/D,MAAMO,kBAAkBC,KAAKC,aAAa,CAACF,eAAe;gBAC1D,MAAMG,WACJ,AAACL,CAAAA,eAAM,CAACK,QAAQ,KAAK,UAAUL,eAAM,CAACK,QAAQ,KAAK,SAAQ,KAC3DH,kBACIA,kBACAF,eAAM,CAACK,QAAQ;gBAErBT,IAAAA,sBAAW,EAACC,KAAKC,SAAS,CAAC;oBAAEC,OAAO;oBAAQL,MAAMW;gBAAS;YAC7D,GAAG;QACL;IACF;AACF"}