/**
 * lucide-react v0.292.0 - ISC
 */

const dynamicIconImports = {
  "accessibility": () => import('./dist/esm/icons/accessibility.js'),
  "activity-square": () => import('./dist/esm/icons/activity-square.js'),
  "activity": () => import('./dist/esm/icons/activity.js'),
  "air-vent": () => import('./dist/esm/icons/air-vent.js'),
  "airplay": () => import('./dist/esm/icons/airplay.js'),
  "alarm-check": () => import('./dist/esm/icons/alarm-check.js'),
  "alarm-clock-off": () => import('./dist/esm/icons/alarm-clock-off.js'),
  "alarm-clock": () => import('./dist/esm/icons/alarm-clock.js'),
  "alarm-minus": () => import('./dist/esm/icons/alarm-minus.js'),
  "alarm-plus": () => import('./dist/esm/icons/alarm-plus.js'),
  "album": () => import('./dist/esm/icons/album.js'),
  "alert-circle": () => import('./dist/esm/icons/alert-circle.js'),
  "alert-octagon": () => import('./dist/esm/icons/alert-octagon.js'),
  "alert-triangle": () => import('./dist/esm/icons/alert-triangle.js'),
  "align-center-horizontal": () => import('./dist/esm/icons/align-center-horizontal.js'),
  "align-center-vertical": () => import('./dist/esm/icons/align-center-vertical.js'),
  "align-center": () => import('./dist/esm/icons/align-center.js'),
  "align-end-horizontal": () => import('./dist/esm/icons/align-end-horizontal.js'),
  "align-end-vertical": () => import('./dist/esm/icons/align-end-vertical.js'),
  "align-horizontal-distribute-center": () => import('./dist/esm/icons/align-horizontal-distribute-center.js'),
  "align-horizontal-distribute-end": () => import('./dist/esm/icons/align-horizontal-distribute-end.js'),
  "align-horizontal-distribute-start": () => import('./dist/esm/icons/align-horizontal-distribute-start.js'),
  "align-horizontal-justify-center": () => import('./dist/esm/icons/align-horizontal-justify-center.js'),
  "align-horizontal-justify-end": () => import('./dist/esm/icons/align-horizontal-justify-end.js'),
  "align-horizontal-justify-start": () => import('./dist/esm/icons/align-horizontal-justify-start.js'),
  "align-horizontal-space-around": () => import('./dist/esm/icons/align-horizontal-space-around.js'),
  "align-horizontal-space-between": () => import('./dist/esm/icons/align-horizontal-space-between.js'),
  "align-justify": () => import('./dist/esm/icons/align-justify.js'),
  "align-left": () => import('./dist/esm/icons/align-left.js'),
  "align-right": () => import('./dist/esm/icons/align-right.js'),
  "align-start-horizontal": () => import('./dist/esm/icons/align-start-horizontal.js'),
  "align-start-vertical": () => import('./dist/esm/icons/align-start-vertical.js'),
  "align-vertical-distribute-center": () => import('./dist/esm/icons/align-vertical-distribute-center.js'),
  "align-vertical-distribute-end": () => import('./dist/esm/icons/align-vertical-distribute-end.js'),
  "align-vertical-distribute-start": () => import('./dist/esm/icons/align-vertical-distribute-start.js'),
  "align-vertical-justify-center": () => import('./dist/esm/icons/align-vertical-justify-center.js'),
  "align-vertical-justify-end": () => import('./dist/esm/icons/align-vertical-justify-end.js'),
  "align-vertical-justify-start": () => import('./dist/esm/icons/align-vertical-justify-start.js'),
  "align-vertical-space-around": () => import('./dist/esm/icons/align-vertical-space-around.js'),
  "align-vertical-space-between": () => import('./dist/esm/icons/align-vertical-space-between.js'),
  "ampersand": () => import('./dist/esm/icons/ampersand.js'),
  "ampersands": () => import('./dist/esm/icons/ampersands.js'),
  "anchor": () => import('./dist/esm/icons/anchor.js'),
  "angry": () => import('./dist/esm/icons/angry.js'),
  "annoyed": () => import('./dist/esm/icons/annoyed.js'),
  "antenna": () => import('./dist/esm/icons/antenna.js'),
  "aperture": () => import('./dist/esm/icons/aperture.js'),
  "app-window": () => import('./dist/esm/icons/app-window.js'),
  "apple": () => import('./dist/esm/icons/apple.js'),
  "archive-restore": () => import('./dist/esm/icons/archive-restore.js'),
  "archive-x": () => import('./dist/esm/icons/archive-x.js'),
  "archive": () => import('./dist/esm/icons/archive.js'),
  "area-chart": () => import('./dist/esm/icons/area-chart.js'),
  "armchair": () => import('./dist/esm/icons/armchair.js'),
  "arrow-big-down-dash": () => import('./dist/esm/icons/arrow-big-down-dash.js'),
  "arrow-big-down": () => import('./dist/esm/icons/arrow-big-down.js'),
  "arrow-big-left-dash": () => import('./dist/esm/icons/arrow-big-left-dash.js'),
  "arrow-big-left": () => import('./dist/esm/icons/arrow-big-left.js'),
  "arrow-big-right-dash": () => import('./dist/esm/icons/arrow-big-right-dash.js'),
  "arrow-big-right": () => import('./dist/esm/icons/arrow-big-right.js'),
  "arrow-big-up-dash": () => import('./dist/esm/icons/arrow-big-up-dash.js'),
  "arrow-big-up": () => import('./dist/esm/icons/arrow-big-up.js'),
  "arrow-down-0-1": () => import('./dist/esm/icons/arrow-down-0-1.js'),
  "arrow-down-1-0": () => import('./dist/esm/icons/arrow-down-1-0.js'),
  "arrow-down-a-z": () => import('./dist/esm/icons/arrow-down-a-z.js'),
  "arrow-down-circle": () => import('./dist/esm/icons/arrow-down-circle.js'),
  "arrow-down-from-line": () => import('./dist/esm/icons/arrow-down-from-line.js'),
  "arrow-down-left-from-circle": () => import('./dist/esm/icons/arrow-down-left-from-circle.js'),
  "arrow-down-left-square": () => import('./dist/esm/icons/arrow-down-left-square.js'),
  "arrow-down-left": () => import('./dist/esm/icons/arrow-down-left.js'),
  "arrow-down-narrow-wide": () => import('./dist/esm/icons/arrow-down-narrow-wide.js'),
  "arrow-down-right-from-circle": () => import('./dist/esm/icons/arrow-down-right-from-circle.js'),
  "arrow-down-right-square": () => import('./dist/esm/icons/arrow-down-right-square.js'),
  "arrow-down-right": () => import('./dist/esm/icons/arrow-down-right.js'),
  "arrow-down-square": () => import('./dist/esm/icons/arrow-down-square.js'),
  "arrow-down-to-dot": () => import('./dist/esm/icons/arrow-down-to-dot.js'),
  "arrow-down-to-line": () => import('./dist/esm/icons/arrow-down-to-line.js'),
  "arrow-down-up": () => import('./dist/esm/icons/arrow-down-up.js'),
  "arrow-down-wide-narrow": () => import('./dist/esm/icons/arrow-down-wide-narrow.js'),
  "arrow-down-z-a": () => import('./dist/esm/icons/arrow-down-z-a.js'),
  "arrow-down": () => import('./dist/esm/icons/arrow-down.js'),
  "arrow-left-circle": () => import('./dist/esm/icons/arrow-left-circle.js'),
  "arrow-left-from-line": () => import('./dist/esm/icons/arrow-left-from-line.js'),
  "arrow-left-right": () => import('./dist/esm/icons/arrow-left-right.js'),
  "arrow-left-square": () => import('./dist/esm/icons/arrow-left-square.js'),
  "arrow-left-to-line": () => import('./dist/esm/icons/arrow-left-to-line.js'),
  "arrow-left": () => import('./dist/esm/icons/arrow-left.js'),
  "arrow-right-circle": () => import('./dist/esm/icons/arrow-right-circle.js'),
  "arrow-right-from-line": () => import('./dist/esm/icons/arrow-right-from-line.js'),
  "arrow-right-left": () => import('./dist/esm/icons/arrow-right-left.js'),
  "arrow-right-square": () => import('./dist/esm/icons/arrow-right-square.js'),
  "arrow-right-to-line": () => import('./dist/esm/icons/arrow-right-to-line.js'),
  "arrow-right": () => import('./dist/esm/icons/arrow-right.js'),
  "arrow-up-0-1": () => import('./dist/esm/icons/arrow-up-0-1.js'),
  "arrow-up-1-0": () => import('./dist/esm/icons/arrow-up-1-0.js'),
  "arrow-up-a-z": () => import('./dist/esm/icons/arrow-up-a-z.js'),
  "arrow-up-circle": () => import('./dist/esm/icons/arrow-up-circle.js'),
  "arrow-up-down": () => import('./dist/esm/icons/arrow-up-down.js'),
  "arrow-up-from-dot": () => import('./dist/esm/icons/arrow-up-from-dot.js'),
  "arrow-up-from-line": () => import('./dist/esm/icons/arrow-up-from-line.js'),
  "arrow-up-left-from-circle": () => import('./dist/esm/icons/arrow-up-left-from-circle.js'),
  "arrow-up-left-square": () => import('./dist/esm/icons/arrow-up-left-square.js'),
  "arrow-up-left": () => import('./dist/esm/icons/arrow-up-left.js'),
  "arrow-up-narrow-wide": () => import('./dist/esm/icons/arrow-up-narrow-wide.js'),
  "arrow-up-right-from-circle": () => import('./dist/esm/icons/arrow-up-right-from-circle.js'),
  "arrow-up-right-square": () => import('./dist/esm/icons/arrow-up-right-square.js'),
  "arrow-up-right": () => import('./dist/esm/icons/arrow-up-right.js'),
  "arrow-up-square": () => import('./dist/esm/icons/arrow-up-square.js'),
  "arrow-up-to-line": () => import('./dist/esm/icons/arrow-up-to-line.js'),
  "arrow-up-wide-narrow": () => import('./dist/esm/icons/arrow-up-wide-narrow.js'),
  "arrow-up-z-a": () => import('./dist/esm/icons/arrow-up-z-a.js'),
  "arrow-up": () => import('./dist/esm/icons/arrow-up.js'),
  "arrows-up-from-line": () => import('./dist/esm/icons/arrows-up-from-line.js'),
  "asterisk": () => import('./dist/esm/icons/asterisk.js'),
  "at-sign": () => import('./dist/esm/icons/at-sign.js'),
  "atom": () => import('./dist/esm/icons/atom.js'),
  "award": () => import('./dist/esm/icons/award.js'),
  "axe": () => import('./dist/esm/icons/axe.js'),
  "axis-3d": () => import('./dist/esm/icons/axis-3d.js'),
  "baby": () => import('./dist/esm/icons/baby.js'),
  "backpack": () => import('./dist/esm/icons/backpack.js'),
  "badge-alert": () => import('./dist/esm/icons/badge-alert.js'),
  "badge-cent": () => import('./dist/esm/icons/badge-cent.js'),
  "badge-check": () => import('./dist/esm/icons/badge-check.js'),
  "badge-dollar-sign": () => import('./dist/esm/icons/badge-dollar-sign.js'),
  "badge-euro": () => import('./dist/esm/icons/badge-euro.js'),
  "badge-help": () => import('./dist/esm/icons/badge-help.js'),
  "badge-indian-rupee": () => import('./dist/esm/icons/badge-indian-rupee.js'),
  "badge-info": () => import('./dist/esm/icons/badge-info.js'),
  "badge-japanese-yen": () => import('./dist/esm/icons/badge-japanese-yen.js'),
  "badge-minus": () => import('./dist/esm/icons/badge-minus.js'),
  "badge-percent": () => import('./dist/esm/icons/badge-percent.js'),
  "badge-plus": () => import('./dist/esm/icons/badge-plus.js'),
  "badge-pound-sterling": () => import('./dist/esm/icons/badge-pound-sterling.js'),
  "badge-russian-ruble": () => import('./dist/esm/icons/badge-russian-ruble.js'),
  "badge-swiss-franc": () => import('./dist/esm/icons/badge-swiss-franc.js'),
  "badge-x": () => import('./dist/esm/icons/badge-x.js'),
  "badge": () => import('./dist/esm/icons/badge.js'),
  "baggage-claim": () => import('./dist/esm/icons/baggage-claim.js'),
  "ban": () => import('./dist/esm/icons/ban.js'),
  "banana": () => import('./dist/esm/icons/banana.js'),
  "banknote": () => import('./dist/esm/icons/banknote.js'),
  "bar-chart-2": () => import('./dist/esm/icons/bar-chart-2.js'),
  "bar-chart-3": () => import('./dist/esm/icons/bar-chart-3.js'),
  "bar-chart-4": () => import('./dist/esm/icons/bar-chart-4.js'),
  "bar-chart-big": () => import('./dist/esm/icons/bar-chart-big.js'),
  "bar-chart-horizontal-big": () => import('./dist/esm/icons/bar-chart-horizontal-big.js'),
  "bar-chart-horizontal": () => import('./dist/esm/icons/bar-chart-horizontal.js'),
  "bar-chart": () => import('./dist/esm/icons/bar-chart.js'),
  "barcode": () => import('./dist/esm/icons/barcode.js'),
  "baseline": () => import('./dist/esm/icons/baseline.js'),
  "bath": () => import('./dist/esm/icons/bath.js'),
  "battery-charging": () => import('./dist/esm/icons/battery-charging.js'),
  "battery-full": () => import('./dist/esm/icons/battery-full.js'),
  "battery-low": () => import('./dist/esm/icons/battery-low.js'),
  "battery-medium": () => import('./dist/esm/icons/battery-medium.js'),
  "battery-warning": () => import('./dist/esm/icons/battery-warning.js'),
  "battery": () => import('./dist/esm/icons/battery.js'),
  "beaker": () => import('./dist/esm/icons/beaker.js'),
  "bean-off": () => import('./dist/esm/icons/bean-off.js'),
  "bean": () => import('./dist/esm/icons/bean.js'),
  "bed-double": () => import('./dist/esm/icons/bed-double.js'),
  "bed-single": () => import('./dist/esm/icons/bed-single.js'),
  "bed": () => import('./dist/esm/icons/bed.js'),
  "beef": () => import('./dist/esm/icons/beef.js'),
  "beer": () => import('./dist/esm/icons/beer.js'),
  "bell-dot": () => import('./dist/esm/icons/bell-dot.js'),
  "bell-minus": () => import('./dist/esm/icons/bell-minus.js'),
  "bell-off": () => import('./dist/esm/icons/bell-off.js'),
  "bell-plus": () => import('./dist/esm/icons/bell-plus.js'),
  "bell-ring": () => import('./dist/esm/icons/bell-ring.js'),
  "bell": () => import('./dist/esm/icons/bell.js'),
  "bike": () => import('./dist/esm/icons/bike.js'),
  "binary": () => import('./dist/esm/icons/binary.js'),
  "biohazard": () => import('./dist/esm/icons/biohazard.js'),
  "bird": () => import('./dist/esm/icons/bird.js'),
  "bitcoin": () => import('./dist/esm/icons/bitcoin.js'),
  "blinds": () => import('./dist/esm/icons/blinds.js'),
  "blocks": () => import('./dist/esm/icons/blocks.js'),
  "bluetooth-connected": () => import('./dist/esm/icons/bluetooth-connected.js'),
  "bluetooth-off": () => import('./dist/esm/icons/bluetooth-off.js'),
  "bluetooth-searching": () => import('./dist/esm/icons/bluetooth-searching.js'),
  "bluetooth": () => import('./dist/esm/icons/bluetooth.js'),
  "bold": () => import('./dist/esm/icons/bold.js'),
  "bomb": () => import('./dist/esm/icons/bomb.js'),
  "bone": () => import('./dist/esm/icons/bone.js'),
  "book-a": () => import('./dist/esm/icons/book-a.js'),
  "book-audio": () => import('./dist/esm/icons/book-audio.js'),
  "book-check": () => import('./dist/esm/icons/book-check.js'),
  "book-copy": () => import('./dist/esm/icons/book-copy.js'),
  "book-dashed": () => import('./dist/esm/icons/book-dashed.js'),
  "book-down": () => import('./dist/esm/icons/book-down.js'),
  "book-headphones": () => import('./dist/esm/icons/book-headphones.js'),
  "book-heart": () => import('./dist/esm/icons/book-heart.js'),
  "book-image": () => import('./dist/esm/icons/book-image.js'),
  "book-key": () => import('./dist/esm/icons/book-key.js'),
  "book-lock": () => import('./dist/esm/icons/book-lock.js'),
  "book-marked": () => import('./dist/esm/icons/book-marked.js'),
  "book-minus": () => import('./dist/esm/icons/book-minus.js'),
  "book-open-check": () => import('./dist/esm/icons/book-open-check.js'),
  "book-open-text": () => import('./dist/esm/icons/book-open-text.js'),
  "book-open": () => import('./dist/esm/icons/book-open.js'),
  "book-plus": () => import('./dist/esm/icons/book-plus.js'),
  "book-text": () => import('./dist/esm/icons/book-text.js'),
  "book-type": () => import('./dist/esm/icons/book-type.js'),
  "book-up-2": () => import('./dist/esm/icons/book-up-2.js'),
  "book-up": () => import('./dist/esm/icons/book-up.js'),
  "book-user": () => import('./dist/esm/icons/book-user.js'),
  "book-x": () => import('./dist/esm/icons/book-x.js'),
  "book": () => import('./dist/esm/icons/book.js'),
  "bookmark-check": () => import('./dist/esm/icons/bookmark-check.js'),
  "bookmark-minus": () => import('./dist/esm/icons/bookmark-minus.js'),
  "bookmark-plus": () => import('./dist/esm/icons/bookmark-plus.js'),
  "bookmark-x": () => import('./dist/esm/icons/bookmark-x.js'),
  "bookmark": () => import('./dist/esm/icons/bookmark.js'),
  "boom-box": () => import('./dist/esm/icons/boom-box.js'),
  "bot": () => import('./dist/esm/icons/bot.js'),
  "box-select": () => import('./dist/esm/icons/box-select.js'),
  "box": () => import('./dist/esm/icons/box.js'),
  "boxes": () => import('./dist/esm/icons/boxes.js'),
  "braces": () => import('./dist/esm/icons/braces.js'),
  "brackets": () => import('./dist/esm/icons/brackets.js'),
  "brain-circuit": () => import('./dist/esm/icons/brain-circuit.js'),
  "brain-cog": () => import('./dist/esm/icons/brain-cog.js'),
  "brain": () => import('./dist/esm/icons/brain.js'),
  "briefcase": () => import('./dist/esm/icons/briefcase.js'),
  "bring-to-front": () => import('./dist/esm/icons/bring-to-front.js'),
  "brush": () => import('./dist/esm/icons/brush.js'),
  "bug-off": () => import('./dist/esm/icons/bug-off.js'),
  "bug-play": () => import('./dist/esm/icons/bug-play.js'),
  "bug": () => import('./dist/esm/icons/bug.js'),
  "building-2": () => import('./dist/esm/icons/building-2.js'),
  "building": () => import('./dist/esm/icons/building.js'),
  "bus-front": () => import('./dist/esm/icons/bus-front.js'),
  "bus": () => import('./dist/esm/icons/bus.js'),
  "cable-car": () => import('./dist/esm/icons/cable-car.js'),
  "cable": () => import('./dist/esm/icons/cable.js'),
  "cake-slice": () => import('./dist/esm/icons/cake-slice.js'),
  "cake": () => import('./dist/esm/icons/cake.js'),
  "calculator": () => import('./dist/esm/icons/calculator.js'),
  "calendar-check-2": () => import('./dist/esm/icons/calendar-check-2.js'),
  "calendar-check": () => import('./dist/esm/icons/calendar-check.js'),
  "calendar-clock": () => import('./dist/esm/icons/calendar-clock.js'),
  "calendar-days": () => import('./dist/esm/icons/calendar-days.js'),
  "calendar-heart": () => import('./dist/esm/icons/calendar-heart.js'),
  "calendar-minus": () => import('./dist/esm/icons/calendar-minus.js'),
  "calendar-off": () => import('./dist/esm/icons/calendar-off.js'),
  "calendar-plus": () => import('./dist/esm/icons/calendar-plus.js'),
  "calendar-range": () => import('./dist/esm/icons/calendar-range.js'),
  "calendar-search": () => import('./dist/esm/icons/calendar-search.js'),
  "calendar-x-2": () => import('./dist/esm/icons/calendar-x-2.js'),
  "calendar-x": () => import('./dist/esm/icons/calendar-x.js'),
  "calendar": () => import('./dist/esm/icons/calendar.js'),
  "camera-off": () => import('./dist/esm/icons/camera-off.js'),
  "camera": () => import('./dist/esm/icons/camera.js'),
  "candlestick-chart": () => import('./dist/esm/icons/candlestick-chart.js'),
  "candy-cane": () => import('./dist/esm/icons/candy-cane.js'),
  "candy-off": () => import('./dist/esm/icons/candy-off.js'),
  "candy": () => import('./dist/esm/icons/candy.js'),
  "car-front": () => import('./dist/esm/icons/car-front.js'),
  "car-taxi-front": () => import('./dist/esm/icons/car-taxi-front.js'),
  "car": () => import('./dist/esm/icons/car.js'),
  "caravan": () => import('./dist/esm/icons/caravan.js'),
  "carrot": () => import('./dist/esm/icons/carrot.js'),
  "case-lower": () => import('./dist/esm/icons/case-lower.js'),
  "case-sensitive": () => import('./dist/esm/icons/case-sensitive.js'),
  "case-upper": () => import('./dist/esm/icons/case-upper.js'),
  "cassette-tape": () => import('./dist/esm/icons/cassette-tape.js'),
  "cast": () => import('./dist/esm/icons/cast.js'),
  "castle": () => import('./dist/esm/icons/castle.js'),
  "cat": () => import('./dist/esm/icons/cat.js'),
  "check-check": () => import('./dist/esm/icons/check-check.js'),
  "check-circle-2": () => import('./dist/esm/icons/check-circle-2.js'),
  "check-circle": () => import('./dist/esm/icons/check-circle.js'),
  "check-square-2": () => import('./dist/esm/icons/check-square-2.js'),
  "check-square": () => import('./dist/esm/icons/check-square.js'),
  "check": () => import('./dist/esm/icons/check.js'),
  "chef-hat": () => import('./dist/esm/icons/chef-hat.js'),
  "cherry": () => import('./dist/esm/icons/cherry.js'),
  "chevron-down-circle": () => import('./dist/esm/icons/chevron-down-circle.js'),
  "chevron-down-square": () => import('./dist/esm/icons/chevron-down-square.js'),
  "chevron-down": () => import('./dist/esm/icons/chevron-down.js'),
  "chevron-first": () => import('./dist/esm/icons/chevron-first.js'),
  "chevron-last": () => import('./dist/esm/icons/chevron-last.js'),
  "chevron-left-circle": () => import('./dist/esm/icons/chevron-left-circle.js'),
  "chevron-left-square": () => import('./dist/esm/icons/chevron-left-square.js'),
  "chevron-left": () => import('./dist/esm/icons/chevron-left.js'),
  "chevron-right-circle": () => import('./dist/esm/icons/chevron-right-circle.js'),
  "chevron-right-square": () => import('./dist/esm/icons/chevron-right-square.js'),
  "chevron-right": () => import('./dist/esm/icons/chevron-right.js'),
  "chevron-up-circle": () => import('./dist/esm/icons/chevron-up-circle.js'),
  "chevron-up-square": () => import('./dist/esm/icons/chevron-up-square.js'),
  "chevron-up": () => import('./dist/esm/icons/chevron-up.js'),
  "chevrons-down-up": () => import('./dist/esm/icons/chevrons-down-up.js'),
  "chevrons-down": () => import('./dist/esm/icons/chevrons-down.js'),
  "chevrons-left-right": () => import('./dist/esm/icons/chevrons-left-right.js'),
  "chevrons-left": () => import('./dist/esm/icons/chevrons-left.js'),
  "chevrons-right-left": () => import('./dist/esm/icons/chevrons-right-left.js'),
  "chevrons-right": () => import('./dist/esm/icons/chevrons-right.js'),
  "chevrons-up-down": () => import('./dist/esm/icons/chevrons-up-down.js'),
  "chevrons-up": () => import('./dist/esm/icons/chevrons-up.js'),
  "chrome": () => import('./dist/esm/icons/chrome.js'),
  "church": () => import('./dist/esm/icons/church.js'),
  "cigarette-off": () => import('./dist/esm/icons/cigarette-off.js'),
  "cigarette": () => import('./dist/esm/icons/cigarette.js'),
  "circle-dashed": () => import('./dist/esm/icons/circle-dashed.js'),
  "circle-dollar-sign": () => import('./dist/esm/icons/circle-dollar-sign.js'),
  "circle-dot-dashed": () => import('./dist/esm/icons/circle-dot-dashed.js'),
  "circle-dot": () => import('./dist/esm/icons/circle-dot.js'),
  "circle-ellipsis": () => import('./dist/esm/icons/circle-ellipsis.js'),
  "circle-equal": () => import('./dist/esm/icons/circle-equal.js'),
  "circle-off": () => import('./dist/esm/icons/circle-off.js'),
  "circle-slash-2": () => import('./dist/esm/icons/circle-slash-2.js'),
  "circle-slash": () => import('./dist/esm/icons/circle-slash.js'),
  "circle": () => import('./dist/esm/icons/circle.js'),
  "circuit-board": () => import('./dist/esm/icons/circuit-board.js'),
  "citrus": () => import('./dist/esm/icons/citrus.js'),
  "clapperboard": () => import('./dist/esm/icons/clapperboard.js'),
  "clipboard-check": () => import('./dist/esm/icons/clipboard-check.js'),
  "clipboard-copy": () => import('./dist/esm/icons/clipboard-copy.js'),
  "clipboard-edit": () => import('./dist/esm/icons/clipboard-edit.js'),
  "clipboard-list": () => import('./dist/esm/icons/clipboard-list.js'),
  "clipboard-paste": () => import('./dist/esm/icons/clipboard-paste.js'),
  "clipboard-signature": () => import('./dist/esm/icons/clipboard-signature.js'),
  "clipboard-type": () => import('./dist/esm/icons/clipboard-type.js'),
  "clipboard-x": () => import('./dist/esm/icons/clipboard-x.js'),
  "clipboard": () => import('./dist/esm/icons/clipboard.js'),
  "clock-1": () => import('./dist/esm/icons/clock-1.js'),
  "clock-10": () => import('./dist/esm/icons/clock-10.js'),
  "clock-11": () => import('./dist/esm/icons/clock-11.js'),
  "clock-12": () => import('./dist/esm/icons/clock-12.js'),
  "clock-2": () => import('./dist/esm/icons/clock-2.js'),
  "clock-3": () => import('./dist/esm/icons/clock-3.js'),
  "clock-4": () => import('./dist/esm/icons/clock-4.js'),
  "clock-5": () => import('./dist/esm/icons/clock-5.js'),
  "clock-6": () => import('./dist/esm/icons/clock-6.js'),
  "clock-7": () => import('./dist/esm/icons/clock-7.js'),
  "clock-8": () => import('./dist/esm/icons/clock-8.js'),
  "clock-9": () => import('./dist/esm/icons/clock-9.js'),
  "clock": () => import('./dist/esm/icons/clock.js'),
  "cloud-cog": () => import('./dist/esm/icons/cloud-cog.js'),
  "cloud-drizzle": () => import('./dist/esm/icons/cloud-drizzle.js'),
  "cloud-fog": () => import('./dist/esm/icons/cloud-fog.js'),
  "cloud-hail": () => import('./dist/esm/icons/cloud-hail.js'),
  "cloud-lightning": () => import('./dist/esm/icons/cloud-lightning.js'),
  "cloud-moon-rain": () => import('./dist/esm/icons/cloud-moon-rain.js'),
  "cloud-moon": () => import('./dist/esm/icons/cloud-moon.js'),
  "cloud-off": () => import('./dist/esm/icons/cloud-off.js'),
  "cloud-rain-wind": () => import('./dist/esm/icons/cloud-rain-wind.js'),
  "cloud-rain": () => import('./dist/esm/icons/cloud-rain.js'),
  "cloud-snow": () => import('./dist/esm/icons/cloud-snow.js'),
  "cloud-sun-rain": () => import('./dist/esm/icons/cloud-sun-rain.js'),
  "cloud-sun": () => import('./dist/esm/icons/cloud-sun.js'),
  "cloud": () => import('./dist/esm/icons/cloud.js'),
  "cloudy": () => import('./dist/esm/icons/cloudy.js'),
  "clover": () => import('./dist/esm/icons/clover.js'),
  "club": () => import('./dist/esm/icons/club.js'),
  "code-2": () => import('./dist/esm/icons/code-2.js'),
  "code": () => import('./dist/esm/icons/code.js'),
  "codepen": () => import('./dist/esm/icons/codepen.js'),
  "codesandbox": () => import('./dist/esm/icons/codesandbox.js'),
  "coffee": () => import('./dist/esm/icons/coffee.js'),
  "cog": () => import('./dist/esm/icons/cog.js'),
  "coins": () => import('./dist/esm/icons/coins.js'),
  "columns": () => import('./dist/esm/icons/columns.js'),
  "combine": () => import('./dist/esm/icons/combine.js'),
  "command": () => import('./dist/esm/icons/command.js'),
  "compass": () => import('./dist/esm/icons/compass.js'),
  "component": () => import('./dist/esm/icons/component.js'),
  "computer": () => import('./dist/esm/icons/computer.js'),
  "concierge-bell": () => import('./dist/esm/icons/concierge-bell.js'),
  "cone": () => import('./dist/esm/icons/cone.js'),
  "construction": () => import('./dist/esm/icons/construction.js'),
  "contact-2": () => import('./dist/esm/icons/contact-2.js'),
  "contact": () => import('./dist/esm/icons/contact.js'),
  "container": () => import('./dist/esm/icons/container.js'),
  "contrast": () => import('./dist/esm/icons/contrast.js'),
  "cookie": () => import('./dist/esm/icons/cookie.js'),
  "copy-check": () => import('./dist/esm/icons/copy-check.js'),
  "copy-minus": () => import('./dist/esm/icons/copy-minus.js'),
  "copy-plus": () => import('./dist/esm/icons/copy-plus.js'),
  "copy-slash": () => import('./dist/esm/icons/copy-slash.js'),
  "copy-x": () => import('./dist/esm/icons/copy-x.js'),
  "copy": () => import('./dist/esm/icons/copy.js'),
  "copyleft": () => import('./dist/esm/icons/copyleft.js'),
  "copyright": () => import('./dist/esm/icons/copyright.js'),
  "corner-down-left": () => import('./dist/esm/icons/corner-down-left.js'),
  "corner-down-right": () => import('./dist/esm/icons/corner-down-right.js'),
  "corner-left-down": () => import('./dist/esm/icons/corner-left-down.js'),
  "corner-left-up": () => import('./dist/esm/icons/corner-left-up.js'),
  "corner-right-down": () => import('./dist/esm/icons/corner-right-down.js'),
  "corner-right-up": () => import('./dist/esm/icons/corner-right-up.js'),
  "corner-up-left": () => import('./dist/esm/icons/corner-up-left.js'),
  "corner-up-right": () => import('./dist/esm/icons/corner-up-right.js'),
  "cpu": () => import('./dist/esm/icons/cpu.js'),
  "creative-commons": () => import('./dist/esm/icons/creative-commons.js'),
  "credit-card": () => import('./dist/esm/icons/credit-card.js'),
  "croissant": () => import('./dist/esm/icons/croissant.js'),
  "crop": () => import('./dist/esm/icons/crop.js'),
  "cross": () => import('./dist/esm/icons/cross.js'),
  "crosshair": () => import('./dist/esm/icons/crosshair.js'),
  "crown": () => import('./dist/esm/icons/crown.js'),
  "cuboid": () => import('./dist/esm/icons/cuboid.js'),
  "cup-soda": () => import('./dist/esm/icons/cup-soda.js'),
  "currency": () => import('./dist/esm/icons/currency.js'),
  "cylinder": () => import('./dist/esm/icons/cylinder.js'),
  "database-backup": () => import('./dist/esm/icons/database-backup.js'),
  "database-zap": () => import('./dist/esm/icons/database-zap.js'),
  "database": () => import('./dist/esm/icons/database.js'),
  "delete": () => import('./dist/esm/icons/delete.js'),
  "dessert": () => import('./dist/esm/icons/dessert.js'),
  "diameter": () => import('./dist/esm/icons/diameter.js'),
  "diamond": () => import('./dist/esm/icons/diamond.js'),
  "dice-1": () => import('./dist/esm/icons/dice-1.js'),
  "dice-2": () => import('./dist/esm/icons/dice-2.js'),
  "dice-3": () => import('./dist/esm/icons/dice-3.js'),
  "dice-4": () => import('./dist/esm/icons/dice-4.js'),
  "dice-5": () => import('./dist/esm/icons/dice-5.js'),
  "dice-6": () => import('./dist/esm/icons/dice-6.js'),
  "dices": () => import('./dist/esm/icons/dices.js'),
  "diff": () => import('./dist/esm/icons/diff.js'),
  "disc-2": () => import('./dist/esm/icons/disc-2.js'),
  "disc-3": () => import('./dist/esm/icons/disc-3.js'),
  "disc": () => import('./dist/esm/icons/disc.js'),
  "divide-circle": () => import('./dist/esm/icons/divide-circle.js'),
  "divide-square": () => import('./dist/esm/icons/divide-square.js'),
  "divide": () => import('./dist/esm/icons/divide.js'),
  "dna-off": () => import('./dist/esm/icons/dna-off.js'),
  "dna": () => import('./dist/esm/icons/dna.js'),
  "dog": () => import('./dist/esm/icons/dog.js'),
  "dollar-sign": () => import('./dist/esm/icons/dollar-sign.js'),
  "donut": () => import('./dist/esm/icons/donut.js'),
  "door-closed": () => import('./dist/esm/icons/door-closed.js'),
  "door-open": () => import('./dist/esm/icons/door-open.js'),
  "dot": () => import('./dist/esm/icons/dot.js'),
  "download-cloud": () => import('./dist/esm/icons/download-cloud.js'),
  "download": () => import('./dist/esm/icons/download.js'),
  "drafting-compass": () => import('./dist/esm/icons/drafting-compass.js'),
  "drama": () => import('./dist/esm/icons/drama.js'),
  "dribbble": () => import('./dist/esm/icons/dribbble.js'),
  "droplet": () => import('./dist/esm/icons/droplet.js'),
  "droplets": () => import('./dist/esm/icons/droplets.js'),
  "drumstick": () => import('./dist/esm/icons/drumstick.js'),
  "dumbbell": () => import('./dist/esm/icons/dumbbell.js'),
  "ear-off": () => import('./dist/esm/icons/ear-off.js'),
  "ear": () => import('./dist/esm/icons/ear.js'),
  "egg-fried": () => import('./dist/esm/icons/egg-fried.js'),
  "egg-off": () => import('./dist/esm/icons/egg-off.js'),
  "egg": () => import('./dist/esm/icons/egg.js'),
  "equal-not": () => import('./dist/esm/icons/equal-not.js'),
  "equal": () => import('./dist/esm/icons/equal.js'),
  "eraser": () => import('./dist/esm/icons/eraser.js'),
  "euro": () => import('./dist/esm/icons/euro.js'),
  "expand": () => import('./dist/esm/icons/expand.js'),
  "external-link": () => import('./dist/esm/icons/external-link.js'),
  "eye-off": () => import('./dist/esm/icons/eye-off.js'),
  "eye": () => import('./dist/esm/icons/eye.js'),
  "facebook": () => import('./dist/esm/icons/facebook.js'),
  "factory": () => import('./dist/esm/icons/factory.js'),
  "fan": () => import('./dist/esm/icons/fan.js'),
  "fast-forward": () => import('./dist/esm/icons/fast-forward.js'),
  "feather": () => import('./dist/esm/icons/feather.js'),
  "ferris-wheel": () => import('./dist/esm/icons/ferris-wheel.js'),
  "figma": () => import('./dist/esm/icons/figma.js'),
  "file-archive": () => import('./dist/esm/icons/file-archive.js'),
  "file-audio-2": () => import('./dist/esm/icons/file-audio-2.js'),
  "file-audio": () => import('./dist/esm/icons/file-audio.js'),
  "file-axis-3d": () => import('./dist/esm/icons/file-axis-3d.js'),
  "file-badge-2": () => import('./dist/esm/icons/file-badge-2.js'),
  "file-badge": () => import('./dist/esm/icons/file-badge.js'),
  "file-bar-chart-2": () => import('./dist/esm/icons/file-bar-chart-2.js'),
  "file-bar-chart": () => import('./dist/esm/icons/file-bar-chart.js'),
  "file-box": () => import('./dist/esm/icons/file-box.js'),
  "file-check-2": () => import('./dist/esm/icons/file-check-2.js'),
  "file-check": () => import('./dist/esm/icons/file-check.js'),
  "file-clock": () => import('./dist/esm/icons/file-clock.js'),
  "file-code-2": () => import('./dist/esm/icons/file-code-2.js'),
  "file-code": () => import('./dist/esm/icons/file-code.js'),
  "file-cog": () => import('./dist/esm/icons/file-cog.js'),
  "file-diff": () => import('./dist/esm/icons/file-diff.js'),
  "file-digit": () => import('./dist/esm/icons/file-digit.js'),
  "file-down": () => import('./dist/esm/icons/file-down.js'),
  "file-edit": () => import('./dist/esm/icons/file-edit.js'),
  "file-heart": () => import('./dist/esm/icons/file-heart.js'),
  "file-image": () => import('./dist/esm/icons/file-image.js'),
  "file-input": () => import('./dist/esm/icons/file-input.js'),
  "file-json-2": () => import('./dist/esm/icons/file-json-2.js'),
  "file-json": () => import('./dist/esm/icons/file-json.js'),
  "file-key-2": () => import('./dist/esm/icons/file-key-2.js'),
  "file-key": () => import('./dist/esm/icons/file-key.js'),
  "file-line-chart": () => import('./dist/esm/icons/file-line-chart.js'),
  "file-lock-2": () => import('./dist/esm/icons/file-lock-2.js'),
  "file-lock": () => import('./dist/esm/icons/file-lock.js'),
  "file-minus-2": () => import('./dist/esm/icons/file-minus-2.js'),
  "file-minus": () => import('./dist/esm/icons/file-minus.js'),
  "file-output": () => import('./dist/esm/icons/file-output.js'),
  "file-pie-chart": () => import('./dist/esm/icons/file-pie-chart.js'),
  "file-plus-2": () => import('./dist/esm/icons/file-plus-2.js'),
  "file-plus": () => import('./dist/esm/icons/file-plus.js'),
  "file-question": () => import('./dist/esm/icons/file-question.js'),
  "file-scan": () => import('./dist/esm/icons/file-scan.js'),
  "file-search-2": () => import('./dist/esm/icons/file-search-2.js'),
  "file-search": () => import('./dist/esm/icons/file-search.js'),
  "file-signature": () => import('./dist/esm/icons/file-signature.js'),
  "file-spreadsheet": () => import('./dist/esm/icons/file-spreadsheet.js'),
  "file-stack": () => import('./dist/esm/icons/file-stack.js'),
  "file-symlink": () => import('./dist/esm/icons/file-symlink.js'),
  "file-terminal": () => import('./dist/esm/icons/file-terminal.js'),
  "file-text": () => import('./dist/esm/icons/file-text.js'),
  "file-type-2": () => import('./dist/esm/icons/file-type-2.js'),
  "file-type": () => import('./dist/esm/icons/file-type.js'),
  "file-up": () => import('./dist/esm/icons/file-up.js'),
  "file-video-2": () => import('./dist/esm/icons/file-video-2.js'),
  "file-video": () => import('./dist/esm/icons/file-video.js'),
  "file-volume-2": () => import('./dist/esm/icons/file-volume-2.js'),
  "file-volume": () => import('./dist/esm/icons/file-volume.js'),
  "file-warning": () => import('./dist/esm/icons/file-warning.js'),
  "file-x-2": () => import('./dist/esm/icons/file-x-2.js'),
  "file-x": () => import('./dist/esm/icons/file-x.js'),
  "file": () => import('./dist/esm/icons/file.js'),
  "files": () => import('./dist/esm/icons/files.js'),
  "film": () => import('./dist/esm/icons/film.js'),
  "filter-x": () => import('./dist/esm/icons/filter-x.js'),
  "filter": () => import('./dist/esm/icons/filter.js'),
  "fingerprint": () => import('./dist/esm/icons/fingerprint.js'),
  "fish-off": () => import('./dist/esm/icons/fish-off.js'),
  "fish-symbol": () => import('./dist/esm/icons/fish-symbol.js'),
  "fish": () => import('./dist/esm/icons/fish.js'),
  "flag-off": () => import('./dist/esm/icons/flag-off.js'),
  "flag-triangle-left": () => import('./dist/esm/icons/flag-triangle-left.js'),
  "flag-triangle-right": () => import('./dist/esm/icons/flag-triangle-right.js'),
  "flag": () => import('./dist/esm/icons/flag.js'),
  "flame-kindling": () => import('./dist/esm/icons/flame-kindling.js'),
  "flame": () => import('./dist/esm/icons/flame.js'),
  "flashlight-off": () => import('./dist/esm/icons/flashlight-off.js'),
  "flashlight": () => import('./dist/esm/icons/flashlight.js'),
  "flask-conical-off": () => import('./dist/esm/icons/flask-conical-off.js'),
  "flask-conical": () => import('./dist/esm/icons/flask-conical.js'),
  "flask-round": () => import('./dist/esm/icons/flask-round.js'),
  "flip-horizontal-2": () => import('./dist/esm/icons/flip-horizontal-2.js'),
  "flip-horizontal": () => import('./dist/esm/icons/flip-horizontal.js'),
  "flip-vertical-2": () => import('./dist/esm/icons/flip-vertical-2.js'),
  "flip-vertical": () => import('./dist/esm/icons/flip-vertical.js'),
  "flower-2": () => import('./dist/esm/icons/flower-2.js'),
  "flower": () => import('./dist/esm/icons/flower.js'),
  "focus": () => import('./dist/esm/icons/focus.js'),
  "fold-horizontal": () => import('./dist/esm/icons/fold-horizontal.js'),
  "fold-vertical": () => import('./dist/esm/icons/fold-vertical.js'),
  "folder-archive": () => import('./dist/esm/icons/folder-archive.js'),
  "folder-check": () => import('./dist/esm/icons/folder-check.js'),
  "folder-clock": () => import('./dist/esm/icons/folder-clock.js'),
  "folder-closed": () => import('./dist/esm/icons/folder-closed.js'),
  "folder-cog": () => import('./dist/esm/icons/folder-cog.js'),
  "folder-dot": () => import('./dist/esm/icons/folder-dot.js'),
  "folder-down": () => import('./dist/esm/icons/folder-down.js'),
  "folder-edit": () => import('./dist/esm/icons/folder-edit.js'),
  "folder-git-2": () => import('./dist/esm/icons/folder-git-2.js'),
  "folder-git": () => import('./dist/esm/icons/folder-git.js'),
  "folder-heart": () => import('./dist/esm/icons/folder-heart.js'),
  "folder-input": () => import('./dist/esm/icons/folder-input.js'),
  "folder-kanban": () => import('./dist/esm/icons/folder-kanban.js'),
  "folder-key": () => import('./dist/esm/icons/folder-key.js'),
  "folder-lock": () => import('./dist/esm/icons/folder-lock.js'),
  "folder-minus": () => import('./dist/esm/icons/folder-minus.js'),
  "folder-open-dot": () => import('./dist/esm/icons/folder-open-dot.js'),
  "folder-open": () => import('./dist/esm/icons/folder-open.js'),
  "folder-output": () => import('./dist/esm/icons/folder-output.js'),
  "folder-plus": () => import('./dist/esm/icons/folder-plus.js'),
  "folder-root": () => import('./dist/esm/icons/folder-root.js'),
  "folder-search-2": () => import('./dist/esm/icons/folder-search-2.js'),
  "folder-search": () => import('./dist/esm/icons/folder-search.js'),
  "folder-symlink": () => import('./dist/esm/icons/folder-symlink.js'),
  "folder-sync": () => import('./dist/esm/icons/folder-sync.js'),
  "folder-tree": () => import('./dist/esm/icons/folder-tree.js'),
  "folder-up": () => import('./dist/esm/icons/folder-up.js'),
  "folder-x": () => import('./dist/esm/icons/folder-x.js'),
  "folder": () => import('./dist/esm/icons/folder.js'),
  "folders": () => import('./dist/esm/icons/folders.js'),
  "footprints": () => import('./dist/esm/icons/footprints.js'),
  "forklift": () => import('./dist/esm/icons/forklift.js'),
  "form-input": () => import('./dist/esm/icons/form-input.js'),
  "forward": () => import('./dist/esm/icons/forward.js'),
  "frame": () => import('./dist/esm/icons/frame.js'),
  "framer": () => import('./dist/esm/icons/framer.js'),
  "frown": () => import('./dist/esm/icons/frown.js'),
  "fuel": () => import('./dist/esm/icons/fuel.js'),
  "fullscreen": () => import('./dist/esm/icons/fullscreen.js'),
  "function-square": () => import('./dist/esm/icons/function-square.js'),
  "gallery-horizontal-end": () => import('./dist/esm/icons/gallery-horizontal-end.js'),
  "gallery-horizontal": () => import('./dist/esm/icons/gallery-horizontal.js'),
  "gallery-thumbnails": () => import('./dist/esm/icons/gallery-thumbnails.js'),
  "gallery-vertical-end": () => import('./dist/esm/icons/gallery-vertical-end.js'),
  "gallery-vertical": () => import('./dist/esm/icons/gallery-vertical.js'),
  "gamepad-2": () => import('./dist/esm/icons/gamepad-2.js'),
  "gamepad": () => import('./dist/esm/icons/gamepad.js'),
  "gantt-chart-square": () => import('./dist/esm/icons/gantt-chart-square.js'),
  "gantt-chart": () => import('./dist/esm/icons/gantt-chart.js'),
  "gauge-circle": () => import('./dist/esm/icons/gauge-circle.js'),
  "gauge": () => import('./dist/esm/icons/gauge.js'),
  "gavel": () => import('./dist/esm/icons/gavel.js'),
  "gem": () => import('./dist/esm/icons/gem.js'),
  "ghost": () => import('./dist/esm/icons/ghost.js'),
  "gift": () => import('./dist/esm/icons/gift.js'),
  "git-branch-plus": () => import('./dist/esm/icons/git-branch-plus.js'),
  "git-branch": () => import('./dist/esm/icons/git-branch.js'),
  "git-commit-horizontal": () => import('./dist/esm/icons/git-commit-horizontal.js'),
  "git-commit-vertical": () => import('./dist/esm/icons/git-commit-vertical.js'),
  "git-compare-arrows": () => import('./dist/esm/icons/git-compare-arrows.js'),
  "git-compare": () => import('./dist/esm/icons/git-compare.js'),
  "git-fork": () => import('./dist/esm/icons/git-fork.js'),
  "git-graph": () => import('./dist/esm/icons/git-graph.js'),
  "git-merge": () => import('./dist/esm/icons/git-merge.js'),
  "git-pull-request-arrow": () => import('./dist/esm/icons/git-pull-request-arrow.js'),
  "git-pull-request-closed": () => import('./dist/esm/icons/git-pull-request-closed.js'),
  "git-pull-request-create-arrow": () => import('./dist/esm/icons/git-pull-request-create-arrow.js'),
  "git-pull-request-create": () => import('./dist/esm/icons/git-pull-request-create.js'),
  "git-pull-request-draft": () => import('./dist/esm/icons/git-pull-request-draft.js'),
  "git-pull-request": () => import('./dist/esm/icons/git-pull-request.js'),
  "github": () => import('./dist/esm/icons/github.js'),
  "gitlab": () => import('./dist/esm/icons/gitlab.js'),
  "glass-water": () => import('./dist/esm/icons/glass-water.js'),
  "glasses": () => import('./dist/esm/icons/glasses.js'),
  "globe-2": () => import('./dist/esm/icons/globe-2.js'),
  "globe": () => import('./dist/esm/icons/globe.js'),
  "goal": () => import('./dist/esm/icons/goal.js'),
  "grab": () => import('./dist/esm/icons/grab.js'),
  "graduation-cap": () => import('./dist/esm/icons/graduation-cap.js'),
  "grape": () => import('./dist/esm/icons/grape.js'),
  "grid-2x2": () => import('./dist/esm/icons/grid-2x2.js'),
  "grid-3x3": () => import('./dist/esm/icons/grid-3x3.js'),
  "grip-horizontal": () => import('./dist/esm/icons/grip-horizontal.js'),
  "grip-vertical": () => import('./dist/esm/icons/grip-vertical.js'),
  "grip": () => import('./dist/esm/icons/grip.js'),
  "group": () => import('./dist/esm/icons/group.js'),
  "hammer": () => import('./dist/esm/icons/hammer.js'),
  "hand-metal": () => import('./dist/esm/icons/hand-metal.js'),
  "hand": () => import('./dist/esm/icons/hand.js'),
  "hard-drive-download": () => import('./dist/esm/icons/hard-drive-download.js'),
  "hard-drive-upload": () => import('./dist/esm/icons/hard-drive-upload.js'),
  "hard-drive": () => import('./dist/esm/icons/hard-drive.js'),
  "hard-hat": () => import('./dist/esm/icons/hard-hat.js'),
  "hash": () => import('./dist/esm/icons/hash.js'),
  "haze": () => import('./dist/esm/icons/haze.js'),
  "hdmi-port": () => import('./dist/esm/icons/hdmi-port.js'),
  "heading-1": () => import('./dist/esm/icons/heading-1.js'),
  "heading-2": () => import('./dist/esm/icons/heading-2.js'),
  "heading-3": () => import('./dist/esm/icons/heading-3.js'),
  "heading-4": () => import('./dist/esm/icons/heading-4.js'),
  "heading-5": () => import('./dist/esm/icons/heading-5.js'),
  "heading-6": () => import('./dist/esm/icons/heading-6.js'),
  "heading": () => import('./dist/esm/icons/heading.js'),
  "headphones": () => import('./dist/esm/icons/headphones.js'),
  "heart-crack": () => import('./dist/esm/icons/heart-crack.js'),
  "heart-handshake": () => import('./dist/esm/icons/heart-handshake.js'),
  "heart-off": () => import('./dist/esm/icons/heart-off.js'),
  "heart-pulse": () => import('./dist/esm/icons/heart-pulse.js'),
  "heart": () => import('./dist/esm/icons/heart.js'),
  "help-circle": () => import('./dist/esm/icons/help-circle.js'),
  "helping-hand": () => import('./dist/esm/icons/helping-hand.js'),
  "hexagon": () => import('./dist/esm/icons/hexagon.js'),
  "highlighter": () => import('./dist/esm/icons/highlighter.js'),
  "history": () => import('./dist/esm/icons/history.js'),
  "home": () => import('./dist/esm/icons/home.js'),
  "hop-off": () => import('./dist/esm/icons/hop-off.js'),
  "hop": () => import('./dist/esm/icons/hop.js'),
  "hotel": () => import('./dist/esm/icons/hotel.js'),
  "hourglass": () => import('./dist/esm/icons/hourglass.js'),
  "ice-cream-2": () => import('./dist/esm/icons/ice-cream-2.js'),
  "ice-cream": () => import('./dist/esm/icons/ice-cream.js'),
  "image-down": () => import('./dist/esm/icons/image-down.js'),
  "image-minus": () => import('./dist/esm/icons/image-minus.js'),
  "image-off": () => import('./dist/esm/icons/image-off.js'),
  "image-plus": () => import('./dist/esm/icons/image-plus.js'),
  "image": () => import('./dist/esm/icons/image.js'),
  "import": () => import('./dist/esm/icons/import.js'),
  "inbox": () => import('./dist/esm/icons/inbox.js'),
  "indent": () => import('./dist/esm/icons/indent.js'),
  "indian-rupee": () => import('./dist/esm/icons/indian-rupee.js'),
  "infinity": () => import('./dist/esm/icons/infinity.js'),
  "info": () => import('./dist/esm/icons/info.js'),
  "instagram": () => import('./dist/esm/icons/instagram.js'),
  "italic": () => import('./dist/esm/icons/italic.js'),
  "iteration-ccw": () => import('./dist/esm/icons/iteration-ccw.js'),
  "iteration-cw": () => import('./dist/esm/icons/iteration-cw.js'),
  "japanese-yen": () => import('./dist/esm/icons/japanese-yen.js'),
  "joystick": () => import('./dist/esm/icons/joystick.js'),
  "kanban-square-dashed": () => import('./dist/esm/icons/kanban-square-dashed.js'),
  "kanban-square": () => import('./dist/esm/icons/kanban-square.js'),
  "kanban": () => import('./dist/esm/icons/kanban.js'),
  "key-round": () => import('./dist/esm/icons/key-round.js'),
  "key-square": () => import('./dist/esm/icons/key-square.js'),
  "key": () => import('./dist/esm/icons/key.js'),
  "keyboard": () => import('./dist/esm/icons/keyboard.js'),
  "lamp-ceiling": () => import('./dist/esm/icons/lamp-ceiling.js'),
  "lamp-desk": () => import('./dist/esm/icons/lamp-desk.js'),
  "lamp-floor": () => import('./dist/esm/icons/lamp-floor.js'),
  "lamp-wall-down": () => import('./dist/esm/icons/lamp-wall-down.js'),
  "lamp-wall-up": () => import('./dist/esm/icons/lamp-wall-up.js'),
  "lamp": () => import('./dist/esm/icons/lamp.js'),
  "land-plot": () => import('./dist/esm/icons/land-plot.js'),
  "landmark": () => import('./dist/esm/icons/landmark.js'),
  "languages": () => import('./dist/esm/icons/languages.js'),
  "laptop-2": () => import('./dist/esm/icons/laptop-2.js'),
  "laptop": () => import('./dist/esm/icons/laptop.js'),
  "lasso-select": () => import('./dist/esm/icons/lasso-select.js'),
  "lasso": () => import('./dist/esm/icons/lasso.js'),
  "laugh": () => import('./dist/esm/icons/laugh.js'),
  "layers-2": () => import('./dist/esm/icons/layers-2.js'),
  "layers-3": () => import('./dist/esm/icons/layers-3.js'),
  "layers": () => import('./dist/esm/icons/layers.js'),
  "layout-dashboard": () => import('./dist/esm/icons/layout-dashboard.js'),
  "layout-grid": () => import('./dist/esm/icons/layout-grid.js'),
  "layout-list": () => import('./dist/esm/icons/layout-list.js'),
  "layout-panel-left": () => import('./dist/esm/icons/layout-panel-left.js'),
  "layout-panel-top": () => import('./dist/esm/icons/layout-panel-top.js'),
  "layout-template": () => import('./dist/esm/icons/layout-template.js'),
  "layout": () => import('./dist/esm/icons/layout.js'),
  "leaf": () => import('./dist/esm/icons/leaf.js'),
  "leafy-green": () => import('./dist/esm/icons/leafy-green.js'),
  "library-big": () => import('./dist/esm/icons/library-big.js'),
  "library-square": () => import('./dist/esm/icons/library-square.js'),
  "library": () => import('./dist/esm/icons/library.js'),
  "life-buoy": () => import('./dist/esm/icons/life-buoy.js'),
  "ligature": () => import('./dist/esm/icons/ligature.js'),
  "lightbulb-off": () => import('./dist/esm/icons/lightbulb-off.js'),
  "lightbulb": () => import('./dist/esm/icons/lightbulb.js'),
  "line-chart": () => import('./dist/esm/icons/line-chart.js'),
  "link-2-off": () => import('./dist/esm/icons/link-2-off.js'),
  "link-2": () => import('./dist/esm/icons/link-2.js'),
  "link": () => import('./dist/esm/icons/link.js'),
  "linkedin": () => import('./dist/esm/icons/linkedin.js'),
  "list-checks": () => import('./dist/esm/icons/list-checks.js'),
  "list-end": () => import('./dist/esm/icons/list-end.js'),
  "list-filter": () => import('./dist/esm/icons/list-filter.js'),
  "list-minus": () => import('./dist/esm/icons/list-minus.js'),
  "list-music": () => import('./dist/esm/icons/list-music.js'),
  "list-ordered": () => import('./dist/esm/icons/list-ordered.js'),
  "list-plus": () => import('./dist/esm/icons/list-plus.js'),
  "list-restart": () => import('./dist/esm/icons/list-restart.js'),
  "list-start": () => import('./dist/esm/icons/list-start.js'),
  "list-todo": () => import('./dist/esm/icons/list-todo.js'),
  "list-tree": () => import('./dist/esm/icons/list-tree.js'),
  "list-video": () => import('./dist/esm/icons/list-video.js'),
  "list-x": () => import('./dist/esm/icons/list-x.js'),
  "list": () => import('./dist/esm/icons/list.js'),
  "loader-2": () => import('./dist/esm/icons/loader-2.js'),
  "loader": () => import('./dist/esm/icons/loader.js'),
  "locate-fixed": () => import('./dist/esm/icons/locate-fixed.js'),
  "locate-off": () => import('./dist/esm/icons/locate-off.js'),
  "locate": () => import('./dist/esm/icons/locate.js'),
  "lock-keyhole": () => import('./dist/esm/icons/lock-keyhole.js'),
  "lock": () => import('./dist/esm/icons/lock.js'),
  "log-in": () => import('./dist/esm/icons/log-in.js'),
  "log-out": () => import('./dist/esm/icons/log-out.js'),
  "lollipop": () => import('./dist/esm/icons/lollipop.js'),
  "luggage": () => import('./dist/esm/icons/luggage.js'),
  "m-square": () => import('./dist/esm/icons/m-square.js'),
  "magnet": () => import('./dist/esm/icons/magnet.js'),
  "mail-check": () => import('./dist/esm/icons/mail-check.js'),
  "mail-minus": () => import('./dist/esm/icons/mail-minus.js'),
  "mail-open": () => import('./dist/esm/icons/mail-open.js'),
  "mail-plus": () => import('./dist/esm/icons/mail-plus.js'),
  "mail-question": () => import('./dist/esm/icons/mail-question.js'),
  "mail-search": () => import('./dist/esm/icons/mail-search.js'),
  "mail-warning": () => import('./dist/esm/icons/mail-warning.js'),
  "mail-x": () => import('./dist/esm/icons/mail-x.js'),
  "mail": () => import('./dist/esm/icons/mail.js'),
  "mailbox": () => import('./dist/esm/icons/mailbox.js'),
  "mails": () => import('./dist/esm/icons/mails.js'),
  "map-pin-off": () => import('./dist/esm/icons/map-pin-off.js'),
  "map-pin": () => import('./dist/esm/icons/map-pin.js'),
  "map-pinned": () => import('./dist/esm/icons/map-pinned.js'),
  "map": () => import('./dist/esm/icons/map.js'),
  "martini": () => import('./dist/esm/icons/martini.js'),
  "maximize-2": () => import('./dist/esm/icons/maximize-2.js'),
  "maximize": () => import('./dist/esm/icons/maximize.js'),
  "medal": () => import('./dist/esm/icons/medal.js'),
  "megaphone-off": () => import('./dist/esm/icons/megaphone-off.js'),
  "megaphone": () => import('./dist/esm/icons/megaphone.js'),
  "meh": () => import('./dist/esm/icons/meh.js'),
  "memory-stick": () => import('./dist/esm/icons/memory-stick.js'),
  "menu-square": () => import('./dist/esm/icons/menu-square.js'),
  "menu": () => import('./dist/esm/icons/menu.js'),
  "merge": () => import('./dist/esm/icons/merge.js'),
  "message-circle": () => import('./dist/esm/icons/message-circle.js'),
  "message-square-dashed": () => import('./dist/esm/icons/message-square-dashed.js'),
  "message-square-plus": () => import('./dist/esm/icons/message-square-plus.js'),
  "message-square": () => import('./dist/esm/icons/message-square.js'),
  "messages-square": () => import('./dist/esm/icons/messages-square.js'),
  "mic-2": () => import('./dist/esm/icons/mic-2.js'),
  "mic-off": () => import('./dist/esm/icons/mic-off.js'),
  "mic": () => import('./dist/esm/icons/mic.js'),
  "microscope": () => import('./dist/esm/icons/microscope.js'),
  "microwave": () => import('./dist/esm/icons/microwave.js'),
  "milestone": () => import('./dist/esm/icons/milestone.js'),
  "milk-off": () => import('./dist/esm/icons/milk-off.js'),
  "milk": () => import('./dist/esm/icons/milk.js'),
  "minimize-2": () => import('./dist/esm/icons/minimize-2.js'),
  "minimize": () => import('./dist/esm/icons/minimize.js'),
  "minus-circle": () => import('./dist/esm/icons/minus-circle.js'),
  "minus-square": () => import('./dist/esm/icons/minus-square.js'),
  "minus": () => import('./dist/esm/icons/minus.js'),
  "monitor-check": () => import('./dist/esm/icons/monitor-check.js'),
  "monitor-dot": () => import('./dist/esm/icons/monitor-dot.js'),
  "monitor-down": () => import('./dist/esm/icons/monitor-down.js'),
  "monitor-off": () => import('./dist/esm/icons/monitor-off.js'),
  "monitor-pause": () => import('./dist/esm/icons/monitor-pause.js'),
  "monitor-play": () => import('./dist/esm/icons/monitor-play.js'),
  "monitor-smartphone": () => import('./dist/esm/icons/monitor-smartphone.js'),
  "monitor-speaker": () => import('./dist/esm/icons/monitor-speaker.js'),
  "monitor-stop": () => import('./dist/esm/icons/monitor-stop.js'),
  "monitor-up": () => import('./dist/esm/icons/monitor-up.js'),
  "monitor-x": () => import('./dist/esm/icons/monitor-x.js'),
  "monitor": () => import('./dist/esm/icons/monitor.js'),
  "moon-star": () => import('./dist/esm/icons/moon-star.js'),
  "moon": () => import('./dist/esm/icons/moon.js'),
  "more-horizontal": () => import('./dist/esm/icons/more-horizontal.js'),
  "more-vertical": () => import('./dist/esm/icons/more-vertical.js'),
  "mountain-snow": () => import('./dist/esm/icons/mountain-snow.js'),
  "mountain": () => import('./dist/esm/icons/mountain.js'),
  "mouse-pointer-2": () => import('./dist/esm/icons/mouse-pointer-2.js'),
  "mouse-pointer-click": () => import('./dist/esm/icons/mouse-pointer-click.js'),
  "mouse-pointer-square-dashed": () => import('./dist/esm/icons/mouse-pointer-square-dashed.js'),
  "mouse-pointer-square": () => import('./dist/esm/icons/mouse-pointer-square.js'),
  "mouse-pointer": () => import('./dist/esm/icons/mouse-pointer.js'),
  "mouse": () => import('./dist/esm/icons/mouse.js'),
  "move-3d": () => import('./dist/esm/icons/move-3d.js'),
  "move-diagonal-2": () => import('./dist/esm/icons/move-diagonal-2.js'),
  "move-diagonal": () => import('./dist/esm/icons/move-diagonal.js'),
  "move-down-left": () => import('./dist/esm/icons/move-down-left.js'),
  "move-down-right": () => import('./dist/esm/icons/move-down-right.js'),
  "move-down": () => import('./dist/esm/icons/move-down.js'),
  "move-horizontal": () => import('./dist/esm/icons/move-horizontal.js'),
  "move-left": () => import('./dist/esm/icons/move-left.js'),
  "move-right": () => import('./dist/esm/icons/move-right.js'),
  "move-up-left": () => import('./dist/esm/icons/move-up-left.js'),
  "move-up-right": () => import('./dist/esm/icons/move-up-right.js'),
  "move-up": () => import('./dist/esm/icons/move-up.js'),
  "move-vertical": () => import('./dist/esm/icons/move-vertical.js'),
  "move": () => import('./dist/esm/icons/move.js'),
  "music-2": () => import('./dist/esm/icons/music-2.js'),
  "music-3": () => import('./dist/esm/icons/music-3.js'),
  "music-4": () => import('./dist/esm/icons/music-4.js'),
  "music": () => import('./dist/esm/icons/music.js'),
  "navigation-2-off": () => import('./dist/esm/icons/navigation-2-off.js'),
  "navigation-2": () => import('./dist/esm/icons/navigation-2.js'),
  "navigation-off": () => import('./dist/esm/icons/navigation-off.js'),
  "navigation": () => import('./dist/esm/icons/navigation.js'),
  "network": () => import('./dist/esm/icons/network.js'),
  "newspaper": () => import('./dist/esm/icons/newspaper.js'),
  "nfc": () => import('./dist/esm/icons/nfc.js'),
  "nut-off": () => import('./dist/esm/icons/nut-off.js'),
  "nut": () => import('./dist/esm/icons/nut.js'),
  "octagon": () => import('./dist/esm/icons/octagon.js'),
  "option": () => import('./dist/esm/icons/option.js'),
  "orbit": () => import('./dist/esm/icons/orbit.js'),
  "outdent": () => import('./dist/esm/icons/outdent.js'),
  "package-2": () => import('./dist/esm/icons/package-2.js'),
  "package-check": () => import('./dist/esm/icons/package-check.js'),
  "package-minus": () => import('./dist/esm/icons/package-minus.js'),
  "package-open": () => import('./dist/esm/icons/package-open.js'),
  "package-plus": () => import('./dist/esm/icons/package-plus.js'),
  "package-search": () => import('./dist/esm/icons/package-search.js'),
  "package-x": () => import('./dist/esm/icons/package-x.js'),
  "package": () => import('./dist/esm/icons/package.js'),
  "paint-bucket": () => import('./dist/esm/icons/paint-bucket.js'),
  "paintbrush-2": () => import('./dist/esm/icons/paintbrush-2.js'),
  "paintbrush": () => import('./dist/esm/icons/paintbrush.js'),
  "palette": () => import('./dist/esm/icons/palette.js'),
  "palmtree": () => import('./dist/esm/icons/palmtree.js'),
  "panel-bottom-close": () => import('./dist/esm/icons/panel-bottom-close.js'),
  "panel-bottom-inactive": () => import('./dist/esm/icons/panel-bottom-inactive.js'),
  "panel-bottom-open": () => import('./dist/esm/icons/panel-bottom-open.js'),
  "panel-bottom": () => import('./dist/esm/icons/panel-bottom.js'),
  "panel-left-close": () => import('./dist/esm/icons/panel-left-close.js'),
  "panel-left-inactive": () => import('./dist/esm/icons/panel-left-inactive.js'),
  "panel-left-open": () => import('./dist/esm/icons/panel-left-open.js'),
  "panel-left": () => import('./dist/esm/icons/panel-left.js'),
  "panel-right-close": () => import('./dist/esm/icons/panel-right-close.js'),
  "panel-right-inactive": () => import('./dist/esm/icons/panel-right-inactive.js'),
  "panel-right-open": () => import('./dist/esm/icons/panel-right-open.js'),
  "panel-right": () => import('./dist/esm/icons/panel-right.js'),
  "panel-top-close": () => import('./dist/esm/icons/panel-top-close.js'),
  "panel-top-inactive": () => import('./dist/esm/icons/panel-top-inactive.js'),
  "panel-top-open": () => import('./dist/esm/icons/panel-top-open.js'),
  "panel-top": () => import('./dist/esm/icons/panel-top.js'),
  "paperclip": () => import('./dist/esm/icons/paperclip.js'),
  "parentheses": () => import('./dist/esm/icons/parentheses.js'),
  "parking-circle-off": () => import('./dist/esm/icons/parking-circle-off.js'),
  "parking-circle": () => import('./dist/esm/icons/parking-circle.js'),
  "parking-meter": () => import('./dist/esm/icons/parking-meter.js'),
  "parking-square-off": () => import('./dist/esm/icons/parking-square-off.js'),
  "parking-square": () => import('./dist/esm/icons/parking-square.js'),
  "party-popper": () => import('./dist/esm/icons/party-popper.js'),
  "pause-circle": () => import('./dist/esm/icons/pause-circle.js'),
  "pause-octagon": () => import('./dist/esm/icons/pause-octagon.js'),
  "pause": () => import('./dist/esm/icons/pause.js'),
  "paw-print": () => import('./dist/esm/icons/paw-print.js'),
  "pc-case": () => import('./dist/esm/icons/pc-case.js'),
  "pen-line": () => import('./dist/esm/icons/pen-line.js'),
  "pen-square": () => import('./dist/esm/icons/pen-square.js'),
  "pen-tool": () => import('./dist/esm/icons/pen-tool.js'),
  "pen": () => import('./dist/esm/icons/pen.js'),
  "pencil-line": () => import('./dist/esm/icons/pencil-line.js'),
  "pencil-ruler": () => import('./dist/esm/icons/pencil-ruler.js'),
  "pencil": () => import('./dist/esm/icons/pencil.js'),
  "pentagon": () => import('./dist/esm/icons/pentagon.js'),
  "percent-circle": () => import('./dist/esm/icons/percent-circle.js'),
  "percent-diamond": () => import('./dist/esm/icons/percent-diamond.js'),
  "percent-square": () => import('./dist/esm/icons/percent-square.js'),
  "percent": () => import('./dist/esm/icons/percent.js'),
  "person-standing": () => import('./dist/esm/icons/person-standing.js'),
  "phone-call": () => import('./dist/esm/icons/phone-call.js'),
  "phone-forwarded": () => import('./dist/esm/icons/phone-forwarded.js'),
  "phone-incoming": () => import('./dist/esm/icons/phone-incoming.js'),
  "phone-missed": () => import('./dist/esm/icons/phone-missed.js'),
  "phone-off": () => import('./dist/esm/icons/phone-off.js'),
  "phone-outgoing": () => import('./dist/esm/icons/phone-outgoing.js'),
  "phone": () => import('./dist/esm/icons/phone.js'),
  "pi-square": () => import('./dist/esm/icons/pi-square.js'),
  "pi": () => import('./dist/esm/icons/pi.js'),
  "picture-in-picture-2": () => import('./dist/esm/icons/picture-in-picture-2.js'),
  "picture-in-picture": () => import('./dist/esm/icons/picture-in-picture.js'),
  "pie-chart": () => import('./dist/esm/icons/pie-chart.js'),
  "piggy-bank": () => import('./dist/esm/icons/piggy-bank.js'),
  "pilcrow-square": () => import('./dist/esm/icons/pilcrow-square.js'),
  "pilcrow": () => import('./dist/esm/icons/pilcrow.js'),
  "pill": () => import('./dist/esm/icons/pill.js'),
  "pin-off": () => import('./dist/esm/icons/pin-off.js'),
  "pin": () => import('./dist/esm/icons/pin.js'),
  "pipette": () => import('./dist/esm/icons/pipette.js'),
  "pizza": () => import('./dist/esm/icons/pizza.js'),
  "plane-landing": () => import('./dist/esm/icons/plane-landing.js'),
  "plane-takeoff": () => import('./dist/esm/icons/plane-takeoff.js'),
  "plane": () => import('./dist/esm/icons/plane.js'),
  "play-circle": () => import('./dist/esm/icons/play-circle.js'),
  "play-square": () => import('./dist/esm/icons/play-square.js'),
  "play": () => import('./dist/esm/icons/play.js'),
  "plug-2": () => import('./dist/esm/icons/plug-2.js'),
  "plug-zap-2": () => import('./dist/esm/icons/plug-zap-2.js'),
  "plug-zap": () => import('./dist/esm/icons/plug-zap.js'),
  "plug": () => import('./dist/esm/icons/plug.js'),
  "plus-circle": () => import('./dist/esm/icons/plus-circle.js'),
  "plus-square": () => import('./dist/esm/icons/plus-square.js'),
  "plus": () => import('./dist/esm/icons/plus.js'),
  "pocket-knife": () => import('./dist/esm/icons/pocket-knife.js'),
  "pocket": () => import('./dist/esm/icons/pocket.js'),
  "podcast": () => import('./dist/esm/icons/podcast.js'),
  "pointer": () => import('./dist/esm/icons/pointer.js'),
  "popcorn": () => import('./dist/esm/icons/popcorn.js'),
  "popsicle": () => import('./dist/esm/icons/popsicle.js'),
  "pound-sterling": () => import('./dist/esm/icons/pound-sterling.js'),
  "power-circle": () => import('./dist/esm/icons/power-circle.js'),
  "power-off": () => import('./dist/esm/icons/power-off.js'),
  "power-square": () => import('./dist/esm/icons/power-square.js'),
  "power": () => import('./dist/esm/icons/power.js'),
  "presentation": () => import('./dist/esm/icons/presentation.js'),
  "printer": () => import('./dist/esm/icons/printer.js'),
  "projector": () => import('./dist/esm/icons/projector.js'),
  "puzzle": () => import('./dist/esm/icons/puzzle.js'),
  "pyramid": () => import('./dist/esm/icons/pyramid.js'),
  "qr-code": () => import('./dist/esm/icons/qr-code.js'),
  "quote": () => import('./dist/esm/icons/quote.js'),
  "rabbit": () => import('./dist/esm/icons/rabbit.js'),
  "radar": () => import('./dist/esm/icons/radar.js'),
  "radiation": () => import('./dist/esm/icons/radiation.js'),
  "radio-receiver": () => import('./dist/esm/icons/radio-receiver.js'),
  "radio-tower": () => import('./dist/esm/icons/radio-tower.js'),
  "radio": () => import('./dist/esm/icons/radio.js'),
  "radius": () => import('./dist/esm/icons/radius.js'),
  "rail-symbol": () => import('./dist/esm/icons/rail-symbol.js'),
  "rainbow": () => import('./dist/esm/icons/rainbow.js'),
  "rat": () => import('./dist/esm/icons/rat.js'),
  "ratio": () => import('./dist/esm/icons/ratio.js'),
  "receipt": () => import('./dist/esm/icons/receipt.js'),
  "rectangle-horizontal": () => import('./dist/esm/icons/rectangle-horizontal.js'),
  "rectangle-vertical": () => import('./dist/esm/icons/rectangle-vertical.js'),
  "recycle": () => import('./dist/esm/icons/recycle.js'),
  "redo-2": () => import('./dist/esm/icons/redo-2.js'),
  "redo-dot": () => import('./dist/esm/icons/redo-dot.js'),
  "redo": () => import('./dist/esm/icons/redo.js'),
  "refresh-ccw-dot": () => import('./dist/esm/icons/refresh-ccw-dot.js'),
  "refresh-ccw": () => import('./dist/esm/icons/refresh-ccw.js'),
  "refresh-cw-off": () => import('./dist/esm/icons/refresh-cw-off.js'),
  "refresh-cw": () => import('./dist/esm/icons/refresh-cw.js'),
  "refrigerator": () => import('./dist/esm/icons/refrigerator.js'),
  "regex": () => import('./dist/esm/icons/regex.js'),
  "remove-formatting": () => import('./dist/esm/icons/remove-formatting.js'),
  "repeat-1": () => import('./dist/esm/icons/repeat-1.js'),
  "repeat-2": () => import('./dist/esm/icons/repeat-2.js'),
  "repeat": () => import('./dist/esm/icons/repeat.js'),
  "replace-all": () => import('./dist/esm/icons/replace-all.js'),
  "replace": () => import('./dist/esm/icons/replace.js'),
  "reply-all": () => import('./dist/esm/icons/reply-all.js'),
  "reply": () => import('./dist/esm/icons/reply.js'),
  "rewind": () => import('./dist/esm/icons/rewind.js'),
  "ribbon": () => import('./dist/esm/icons/ribbon.js'),
  "rocket": () => import('./dist/esm/icons/rocket.js'),
  "rocking-chair": () => import('./dist/esm/icons/rocking-chair.js'),
  "roller-coaster": () => import('./dist/esm/icons/roller-coaster.js'),
  "rotate-3d": () => import('./dist/esm/icons/rotate-3d.js'),
  "rotate-ccw": () => import('./dist/esm/icons/rotate-ccw.js'),
  "rotate-cw": () => import('./dist/esm/icons/rotate-cw.js'),
  "route-off": () => import('./dist/esm/icons/route-off.js'),
  "route": () => import('./dist/esm/icons/route.js'),
  "router": () => import('./dist/esm/icons/router.js'),
  "rows": () => import('./dist/esm/icons/rows.js'),
  "rss": () => import('./dist/esm/icons/rss.js'),
  "ruler": () => import('./dist/esm/icons/ruler.js'),
  "russian-ruble": () => import('./dist/esm/icons/russian-ruble.js'),
  "sailboat": () => import('./dist/esm/icons/sailboat.js'),
  "salad": () => import('./dist/esm/icons/salad.js'),
  "sandwich": () => import('./dist/esm/icons/sandwich.js'),
  "satellite-dish": () => import('./dist/esm/icons/satellite-dish.js'),
  "satellite": () => import('./dist/esm/icons/satellite.js'),
  "save-all": () => import('./dist/esm/icons/save-all.js'),
  "save": () => import('./dist/esm/icons/save.js'),
  "scale-3d": () => import('./dist/esm/icons/scale-3d.js'),
  "scale": () => import('./dist/esm/icons/scale.js'),
  "scaling": () => import('./dist/esm/icons/scaling.js'),
  "scan-barcode": () => import('./dist/esm/icons/scan-barcode.js'),
  "scan-eye": () => import('./dist/esm/icons/scan-eye.js'),
  "scan-face": () => import('./dist/esm/icons/scan-face.js'),
  "scan-line": () => import('./dist/esm/icons/scan-line.js'),
  "scan-search": () => import('./dist/esm/icons/scan-search.js'),
  "scan-text": () => import('./dist/esm/icons/scan-text.js'),
  "scan": () => import('./dist/esm/icons/scan.js'),
  "scatter-chart": () => import('./dist/esm/icons/scatter-chart.js'),
  "school-2": () => import('./dist/esm/icons/school-2.js'),
  "school": () => import('./dist/esm/icons/school.js'),
  "scissors-line-dashed": () => import('./dist/esm/icons/scissors-line-dashed.js'),
  "scissors-square-dashed-bottom": () => import('./dist/esm/icons/scissors-square-dashed-bottom.js'),
  "scissors-square": () => import('./dist/esm/icons/scissors-square.js'),
  "scissors": () => import('./dist/esm/icons/scissors.js'),
  "screen-share-off": () => import('./dist/esm/icons/screen-share-off.js'),
  "screen-share": () => import('./dist/esm/icons/screen-share.js'),
  "scroll-text": () => import('./dist/esm/icons/scroll-text.js'),
  "scroll": () => import('./dist/esm/icons/scroll.js'),
  "search-check": () => import('./dist/esm/icons/search-check.js'),
  "search-code": () => import('./dist/esm/icons/search-code.js'),
  "search-slash": () => import('./dist/esm/icons/search-slash.js'),
  "search-x": () => import('./dist/esm/icons/search-x.js'),
  "search": () => import('./dist/esm/icons/search.js'),
  "send-horizontal": () => import('./dist/esm/icons/send-horizontal.js'),
  "send-to-back": () => import('./dist/esm/icons/send-to-back.js'),
  "send": () => import('./dist/esm/icons/send.js'),
  "separator-horizontal": () => import('./dist/esm/icons/separator-horizontal.js'),
  "separator-vertical": () => import('./dist/esm/icons/separator-vertical.js'),
  "server-cog": () => import('./dist/esm/icons/server-cog.js'),
  "server-crash": () => import('./dist/esm/icons/server-crash.js'),
  "server-off": () => import('./dist/esm/icons/server-off.js'),
  "server": () => import('./dist/esm/icons/server.js'),
  "settings-2": () => import('./dist/esm/icons/settings-2.js'),
  "settings": () => import('./dist/esm/icons/settings.js'),
  "shapes": () => import('./dist/esm/icons/shapes.js'),
  "share-2": () => import('./dist/esm/icons/share-2.js'),
  "share": () => import('./dist/esm/icons/share.js'),
  "sheet": () => import('./dist/esm/icons/sheet.js'),
  "shell": () => import('./dist/esm/icons/shell.js'),
  "shield-alert": () => import('./dist/esm/icons/shield-alert.js'),
  "shield-ban": () => import('./dist/esm/icons/shield-ban.js'),
  "shield-check": () => import('./dist/esm/icons/shield-check.js'),
  "shield-ellipsis": () => import('./dist/esm/icons/shield-ellipsis.js'),
  "shield-half": () => import('./dist/esm/icons/shield-half.js'),
  "shield-minus": () => import('./dist/esm/icons/shield-minus.js'),
  "shield-off": () => import('./dist/esm/icons/shield-off.js'),
  "shield-plus": () => import('./dist/esm/icons/shield-plus.js'),
  "shield-question": () => import('./dist/esm/icons/shield-question.js'),
  "shield-x": () => import('./dist/esm/icons/shield-x.js'),
  "shield": () => import('./dist/esm/icons/shield.js'),
  "ship-wheel": () => import('./dist/esm/icons/ship-wheel.js'),
  "ship": () => import('./dist/esm/icons/ship.js'),
  "shirt": () => import('./dist/esm/icons/shirt.js'),
  "shopping-bag": () => import('./dist/esm/icons/shopping-bag.js'),
  "shopping-basket": () => import('./dist/esm/icons/shopping-basket.js'),
  "shopping-cart": () => import('./dist/esm/icons/shopping-cart.js'),
  "shovel": () => import('./dist/esm/icons/shovel.js'),
  "shower-head": () => import('./dist/esm/icons/shower-head.js'),
  "shrink": () => import('./dist/esm/icons/shrink.js'),
  "shrub": () => import('./dist/esm/icons/shrub.js'),
  "shuffle": () => import('./dist/esm/icons/shuffle.js'),
  "sigma-square": () => import('./dist/esm/icons/sigma-square.js'),
  "sigma": () => import('./dist/esm/icons/sigma.js'),
  "signal-high": () => import('./dist/esm/icons/signal-high.js'),
  "signal-low": () => import('./dist/esm/icons/signal-low.js'),
  "signal-medium": () => import('./dist/esm/icons/signal-medium.js'),
  "signal-zero": () => import('./dist/esm/icons/signal-zero.js'),
  "signal": () => import('./dist/esm/icons/signal.js'),
  "signpost-big": () => import('./dist/esm/icons/signpost-big.js'),
  "signpost": () => import('./dist/esm/icons/signpost.js'),
  "siren": () => import('./dist/esm/icons/siren.js'),
  "skip-back": () => import('./dist/esm/icons/skip-back.js'),
  "skip-forward": () => import('./dist/esm/icons/skip-forward.js'),
  "skull": () => import('./dist/esm/icons/skull.js'),
  "slack": () => import('./dist/esm/icons/slack.js'),
  "slash": () => import('./dist/esm/icons/slash.js'),
  "slice": () => import('./dist/esm/icons/slice.js'),
  "sliders-horizontal": () => import('./dist/esm/icons/sliders-horizontal.js'),
  "sliders": () => import('./dist/esm/icons/sliders.js'),
  "smartphone-charging": () => import('./dist/esm/icons/smartphone-charging.js'),
  "smartphone-nfc": () => import('./dist/esm/icons/smartphone-nfc.js'),
  "smartphone": () => import('./dist/esm/icons/smartphone.js'),
  "smile-plus": () => import('./dist/esm/icons/smile-plus.js'),
  "smile": () => import('./dist/esm/icons/smile.js'),
  "snail": () => import('./dist/esm/icons/snail.js'),
  "snowflake": () => import('./dist/esm/icons/snowflake.js'),
  "sofa": () => import('./dist/esm/icons/sofa.js'),
  "soup": () => import('./dist/esm/icons/soup.js'),
  "space": () => import('./dist/esm/icons/space.js'),
  "spade": () => import('./dist/esm/icons/spade.js'),
  "sparkle": () => import('./dist/esm/icons/sparkle.js'),
  "sparkles": () => import('./dist/esm/icons/sparkles.js'),
  "speaker": () => import('./dist/esm/icons/speaker.js'),
  "speech": () => import('./dist/esm/icons/speech.js'),
  "spell-check-2": () => import('./dist/esm/icons/spell-check-2.js'),
  "spell-check": () => import('./dist/esm/icons/spell-check.js'),
  "spline": () => import('./dist/esm/icons/spline.js'),
  "split-square-horizontal": () => import('./dist/esm/icons/split-square-horizontal.js'),
  "split-square-vertical": () => import('./dist/esm/icons/split-square-vertical.js'),
  "split": () => import('./dist/esm/icons/split.js'),
  "spray-can": () => import('./dist/esm/icons/spray-can.js'),
  "sprout": () => import('./dist/esm/icons/sprout.js'),
  "square-asterisk": () => import('./dist/esm/icons/square-asterisk.js'),
  "square-code": () => import('./dist/esm/icons/square-code.js'),
  "square-dashed-bottom-code": () => import('./dist/esm/icons/square-dashed-bottom-code.js'),
  "square-dashed-bottom": () => import('./dist/esm/icons/square-dashed-bottom.js'),
  "square-dot": () => import('./dist/esm/icons/square-dot.js'),
  "square-equal": () => import('./dist/esm/icons/square-equal.js'),
  "square-slash": () => import('./dist/esm/icons/square-slash.js'),
  "square-stack": () => import('./dist/esm/icons/square-stack.js'),
  "square": () => import('./dist/esm/icons/square.js'),
  "squirrel": () => import('./dist/esm/icons/squirrel.js'),
  "stamp": () => import('./dist/esm/icons/stamp.js'),
  "star-half": () => import('./dist/esm/icons/star-half.js'),
  "star-off": () => import('./dist/esm/icons/star-off.js'),
  "star": () => import('./dist/esm/icons/star.js'),
  "step-back": () => import('./dist/esm/icons/step-back.js'),
  "step-forward": () => import('./dist/esm/icons/step-forward.js'),
  "stethoscope": () => import('./dist/esm/icons/stethoscope.js'),
  "sticker": () => import('./dist/esm/icons/sticker.js'),
  "sticky-note": () => import('./dist/esm/icons/sticky-note.js'),
  "stop-circle": () => import('./dist/esm/icons/stop-circle.js'),
  "store": () => import('./dist/esm/icons/store.js'),
  "stretch-horizontal": () => import('./dist/esm/icons/stretch-horizontal.js'),
  "stretch-vertical": () => import('./dist/esm/icons/stretch-vertical.js'),
  "strikethrough": () => import('./dist/esm/icons/strikethrough.js'),
  "subscript": () => import('./dist/esm/icons/subscript.js'),
  "subtitles": () => import('./dist/esm/icons/subtitles.js'),
  "sun-dim": () => import('./dist/esm/icons/sun-dim.js'),
  "sun-medium": () => import('./dist/esm/icons/sun-medium.js'),
  "sun-moon": () => import('./dist/esm/icons/sun-moon.js'),
  "sun-snow": () => import('./dist/esm/icons/sun-snow.js'),
  "sun": () => import('./dist/esm/icons/sun.js'),
  "sunrise": () => import('./dist/esm/icons/sunrise.js'),
  "sunset": () => import('./dist/esm/icons/sunset.js'),
  "superscript": () => import('./dist/esm/icons/superscript.js'),
  "swiss-franc": () => import('./dist/esm/icons/swiss-franc.js'),
  "switch-camera": () => import('./dist/esm/icons/switch-camera.js'),
  "sword": () => import('./dist/esm/icons/sword.js'),
  "swords": () => import('./dist/esm/icons/swords.js'),
  "syringe": () => import('./dist/esm/icons/syringe.js'),
  "table-2": () => import('./dist/esm/icons/table-2.js'),
  "table-properties": () => import('./dist/esm/icons/table-properties.js'),
  "table": () => import('./dist/esm/icons/table.js'),
  "tablet-smartphone": () => import('./dist/esm/icons/tablet-smartphone.js'),
  "tablet": () => import('./dist/esm/icons/tablet.js'),
  "tablets": () => import('./dist/esm/icons/tablets.js'),
  "tag": () => import('./dist/esm/icons/tag.js'),
  "tags": () => import('./dist/esm/icons/tags.js'),
  "tally-1": () => import('./dist/esm/icons/tally-1.js'),
  "tally-2": () => import('./dist/esm/icons/tally-2.js'),
  "tally-3": () => import('./dist/esm/icons/tally-3.js'),
  "tally-4": () => import('./dist/esm/icons/tally-4.js'),
  "tally-5": () => import('./dist/esm/icons/tally-5.js'),
  "tangent": () => import('./dist/esm/icons/tangent.js'),
  "target": () => import('./dist/esm/icons/target.js'),
  "tent-tree": () => import('./dist/esm/icons/tent-tree.js'),
  "tent": () => import('./dist/esm/icons/tent.js'),
  "terminal-square": () => import('./dist/esm/icons/terminal-square.js'),
  "terminal": () => import('./dist/esm/icons/terminal.js'),
  "test-tube-2": () => import('./dist/esm/icons/test-tube-2.js'),
  "test-tube": () => import('./dist/esm/icons/test-tube.js'),
  "test-tubes": () => import('./dist/esm/icons/test-tubes.js'),
  "text-cursor-input": () => import('./dist/esm/icons/text-cursor-input.js'),
  "text-cursor": () => import('./dist/esm/icons/text-cursor.js'),
  "text-quote": () => import('./dist/esm/icons/text-quote.js'),
  "text-select": () => import('./dist/esm/icons/text-select.js'),
  "text": () => import('./dist/esm/icons/text.js'),
  "theater": () => import('./dist/esm/icons/theater.js'),
  "thermometer-snowflake": () => import('./dist/esm/icons/thermometer-snowflake.js'),
  "thermometer-sun": () => import('./dist/esm/icons/thermometer-sun.js'),
  "thermometer": () => import('./dist/esm/icons/thermometer.js'),
  "thumbs-down": () => import('./dist/esm/icons/thumbs-down.js'),
  "thumbs-up": () => import('./dist/esm/icons/thumbs-up.js'),
  "ticket": () => import('./dist/esm/icons/ticket.js'),
  "timer-off": () => import('./dist/esm/icons/timer-off.js'),
  "timer-reset": () => import('./dist/esm/icons/timer-reset.js'),
  "timer": () => import('./dist/esm/icons/timer.js'),
  "toggle-left": () => import('./dist/esm/icons/toggle-left.js'),
  "toggle-right": () => import('./dist/esm/icons/toggle-right.js'),
  "tornado": () => import('./dist/esm/icons/tornado.js'),
  "torus": () => import('./dist/esm/icons/torus.js'),
  "touchpad-off": () => import('./dist/esm/icons/touchpad-off.js'),
  "touchpad": () => import('./dist/esm/icons/touchpad.js'),
  "tower-control": () => import('./dist/esm/icons/tower-control.js'),
  "toy-brick": () => import('./dist/esm/icons/toy-brick.js'),
  "tractor": () => import('./dist/esm/icons/tractor.js'),
  "traffic-cone": () => import('./dist/esm/icons/traffic-cone.js'),
  "train-front-tunnel": () => import('./dist/esm/icons/train-front-tunnel.js'),
  "train-front": () => import('./dist/esm/icons/train-front.js'),
  "train-track": () => import('./dist/esm/icons/train-track.js'),
  "tram-front": () => import('./dist/esm/icons/tram-front.js'),
  "trash-2": () => import('./dist/esm/icons/trash-2.js'),
  "trash": () => import('./dist/esm/icons/trash.js'),
  "tree-deciduous": () => import('./dist/esm/icons/tree-deciduous.js'),
  "tree-pine": () => import('./dist/esm/icons/tree-pine.js'),
  "trees": () => import('./dist/esm/icons/trees.js'),
  "trello": () => import('./dist/esm/icons/trello.js'),
  "trending-down": () => import('./dist/esm/icons/trending-down.js'),
  "trending-up": () => import('./dist/esm/icons/trending-up.js'),
  "triangle-right": () => import('./dist/esm/icons/triangle-right.js'),
  "triangle": () => import('./dist/esm/icons/triangle.js'),
  "trophy": () => import('./dist/esm/icons/trophy.js'),
  "truck": () => import('./dist/esm/icons/truck.js'),
  "turtle": () => import('./dist/esm/icons/turtle.js'),
  "tv-2": () => import('./dist/esm/icons/tv-2.js'),
  "tv": () => import('./dist/esm/icons/tv.js'),
  "twitch": () => import('./dist/esm/icons/twitch.js'),
  "twitter": () => import('./dist/esm/icons/twitter.js'),
  "type": () => import('./dist/esm/icons/type.js'),
  "umbrella-off": () => import('./dist/esm/icons/umbrella-off.js'),
  "umbrella": () => import('./dist/esm/icons/umbrella.js'),
  "underline": () => import('./dist/esm/icons/underline.js'),
  "undo-2": () => import('./dist/esm/icons/undo-2.js'),
  "undo-dot": () => import('./dist/esm/icons/undo-dot.js'),
  "undo": () => import('./dist/esm/icons/undo.js'),
  "unfold-horizontal": () => import('./dist/esm/icons/unfold-horizontal.js'),
  "unfold-vertical": () => import('./dist/esm/icons/unfold-vertical.js'),
  "ungroup": () => import('./dist/esm/icons/ungroup.js'),
  "unlink-2": () => import('./dist/esm/icons/unlink-2.js'),
  "unlink": () => import('./dist/esm/icons/unlink.js'),
  "unlock-keyhole": () => import('./dist/esm/icons/unlock-keyhole.js'),
  "unlock": () => import('./dist/esm/icons/unlock.js'),
  "unplug": () => import('./dist/esm/icons/unplug.js'),
  "upload-cloud": () => import('./dist/esm/icons/upload-cloud.js'),
  "upload": () => import('./dist/esm/icons/upload.js'),
  "usb": () => import('./dist/esm/icons/usb.js'),
  "user-2": () => import('./dist/esm/icons/user-2.js'),
  "user-check-2": () => import('./dist/esm/icons/user-check-2.js'),
  "user-check": () => import('./dist/esm/icons/user-check.js'),
  "user-circle-2": () => import('./dist/esm/icons/user-circle-2.js'),
  "user-circle": () => import('./dist/esm/icons/user-circle.js'),
  "user-cog-2": () => import('./dist/esm/icons/user-cog-2.js'),
  "user-cog": () => import('./dist/esm/icons/user-cog.js'),
  "user-minus-2": () => import('./dist/esm/icons/user-minus-2.js'),
  "user-minus": () => import('./dist/esm/icons/user-minus.js'),
  "user-plus-2": () => import('./dist/esm/icons/user-plus-2.js'),
  "user-plus": () => import('./dist/esm/icons/user-plus.js'),
  "user-square-2": () => import('./dist/esm/icons/user-square-2.js'),
  "user-square": () => import('./dist/esm/icons/user-square.js'),
  "user-x-2": () => import('./dist/esm/icons/user-x-2.js'),
  "user-x": () => import('./dist/esm/icons/user-x.js'),
  "user": () => import('./dist/esm/icons/user.js'),
  "users-2": () => import('./dist/esm/icons/users-2.js'),
  "users": () => import('./dist/esm/icons/users.js'),
  "utensils-crossed": () => import('./dist/esm/icons/utensils-crossed.js'),
  "utensils": () => import('./dist/esm/icons/utensils.js'),
  "utility-pole": () => import('./dist/esm/icons/utility-pole.js'),
  "variable": () => import('./dist/esm/icons/variable.js'),
  "vegan": () => import('./dist/esm/icons/vegan.js'),
  "venetian-mask": () => import('./dist/esm/icons/venetian-mask.js'),
  "vibrate-off": () => import('./dist/esm/icons/vibrate-off.js'),
  "vibrate": () => import('./dist/esm/icons/vibrate.js'),
  "video-off": () => import('./dist/esm/icons/video-off.js'),
  "video": () => import('./dist/esm/icons/video.js'),
  "videotape": () => import('./dist/esm/icons/videotape.js'),
  "view": () => import('./dist/esm/icons/view.js'),
  "voicemail": () => import('./dist/esm/icons/voicemail.js'),
  "volume-1": () => import('./dist/esm/icons/volume-1.js'),
  "volume-2": () => import('./dist/esm/icons/volume-2.js'),
  "volume-x": () => import('./dist/esm/icons/volume-x.js'),
  "volume": () => import('./dist/esm/icons/volume.js'),
  "vote": () => import('./dist/esm/icons/vote.js'),
  "wallet-2": () => import('./dist/esm/icons/wallet-2.js'),
  "wallet-cards": () => import('./dist/esm/icons/wallet-cards.js'),
  "wallet": () => import('./dist/esm/icons/wallet.js'),
  "wallpaper": () => import('./dist/esm/icons/wallpaper.js'),
  "wand-2": () => import('./dist/esm/icons/wand-2.js'),
  "wand": () => import('./dist/esm/icons/wand.js'),
  "warehouse": () => import('./dist/esm/icons/warehouse.js'),
  "watch": () => import('./dist/esm/icons/watch.js'),
  "waves": () => import('./dist/esm/icons/waves.js'),
  "waypoints": () => import('./dist/esm/icons/waypoints.js'),
  "webcam": () => import('./dist/esm/icons/webcam.js'),
  "webhook": () => import('./dist/esm/icons/webhook.js'),
  "weight": () => import('./dist/esm/icons/weight.js'),
  "wheat-off": () => import('./dist/esm/icons/wheat-off.js'),
  "wheat": () => import('./dist/esm/icons/wheat.js'),
  "whole-word": () => import('./dist/esm/icons/whole-word.js'),
  "wifi-off": () => import('./dist/esm/icons/wifi-off.js'),
  "wifi": () => import('./dist/esm/icons/wifi.js'),
  "wind": () => import('./dist/esm/icons/wind.js'),
  "wine-off": () => import('./dist/esm/icons/wine-off.js'),
  "wine": () => import('./dist/esm/icons/wine.js'),
  "workflow": () => import('./dist/esm/icons/workflow.js'),
  "wrap-text": () => import('./dist/esm/icons/wrap-text.js'),
  "wrench": () => import('./dist/esm/icons/wrench.js'),
  "x-circle": () => import('./dist/esm/icons/x-circle.js'),
  "x-octagon": () => import('./dist/esm/icons/x-octagon.js'),
  "x-square": () => import('./dist/esm/icons/x-square.js'),
  "x": () => import('./dist/esm/icons/x.js'),
  "youtube": () => import('./dist/esm/icons/youtube.js'),
  "zap-off": () => import('./dist/esm/icons/zap-off.js'),
  "zap": () => import('./dist/esm/icons/zap.js'),
  "zoom-in": () => import('./dist/esm/icons/zoom-in.js'),
  "zoom-out": () => import('./dist/esm/icons/zoom-out.js')
};

export { dynamicIconImports as default };
//# sourceMappingURL=dynamicIconImports.js.map
