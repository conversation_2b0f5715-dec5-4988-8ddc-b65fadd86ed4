import './globals.css';
import { CartProvider } from '@/context/CartContext';
import { AuthProvider } from '@/context/AuthContext';
import { Toaster } from 'react-hot-toast';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import BackToTopButton from '@/components/ui/BackToTopButton';
import WhatsAppButton from '@/components/ui/WhatsAppButton';
import ClientScripts from '@/components/ClientScripts';
import PWAInstallPrompt, { PWAStatusIndicator } from '@/components/PWAInstallPrompt';


export const metadata = {
  title: {
    default: 'Deal4u - Amazing Deals on Premium Products',
    template: '%s | Deal4u'
  },
  description: 'Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.',
  keywords: ['ecommerce', 'deals', 'shopping', 'products', 'online store'],
  authors: [{ name: 'Deal4u Team' }],
  creator: 'Deal4u',
  publisher: 'Deal4u',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    siteName: 'Deal4u',
    title: 'Deal4u - Amazing Deals on Premium Products',
    description: 'Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.',
    images: [
      {
        url: '/og-image.jpg', // Add this image to your public folder
        width: 1200,
        height: 630,
        alt: 'Deal4u - Amazing Deals on Premium Products',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@deal4u',
    creator: '@deal4u',
    title: 'Deal4u - Amazing Deals on Premium Products',
    description: 'Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // Add your verification codes here
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
    // yahoo: 'your-yahoo-verification-code',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#3b82f6' },
    { media: '(prefers-color-scheme: dark)', color: '#1e40af' },
  ],
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://images.unsplash.com" />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />



        {/* PWA Meta Tags */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Deal4u" />
        <meta name="application-name" content="Deal4u" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        <meta name="msapplication-tooltip" content="Deal4u - Amazing Deals" />
        <meta name="msapplication-starturl" content="/" />
        <meta name="msapplication-navbutton-color" content="#3b82f6" />

        {/* Prevent FOUC (Flash of Unstyled Content) */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                const mode = localStorage.getItem('theme');
                if (mode && mode === 'dark') {
                  document.documentElement.classList.add('dark');
                }
              } catch (e) {
                console.error('Error accessing localStorage:', e);
              }
            `
          }}
        />
      </head>
      <body className="antialiased expansion-alids-init">
        {/* Analytics script placeholder */}
        {process.env.NODE_ENV === 'production' && (
          <>
            {/* Google Analytics */}
            {/* <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
              strategy="afterInteractive"
            />
            <Script id="google-analytics" strategy="afterInteractive">
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
              `}
            </Script> */}
          </>
        )}

        {/* Main App Providers */}
        <AuthProvider>
          <CartProvider>
            <div className="min-h-screen flex flex-col bg-gray-50">
              {/* Header */}
              <Header />
              
              {/* Main Content */}
              <main className="flex-1 relative">
                {children}
              </main>
              
              {/* Footer */}
              <Footer />
            </div>

            {/* Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#fff',
                  color: '#374151',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                },
                success: {
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />

            {/* Loading indicator for navigation */}
            <div id="loading-indicator" className="hidden fixed top-0 left-0 w-full h-1 bg-blue-600 z-50">
              <div className="h-full bg-blue-400 animate-pulse"></div>
            </div>
          </CartProvider>
        </AuthProvider>

        {/* Skip to main content link for accessibility */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
        >
          Skip to main content
        </a>

        {/* Back to top button - now a client component */}
        <BackToTopButton />
        
        {/* WhatsApp Support Button */}
        <WhatsAppButton />

        {/* PWA Components */}
        <PWAInstallPrompt />
        <PWAStatusIndicator />

        {/* Client-side scripts for performance monitoring and smooth scroll */}
        <ClientScripts />


      </body>
    </html>
  );
}