{"name": "safe-push-apply", "version": "1.0.0", "description": "Push an array of items into an array, while being robust against prototype modification", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "nyc tape test", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production"}, "keywords": ["array", "push", "apply", "pushApply", "safe"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ljharb/safe-push-apply.git"}, "bugs": {"url": "https://github.com/ljharb/safe-push-apply/issues"}, "homepage": "https://github.com/ljharb/safe-push-apply#readme", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/isarray": "^2.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}