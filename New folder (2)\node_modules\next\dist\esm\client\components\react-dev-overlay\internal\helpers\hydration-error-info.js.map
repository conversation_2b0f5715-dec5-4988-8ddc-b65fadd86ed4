{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/hydration-error-info.ts"], "names": ["getHydrationWarningType", "msg", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "Boolean", "htmlTagsWarnings", "has", "isTextMismatchWarning", "textMismatchWarning", "textAndTagsMismatchWarnings", "isKnownHydrationWarning", "hydrationErrorState", "Set", "patchConsoleError", "prev", "console", "error", "serverContent", "clientContent", "componentStack", "warning", "apply", "arguments"], "mappings": "AAUA,OAAO,MAAMA,0BAA0B,CACrCC;IAEA,IAAIC,kBAAkBD,MAAM,OAAO;IACnC,IAAIE,4BAA4BF,MAAM,OAAO;IAC7C,OAAO;AACT,EAAC;AAED,MAAMC,oBAAoB,CAACD,MACzBG,QAAQH,OAAOI,iBAAiBC,GAAG,CAACL;AAEtC,MAAMM,wBAAwB,CAACN,MAAsBO,wBAAwBP;AAC7E,MAAME,8BAA8B,CAACF,MACnCG,QAAQH,OAAOQ,4BAA4BH,GAAG,CAACL;AAEjD,MAAMS,0BAA0B,CAACT,MAC/BC,kBAAkBD,QAClBE,4BAA4BF,QAC5BM,sBAAsBN;AAExB,OAAO,MAAMU,sBAA2C,CAAC,EAAC;AAE1D,iIAAiI;AACjI,MAAMN,mBAAmB,IAAIO,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMH,8BAA8B,IAAIG,IAAI;IAC1C;IACA;CACD;AACD,MAAMJ,sBACJ;AAEF;;;;;CAKC,GACD,OAAO,SAASK;IACd,MAAMC,OAAOC,QAAQC,KAAK;IAC1BD,QAAQC,KAAK,GAAG,SAAUf,GAAG,EAAEgB,aAAa,EAAEC,aAAa,EAAEC,cAAc;QACzE,IAAIT,wBAAwBT,MAAM;YAChCU,oBAAoBS,OAAO,GAAG;gBAC5B,sCAAsC;gBACtCnB;gBACAgB;gBACAC;aACD;YACDP,oBAAoBQ,cAAc,GAAGA;YACrCR,oBAAoBM,aAAa,GAAGA;YACpCN,oBAAoBO,aAAa,GAAGA;QACtC;QAEA,uCAAuC;QACvCJ,KAAKO,KAAK,CAACN,SAASO;IACtB;AACF"}