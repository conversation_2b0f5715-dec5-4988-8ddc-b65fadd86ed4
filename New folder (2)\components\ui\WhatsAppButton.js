'use client';

import { MessageCircle } from 'lucide-react';
import { useState, useEffect } from 'react';

export default function WhatsAppButton() {
  const [isVisible, setIsVisible] = useState(false);

  // Show button after scrolling a bit
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  return (
    <div className={`fixed bottom-6 right-6 z-50 transition-all duration-300 ${
      isVisible ? 'scale-100 opacity-100' : 'scale-75 opacity-0'
    }`}>
      <a
        href="https://wa.me/447447186806"
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 
                   rounded-full shadow-lg transition-all duration-300 hover:shadow-xl 
                   transform hover:-translate-y-1 hover:scale-110"
        aria-label="Contact us on WhatsApp"
      >
        <div className="absolute animate-ping w-14 h-14 bg-green-400 rounded-full opacity-60"></div>
        <MessageCircle size={28} className="text-white" />
      </a>
      <div className="bg-black text-white text-xs font-medium py-1 px-2 rounded-md 
                     absolute -top-8 left-1/2 transform -translate-x-1/2 
                     opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
        Chat with us
      </div>
    </div>
  );
}
