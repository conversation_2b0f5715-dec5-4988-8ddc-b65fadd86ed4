{"version": 3, "sources": ["../../../../src/server/typescript/rules/client-boundary.ts"], "names": ["NEXT_TS_ERRORS", "getTs", "getType<PERSON><PERSON>cker", "clientBoundary", "getSemanticDiagnosticsForExportVariableStatement", "source", "node", "ts", "diagnostics", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "push", "getSemanticDiagnosticsForFunctionExport", "typeC<PERSON>cker", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "props", "parameters", "name", "isObjectBindingPattern", "prop", "elements", "type", "getTypeAtLocation", "typeDeclarationNode", "symbol", "getDeclarations", "propName", "propertyName", "getText", "isFunctionOrConstructorTypeNode", "isClassDeclaration", "file", "category", "DiagnosticCategory", "Warning", "code", "INVALID_CLIENT_ENTRY_PROP", "messageText", "start", "getStart", "length", "getWidth"], "mappings": "AAAA,8FAA8F;AAE9F,SAASA,cAAc,QAAQ,cAAa;AAC5C,SAASC,KAAK,EAAEC,cAAc,QAAQ,WAAU;AAGhD,MAAMC,iBAAiB;IACrBC,kDACEC,MAA2B,EAC3BC,IAAgC;QAEhC,MAAMC,KAAKN;QAEX,MAAMO,cAAqC,EAAE;QAE7C,IAAID,GAAGE,yBAAyB,CAACH,KAAKI,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeL,KAAKI,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IAAIA,eAAeN,GAAGO,eAAe,CAACD,cAAc;oBAClDL,YAAYO,IAAI,IACXZ,eAAea,uCAAuC,CACvDX,QACAQ;gBAGN;YACF;QACF;QAEA,OAAOL;IACT;IAEAQ,yCACEX,MAA2B,EAC3BC,IAA2D;YAW7CA,mBAAAA;QATd,MAAMC,KAAKN;QACX,MAAMgB,cAAcf;QACpB,IAAI,CAACe,aAAa,OAAO,EAAE;QAE3B,MAAMT,cAAqC,EAAE;QAE7C,MAAMU,cAAc,oBAAoBC,IAAI,CAACd,OAAOe,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAACd,OAAOe,QAAQ;QAEzE,MAAME,SAAQhB,mBAAAA,KAAKiB,UAAU,sBAAfjB,oBAAAA,gBAAiB,CAAC,EAAE,qBAApBA,kBAAsBkB,IAAI;QACxC,IAAIF,SAASf,GAAGkB,sBAAsB,CAACH,QAAQ;YAC7C,KAAK,MAAMI,QAAQ,AAACJ,MAAwCK,QAAQ,CAAE;oBAExCC,8BAAAA;gBAD5B,MAAMA,OAAOX,YAAYY,iBAAiB,CAACH;gBAC3C,MAAMI,uBAAsBF,eAAAA,KAAKG,MAAM,sBAAXH,+BAAAA,aAAaI,eAAe,uBAA5BJ,4BAAgC,CAAC,EAAE;gBAC/D,MAAMK,WAAW,AAACP,CAAAA,KAAKQ,YAAY,IAAIR,KAAKF,IAAI,AAAD,EAAGW,OAAO;gBAEzD,IAAIL,qBAAqB;oBACvB,IACE,2CAA2C;oBAC3CvB,GAAG6B,+BAA+B,CAACN,wBACnCvB,GAAG8B,kBAAkB,CAACP,sBACtB;wBACA,6EAA6E;wBAC7E,oBAAoB;wBACpB,iDAAiD;wBACjD,IAAI,CAAEZ,CAAAA,eAAeG,iBAAgB,KAAMY,aAAa,SAAS;4BAC/DzB,YAAYO,IAAI,CAAC;gCACfuB,MAAMjC;gCACNkC,UAAUhC,GAAGiC,kBAAkB,CAACC,OAAO;gCACvCC,MAAM1C,eAAe2C,yBAAyB;gCAC9CC,aAAa,CAAC,2EAA2E,EAAEX,SAAS,aAAa,CAAC;gCAClHY,OAAOnB,KAAKoB,QAAQ;gCACpBC,QAAQrB,KAAKsB,QAAQ;4BACvB;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOxC;IACT;AACF;AAEA,eAAeL,eAAc"}