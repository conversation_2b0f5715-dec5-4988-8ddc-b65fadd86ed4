#!/usr/bin/env node

/**
 * cPanel Deployment Script for Next.js Static Export
 * This script prepares your Next.js app for cPanel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting cPanel deployment preparation...\n');

// Configuration
const config = {
    buildDir: './.next',
    deployDir: './cpanel-ready',
    requiredFiles: [
        'server.js',
        'package.json',
        'next.config.js'
    ],
    requiredDirs: [
        'app',
        'components',
        'context',
        'lib',
        'public'
    ]
};

// Step 1: Clean previous deployment
console.log('📁 Cleaning previous deployment...');
if (fs.existsSync(config.deployDir)) {
    fs.rmSync(config.deployDir, { recursive: true, force: true });
}
fs.mkdirSync(config.deployDir, { recursive: true });

// Step 2: Build Next.js app
console.log('🔨 Building Next.js application...');
try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completed successfully\n');
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}

// Step 3: Check if build directory exists
if (!fs.existsSync(config.buildDir)) {
    console.error(`❌ Build directory ${config.buildDir} not found!`);
    console.log('Run "npm run build" first to create the build');
    process.exit(1);
}

// Step 4: Copy Next.js build files
console.log('📋 Copying Next.js build files...');
copyDirectory(config.buildDir, path.join(config.deployDir, '.next'));

// Step 5: Copy required directories
console.log('📋 Copying application directories...');
config.requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        copyDirectory(dir, path.join(config.deployDir, dir));
        console.log(`✅ Copied ${dir}/`);
    } else {
        console.warn(`⚠️  Warning: ${dir}/ directory not found`);
    }
});

// Step 6: Copy required server files
console.log('📋 Copying server files...');
config.requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(config.deployDir, file));
        console.log(`✅ Copied ${file}`);
    } else {
        console.warn(`⚠️  Warning: ${file} not found`);
    }
});

// Step 7: Create production package.json
console.log('📦 Creating production package.json...');
createProductionPackageJson();

// Step 8: Create deployment instructions
console.log('📝 Creating deployment instructions...');
createDeploymentInstructions();

console.log('\n🎉 Dynamic Next.js deployment ready!');
console.log(`📁 Files ready in: ${config.deployDir}`);
console.log('🔄 Your app will sync with WooCommerce in real-time');
console.log('📖 Check DEPLOYMENT_INSTRUCTIONS.txt for next steps');

// Helper functions
function copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

function createProductionPackageJson() {
    const packageJsonPath = path.join(config.deployDir, 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Remove devDependencies for production
        delete packageJson.devDependencies;
        
        // Ensure production scripts for Next.js
        packageJson.scripts = {
            start: 'node server.js',
            build: 'next build',
            ...packageJson.scripts
        };
        
        // Set production environment
        packageJson.engines = packageJson.engines || {};
        if (!packageJson.engines.node) {
            packageJson.engines.node = '>=18.0.0';
        }
        
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        console.log('✅ Production package.json created');
    }
}

function createDeploymentInstructions() {
    const instructions = `
CPANEL DEPLOYMENT INSTRUCTIONS - DYNAMIC NEXT.JS APP
===================================================

🔄 REAL-TIME WOOCOMMERCE SYNC ENABLED
Your app will fetch fresh product data from WooCommerce on every page load!

1. CREATE NODE.JS APP IN CPANEL:
   - Go to cPanel → Node.js Apps
   - Click "Create Application"
   - Node.js Version: 18.x (or latest supported)
   - Application Root: /public_html/your-app-name
   - Application URL: your-domain.com/your-app-name
   - Startup File: server.js

2. UPLOAD FILES:
   - Upload ALL files from the 'cpanel-ready' folder
   - Make sure to upload to the Application Root directory
   - Ensure .next folder is uploaded (contains Next.js build)

3. INSTALL DEPENDENCIES:
   - In cPanel Node.js Apps, click on your app
   - Go to "Package.json" tab
   - Click "Run NPM Install"
   - This may take a few minutes for Next.js dependencies

4. SET ENVIRONMENT VARIABLES:
   - NODE_ENV: production
   - (Don't set PORT - cPanel handles this automatically)

5. START APPLICATION:
   - Click "Start App" in cPanel Node.js Apps
   - Wait for "Application is running" status
   - Check the logs for any errors

6. TEST YOUR DYNAMIC SYNC:
   - Visit your application URL
   - Add a new product to your WooCommerce store
   - Refresh your website - new product should appear!
   - Test product pages and categories

DYNAMIC FEATURES:
✅ Real-time product sync from WooCommerce
✅ Live inventory updates
✅ Dynamic pricing
✅ New products appear automatically
✅ No need to rebuild when adding products

TROUBLESHOOTING:
- If app fails to start, check Node.js version (18.x recommended)
- Ensure all directories (.next, app, components, etc.) are uploaded
- Check cPanel logs for specific error messages
- Verify WooCommerce API credentials in your code

For detailed troubleshooting, see CPANEL_DEPLOYMENT_GUIDE.md
`;
    
    fs.writeFileSync(path.join(config.deployDir, 'DEPLOYMENT_INSTRUCTIONS.txt'), instructions);
    console.log('✅ Deployment instructions created');
}
