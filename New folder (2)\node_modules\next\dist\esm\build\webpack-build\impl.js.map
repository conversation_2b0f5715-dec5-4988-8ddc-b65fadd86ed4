{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["red", "formatWebpackMessages", "nonNullable", "COMPILER_NAMES", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "PHASE_PRODUCTION_BUILD", "runCompiler", "Log", "getBaseWebpackConfig", "loadProjectInfo", "TelemetryPlugin", "NextBuildContext", "resumePluginState", "getPluginState", "createEntrypoints", "loadConfig", "getTraceEvents", "initializeTraceState", "setGlobal", "trace", "WEBPACK_LAYERS", "TraceEntryPointsPlugin", "origDebug", "Telemetry", "debug", "isTelemetryPlugin", "plugin", "isTraceEntryPointsPlugin", "webpackBuildImpl", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "dev", "Promise", "all", "middlewareMatchers", "compilerType", "client", "server", "edgeServer", "edgePreviewProps", "__NEXT_PREVIEW_MODE_ID", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "warn", "process", "hrtime", "inputFileSystem", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "pluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "import", "layer", "appPagesBrowser", "dependOn", "purge", "filter", "traceFn", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "event", "duration", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "worker<PERSON>ain", "workerData", "telemetry", "distDir", "buildContext", "Object", "assign", "traceState", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents"], "mappings": "AACA,SAASA,GAAG,QAAQ,uBAAsB;AAC1C,OAAOC,2BAA2B,qFAAoF;AACtH,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,SACEC,cAAc,EACdC,oCAAoC,EACpCC,oBAAoB,EACpBC,sBAAsB,QACjB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,cAAa;AACzC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,wBAAwBC,eAAe,QAAQ,oBAAmB;AAEzE,SACEC,eAAe,QAEV,sCAAqC;AAC5C,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,QACT,mBAAkB;AACzB,SAASC,iBAAiB,QAAQ,aAAY;AAC9C,OAAOC,gBAAgB,sBAAqB;AAC5C,SACEC,cAAc,EACdC,oBAAoB,EACpBC,SAAS,EACTC,KAAK,QAGA,cAAa;AACpB,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,sBAAsB,QAAQ,mDAAkD;AAIzF,OAAOC,eAAe,2BAA0B;AAChD,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,MAAMC,QAAQF,UAAU;AAcxB,SAASG,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBhB;AAC3B;AAEA,SAASiB,yBACPD,MAAe;IAEf,OAAOA,kBAAkBL;AAC3B;AAEA,OAAO,eAAeO,iBACpBC,YAAkD;QAqN1B,uBAIO;IAlN/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBxB,iBAAiBwB,aAAa;IACpD,MAAMC,MAAMzB,iBAAiByB,GAAG;IAChC,MAAMC,SAAS1B,iBAAiB0B,MAAM;IAEtC,MAAMC,iBAAiBH,cAAcI,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAML,cACvBI,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZ3B,kBAAkB;YAChB4B,SAAS/B,iBAAiB+B,OAAO;YACjCL,QAAQA;YACRM,UAAUhC,iBAAiBiC,cAAc;YACzCC,OAAO;YACPC,SAASV;YACTW,gBAAgBV,OAAOU,cAAc;YACrCC,UAAUrC,iBAAiBqC,QAAQ;YACnCC,QAAQtC,iBAAiBsC,MAAM;YAC/BC,OAAOvC,iBAAiBwC,WAAW;YACnCC,UAAUzC,iBAAiB0C,cAAc;YACzCC,aAAa3C,iBAAiB4C,YAAY;YAC1CC,WAAW7C,iBAAiB8C,eAAe;YAC3CC,wBAAwB/C,iBAAiB+C,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAAS/B,iBAAiB+B,OAAO;QACjCmB,eAAelD,iBAAiBkD,aAAa;QAC7CxB,QAAQA;QACRY,QAAQtC,iBAAiBsC,MAAM;QAC/BD,UAAUrC,iBAAiBqC,QAAQ;QACnCc,UAAUnD,iBAAiBmD,QAAQ;QACnCC,kBAAkBpD,iBAAiBoD,gBAAgB;QACnDC,mBAAmBrD,iBAAiBqD,iBAAiB;QACrDC,0BAA0BtD,iBAAiBsD,wBAAwB;QACnEC,YAAYvD,iBAAiBuD,UAAU;QACvCC,qBAAqBxD,iBAAiBwD,mBAAmB;QACzDC,eAAezD,iBAAiByD,aAAa;QAC7CC,6BAA6B1D,iBAAiB0D,2BAA2B;QACzEC,qBAAqB3D,iBAAiB2D,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMjC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAM+B,OAAO,MAAM/D,gBAAgB;YACjC2B;YACAC,QAAQsB,qBAAqBtB,MAAM;YACnCoC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBnE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBiB,oBAAoBpC,YAAYoC,kBAAkB;gBAClDtC;gBACAuC,cAAc3E,eAAe4E,MAAM;gBACnCtC,aAAaA,YAAYsC,MAAM;gBAC/B,GAAGN,IAAI;YACT;YACAhE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAsC,oBAAoBpC,YAAYoC,kBAAkB;gBAClDC,cAAc3E,eAAe6E,MAAM;gBACnCvC,aAAaA,YAAYuC,MAAM;gBAC/B,GAAGP,IAAI;YACT;YACAhE,qBAAqB4B,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAsC,oBAAoBpC,YAAYoC,kBAAkB;gBAClDC,cAAc3E,eAAe8E,UAAU;gBACvCxC,aAAaA,YAAYwC,UAAU;gBACnCC,kBAAkB;oBAChBC,wBACEvE,iBAAiB4C,YAAY,CAAEa,aAAa;oBAC9Ce,oCACExE,iBAAiB4C,YAAY,CAAE6B,wBAAwB;oBACzDC,iCACE1E,iBAAiB4C,YAAY,CAAE+B,qBAAqB;gBACxD;gBACA,GAAGd,IAAI;YACT;SACD;IACH;IAEF,MAAMe,eAAehB,OAAO,CAAC,EAAE;IAC/B,MAAMiB,eAAejB,OAAO,CAAC,EAAE;IAC/B,MAAMkB,aAAalB,OAAO,CAAC,EAAE;IAE7B,IACEgB,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAtF,IAAIuF,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEA5D,oBAAoB6D,QAAQC,MAAM;IAElCxE,MAAM,CAAC,iBAAiB,CAAC,EAAEK;IAC3B,+EAA+E;IAC/E,MAAMS,eAAeG,YAAY,CAAC;YAsEhCwD;QArEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIH;QAEJ,IAAI,CAACpE,gBAAgBA,iBAAiB,UAAU;YAC9CL,MAAM;YACN,MAAM6E,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcF,gBAAgB,GAAG,MAAM3F,YAAYkF,cAAc;gBACjElD;gBACA2D;YACF;YACAzE,MAAM,CAAC,yBAAyB,EAAE8E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACxE,gBAAgBA,iBAAiB,eAAe;YACnDL,MAAM;YACN,MAAM6E,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBH,gBAAgB,GAAGR,aACnC,MAAMnF,YAAYmF,YAAY;gBAAEnD;gBAAgB2D;YAAgB,KAChE;gBAAC;aAAK;YACVzE,MAAM,CAAC,8BAA8B,EAAE8E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAcnE,MAAM,CAAC6D,MAAM,KAAI,EAACO,oCAAAA,iBAAkBpE,MAAM,CAAC6D,MAAM,GAAE;YACpE,MAAMW,cAAc3F;YACpB,IAAK,MAAM4F,OAAOD,YAAYE,qBAAqB,CAAE;gBACnD,MAAMC,QAAQH,YAAYE,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcrB,aAAasB,KAAK;gBACtC,IAAIJ,QAAQrG,sBAAsB;oBAChCwG,WAAW,CAACzG,qCAAqC,GAAG;wBAClD2G,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFF,WAAW,CAACzG,qCAAqC,CAAC2G,MAAM;4BAC3DH;yBACD;wBACDI,OAAO3F,eAAe4F,eAAe;oBACvC;gBACF,OAAO;oBACLJ,WAAW,CAACH,IAAI,GAAG;wBACjBQ,UAAU;4BAAC9G;yBAAqC;wBAChD2G,QAAQH;wBACRI,OAAO3F,eAAe4F,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAACnF,gBAAgBA,iBAAiB,UAAU;gBAC9CL,MAAM;gBACN,MAAM6E,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcD,gBAAgB,GAAG,MAAM3F,YAAYiF,cAAc;oBACjEjD;oBACA2D;gBACF;gBACAzE,MAAM,CAAC,yBAAyB,EAAE8E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAJ,oCAAAA,yBAAAA,gBAAiBiB,KAAK,qBAAtBjB,4BAAAA;QAEAnE,SAAS;YACPC,UAAU;mBACJmE,CAAAA,gCAAAA,aAAcnE,QAAQ,KAAI,EAAE;mBAC5BoE,CAAAA,gCAAAA,aAAcpE,QAAQ,KAAI,EAAE;mBAC5BqE,CAAAA,oCAAAA,iBAAkBrE,QAAQ,KAAI,EAAE;aACrC,CAACoF,MAAM,CAAClH;YACT+B,QAAQ;mBACFkE,CAAAA,gCAAAA,aAAclE,MAAM,KAAI,EAAE;mBAC1BmE,CAAAA,gCAAAA,aAAcnE,MAAM,KAAI,EAAE;mBAC1BoE,CAAAA,oCAAAA,iBAAkBpE,MAAM,KAAI,EAAE;aACnC,CAACmF,MAAM,CAAClH;YACTgC,OAAO;gBACLiE,gCAAAA,aAAcjE,KAAK;gBACnBkE,gCAAAA,aAAclE,KAAK;gBACnBmE,oCAAAA,iBAAkBnE,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNI,UAAU,CAAC,2BACX6E,OAAO,CAAC,IAAMpH,sBAAsB8B,QAAQ;IAE/C,MAAMuF,mBAAkB,wBAAA,AAAC9B,aAAuC+B,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3E9F;IAGF,MAAM+F,0BAAyB,wBAAA,AAC7BhC,aACA8B,OAAO,qBAFsB,sBAEpBC,IAAI,CAAC5F;IAEhB,MAAM8F,kBAAkB1B,QAAQC,MAAM,CAAC9D;IAEvC,IAAIJ,OAAOE,MAAM,CAAC6D,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAI/D,OAAOE,MAAM,CAAC6D,MAAM,GAAG,GAAG;YAC5B/D,OAAOE,MAAM,CAAC6D,MAAM,GAAG;QACzB;QACA,IAAI6B,QAAQ5F,OAAOE,MAAM,CAACmF,MAAM,CAACQ,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAAC3H,IAAI;QAElB,IACE2H,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACP;YACpC,MAAMQ,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAL,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAIvG,OAAOC,QAAQ,CAAC8D,MAAM,GAAG,GAAG;YAC9BtF,IAAIuF,IAAI,CAAC;YACT+B,QAAQ/B,IAAI,CAAChE,OAAOC,QAAQ,CAACoF,MAAM,CAACQ,SAASC,IAAI,CAAC;YAClDC,QAAQ/B,IAAI;QACd,OAAO,IAAI,CAACjE,cAAc;YACxBtB,IAAIgI,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUf,eAAe,CAAC,EAAE;YAC5BgB,iBAAiB,EAAEjB,0CAAAA,uBAAwBiB,iBAAiB;YAC5DjC,aAAa3F;YACb6H,gBAAgB;gBACdC,QAAQtB,CAAAA,mCAAAA,gBAAiBsB,MAAM,OAAM,EAAE;gBACvCC,+BACEvB,CAAAA,mCAAAA,gBAAiBuB,6BAA6B,OAAM,EAAE;YAC1D;QACF;IACF;AACF;AAEA,sDAAsD;AACtD,OAAO,eAAeC,WAAWC,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAIxH,UAAU;QAC9ByH,SAASF,WAAWG,YAAY,CAAC5G,MAAM,CAAE2G,OAAO;IAClD;IACA9H,UAAU,aAAa6H;IACvB,0EAA0E;IAC1EG,OAAOC,MAAM,CAACxI,kBAAkBmI,WAAWG,YAAY;IAEvD,0CAA0C;IAC1ChI,qBAAqB6H,WAAWM,UAAU;IAE1C,sBAAsB;IACtBxI,kBAAkBD,iBAAiB6F,WAAW;IAE9C,iDAAiD;IACjD7F,iBAAiB0B,MAAM,GAAG,MAAMtB,WAC9BV,wBACAM,iBAAiByB,GAAG;IAEtBzB,iBAAiBwB,aAAa,GAAGhB,MAC/B,CAAC,YAAY,EAAE2H,WAAWjH,YAAY,CAAC,CAAC;IAG1C,MAAMC,SAAS,MAAMF,iBAAiBkH,WAAWjH,YAAY;IAC7D,MAAM,EAAEwH,YAAY,EAAEC,WAAW,EAAE,GAAGxH,OAAO2G,iBAAiB,IAAI,CAAC;IACnE,IAAIY,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACf1H,OAAO2G,iBAAiB,CAAEY,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrBzH,OAAO2G,iBAAiB,CAAEY,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvD5H,OAAO2G,iBAAiB,CAAEa,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA/I,iBAAiBwB,aAAa,CAACwH,IAAI;IACnC,OAAO;QAAE,GAAG7H,MAAM;QAAE8H,kBAAkB5I;IAAiB;AACzD"}