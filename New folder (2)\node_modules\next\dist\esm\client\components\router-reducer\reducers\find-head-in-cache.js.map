{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts"], "names": ["createRouterCache<PERSON>ey", "findHeadInCache", "cache", "parallelRoutes", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "Object", "keys", "length", "key", "segment", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "childSegmentMap", "get", "cache<PERSON>ey", "cacheNode", "item"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,6BAA4B;AAEjE,OAAO,SAASC,gBACdC,KAAgB,EAChBC,cAAoC;IAEpC,OAAOC,oBAAoBF,OAAOC,gBAAgB;AACpD;AAEA,SAASC,oBACPF,KAAgB,EAChBC,cAAoC,EACpCE,SAAiB;IAEjB,MAAMC,aAAaC,OAAOC,IAAI,CAACL,gBAAgBM,MAAM,KAAK;IAC1D,IAAIH,YAAY;QACd,0EAA0E;QAC1E,OAAO;YAACJ;YAAOG;SAAU;IAC3B;IACA,IAAK,MAAMK,OAAOP,eAAgB;QAChC,MAAM,CAACQ,SAASC,oBAAoB,GAAGT,cAAc,CAACO,IAAI;QAC1D,MAAMG,kBAAkBX,MAAMC,cAAc,CAACW,GAAG,CAACJ;QACjD,IAAI,CAACG,iBAAiB;YACpB;QACF;QAEA,MAAME,WAAWf,qBAAqBW;QAEtC,MAAMK,YAAYH,gBAAgBC,GAAG,CAACC;QACtC,IAAI,CAACC,WAAW;YACd;QACF;QAEA,MAAMC,OAAOb,oBACXY,WACAJ,qBACAP,YAAY,MAAMU;QAEpB,IAAIE,MAAM;YACR,OAAOA;QACT;IACF;IAEA,OAAO;AACT"}