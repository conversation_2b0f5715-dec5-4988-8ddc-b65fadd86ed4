{"version": 3, "sources": ["../../../src/lib/eslint/hasEslintConfiguration.ts"], "names": ["promises", "fs", "hasEslintConfiguration", "eslintrcFile", "packageJsonConfig", "configObject", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "content", "readFile", "encoding", "then", "txt", "trim", "replace", "eslintConfig", "Object", "keys", "length"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AASnC,OAAO,eAAeC,uBACpBC,YAA2B,EAC3BC,iBAA+C;IAE/C,MAAMC,eAAe;QACnBC,QAAQ;QACRC,eAAe;QACfC,oBAAoB;IACtB;IAEA,IAAIL,cAAc;QAChB,MAAMM,UAAU,MAAMR,GAAGS,QAAQ,CAACP,cAAc;YAAEQ,UAAU;QAAO,GAAGC,IAAI,CACxE,CAACC,MAAQA,IAAIC,IAAI,GAAGC,OAAO,CAAC,OAAO,KACnC,IAAM;QAGR,IACEN,YAAY,MACZA,YAAY,QACZA,YAAY,SACZA,YAAY,uBACZ;YACAJ,aAAaE,aAAa,GAAG;QAC/B,OAAO;YACLF,aAAaC,MAAM,GAAG;QACxB;IACF,OAAO,IAAIF,qCAAAA,kBAAmBY,YAAY,EAAE;QAC1C,IAAIC,OAAOC,IAAI,CAACd,kBAAkBY,YAAY,EAAEG,MAAM,EAAE;YACtDd,aAAaC,MAAM,GAAG;QACxB,OAAO;YACLD,aAAaG,kBAAkB,GAAG;QACpC;IACF;IACA,OAAOH;AACT"}