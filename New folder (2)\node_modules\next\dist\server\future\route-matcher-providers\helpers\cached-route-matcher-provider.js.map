{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/helpers/cached-route-matcher-provider.ts"], "names": ["CachedRouteMatcherProvider", "constructor", "loader", "cached", "matchers", "data", "load", "compare", "transform"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;AAAf,MAAeA;IAQpBC,YAAY,AAAiBC,MAA2B,CAAE;aAA7BA,SAAAA;aAFrBC,SAA2B,EAAE;IAEsB;IAI3D,MAAaC,WAAkC;QAC7C,MAAMC,OAAO,MAAM,IAAI,CAACH,MAAM,CAACI,IAAI;QACnC,IAAI,CAACD,MAAM,OAAO,EAAE;QAEpB,0DAA0D;QAC1D,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACH,MAAM,CAACK,OAAO,CAAC,IAAI,CAACF,IAAI,EAAEA,OAAO,OAAO,IAAI,CAACF,MAAM;QACzE,IAAI,CAACE,IAAI,GAAGA;QAEZ,wCAAwC;QACxC,MAAMD,WAAW,MAAM,IAAI,CAACI,SAAS,CAACH;QAEtC,sBAAsB;QACtB,IAAI,CAACF,MAAM,GAAGC;QAEd,OAAOA;IACT;AACF"}