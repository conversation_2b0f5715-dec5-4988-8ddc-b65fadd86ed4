/*
 React
 react.react-server.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var m=Object.assign,n={current:null};function p(){return new Map}
if("function"===typeof fetch){var q=fetch,r=function(a,b){var d=n.current;if(!d||b&&b.signal&&b.signal!==d.getCacheSignal())return q(a,b);if("string"!==typeof a||b){var c="string"===typeof a||a instanceof URL?new Request(a,b):a;if("GET"!==c.method&&"HEAD"!==c.method||c.keepalive)return q(a,b);var e=JSON.stringify([c.method,Array.from(c.headers.entries()),c.mode,c.redirect,c.credentials,c.referrer,c.referrerPolicy,c.integrity]);c=c.url}else e='["GET",[],null,"follow",null,null,null,null]',c=a;var f=
d.getCacheForType(p);d=f.get(c);if(void 0===d)a=q(a,b),f.set(c,[e,a]);else{c=0;for(f=d.length;c<f;c+=2){var g=d[c+1];if(d[c]===e)return a=g,a.then(function(k){return k.clone()})}a=q(a,b);d.push(e,a)}return a.then(function(k){return k.clone()})};m(r,q);try{fetch=r}catch(a){try{globalThis.fetch=r}catch(b){console.warn("React was unable to patch the fetch() function in this environment. Suspensey APIs might not work correctly as a result.")}}}
var t={current:null},u={ReactCurrentDispatcher:t,ReactCurrentOwner:{current:null}},v={ReactCurrentCache:n};function w(a){var b="https://react.dev/errors/"+a;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var d=2;d<arguments.length;d++)b+="&args[]="+encodeURIComponent(arguments[d])}return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
var x=Array.isArray,y=Symbol.for("react.element"),z=Symbol.for("react.portal"),A=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),D=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),F=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),H=Symbol.iterator;function I(a){if(null===a||"object"!==typeof a)return null;a=H&&a[H]||a["@@iterator"];return"function"===typeof a?a:null}var J=Object.prototype.hasOwnProperty,K=u.ReactCurrentOwner;
function L(a,b){return{$$typeof:y,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function M(a){return"object"===typeof a&&null!==a&&a.$$typeof===y}function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(d){return b[d]})}var N=/\/+/g;function O(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}function P(){}
function Q(a){switch(a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;default:switch("string"===typeof a.status?a.then(P,P):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;}}throw a;}
function R(a,b,d,c,e){var f=typeof a;if("undefined"===f||"boolean"===f)a=null;var g=!1;if(null===a)g=!0;else switch(f){case "string":case "number":g=!0;break;case "object":switch(a.$$typeof){case y:case z:g=!0;break;case G:return g=a._init,R(g(a._payload),b,d,c,e)}}if(g)return e=e(a),g=""===c?"."+O(a,0):c,x(e)?(d="",null!=g&&(d=g.replace(N,"$&/")+"/"),R(e,b,d,"",function(l){return l})):null!=e&&(M(e)&&(e=L(e,d+(!e.key||a&&a.key===e.key?"":(""+e.key).replace(N,"$&/")+"/")+g)),b.push(e)),1;g=0;var k=
""===c?".":c+":";if(x(a))for(var h=0;h<a.length;h++)c=a[h],f=k+O(c,h),g+=R(c,b,d,f,e);else if(h=I(a),"function"===typeof h)for(a=h.call(a),h=0;!(c=a.next()).done;)c=c.value,f=k+O(c,h++),g+=R(c,b,d,f,e);else if("object"===f){if("function"===typeof a.then)return R(Q(a),b,d,c,e);b=String(a);throw Error(w(31,"[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b));}return g}
function S(a,b,d){if(null==a)return a;var c=[],e=0;R(a,c,"","",function(f){return b.call(d,f,e++)});return c}function T(a){if(-1===a._status){var b=a._result;b=b();b.then(function(d){if(0===a._status||-1===a._status)a._status=1,a._result=d},function(d){if(0===a._status||-1===a._status)a._status=2,a._result=d});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}function U(){return new WeakMap}function V(){return{s:0,v:void 0,o:null,p:null}}var W={transition:null};
function X(){}var Y="function"===typeof reportError?reportError:function(a){console.error(a)};exports.Children={map:S,forEach:function(a,b,d){S(a,function(){b.apply(this,arguments)},d)},count:function(a){var b=0;S(a,function(){b++});return b},toArray:function(a){return S(a,function(b){return b})||[]},only:function(a){if(!M(a))throw Error(w(143));return a}};exports.Fragment=A;exports.Profiler=C;exports.StrictMode=B;exports.Suspense=E;exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=u;
exports.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=v;
exports.cache=function(a){return function(){var b=n.current;if(!b)return a.apply(null,arguments);var d=b.getCacheForType(U);b=d.get(a);void 0===b&&(b=V(),d.set(a,b));d=0;for(var c=arguments.length;d<c;d++){var e=arguments[d];if("function"===typeof e||"object"===typeof e&&null!==e){var f=b.o;null===f&&(b.o=f=new WeakMap);b=f.get(e);void 0===b&&(b=V(),f.set(e,b))}else f=b.p,null===f&&(b.p=f=new Map),b=f.get(e),void 0===b&&(b=V(),f.set(e,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var g=a.apply(null,
arguments);d=b;d.s=1;return d.v=g}catch(k){throw g=b,g.s=2,g.v=k,k;}}};
exports.cloneElement=function(a,b,d){if(null===a||void 0===a)throw Error(w(267,a));var c=m({},a.props),e=a.key,f=a.ref,g=a._owner;if(null!=b){void 0!==b.ref&&(f=b.ref,g=K.current);void 0!==b.key&&(e=""+b.key);if(a.type&&a.type.defaultProps)var k=a.type.defaultProps;for(h in b)J.call(b,h)&&"key"!==h&&"ref"!==h&&"__self"!==h&&"__source"!==h&&(c[h]=void 0===b[h]&&void 0!==k?k[h]:b[h])}var h=arguments.length-2;if(1===h)c.children=d;else if(1<h){k=Array(h);for(var l=0;l<h;l++)k[l]=arguments[l+2];c.children=
k}return{$$typeof:y,type:a.type,key:e,ref:f,props:c,_owner:g}};
exports.createElement=function(a,b,d){var c,e={},f=null,g=null;if(null!=b)for(c in void 0!==b.ref&&(g=b.ref),void 0!==b.key&&(f=""+b.key),b)J.call(b,c)&&"key"!==c&&"ref"!==c&&"__self"!==c&&"__source"!==c&&(e[c]=b[c]);var k=arguments.length-2;if(1===k)e.children=d;else if(1<k){for(var h=Array(k),l=0;l<k;l++)h[l]=arguments[l+2];e.children=h}if(a&&a.defaultProps)for(c in k=a.defaultProps,k)void 0===e[c]&&(e[c]=k[c]);return{$$typeof:y,type:a,key:f,ref:g,props:e,_owner:K.current}};exports.createRef=function(){return{current:null}};
exports.forwardRef=function(a){return{$$typeof:D,render:a}};exports.isValidElement=M;exports.lazy=function(a){return{$$typeof:G,_payload:{_status:-1,_result:a},_init:T}};exports.memo=function(a,b){return{$$typeof:F,type:a,compare:void 0===b?null:b}};
exports.startTransition=function(a){var b=W.transition,d=new Set;W.transition={_callbacks:d};var c=W.transition;try{var e=a();"object"===typeof e&&null!==e&&"function"===typeof e.then&&(d.forEach(function(f){return f(c,e)}),e.then(X,Y))}catch(f){Y(f)}finally{W.transition=b}};exports.use=function(a){return t.current.use(a)};exports.useCallback=function(a,b){return t.current.useCallback(a,b)};exports.useDebugValue=function(){};exports.useId=function(){return t.current.useId()};
exports.useMemo=function(a,b){return t.current.useMemo(a,b)};exports.version="18.3.0-canary-178c267a4e-20241218";

//# sourceMappingURL=react.react-server.production.min.js.map
