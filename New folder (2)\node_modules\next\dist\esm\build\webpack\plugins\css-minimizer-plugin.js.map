{"version": 3, "sources": ["../../../../src/build/webpack/plugins/css-minimizer-plugin.ts"], "names": ["cssnanoSimple", "postcssScss", "postcss", "webpack", "sources", "spans", "CSS_REGEX", "CssMinimizerPlugin", "constructor", "options", "__next_css_remove", "optimizeAsset", "file", "asset", "postcssOptions", "to", "from", "parser", "input", "map", "sourceAndMap", "source", "prev", "process", "then", "res", "SourceMapSource", "css", "toJSON", "RawSource", "apply", "compiler", "hooks", "compilation", "tap", "cache", "getCache", "processAssets", "tapPromise", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "assets", "compilationSpan", "get", "cssMinimizerSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "files", "Object", "keys", "Promise", "all", "filter", "test", "assetSpan", "setAttribute", "etag", "getLazyHashedEtag", "cachedResult", "getPromise", "result", "storePromise"], "mappings": "AAAA,OAAOA,mBAAmB,oCAAmC;AAC7D,OAAOC,iBAAiB,kCAAiC;AACzD,OAAOC,aAAa,UAAS;AAE7B,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SAASC,KAAK,QAAQ,qBAAoB;AAE1C,4HAA4H;AAC5H,MAAMC,YAAY;AAQlB,OAAO,MAAMC;IAKXC,YAAYC,OAAkC,CAAE;aAJhDC,oBAAoB;QAKlB,IAAI,CAACD,OAAO,GAAGA;IACjB;IAEAE,cAAcC,IAAY,EAAEC,KAAU,EAAE;QACtC,MAAMC,iBAAiB;YACrB,GAAG,IAAI,CAACL,OAAO,CAACK,cAAc;YAC9BC,IAAIH;YACJI,MAAMJ;YAEN,yEAAyE;YACzE,8CAA8C;YAC9C,8FAA8F;YAC9FK,QAAQhB;QACV;QAEA,IAAIiB;QACJ,IAAIJ,eAAeK,GAAG,IAAIN,MAAMO,YAAY,EAAE;YAC5C,MAAM,EAAEC,MAAM,EAAEF,GAAG,EAAE,GAAGN,MAAMO,YAAY;YAC1CF,QAAQG;YACRP,eAAeK,GAAG,CAACG,IAAI,GAAGH,MAAMA,MAAM;QACxC,OAAO;YACLD,QAAQL,MAAMQ,MAAM;QACtB;QAEA,OAAOnB,QAAQ;YAACF,cAAc,CAAC,GAAGE;SAAS,EACxCqB,OAAO,CAACL,OAAOJ,gBACfU,IAAI,CAAC,CAACC;YACL,IAAIA,IAAIN,GAAG,EAAE;gBACX,OAAO,IAAIf,QAAQsB,eAAe,CAACD,IAAIE,GAAG,EAAEf,MAAMa,IAAIN,GAAG,CAACS,MAAM;YAClE,OAAO;gBACL,OAAO,IAAIxB,QAAQyB,SAAS,CAACJ,IAAIE,GAAG;YACtC;QACF;IACJ;IAEAG,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,sBAAsB,CAACD;YACpD,MAAME,QAAQF,YAAYG,QAAQ,CAAC;YACnCH,YAAYD,KAAK,CAACK,aAAa,CAACC,UAAU,CACxC;gBACEC,MAAM;gBACNC,OAAOrC,QAAQsC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOC;gBACL,MAAMC,kBAAkBvC,MAAMwC,GAAG,CAACZ,gBAAgB5B,MAAMwC,GAAG,CAACd;gBAC5D,MAAMe,mBAAmBF,gBAAiBG,UAAU,CAClD;gBAGF,OAAOD,iBAAiBE,YAAY,CAAC;oBACnC,MAAMC,QAAQC,OAAOC,IAAI,CAACR;oBAC1B,MAAMS,QAAQC,GAAG,CACfJ,MACGK,MAAM,CAAC,CAAC1C,OAASN,UAAUiD,IAAI,CAAC3C,OAChCO,GAAG,CAAC,OAAOP;wBACV,MAAM4C,YAAYV,iBAAiBC,UAAU,CAAC;wBAC9CS,UAAUC,YAAY,CAAC,QAAQ7C;wBAE/B,OAAO4C,UAAUR,YAAY,CAAC;4BAC5B,MAAMnC,QAAQ8B,MAAM,CAAC/B,KAAK;4BAE1B,MAAM8C,OAAOvB,MAAMwB,iBAAiB,CAAC9C;4BAErC,MAAM+C,eAAe,MAAMzB,MAAM0B,UAAU,CAACjD,MAAM8C;4BAElDF,UAAUC,YAAY,CACpB,SACAG,eAAe,QAAQ;4BAEzB,IAAIA,cAAc;gCAChBjB,MAAM,CAAC/B,KAAK,GAAGgD;gCACf;4BACF;4BAEA,MAAME,SAAS,MAAM,IAAI,CAACnD,aAAa,CAACC,MAAMC;4BAC9C,MAAMsB,MAAM4B,YAAY,CAACnD,MAAM8C,MAAMI;4BACrCnB,MAAM,CAAC/B,KAAK,GAAGkD;wBACjB;oBACF;gBAEN;YACF;QAEJ;IACF;AACF"}