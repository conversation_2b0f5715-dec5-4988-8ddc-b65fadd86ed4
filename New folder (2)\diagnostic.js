#!/usr/bin/env node

/**
 * Diagnostic script for Deal4u deployment
 * Checks for common issues and provides detailed information
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('🔍 Deal4u Deployment Diagnostic Tool');
console.log('=====================================\n');

// Check Node.js version
console.log('📋 System Information:');
console.log(`   Node.js version: ${process.version}`);
console.log(`   Platform: ${process.platform}`);
console.log(`   Architecture: ${process.arch}`);
console.log(`   Working directory: ${process.cwd()}\n`);

// Check required files
console.log('📁 File System Check:');
const requiredFiles = [
    'package.json',
    'server.js',
    'out/index.html'
];

const requiredDirs = [
    'out'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`   ✅ ${file} - Found`);
        
        // Check file size
        const stats = fs.statSync(filePath);
        console.log(`      Size: ${stats.size} bytes`);
        
        // For HTML files, check content type indicators
        if (file.endsWith('.html')) {
            const content = fs.readFileSync(filePath, 'utf8');
            if (content.includes('charset=utf-8') || content.includes('charset=UTF-8')) {
                console.log(`      ✅ UTF-8 charset found`);
            } else {
                console.log(`      ⚠️  UTF-8 charset not explicitly set`);
            }
        }
    } else {
        console.log(`   ❌ ${file} - Missing`);
        allFilesExist = false;
    }
});

requiredDirs.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (fs.existsSync(dirPath)) {
        console.log(`   ✅ ${dir}/ - Found`);
        
        // List contents
        const contents = fs.readdirSync(dirPath);
        console.log(`      Contents: ${contents.length} items`);
        if (contents.length > 0) {
            console.log(`      Files: ${contents.slice(0, 5).join(', ')}${contents.length > 5 ? '...' : ''}`);
        }
    } else {
        console.log(`   ❌ ${dir}/ - Missing`);
        allFilesExist = false;
    }
});

// Check package.json
console.log('\n📦 Package Configuration:');
try {
    const packagePath = path.join(__dirname, 'package.json');
    if (fs.existsSync(packagePath)) {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        console.log(`   Name: ${packageJson.name}`);
        console.log(`   Version: ${packageJson.version}`);
        console.log(`   Main: ${packageJson.main}`);
        
        if (packageJson.dependencies) {
            console.log(`   Dependencies: ${Object.keys(packageJson.dependencies).length}`);
            Object.entries(packageJson.dependencies).forEach(([name, version]) => {
                console.log(`      ${name}: ${version}`);
            });
        }
        
        if (packageJson.engines) {
            console.log(`   Node.js requirement: ${packageJson.engines.node || 'Not specified'}`);
        }
    }
} catch (error) {
    console.log(`   ❌ Error reading package.json: ${error.message}`);
}

// Memory usage
console.log('\n💾 Memory Usage:');
const memUsage = process.memoryUsage();
console.log(`   RSS: ${Math.round(memUsage.rss / 1024 / 1024)} MB`);
console.log(`   Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`);
console.log(`   Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`);
console.log(`   External: ${Math.round(memUsage.external / 1024 / 1024)} MB`);

// Test server startup (if not already running)
console.log('\n🚀 Server Test:');
if (allFilesExist) {
    console.log('   All required files found. Server should start successfully.');
    console.log('   To test manually, run: node server.js');
    
    // Try to make a quick HTTP request to check if server is already running
    const testPort = process.env.PORT || 3000;
    const req = http.request({
        hostname: 'localhost',
        port: testPort,
        path: '/health',
        method: 'GET',
        timeout: 2000
    }, (res) => {
        console.log(`   ✅ Server is running on port ${testPort}`);
        console.log(`   Status: ${res.statusCode}`);
        console.log(`   Content-Type: ${res.headers['content-type']}`);
        
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            try {
                const healthData = JSON.parse(data);
                console.log(`   Uptime: ${Math.round(healthData.uptime)} seconds`);
            } catch (e) {
                console.log(`   Response: ${data.substring(0, 100)}...`);
            }
        });
    });
    
    req.on('error', (err) => {
        if (err.code === 'ECONNREFUSED') {
            console.log(`   ℹ️  Server not currently running on port ${testPort}`);
        } else {
            console.log(`   ⚠️  Connection error: ${err.message}`);
        }
    });
    
    req.on('timeout', () => {
        console.log(`   ⚠️  Connection timeout`);
        req.destroy();
    });
    
    req.end();
} else {
    console.log('   ❌ Missing required files. Server will not start properly.');
}

// Recommendations
console.log('\n💡 Recommendations:');
if (!allFilesExist) {
    console.log('   1. Ensure all required files are present');
    console.log('   2. Run "npm install" to install dependencies');
    console.log('   3. Make sure the "out" directory contains your built application');
}

console.log('   4. Check cPanel Node.js app configuration:');
console.log('      - Application Root: /public_html/deal4u');
console.log('      - Startup File: server.js');
console.log('      - Node.js Version: 18.x or higher');

console.log('   5. Monitor server logs for detailed error information');
console.log('   6. Use the /health endpoint to check server status');

// Test WooCommerce Product Data Accuracy
console.log('\n🛍️ WooCommerce Product Data Test:');
const axios = require('axios');

// Load environment variables
const dotenv = require('dotenv');
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  Object.assign(process.env, envConfig);
}

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WORDPRESS_URL
    ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/wc/v3`
    : 'https://deal4u.co/wp-json/wc/v3',
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET
};

if (WC_CONFIG.consumerKey && WC_CONFIG.consumerSecret) {
  console.log('   ✅ WooCommerce credentials found');
  console.log(`   API URL: ${WC_CONFIG.baseURL}`);

  // Test product data accuracy
  const testProductData = async () => {
    try {
      const api = axios.create({
        baseURL: WC_CONFIG.baseURL,
        timeout: 10000,
        auth: {
          username: WC_CONFIG.consumerKey,
          password: WC_CONFIG.consumerSecret
        }
      });

      console.log('   🔍 Testing product data accuracy...');
      const response = await api.get('/products', {
        params: { per_page: 3, status: 'publish' }
      });

      if (response.data && response.data.length > 0) {
        console.log(`   ✅ Found ${response.data.length} products`);
        console.log('   📋 Product Data Verification:');

        response.data.forEach((product, index) => {
          console.log(`\n   Product ${index + 1}:`);
          console.log(`      ✅ Name: "${product.name}"`);
          console.log(`      ✅ Price: $${product.price} (Regular: $${product.regular_price})`);
          console.log(`      ✅ Description: ${product.short_description ? 'Present' : 'Missing'}`);
          console.log(`      ✅ Stock: ${product.stock_status}`);
          console.log(`      ✅ Images: ${product.images?.length || 0} image(s)`);

          // Check for sale price
          if (product.sale_price && parseFloat(product.sale_price) < parseFloat(product.regular_price)) {
            const discount = Math.round(((parseFloat(product.regular_price) - parseFloat(product.sale_price)) / parseFloat(product.regular_price)) * 100);
            console.log(`      🏷️ On Sale: ${discount}% off`);
          }
        });

        console.log('\n   ✅ Product data is correctly formatted and will display properly');
      } else {
        console.log('   ⚠️ No products found in WooCommerce store');
      }
    } catch (error) {
      console.log('   ❌ WooCommerce connection failed:', error.message);
      console.log('   ℹ️ No products will be available - WooCommerce required');
    }
  };

  testProductData();
} else {
  console.log('   ⚠️ WooCommerce credentials not configured');
  console.log('   ℹ️ No products will be available - WooCommerce required');
}

console.log('\n✅ Diagnostic complete!');
