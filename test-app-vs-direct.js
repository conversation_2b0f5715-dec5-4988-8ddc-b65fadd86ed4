const axios = require('axios');

async function testAppVsDirect() {
  console.log('🔍 Comparing App Request vs Direct Request...\n');
  
  const consumerKey = 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193';
  const consumerSecret = 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3';
  const baseURL = 'https://deal4u.co/wp-json/wc/v3';
  
  try {
    // Test 1: Direct request (like the working test)
    console.log('1. Testing DIRECT request (working method)...');
    const directResponse = await axios.get(`${baseURL}/products`, {
      auth: {
        username: consumerKey,
        password: consumerSecret
      },
      params: { per_page: 1 },
      timeout: 10000
    });
    console.log(`✅ Direct request: ${directResponse.status} OK`);
    console.log(`   Products found: ${directResponse.data.length}`);
    
    // Test 2: App-style request (how the application does it)
    console.log('\n2. Testing APP-STYLE request (failing method)...');
    
    // Create axios instance like the app does
    const appConfig = {
      baseURL: baseURL,
      timeout: 30000,
      auth: {
        username: consumerKey,
        password: consumerSecret
      }
    };
    
    const appInstance = axios.create(appConfig);
    
    // Add the same interceptor as the app
    appInstance.interceptors.request.use(config => {
      if (!config.url.startsWith('http')) {
        config.url = `${baseURL}${config.url}`;
      }
      return config;
    });
    
    try {
      const appResponse = await appInstance.get('/products', {
        params: {
          per_page: 1,
          status: 'publish'
        }
      });
      console.log(`✅ App-style request: ${appResponse.status} OK`);
      console.log(`   Products found: ${appResponse.data.length}`);
    } catch (appError) {
      console.log(`❌ App-style request failed: ${appError.response?.status || 'No response'}`);
      console.log(`   Error: ${appError.response?.data?.message || appError.message}`);
      
      // Check the actual URL being requested
      console.log(`   URL attempted: ${appError.config?.url}`);
      console.log(`   Base URL: ${appError.config?.baseURL}`);
      
      // Check if there's a URL construction issue
      if (appError.config?.url?.includes('wp-json/wc/v3/wp-json/wc/v3')) {
        console.log('   🔍 FOUND THE ISSUE: URL is being doubled!');
        console.log('   The interceptor is causing URL duplication');
      }
    }
    
    // Test 3: Simple app request without interceptor
    console.log('\n3. Testing APP request WITHOUT interceptor...');
    const simpleAppInstance = axios.create({
      baseURL: baseURL,
      timeout: 30000,
      auth: {
        username: consumerKey,
        password: consumerSecret
      }
    });
    
    try {
      const simpleResponse = await simpleAppInstance.get('/products', {
        params: {
          per_page: 1,
          status: 'publish'
        }
      });
      console.log(`✅ Simple app request: ${simpleResponse.status} OK`);
      console.log(`   Products found: ${simpleResponse.data.length}`);
    } catch (simpleError) {
      console.log(`❌ Simple app request failed: ${simpleError.response?.status || 'No response'}`);
      console.log(`   Error: ${simpleError.response?.data?.message || simpleError.message}`);
    }
    
  } catch (error) {
    console.log('❌ Error in comparison test:');
    console.log(`   ${error.message}`);
  }
}

testAppVsDirect();
