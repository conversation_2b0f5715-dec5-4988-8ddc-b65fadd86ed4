{"version": 3, "sources": ["../../../src/lib/eslint/customFormatter.ts"], "names": ["formatResults", "MessageSeverity", "pluginCount", "messages", "nextPluginWarningCount", "nextPluginErrorCount", "i", "length", "severity", "ruleId", "includes", "formatMessage", "dir", "filePath", "fileName", "path", "posix", "normalize", "relative", "replace", "startsWith", "output", "cyan", "message", "line", "column", "yellow", "toString", "bold", "red", "gray", "baseDir", "results", "format", "totalNextPluginErrorCount", "totalNextPluginWarningCount", "resultsWithMessages", "filter", "for<PERSON>ach", "res", "map", "join", "outputWithMessages"], "mappings": ";;;;;;;;;;;;;;;;;;IAgGgBA,aAAa;eAAbA;;;4BAhG8B;6DAC7B;;;;;;;UAGLC;;;GAAAA,oBAAAA;AAsBZ,SAASC,YAAYC,QAAuB;IAI1C,IAAIC,yBAAyB;IAC7B,IAAIC,uBAAuB;IAE3B,IAAK,IAAIC,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAExC,IAAIG,0BAAAA,OAAQC,QAAQ,CAAC,eAAe;YAClC,IAAIF,gBAAsC;gBACxCJ,0BAA0B;YAC5B,OAAO;gBACLC,wBAAwB;YAC1B;QACF;IACF;IAEA,OAAO;QACLA;QACAD;IACF;AACF;AAEA,SAASO,cACPC,GAAW,EACXT,QAAuB,EACvBU,QAAgB;IAEhB,IAAIC,WAAWC,aAAI,CAACC,KAAK,CAACC,SAAS,CACjCF,aAAI,CAACG,QAAQ,CAACN,KAAKC,UAAUM,OAAO,CAAC,OAAO;IAG9C,IAAI,CAACL,SAASM,UAAU,CAAC,MAAM;QAC7BN,WAAW,OAAOA;IACpB;IAEA,IAAIO,SAAS,OAAOC,IAAAA,gBAAI,EAACR;IAEzB,IAAK,IAAIR,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEiB,OAAO,EAAEf,QAAQ,EAAEgB,IAAI,EAAEC,MAAM,EAAEhB,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAE/De,SAASA,SAAS;QAElB,IAAIG,QAAQC,QAAQ;YAClBJ,SACEA,SACAK,IAAAA,kBAAM,EAACF,KAAKG,QAAQ,MACpB,MACAD,IAAAA,kBAAM,EAACD,OAAOE,QAAQ,MACtB;QACJ;QAEA,IAAInB,gBAAsC;YACxCa,UAAUK,IAAAA,kBAAM,EAACE,IAAAA,gBAAI,EAAC,cAAc;QACtC,OAAO;YACLP,UAAUQ,IAAAA,eAAG,EAACD,IAAAA,gBAAI,EAAC,YAAY;QACjC;QAEAP,UAAUE;QAEV,IAAId,QAAQ;YACVY,UAAU,OAAOS,IAAAA,gBAAI,EAACF,IAAAA,gBAAI,EAACnB;QAC7B;IACF;IAEA,OAAOY;AACT;AAEO,SAASrB,cACd+B,OAAe,EACfC,OAAqB,EACrBC,MAAmC;IAOnC,IAAIC,4BAA4B;IAChC,IAAIC,8BAA8B;IAClC,IAAIC,sBAAsBJ,QAAQK,MAAM,CAAC,CAAC,EAAElC,QAAQ,EAAE,GAAKA,4BAAAA,SAAUI,MAAM;IAE3E,qDAAqD;IACrD6B,oBAAoBE,OAAO,CAAC,CAAC,EAAEnC,QAAQ,EAAE;QACvC,MAAMoC,MAAMrC,YAAYC;QACxB+B,6BAA6BK,IAAIlC,oBAAoB;QACrD8B,+BAA+BI,IAAInC,sBAAsB;IAC3D;IAEA,oEAAoE;IACpE,MAAMiB,SAASY,SACXA,OAAOG,uBACPA,oBACGI,GAAG,CAAC,CAAC,EAAErC,QAAQ,EAAEU,QAAQ,EAAE,GAC1BF,cAAcoB,SAAS5B,UAAUU,WAElC4B,IAAI,CAAC;IAEZ,OAAO;QACLpB,QAAQA;QACRqB,oBACEN,oBAAoB7B,MAAM,GAAG,IACzBc,SACA,CAAC,IAAI,EAAEC,IAAAA,gBAAI,EACT,QACA,qHAAqH,CAAC,GACxH;QACNY;QACAC;IACF;AACF"}