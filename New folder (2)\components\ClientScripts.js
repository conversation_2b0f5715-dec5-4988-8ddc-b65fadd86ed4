'use client';

import { useEffect } from 'react';

export default function ClientScripts() {
  useEffect(() => {
    // Performance monitoring
    if (typeof window !== 'undefined') {
      // Monitor page load performance
      window.addEventListener('load', () => {
        if ('performance' in window) {
          const perfData = performance.getEntriesByType('navigation')[0];
          console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }
      });

      // Smooth scroll polyfill for older browsers
      if (!('scrollBehavior' in document.documentElement.style)) {
        // Simple polyfill for smooth scroll
        document.documentElement.style.scrollBehavior = 'smooth';
      }

      // Service Worker registration for PWA
      if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      }

      // Initialize Features Manager
      import('../lib/featuresManager.js').then(module => {
        const featuresManager = module.default;
        if (featuresManager && !featuresManager.initialized) {
          featuresManager.initialize();
        }
      }).catch(error => {
        console.error('Error loading features manager:', error);
      });

      // Admin keyboard shortcut removed from public website for security

      // Admin access removed from public website for security
    }
  }, []);

  return null;
}
