import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Deal4U Admin Panel',
  description: 'Admin dashboard for Deal4U e-commerce store management',
};

export default function AdminLayout({ children }) {
  return (
    <div className={inter.className}>
      <div className="min-h-screen bg-gray-50">
        {/* Admin Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-4">
                <a href="/admin" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">D4U</span>
                  </div>
                  <span className="text-xl font-bold text-gray-900">Admin Panel</span>
                </a>
                
                <nav className="hidden md:flex space-x-6">
                  <a
                    href="/admin"
                    className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                  >
                    Dashboard
                  </a>
                  <a
                    href="/admin/master-control"
                    className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                  >
                    🎛️ Master Control
                  </a>
                </nav>
              </div>
              
              <div className="flex items-center space-x-4">
                <a 
                  href="/" 
                  className="text-gray-600 hover:text-gray-900 text-sm font-medium"
                >
                  ← Back to Store
                </a>
              </div>
            </div>
          </div>
        </header>

        {/* Admin Content */}
        <main>
          {children}
        </main>
      </div>
    </div>
  );
}
