{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["NextRequestHint", "adapter", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "getTracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "ensureInstrumentationRegistered", "isEdgeRendering", "self", "__BUILD_MANIFEST", "url", "normalizeRscURL", "requestUrl", "NextURL", "nextConfig", "searchParams", "value", "getAll", "normalizeNextQueryParam", "normalizedKey", "delete", "val", "append", "buildId", "isNextDataRequest", "pathname", "requestHeaders", "fromNodeOutgoingHttpHeaders", "flightHeaders", "Map", "param", "FLIGHT_PARAMETERS", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "stripInternalSearchParams", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCacheShared", "IncrementalCache", "__incrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getEdgePreviewProps", "event", "NextFetchEvent", "response", "cookiesFromResponse", "isMiddleware", "trace", "MiddlewareSpan", "execute", "spanName", "nextUrl", "attributes", "RequestAsyncStorageWrapper", "wrap", "requestAsyncStorage", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "relativizeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "Promise", "all", "waitUntilSymbol", "fetchMetrics"], "mappings": ";;;;;;;;;;;;;;;IAqBaA,eAAe;eAAfA;;IA8DSC,OAAO;eAAPA;;;uBAjFa;uBACkC;4BACtC;yBACH;0BACC;+BACC;yBAEN;+BACkB;0BACV;kCACE;yBACc;4CACL;6CACP;wBACV;2BAEK;qCACK;AAE7B,MAAMD,wBAAwBE,oBAAW;IAI9CC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,cAAc;QACZ,MAAM,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAK,YAAY;QACV,MAAM,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMM,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEZ,SACAa;IAEA,MAAMC,SAASC,IAAAA,iBAAS;IACxB,OAAOD,OAAOE,qBAAqB,CAAChB,QAAQM,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIa,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAV,aAAaW,mBAAmBX;QAClC;IACF;AACF;AAEO,eAAepB,QACpBG,MAAsB;IAEtBuB;IACA,MAAMO,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IAEzDjC,OAAOK,OAAO,CAAC6B,GAAG,GAAGC,IAAAA,yBAAe,EAACnC,OAAOK,OAAO,CAAC6B,GAAG;IAEvD,MAAME,aAAa,IAAIC,gBAAO,CAACrC,OAAOK,OAAO,CAAC6B,GAAG,EAAE;QACjDvB,SAASX,OAAOK,OAAO,CAACM,OAAO;QAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM5B,OAAO;WAAI0B,WAAWG,YAAY,CAAC7B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM8B,QAAQJ,WAAWG,YAAY,CAACE,MAAM,CAAC1B;QAE7C2B,IAAAA,8BAAuB,EAAC3B,KAAK,CAAC4B;YAC5BP,WAAWG,YAAY,CAACK,MAAM,CAACD;YAE/B,KAAK,MAAME,OAAOL,MAAO;gBACvBJ,WAAWG,YAAY,CAACO,MAAM,CAACH,eAAeE;YAChD;YACAT,WAAWG,YAAY,CAACK,MAAM,CAAC7B;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMgC,UAAUX,WAAWW,OAAO;IAClCX,WAAWW,OAAO,GAAG;IAErB,MAAMC,oBAAoBhD,OAAOK,OAAO,CAACM,OAAO,CAAC,gBAAgB;IAEjE,IAAIqC,qBAAqBZ,WAAWa,QAAQ,KAAK,UAAU;QACzDb,WAAWa,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBC,IAAAA,kCAA2B,EAACnD,OAAOK,OAAO,CAACM,OAAO;IACzE,MAAMyC,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACtB,iBAAiB;QACpB,KAAK,MAAMuB,SAASC,mCAAiB,CAAE;YACrC,MAAMxC,MAAMuC,MAAME,QAAQ,GAAGC,WAAW;YACxC,MAAMjB,QAAQU,eAAepC,GAAG,CAACC;YACjC,IAAIyB,OAAO;gBACTY,cAAcM,GAAG,CAAC3C,KAAKmC,eAAepC,GAAG,CAACC;gBAC1CmC,eAAeN,MAAM,CAAC7B;YACxB;QACF;IACF;IAEA,MAAM4C,eAAenC,QAAQC,GAAG,CAACmC,kCAAkC,GAC/D,IAAIC,IAAI7D,OAAOK,OAAO,CAAC6B,GAAG,IAC1BE;IAEJ,MAAM/B,UAAU,IAAIT,gBAAgB;QAClCQ,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAO6D,IAAAA,wCAAyB,EAACH,cAAc,MAAMH,QAAQ;QAC7DtD,MAAM;YACJ6D,MAAM/D,OAAOK,OAAO,CAAC0D,IAAI;YACzBC,KAAKhE,OAAOK,OAAO,CAAC2D,GAAG;YACvBrD,SAASuC;YACTe,IAAIjE,OAAOK,OAAO,CAAC4D,EAAE;YACrBC,QAAQlE,OAAOK,OAAO,CAAC6D,MAAM;YAC7B5B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;YACrC6B,QAAQnE,OAAOK,OAAO,CAAC8D,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAInB,mBAAmB;QACrBoB,OAAOC,cAAc,CAAChE,SAAS,YAAY;YACzCiE,YAAY;YACZ9B,OAAO;QACT;IACF;IAEA,IACE,6CAA6C;IAC7C,8CAA8C;IAC9C,6CAA6C;IAC7C,CAAC,AAAC+B,WAAmBC,wBAAwB,IAC7C,AAACxE,OAAeyE,gBAAgB,EAChC;QACEF,WAAmBG,kBAAkB,GAAG,IAAI,AAC5C1E,OACAyE,gBAAgB,CAAC;YACjBE,QAAQ;YACRC,YAAY;YACZC,aAAarD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YACtCC,qBAAqBvD,QAAQC,GAAG,CAACuD,6BAA6B;YAC9DC,KAAKzD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YAC9B5B,gBAAgBlD,OAAOK,OAAO,CAACM,OAAO;YACtCuE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASC,IAAAA,wCAAmB;gBAC9B;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIC,0BAAc,CAAC;QAAEtF;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAIwF;IACJ,IAAIC;IAEJD,WAAW,MAAM3E,WAAWZ,SAAS;QACnC,8DAA8D;QAC9D,MAAMyF,eACJ9F,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAI0F,cAAc;YAChB,OAAO1E,IAAAA,iBAAS,IAAG2E,KAAK,CACtBC,yBAAc,CAACC,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAE7F,QAAQ6D,MAAM,CAAC,CAAC,EAAE7D,QAAQ8F,OAAO,CAAClD,QAAQ,CAAC,CAAC;gBACpEmD,YAAY;oBACV,eAAe/F,QAAQ8F,OAAO,CAAClD,QAAQ;oBACvC,eAAe5C,QAAQ6D,MAAM;gBAC/B;YACF,GACA,IACEmC,sDAA0B,CAACC,IAAI,CAC7BC,gDAAmB,EACnB;oBACEC,KAAKnG;oBACLoG,YAAY;wBACVC,iBAAiB,CAACC;4BAChBd,sBAAsBc;wBACxB;wBACA,2EAA2E;wBAC3EC,cAAcnB,IAAAA,wCAAmB;oBACnC;gBACF,GACA,IAAMzF,OAAO6G,OAAO,CAACxG,SAASqF;QAGtC;QACA,OAAO1F,OAAO6G,OAAO,CAACxG,SAASqF;IACjC;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBkB,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAInB,YAAYC,qBAAqB;QACnCD,SAASjF,OAAO,CAAC+C,GAAG,CAAC,cAAcmC;IACrC;IAEA;;;;;GAKC,GACD,MAAMmB,UAAUpB,4BAAAA,SAAUjF,OAAO,CAACG,GAAG,CAAC;IACtC,IAAI8E,YAAYoB,WAAW,CAACjF,iBAAiB;QAC3C,MAAMkF,aAAa,IAAI5E,gBAAO,CAAC2E,SAAS;YACtCE,aAAa;YACbvG,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA,IAAI,CAACd,QAAQC,GAAG,CAACmC,kCAAkC,EAAE;YACnD,IAAIqD,WAAWE,IAAI,KAAK9G,QAAQ8F,OAAO,CAACgB,IAAI,EAAE;gBAC5CF,WAAWlE,OAAO,GAAGA,WAAWkE,WAAWlE,OAAO;gBAClD6C,SAASjF,OAAO,CAAC+C,GAAG,CAAC,wBAAwB0D,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqBC,IAAAA,4BAAa,EACtCF,OAAOH,aACPG,OAAOhF;QAGT,IACEY,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACExB,CAAAA,QAAQC,GAAG,CAAC8F,0CAA0C,IACtDF,mBAAmBG,KAAK,CAAC,gBAAe,GAE1C;YACA5B,SAASjF,OAAO,CAAC+C,GAAG,CAAC,oBAAoB2D;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMI,WAAW7B,4BAAAA,SAAUjF,OAAO,CAACG,GAAG,CAAC;IACvC,IAAI8E,YAAY6B,YAAY,CAAC1F,iBAAiB;QAC5C,MAAM2F,cAAc,IAAIrF,gBAAO,CAACoF,UAAU;YACxCP,aAAa;YACbvG,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA;;;KAGC,GACDsD,WAAW,IAAIkB,SAASlB,SAAS7B,IAAI,EAAE6B;QAEvC,IAAI,CAACpE,QAAQC,GAAG,CAACmC,kCAAkC,EAAE;YACnD,IAAI8D,YAAYP,IAAI,KAAK9G,QAAQ8F,OAAO,CAACgB,IAAI,EAAE;gBAC7CO,YAAY3E,OAAO,GAAGA,WAAW2E,YAAY3E,OAAO;gBACpD6C,SAASjF,OAAO,CAAC+C,GAAG,CAAC,YAAY0D,OAAOM;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAI1E,mBAAmB;YACrB4C,SAASjF,OAAO,CAACiC,MAAM,CAAC;YACxBgD,SAASjF,OAAO,CAAC+C,GAAG,CAClB,qBACA4D,IAAAA,4BAAa,EAACF,OAAOM,cAAcN,OAAOhF;QAE9C;IACF;IAEA,MAAMuF,gBAAgB/B,WAAWA,WAAWgC,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAchH,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMiH,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAC/G,KAAKyB,MAAM,IAAIY,cAAe;YACxCuE,cAAchH,OAAO,CAAC+C,GAAG,CAAC,CAAC,qBAAqB,EAAE3C,IAAI,CAAC,EAAEyB;YACzDuF,mBAAmBC,IAAI,CAACjH;QAC1B;QAEA,IAAIgH,mBAAmBE,MAAM,GAAG,GAAG;YACjCN,cAAchH,OAAO,CAAC+C,GAAG,CACvB,iCACAoE,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLtC,UAAU+B;QACVnH,WAAW2H,QAAQC,GAAG,CAAC1C,KAAK,CAAC2C,2BAAe,CAAC;QAC7CC,cAAcjI,QAAQiI,YAAY;IACpC;AACF"}