'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { 
  Search, 
  ShoppingCart, 
  User, 
  Menu, 
  X, 
  Heart,
  LogOut,
  Settings,
  Package,
  Bell,
  Image as ImageIcon,
  Camera,
  Upload
} from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import Image from 'next/image';

const Header = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { items, getTotalItems } = useCart();
  const { user, isAuthenticated, logout } = useAuth();
  const fileInputRef = useRef(null);
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchImage, setSearchImage] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [isImageSearchOpen, setIsImageSearchOpen] = useState(false);

  // Navigation items
  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Shop', href: '/shop' },
    { name: 'Categories', href: '/categories' },
    { name: 'Track Order', href: '/track-order' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'FAQ', href: '/faq' }
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setIsUserMenuOpen(false);
  }, [pathname]);

  // Handle search
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    
    if (searchImage) {
      // For image search, we need to upload the image to the server
      // and then redirect to results based on image analysis
      handleImageSearch();
    } else if (searchQuery.trim()) {
      // Regular text search
      router.push(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };
  
  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSearchImage(file);
      // Only create object URLs on the client side
      if (typeof window !== 'undefined') {
        const imageUrl = URL.createObjectURL(file);
        setImagePreview(imageUrl);
      }
      setIsImageSearchOpen(true);
    }
  };
  
  // Handle image search
  const handleImageSearch = async () => {
    if (!searchImage) return;
    
    try {
      // Create a FormData object to send the image
      const formData = new FormData();
      formData.append('image', searchImage);
      
      // In a production environment, we would send the image to an API
      // and get back relevant product matches. For now, we'll demonstrate
      // the UI flow with a simulated API call.
      
      let searchParams;
      
      try {
        // Attempt to call our API endpoint
        const response = await fetch('/api/image-search', {
          method: 'POST',
          body: formData
        });
        
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // If we get successful results, use the returned data
            searchParams = `imageSearch=true&category=${data.suggestedCategories[0] || 'electronics'}`;
          } else {
            // If API returns an error, fall back to default search
            searchParams = 'imageSearch=true&category=electronics';
          }
        } else {
          // If API call fails, fall back to default search
          searchParams = 'imageSearch=true&category=electronics';
        }
      } catch (error) {
        console.error('API call failed:', error);
        // If API call throws an exception, fall back to default search
        searchParams = 'imageSearch=true&category=electronics';
      }
      
      // Navigate to the shop page with the appropriate parameters
      router.push(`/shop?${searchParams}`);
      
      // Clear the image after search
      clearImageSearch();
    } catch (error) {
      console.error('Error during image search:', error);
    }
  };
  
  // Clear image search
  const clearImageSearch = () => {
    // Revoke the object URL to prevent memory leaks
    if (imagePreview && typeof window !== 'undefined') {
      URL.revokeObjectURL(imagePreview);
    }
    setSearchImage(null);
    setImagePreview('');
    setIsImageSearchOpen(false);
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    setIsUserMenuOpen(false);
    router.push('/');
  };

  const cartItemsCount = getTotalItems();

  return (
    <header 
      className={`sticky top-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-lg' 
          : 'bg-white shadow-md'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link 
            href="/" 
            className="flex items-center space-x-2 flex-shrink-0"
          >
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-colors">
              Deal4u
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  pathname === item.href
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearchSubmit} className="relative w-full">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                />
                <button 
                  type="button" 
                  onClick={() => fileInputRef.current.click()}
                  className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                  title="Search by image"
                >
                  <Camera className="w-5 h-5" />
                </button>
                <input 
                  ref={fileInputRef}
                  type="file" 
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                />
                {(searchQuery || searchImage) && (
                  <button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                  >
                    Search
                  </button>
                )}
              </div>
              {typeof window !== 'undefined' && isImageSearchOpen && imagePreview && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-700">Image Search</h4>
                    <button 
                      type="button" 
                      onClick={clearImageSearch}
                      className="text-gray-500 hover:text-red-500"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="relative h-40 bg-gray-100 rounded overflow-hidden">
                    <Image 
                      key={imagePreview}
                      src={imagePreview} 
                      alt="Search by this image"
                      fill
                      style={{ objectFit: 'contain' }}
                      priority
                    />
                  </div>
                  <div className="mt-2 flex justify-end">
                    <button 
                      type="submit"
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center"
                    >
                      <Search className="w-3 h-3 mr-1" />
                      Find similar products
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Wishlist - Desktop */}
            <Link
              href="/wishlist"
              className="hidden md:flex items-center space-x-1 text-gray-700 hover:text-red-500 transition-colors"
            >
              <Heart className="w-5 h-5" />
              <span className="text-sm">Wishlist</span>
            </Link>

            {/* Shopping Cart */}
            <Link
              href="/cart"
              className="relative flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors"
            >
              <ShoppingCart className="w-5 h-5" />
              <span className="hidden md:block text-sm">Cart</span>
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItemsCount > 99 ? '99+' : cartItemsCount}
                </span>
              )}
            </Link>

            {/* User Menu */}
            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors"
                >
                  <img
                    src={user?.avatar}
                    alt={user?.name}
                    className="w-8 h-8 rounded-full border-2 border-gray-200"
                  />
                  <span className="hidden md:block text-sm font-medium">
                    {user?.firstName || user?.name}
                  </span>
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50">
                    <Link
                      href="/account"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <User className="w-4 h-4 mr-3" />
                      Account
                    </Link>
                    <Link
                      href="/account/orders"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <Package className="w-4 h-4 mr-3" />
                      Orders
                    </Link>
                    <Link
                      href="/account/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <Settings className="w-4 h-4 mr-3" />
                      Settings
                    </Link>
                    <Link
                      href="/wishlist"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors md:hidden"
                    >
                      <Heart className="w-4 h-4 mr-3" />
                      Wishlist
                    </Link>
                    <hr className="my-1" />
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                    >
                      <LogOut className="w-4 h-4 mr-3" />
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/login"
                className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors"
              >
                <User className="w-5 h-5" />
                <span className="hidden md:block text-sm">Login</span>
              </Link>
            )}

            {/* Mobile menu button */}
            <button
              className="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200 py-4">
            {/* Mobile Search */}
            <div className="px-2 pb-4">
              <form onSubmit={handleSearchSubmit} className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                
                {/* Mobile image search button */}
                <button 
                  type="button" 
                  onClick={() => fileInputRef.current.click()}
                  className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600 transition-colors"
                  title="Search by image"
                >
                  <Camera className="w-5 h-5" />
                </button>
                
                {(searchQuery || searchImage) && (
                  <button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                  >
                    Search
                  </button>
                )}
              </form>
              
              {/* Mobile image preview */}
              {typeof window !== 'undefined' && isImageSearchOpen && imagePreview && (
                <div className="mt-2 bg-white rounded-lg border border-gray-200 p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-700">Image Search</h4>
                    <button 
                      type="button" 
                      onClick={clearImageSearch}
                      className="text-gray-500 hover:text-red-500"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="relative h-32 bg-gray-100 rounded overflow-hidden">
                    <Image 
                      key={imagePreview}
                      src={imagePreview} 
                      alt="Search by this image"
                      fill
                      style={{ objectFit: 'contain' }}
                      priority
                    />
                  </div>
                  <div className="mt-2 flex justify-end">
                    <button 
                      type="button"
                      onClick={handleImageSearch}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center"
                    >
                      <Search className="w-3 h-3 mr-1" />
                      Find similar products
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Navigation */}
            <div className="space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    pathname === item.href
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
              
              {/* Mobile-only links */}
              <Link
                href="/wishlist"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-500 hover:bg-gray-50 transition-colors"
              >
                Wishlist
              </Link>
              
              {!isAuthenticated && (
                <Link
                  href="/register"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors"
                >
                  Register
                </Link>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close dropdowns */}
      {(isUserMenuOpen || isMenuOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsUserMenuOpen(false);
            setIsMenuOpen(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;