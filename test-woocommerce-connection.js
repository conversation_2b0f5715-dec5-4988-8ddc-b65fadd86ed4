const axios = require('axios');

// Test WooCommerce API connection
async function testWooCommerceConnection() {
  const baseURL = 'https://deal4u.co/wp-json/wc/v3';
  const consumerKey = 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193';
  const consumerSecret = 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3';

  console.log('Testing WooCommerce API connection...');
  console.log('Base URL:', baseURL);
  console.log('Consumer Key:', consumerKey ? 'Present' : 'Missing');
  console.log('Consumer Secret:', consumerSecret ? 'Present' : 'Missing');

  try {
    // Test basic connection
    const response = await axios.get(`${baseURL}/products`, {
      auth: {
        username: consumerKey,
        password: consumerSecret
      },
      params: {
        per_page: 5,
        status: 'publish'
      },
      timeout: 10000
    });

    console.log('\n✅ WooCommerce API Connection Successful!');
    console.log('Status:', response.status);
    console.log('Products found:', response.data.length);
    
    if (response.data.length > 0) {
      console.log('\nFirst product:');
      const product = response.data[0];
      console.log('- ID:', product.id);
      console.log('- Name:', product.name);
      console.log('- Price:', product.price);
      console.log('- Stock Status:', product.stock_status);
      console.log('- Categories:', product.categories.map(cat => cat.name).join(', '));
    } else {
      console.log('\n⚠️  No products found in WooCommerce store');
    }

    // Test categories
    const categoriesResponse = await axios.get(`${baseURL}/products/categories`, {
      auth: {
        username: consumerKey,
        password: consumerSecret
      },
      params: {
        per_page: 10,
        hide_empty: false
      }
    });

    console.log('\nCategories found:', categoriesResponse.data.length);
    if (categoriesResponse.data.length > 0) {
      console.log('Categories:');
      categoriesResponse.data.forEach(cat => {
        console.log(`- ${cat.name} (${cat.count} products)`);
      });
    }

  } catch (error) {
    console.log('\n❌ WooCommerce API Connection Failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Error Data:', error.response.data);
      
      if (error.response.status === 401) {
        console.log('\n🔑 Authentication Error - Check your API keys');
      } else if (error.response.status === 404) {
        console.log('\n🔍 API Endpoint Not Found - Check your WordPress URL');
      }
    } else if (error.request) {
      console.log('No response received:', error.message);
      console.log('\n🌐 Network Error - Check your internet connection and WordPress URL');
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Run the test
testWooCommerceConnection();
