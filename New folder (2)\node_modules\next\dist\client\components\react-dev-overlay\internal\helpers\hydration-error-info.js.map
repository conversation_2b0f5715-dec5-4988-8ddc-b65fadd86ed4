{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/hydration-error-info.ts"], "names": ["getHydrationWarningType", "hydrationErrorState", "patchConsoleError", "msg", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "Boolean", "htmlTagsWarnings", "has", "isTextMismatchWarning", "textMismatchWarning", "textAndTagsMismatchWarnings", "isKnownHydrationWarning", "Set", "prev", "console", "error", "serverContent", "clientContent", "componentStack", "warning", "apply", "arguments"], "mappings": ";;;;;;;;;;;;;;;;IAUaA,uBAAuB;eAAvBA;;IAoBAC,mBAAmB;eAAnBA;;IAyBGC,iBAAiB;eAAjBA;;;AA7CT,MAAMF,0BAA0B,CACrCG;IAEA,IAAIC,kBAAkBD,MAAM,OAAO;IACnC,IAAIE,4BAA4BF,MAAM,OAAO;IAC7C,OAAO;AACT;AAEA,MAAMC,oBAAoB,CAACD,MACzBG,QAAQH,OAAOI,iBAAiBC,GAAG,CAACL;AAEtC,MAAMM,wBAAwB,CAACN,MAAsBO,wBAAwBP;AAC7E,MAAME,8BAA8B,CAACF,MACnCG,QAAQH,OAAOQ,4BAA4BH,GAAG,CAACL;AAEjD,MAAMS,0BAA0B,CAACT,MAC/BC,kBAAkBD,QAClBE,4BAA4BF,QAC5BM,sBAAsBN;AAEjB,MAAMF,sBAA2C,CAAC;AAEzD,iIAAiI;AACjI,MAAMM,mBAAmB,IAAIM,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMF,8BAA8B,IAAIE,IAAI;IAC1C;IACA;CACD;AACD,MAAMH,sBACJ;AAQK,SAASR;IACd,MAAMY,OAAOC,QAAQC,KAAK;IAC1BD,QAAQC,KAAK,GAAG,SAAUb,GAAG,EAAEc,aAAa,EAAEC,aAAa,EAAEC,cAAc;QACzE,IAAIP,wBAAwBT,MAAM;YAChCF,oBAAoBmB,OAAO,GAAG;gBAC5B,sCAAsC;gBACtCjB;gBACAc;gBACAC;aACD;YACDjB,oBAAoBkB,cAAc,GAAGA;YACrClB,oBAAoBgB,aAAa,GAAGA;YACpChB,oBAAoBiB,aAAa,GAAGA;QACtC;QAEA,uCAAuC;QACvCJ,KAAKO,KAAK,CAACN,SAASO;IACtB;AACF"}