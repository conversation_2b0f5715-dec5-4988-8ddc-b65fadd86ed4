{"name": "doctrine", "description": "JSDoc parser", "homepage": "https://github.com/eslint/doctrine", "main": "lib/doctrine.js", "version": "2.1.0", "engines": {"node": ">=0.10.0"}, "directories": {"lib": "./lib"}, "files": ["lib"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://www.nczonline.net"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "https://github.com/Constellation"}], "repository": "eslint/doctrine", "devDependencies": {"coveralls": "^2.11.2", "dateformat": "^1.0.11", "eslint": "^1.10.3", "eslint-release": "^0.10.0", "linefix": "^0.1.1", "mocha": "^3.4.2", "npm-license": "^0.3.1", "nyc": "^10.3.2", "semver": "^5.0.3", "shelljs": "^0.5.3", "shelljs-nodecli": "^0.1.1", "should": "^5.0.1"}, "license": "Apache-2.0", "scripts": {"pretest": "npm run lint", "test": "nyc mocha", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint lib/", "release": "eslint-release", "ci-release": "eslint-ci-release", "alpharelease": "eslint-prerelease alpha", "betarelease": "eslint-prerelease beta"}, "dependencies": {"esutils": "^2.0.2"}}