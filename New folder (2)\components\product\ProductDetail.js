'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Minus, Plus, ShoppingCart, AlertCircle, Package, Truck, Shield, Star, Heart, Share2, ChevronLeft, ChevronRight, ZoomIn, Palette, Ruler } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useCart } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import ReviewList from '@/components/reviews/ReviewList';
import ReviewForm from '@/components/reviews/ReviewForm';

const ProductDetail = ({ product }) => {
  const { addToCart } = useCart();
  const { user } = useAuth();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [selectedAttributes, setSelectedAttributes] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [imageError, setImageError] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [activeTab, setActiveTab] = useState('description');
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });

  // Format price with currency
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const currentPrice = selectedVariant ? selectedVariant.price : product.price;
  const regularPrice = selectedVariant ? selectedVariant.regular_price : product.regular_price;
  const isOnSale = regularPrice > currentPrice;
  // Extract unique attributes from variations for simplified selection
  const getUniqueAttributes = () => {
    if (!product.variations || product.variations.length === 0) return {};

    const attributes = {};

    product.variations.forEach(variant => {
      if (variant.attributes && variant.attributes.length > 0) {
        variant.attributes.forEach(attr => {
          if (attr.name && attr.option && attr.option !== 'Option') {
            const attrName = attr.name.toLowerCase();
            if (!attributes[attrName]) {
              attributes[attrName] = new Set();
            }
            attributes[attrName].add(attr.option);
          }
        });
      }
    });

    // Convert Sets to Arrays
    Object.keys(attributes).forEach(key => {
      attributes[key] = Array.from(attributes[key]);
    });

    return attributes;
  };

  const uniqueAttributes = getUniqueAttributes();
  const hasVariants = Object.keys(uniqueAttributes).length > 0;

  // Find variant that matches selected attributes
  const findMatchingVariant = (attributes) => {
    if (!product.variations || Object.keys(attributes).length === 0) return null;

    return product.variations.find(variant => {
      if (!variant.attributes) return false;

      return Object.keys(attributes).every(attrName => {
        const selectedValue = attributes[attrName];
        return variant.attributes.some(attr =>
          attr.name.toLowerCase() === attrName.toLowerCase() &&
          attr.option === selectedValue
        );
      });
    });
  };

  // Handle attribute selection
  const handleAttributeSelect = (attributeName, value) => {
    const newAttributes = {
      ...selectedAttributes,
      [attributeName]: value
    };

    setSelectedAttributes(newAttributes);

    // Find and set matching variant
    const matchingVariant = findMatchingVariant(newAttributes);
    setSelectedVariant(matchingVariant);
  };

  const handleQuantityChange = (amount) => {
    const newQuantity = Math.max(1, quantity + amount);
    if (product.stockQuantity && newQuantity > product.stockQuantity) {
      toast.error('Not enough stock available');
      return;
    }
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    try {
      setIsLoading(true);

      // Check if the product is in stock
      if (!product.inStock) {
        toast.error('This product is out of stock');
        return;
      }

      // Check stock availability
      if (product.stockQuantity && quantity > product.stockQuantity) {
        toast.error('Not enough stock available');
        return;
      }

      // Prepare the item to add to cart
      const itemToAdd = {
        id: product.id,
        name: product.name,
        price: currentPrice,
        image: product.images?.[activeImageIndex]?.src || product.image || '/placeholder.jpg',
        quantity: quantity,
        stockQuantity: product.stockQuantity || 0,
        inStock: product.inStock
      };

      // If a variant is selected, include that information
      if (selectedVariant) {
        itemToAdd.selectedVariant = selectedVariant;
        itemToAdd.name = `${product.name} - ${selectedVariant.name || 'Selected Option'}`;
      }

      await addToCart(itemToAdd, quantity);
      toast.success(`Added ${itemToAdd.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Get product image with proper fallback
  const getProductImage = (image) => {
    if (imageError) return '/placeholder.jpg';
    
    // If image is already a string URL, use it directly
    if (typeof image === 'string') return image;
    
    // Check for WooCommerce image object
    if (image && image.src) return image.src;
    
    return '/placeholder.jpg';
  };

  // Get all product images
  const getProductImages = () => {
    // Check for WooCommerce image structure
    if (product.images && product.images.length > 0) {
      return product.images.map(img => getProductImage(img));
    }
    
    // Check for direct image URL
    if (product.image) {
      return [getProductImage(product.image)];
    }
    
    return [];
  };

  const productImages = getProductImages();
  const isInStock = product.inStock || product.stockStatus === 'instock';
  const discountPercentage = isOnSale ? Math.round(((regularPrice - currentPrice) / regularPrice) * 100) : 0;

  // Enhanced image navigation
  const nextImage = () => {
    if (productImages.length > 1) {
      setActiveImageIndex((prev) => (prev + 1) % productImages.length);
    }
  };

  const prevImage = () => {
    if (productImages.length > 1) {
      setActiveImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
    }
  };

  // Handle zoom toggle for mobile
  const handleZoomToggle = () => {
    setIsZoomed(!isZoomed);
  };



  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Enhanced Product Images Gallery - Larger */}
        <div className="lg:col-span-2 space-y-4">
          {/* Main Image with Zoom */}
          <div
            className="relative aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg cursor-zoom-in"
            onMouseEnter={() => setIsZoomed(true)}
            onMouseLeave={() => setIsZoomed(false)}
            onMouseMove={(e) => {
              const rect = e.currentTarget.getBoundingClientRect();
              const x = ((e.clientX - rect.left) / rect.width) * 100;
              const y = ((e.clientY - rect.top) / rect.height) * 100;
              setZoomPosition({ x, y });
            }}
            onClick={handleZoomToggle}
          >
            <Image
              src={productImages[activeImageIndex] || getProductImage(product.image)}
              alt={product.name}
              fill
              className={`object-cover transition-transform duration-500 ${
                isZoomed ? 'scale-150' : 'hover:scale-105'
              }`}
              style={isZoomed ? {
                transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`
              } : {}}
              onError={() => setImageError(true)}
              priority
            />

            {/* Zoom Indicator */}
            {!isZoomed && (
              <div className="absolute top-4 right-4 bg-black/60 text-white p-2 rounded-full opacity-0 hover:opacity-100 transition-opacity">
                <ZoomIn className="w-4 h-4" />
              </div>
            )}

            {/* Discount Badge */}
            {discountPercentage > 0 && (
              <div className="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                -{discountPercentage}% OFF
              </div>
            )}

            {/* Image Navigation */}
            {productImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}

            {/* Image Counter */}
            {productImages.length > 1 && (
              <div className="absolute bottom-4 right-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm">
                {activeImageIndex + 1} / {productImages.length}
              </div>
            )}
          </div>

          {/* Thumbnail Gallery */}
          {productImages.length > 1 && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              {productImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setActiveImageIndex(index)}
                  className={`relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                    index === activeImageIndex
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Compact Product Info Sidebar */}
        <div className="space-y-4">
          {/* Product Title & Rating */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h1 className="text-2xl font-bold text-gray-900 leading-tight mb-3">
              {product.name}
            </h1>

            {/* Rating and Reviews */}
            <div className="flex items-center gap-2 mb-4">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(product.rating || 4.5)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="text-sm font-semibold text-gray-900 ml-1">
                  {product.rating || '4.5'}
                </span>
              </div>
              <span className="text-sm text-gray-600">
                ({product.reviews || 0} reviews)
              </span>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-2 mb-4">
              <div className={`w-2 h-2 rounded-full ${isInStock ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className={`text-sm font-medium ${isInStock ? 'text-green-600' : 'text-red-600'}`}>
                {isInStock ? 'In Stock' : 'Out of Stock'}
              </span>
              {isInStock && product.stock_quantity && product.stock_quantity < 10 && (
                <span className="text-orange-600 text-xs">
                  Only {product.stock_quantity} left!
                </span>
              )}
            </div>
          </div>

          {/* Price Section */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
            <div className="flex items-baseline gap-2 mb-3">
              <span className="text-3xl font-bold text-red-600">
                {formatPrice(currentPrice)}
              </span>
              {isOnSale && (
                <>
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(regularPrice)}
                  </span>
                  <span className="bg-red-500 text-white px-2 py-1 rounded-md text-xs font-bold">
                    Save {formatPrice(regularPrice - currentPrice)}
                  </span>
                </>
              )}
            </div>

            {/* Price Benefits */}
            <div className="flex flex-wrap gap-2 text-xs">
              <div className="flex items-center gap-1 text-green-600">
                <Package className="w-3 h-3" />
                <span>Free shipping</span>
              </div>
              <div className="flex items-center gap-1 text-blue-600">
                <Truck className="w-3 h-3" />
                <span>Fast delivery</span>
              </div>
              <div className="flex items-center gap-1 text-purple-600">
                <Shield className="w-3 h-3" />
                <span>Buyer protection</span>
              </div>
            </div>
          </div>

          {/* Product Variants */}
          {hasVariants && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Palette className="w-4 h-4 text-blue-500" />
                Product Options
              </h3>
              <div className="space-y-6">
                {Object.entries(uniqueAttributes).map(([attributeName, options]) => (
                  <div key={attributeName}>
                    <h4 className="text-sm font-medium text-gray-700 mb-3 capitalize">
                      {attributeName}:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {options.map((option) => (
                        <button
                          key={option}
                          onClick={() => handleAttributeSelect(attributeName, option)}
                          className={`px-4 py-2 border-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                            selectedAttributes[attributeName] === option
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-200 hover:border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Selected variant info */}
              {selectedVariant && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-blue-800">
                      <span className="font-medium">Selected:</span> {
                        Object.entries(selectedAttributes)
                          .map(([key, value]) => `${key}: ${value}`)
                          .join(', ')
                      }
                    </div>
                    <div className="text-sm font-semibold text-blue-900">
                      {formatPrice(selectedVariant.price || product.price)}
                    </div>
                  </div>
                </div>
              )}

              {/* Size Guide Link */}
              {hasVariants && product.variations.some(v =>
                v.attributes?.some(attr =>
                  attr.name?.toLowerCase().includes('size') ||
                  attr.name?.toLowerCase().includes('dimension')
                )
              ) && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <Ruler className="w-4 h-4" />
                    <span>Size Guide</span>
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Quantity and Actions */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 space-y-4">
            {/* Quantity Selector */}
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-700 font-medium">Qty:</span>
              <div className="flex items-center border-2 border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  className="p-2 hover:bg-gray-100 transition-colors disabled:opacity-50"
                  disabled={quantity <= 1 || isLoading}
                >
                  <Minus className="w-3 h-3" />
                </button>
                <span className="px-4 py-2 text-center min-w-[3rem] font-semibold bg-gray-50 text-sm">
                  {quantity}
                </span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  className="p-2 hover:bg-gray-100 transition-colors disabled:opacity-50"
                  disabled={isLoading || (product.stockQuantity && quantity >= product.stockQuantity)}
                >
                  <Plus className="w-3 h-3" />
                </button>
              </div>
              {product.stockQuantity && (
                <span className="text-xs text-gray-500">
                  {product.stockQuantity} available
                </span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              {/* Add to Cart Button */}
              <button
                onClick={handleAddToCart}
                disabled={!isInStock || isLoading}
                className={`w-full py-3 px-4 rounded-xl flex items-center justify-center gap-2 font-semibold text-sm transition-all transform hover:scale-105 ${
                  isInStock
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg'
                    : 'bg-gray-400 cursor-not-allowed text-white'
                }`}
              >
                <ShoppingCart className="w-4 h-4" />
                {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
              </button>

              {/* Secondary Actions */}
              <div className="flex gap-2">
                <button className="flex-1 p-3 border-2 border-gray-200 rounded-xl hover:border-red-300 hover:bg-red-50 transition-all group flex items-center justify-center gap-1">
                  <Heart className="w-4 h-4 text-gray-600 group-hover:text-red-500" />
                  <span className="text-xs text-gray-600 group-hover:text-red-500">Wishlist</span>
                </button>
                <button className="flex-1 p-3 border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all group flex items-center justify-center gap-1">
                  <Share2 className="w-4 h-4 text-gray-600 group-hover:text-blue-500" />
                  <span className="text-xs text-gray-600 group-hover:text-blue-500">Share</span>
                </button>
              </div>
            </div>
          </div>

          {/* Quick Features */}
          <div className="bg-gray-50 p-4 rounded-2xl">
            <h3 className="font-semibold text-gray-900 mb-3 text-sm">Why choose this?</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <Package className="w-3 h-3 text-green-600" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 text-xs">Free Shipping</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <Truck className="w-3 h-3 text-blue-600" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 text-xs">Fast Delivery</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                  <Shield className="w-3 h-3 text-purple-600" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 text-xs">Secure Payment</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Product Information Slides */}
      <div className="mt-8">
        {/* Slide Navigation */}
        <div className="flex justify-center mb-6">
          <div className="bg-white rounded-full p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              <button
                onClick={() => setActiveTab('description')}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === 'description'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Description
              </button>
              <button
                onClick={() => setActiveTab('specs')}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === 'specs'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Specifications
              </button>
              <button
                onClick={() => setActiveTab('reviews')}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === 'reviews'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Reviews
              </button>
              <button
                onClick={() => setActiveTab('shipping')}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === 'shipping'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Shipping
              </button>
            </div>
          </div>
        </div>

        {/* Slide Content */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-6">
            {activeTab === 'description' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Product Description</h3>
                  <div className="prose prose-gray max-w-none">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: product.description || product.short_description || 'No description available.'
                      }}
                      className="text-gray-700 leading-relaxed"
                    />
                  </div>
                </div>

                {/* Key Features in Description */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                  <h4 className="text-lg font-bold text-gray-900 mb-4">Key Features</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-700 text-sm">Premium quality materials</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-700 text-sm">Durable construction</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-700 text-sm">Easy to use design</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-700 text-sm">Excellent value for money</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'specs' && (
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center">
                    <AlertCircle className="w-3 h-3 text-white" />
                  </div>
                  Product Specifications
                </h3>
                {product.attributes && product.attributes.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {product.attributes.map((attr, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <dt className="font-semibold text-gray-900 mb-1">{attr.name}</dt>
                        <dd className="text-gray-600 text-sm">
                          {attr.options?.join(', ') || attr.value || 'Not specified'}
                        </dd>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <AlertCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>No specifications available for this product.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <ReviewList
                  productId={product.id}
                  currentUser={user}
                  onWriteReview={() => setShowReviewForm(true)}
                />
              </div>
            )}

            {activeTab === 'shipping' && (
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Shipping & Returns</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Delivery Options</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Standard Delivery: 3-5 business days</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Express Delivery: 1-2 business days</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Free shipping on orders over $50</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>Same-day delivery available in select areas</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Returns & Exchanges</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>30-day return policy</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Free returns on all orders</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span>Items must be in original condition</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Refunds processed within 5-7 business days</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Review Form Modal */}
      {showReviewForm && (
        <ReviewForm
          productId={product.id}
          productName={product.name}
          user={user}
          isOpen={showReviewForm}
          onSubmit={() => {
            setShowReviewForm(false);
            // Optionally refresh reviews or show success message
            toast.success('Review submitted successfully!');
          }}
          onCancel={() => setShowReviewForm(false)}
        />
      )}


    </div>
  );
};

export default ProductDetail;
