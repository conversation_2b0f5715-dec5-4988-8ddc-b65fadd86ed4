import { NextResponse } from 'next/server';
import { wooCommerceApi } from '../../../../lib/woocommerce.js';

export async function GET() {
  try {
    console.log('📦 Loading admin products...');

    // Get ALL products from WooCommerce (not just 100)
    console.log('📦 Fetching ALL products from WooCommerce...');
    
    // Get first page to check total count
    const firstPageResponse = await wooCommerceApi.getProducts({ per_page: 100, page: 1 });
    const firstPageProducts = firstPageResponse || [];
    
    let allProducts = [...firstPageProducts];
    let page = 2;
    let hasMoreProducts = firstPageProducts.length === 100;
    
    console.log(`📄 Page 1: ${firstPageProducts.length} products`);
    
    // Keep fetching until we get all products
    while (hasMoreProducts) {
      try {
        console.log(`📄 Fetching page ${page}...`);
        const pageResponse = await wooCommerceApi.getProducts({ per_page: 100, page });
        
        if (pageResponse && pageResponse.length > 0) {
          allProducts = [...allProducts, ...pageResponse];
          console.log(`📄 Page ${page}: ${pageResponse.length} products (Total so far: ${allProducts.length})`);
          
          hasMoreProducts = pageResponse.length === 100;
          page++;
        } else {
          hasMoreProducts = false;
        }
      } catch (error) {
        console.error(`❌ Error fetching page ${page}:`, error);
        hasMoreProducts = false;
      }
    }

    const products = allProducts;
    console.log(`✅ TOTAL PRODUCTS LOADED: ${products.length} (All pages fetched)`);

    return NextResponse.json({
      success: true,
      products,
      message: `Loaded ${products.length} products successfully`
    });

  } catch (error) {
    console.error('❌ Error loading products:', error);
    
    return NextResponse.json({
      success: false,
      products: [],
      error: error.message,
      message: 'Failed to load products'
    }, { status: 200 }); // Return 200 so frontend can handle gracefully
  }
}
