{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["isDynamicUsageError", "NEXT_CACHE_TAGS_HEADER", "NEXT_META_SUFFIX", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "hasNextSupport", "lazyRenderAppPage", "isBailoutToCSRError", "ExportedAppPageFiles", "exportAppPage", "req", "res", "page", "path", "pathname", "query", "renderOpts", "htmlFilepath", "debugOutput", "isDynamicError", "fileWriter", "isDefaultNotFound", "result", "html", "toUnchunkedString", "metadata", "flightData", "revalidate", "postponed", "fetchTags", "experimental", "ppr", "Error", "staticBailoutInfo", "description", "logDynamicUsageWarning", "stack", "replace", "headers", "Object", "assign", "getHeaders", "isParallelRoute", "test", "isNonSuccessfulStatusCode", "statusCode", "status", "undefined", "meta", "JSON", "stringify", "hasEmptyPrelude", "Boolean", "hasPostponed", "err", "missingSuspenseWithCSRBailout", "dynamicUsageDescription", "dynamicUsageStack", "store", "errMessage", "message", "substring", "indexOf", "console", "warn"], "mappings": "AAUA,SAASA,mBAAmB,QAAQ,oCAAmC;AACvE,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,mBAAmB,EACnBC,UAAU,QACL,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,iBAAiB,QAAQ,2DAA0D;AAC5F,SAASC,mBAAmB,QAAQ,+CAA8C;;UAEhEC;;;;;;GAAAA,yBAAAA;AAQlB,OAAO,eAAeC,cACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,QAAgB,EAChBC,KAAyB,EACzBC,UAAsB,EACtBC,YAAoB,EACpBC,WAAoB,EACpBC,cAAuB,EACvBC,UAAsB;IAEtB,IAAIC,oBAAoB;IACxB,6EAA6E;IAC7E,qJAAqJ;IACrJ,IAAIT,SAAS,oBAAoB;QAC/BS,oBAAoB;QACpBP,WAAW;IACb;IAEA,IAAI;QACF,MAAMQ,SAAS,MAAMhB,kBACnBI,KACAC,KACAG,UACAC,OACAC;QAGF,MAAMO,OAAOD,OAAOE,iBAAiB;QAErC,MAAM,EAAEC,QAAQ,EAAE,GAAGH;QACrB,MAAM,EAAEI,UAAU,EAAEC,aAAa,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE,GAAGJ;QAEjE,uDAAuD;QACvD,IAAIG,aAAa,CAACZ,WAAWc,YAAY,CAACC,GAAG,EAAE;YAC7C,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIL,eAAe,GAAG;YACpB,IAAIR,gBAAgB;gBAClB,MAAM,IAAIa,MACR,CAAC,+DAA+D,EAAEnB,KAAK,CAAC,CAAC;YAE7E;YACA,MAAM,EAAEoB,oBAAoB,CAAC,CAAC,EAAE,GAAGR;YAEnC,IAAIE,eAAe,KAAKT,gBAAee,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrEC,uBAAuB;oBACrBtB;oBACAqB,aAAaD,kBAAkBC,WAAW;oBAC1CE,OAAOH,kBAAkBG,KAAK;gBAChC;YACF;YAEA,OAAO;gBAAET,YAAY;YAAE;QACzB,OAGK,IAAI,CAACD,YAAY;YACpB,MAAM,IAAIM,MAAM,CAAC,uCAAuC,EAAEnB,KAAK,CAAC;QAClE,OAIK,IAAIG,WAAWc,YAAY,CAACC,GAAG,EAAE;YACpC,oEAAoE;YACpE,WAAW;YACX,MAAMX,8BAEJH,aAAaoB,OAAO,CAAC,WAAWlC,sBAChCuB;QAEJ,OAAO;YACL,kEAAkE;YAClE,MAAMN,qBAEJH,aAAaoB,OAAO,CAAC,WAAWjC,aAChCsB;QAEJ;QAEA,MAAMY,UAA+B;YAAE,GAAGb,SAASa,OAAO;QAAC;QAE3D,2EAA2E;QAC3E,6BAA6B;QAC7B,IAAItB,WAAWc,YAAY,CAACC,GAAG,EAAE;YAC/BQ,OAAOC,MAAM,CAACF,SAAS3B,IAAI8B,UAAU;QACvC;QAEA,IAAIZ,WAAW;YACbS,OAAO,CAACrC,uBAAuB,GAAG4B;QACpC;QAEA,iCAAiC;QACjC,MAAMT,mBAEJH,cACAM,QAAQ,IACR;QAGF,MAAMmB,kBAAkB,SAASC,IAAI,CAAC/B;QACtC,MAAMgC,4BAA4BjC,IAAIkC,UAAU,GAAG;QACnD,0EAA0E;QAC1E,kEAAkE;QAClE,YAAY;QACZ,IAAIC,SAA6B9B,WAAWc,YAAY,CAACC,GAAG,GACxDpB,IAAIkC,UAAU,GACdE;QAEJ,IAAI1B,mBAAmB;YACrB,2DAA2D;YAC3DyB,SAAS;QACX,OAAO,IAAIF,6BAA6B,CAACF,iBAAiB;YACxD,8DAA8D;YAC9DI,SAASnC,IAAIkC,UAAU;QACzB;QAEA,0CAA0C;QAC1C,MAAMG,OAAsB;YAC1BF;YACAR;YACAV;QACF;QAEA,MAAMR,mBAEJH,aAAaoB,OAAO,CAAC,WAAWnC,mBAChC+C,KAAKC,SAAS,CAACF,MAAM,MAAM;QAG7B,OAAO;YACL,iEAAiE;YACjEvB,UAAUpB,iBAAiB2C,OAAOD;YAClCI,iBAAiBC,QAAQxB,cAAcL,SAAS;YAChD8B,cAAcD,QAAQxB;YACtBD;QACF;IACF,EAAE,OAAO2B,KAAK;QACZ,IAAI,CAACtD,oBAAoBsD,MAAM;YAC7B,MAAMA;QACR;QAEA,0EAA0E;QAC1E,8BAA8B;QAC9B,IACEtC,WAAWc,YAAY,CAACyB,6BAA6B,IACrDhD,oBAAoB+C,MACpB;YACA,MAAMA;QACR;QAEA,IAAIpC,aAAa;YACf,MAAM,EAAEsC,uBAAuB,EAAEC,iBAAiB,EAAE,GAAG,AAACzC,WACrD0C,KAAK;YAERvB,uBAAuB;gBACrBtB;gBACAqB,aAAasB;gBACbpB,OAAOqB;YACT;QACF;QAEA,OAAO;YAAE9B,YAAY;QAAE;IACzB;AACF;AAEA,SAASQ,uBAAuB,EAC9BtB,IAAI,EACJqB,WAAW,EACXE,KAAK,EAKN;IACC,MAAMuB,aAAa,IAAI3B,MACrB,CAAC,iDAAiD,EAAEnB,KAAK,UAAU,EAAEqB,YAAY,CAAC;IAGpF,IAAIE,OAAO;QACTuB,WAAWvB,KAAK,GAAGuB,WAAWC,OAAO,GAAGxB,MAAMyB,SAAS,CAACzB,MAAM0B,OAAO,CAAC;IACxE;IAEAC,QAAQC,IAAI,CAACL;AACf"}