'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, MessageCircle } from 'lucide-react';

export default function Error({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application Error:', error);
    
    // In production, you would send this to your error tracking service
    // Example: Sentry, LogRocket, etc.
    if (process.env.NODE_ENV === 'production') {
      // sendErrorToService(error);
    }
  }, [error]);

  const handleRetry = () => {
    // Attempt to recover by trying to re-render the segment
    reset();
  };

  const getErrorMessage = () => {
    if (error?.message) {
      // Common error messages
      if (error.message.includes('fetch')) {
        return 'Unable to connect to our servers. Please check your internet connection and try again.';
      }
      if (error.message.includes('timeout')) {
        return 'The request took too long to complete. Please try again.';
      }
      if (error.message.includes('404')) {
        return 'The requested resource was not found.';
      }
      if (error.message.includes('500')) {
        return 'We\'re experiencing server issues. Please try again in a few moments.';
      }
      return error.message;
    }
    return 'An unexpected error occurred. We apologize for the inconvenience.';
  };

  const getErrorType = () => {
    if (error?.message?.includes('fetch') || error?.message?.includes('network')) {
      return 'Network Error';
    }
    if (error?.message?.includes('timeout')) {
      return 'Timeout Error';
    }
    if (error?.message?.includes('404')) {
      return 'Not Found';
    }
    if (error?.message?.includes('500')) {
      return 'Server Error';
    }
    return 'Application Error';
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full text-center">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="bg-red-100 rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center">
            <AlertTriangle className="w-12 h-12 text-red-600" />
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {getErrorType()}
          </h1>
          <h2 className="text-xl text-gray-600 mb-4">
            Oops! Something went wrong
          </h2>
          <p className="text-gray-500 leading-relaxed">
            {getErrorMessage()}
          </p>
        </div>

        {/* Error Details (Development only) */}
        {process.env.NODE_ENV === 'development' && error?.stack && (
          <div className="mb-8 p-4 bg-gray-100 rounded-lg text-left">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">
              Error Details (Development)
            </h3>
            <pre className="text-xs text-gray-600 overflow-auto max-h-32">
              {error.stack}
            </pre>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-4 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleRetry}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-5 h-5 mr-2" />
              Try Again
            </button>
            
            <Link
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Home className="w-5 h-5 mr-2" />
              Go Home
            </Link>
          </div>

          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            Refresh Page
          </button>
        </div>

        {/* Help Section */}
        <div className="border-t border-gray-200 pt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Need Help?
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <Link 
              href="/contact" 
              className="flex items-center justify-center p-3 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Support
            </Link>
            <Link 
              href="/faq" 
              className="flex items-center justify-center p-3 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              View FAQ
            </Link>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-blue-800 font-medium mb-2">What can you do?</h4>
          <ul className="text-blue-700 text-sm space-y-1 text-left">
            <li>• Check your internet connection</li>
            <li>• Try refreshing the page</li>
            <li>• Clear your browser cache</li>
            <li>• Try again in a few minutes</li>
            <li>• Contact support if the problem persists</li>
          </ul>
        </div>

        {/* Error ID for Support */}
        <div className="mt-6 text-xs text-gray-400">
          Error ID: {Date.now().toString(36).toUpperCase()}
        </div>
      </div>
    </div>
  );
}