<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Deal4u</title>
    <meta name="description" content="The page you're looking for doesn't exist. Return to Deal4u homepage or browse our products.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- PWA -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#3b82f6">
</head>
<body class="font-inter bg-gray-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <a href="index.html" class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">D</span>
                    </div>
                    <span class="text-xl font-bold text-gray-900">Deal4u</span>
                </a>
                
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html" class="text-gray-600 hover:text-blue-600 transition-colors">Home</a>
                    <a href="shop.html" class="text-gray-600 hover:text-blue-600 transition-colors">Shop</a>
                    <a href="categories.html" class="text-gray-600 hover:text-blue-600 transition-colors">Categories</a>
                    <a href="contact.html" class="text-gray-600 hover:text-blue-600 transition-colors">Contact</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <a href="cart.html" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i data-lucide="shopping-cart" class="w-6 h-6"></i>
                    </a>
                    <a href="login.html" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i data-lucide="user" class="w-6 h-6"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-lg w-full text-center">
            <!-- 404 Illustration -->
            <div class="mb-8">
                <div class="relative">
                    <!-- Large 404 Text -->
                    <div class="text-9xl font-bold text-gray-200 select-none">404</div>
                    
                    <!-- Floating Icons -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="relative">
                            <i data-lucide="search" class="w-16 h-16 text-blue-600 animate-bounce"></i>
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                <i data-lucide="x" class="w-4 h-4 text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Oops! Page Not Found
                </h1>
                <p class="text-lg text-gray-600 mb-6">
                    The page you're looking for doesn't exist or has been moved. 
                    Don't worry, let's get you back on track!
                </p>
            </div>

            <!-- Search Box -->
            <div class="mb-8">
                <div class="relative max-w-md mx-auto">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-5 w-5 text-gray-400"></i>
                    </div>
                    <input
                        type="text"
                        id="search-input"
                        placeholder="Search for products..."
                        class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        onkeypress="handleSearch(event)"
                    />
                    <button
                        onclick="performSearch()"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-600 hover:text-blue-700"
                    >
                        <i data-lucide="arrow-right" class="h-5 w-5"></i>
                    </button>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center mb-8">
                <a href="index.html" class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    <i data-lucide="home" class="w-5 h-5 mr-2"></i>
                    Go Home
                </a>
                <a href="shop.html" class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <i data-lucide="shopping-bag" class="w-5 h-5 mr-2"></i>
                    Browse Products
                </a>
            </div>

            <!-- Quick Links -->
            <div class="border-t border-gray-200 pt-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Pages</h3>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <a href="categories.html" class="text-blue-600 hover:text-blue-700 transition-colors">
                        <i data-lucide="grid-3x3" class="w-4 h-4 inline mr-2"></i>
                        Categories
                    </a>
                    <a href="about.html" class="text-blue-600 hover:text-blue-700 transition-colors">
                        <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>
                        About Us
                    </a>
                    <a href="contact.html" class="text-blue-600 hover:text-blue-700 transition-colors">
                        <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                        Contact
                    </a>
                    <a href="faq.html" class="text-blue-600 hover:text-blue-700 transition-colors">
                        <i data-lucide="help-circle" class="w-4 h-4 inline mr-2"></i>
                        FAQ
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">D</span>
                        </div>
                        <span class="text-xl font-bold">Deal4u</span>
                    </div>
                    <p class="text-gray-300 text-sm">
                        Your trusted partner for premium products at amazing deals.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-300 hover:text-white transition-colors text-sm">Home</a></li>
                        <li><a href="shop.html" class="text-gray-300 hover:text-white transition-colors text-sm">Shop</a></li>
                        <li><a href="categories.html" class="text-gray-300 hover:text-white transition-colors text-sm">Categories</a></li>
                        <li><a href="about.html" class="text-gray-300 hover:text-white transition-colors text-sm">About Us</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="mail" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i data-lucide="phone" class="w-5 h-5 text-blue-400"></i>
                            <span class="text-gray-300 text-sm">+447447186806</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">&copy; 2024 Deal4u. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello! I need help finding something on Deal4u." 
       class="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-40 hover:scale-110">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
        </svg>
    </a>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // Focus on search input
            document.getElementById('search-input').focus();
            
            // Add some animation to the 404 text
            animateFloatingIcons();
        });

        // Handle search on Enter key
        function handleSearch(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        // Perform search
        function performSearch() {
            const searchTerm = document.getElementById('search-input').value.trim();
            if (searchTerm) {
                // Redirect to shop page with search query
                window.location.href = `shop.html?search=${encodeURIComponent(searchTerm)}`;
            }
        }

        // Animate floating icons
        function animateFloatingIcons() {
            // Add some random floating animation to make the page more engaging
            const searchIcon = document.querySelector('.animate-bounce');
            
            // Add additional animations
            setInterval(() => {
                searchIcon.style.transform = `translateY(${Math.sin(Date.now() / 1000) * 5}px)`;
            }, 50);
        }

        // Track 404 errors (for analytics)
        function track404Error() {
            // In a real application, you would send this to your analytics service
            console.log('404 Error tracked:', {
                url: window.location.href,
                referrer: document.referrer,
                timestamp: new Date().toISOString()
            });
        }

        // Call tracking function
        track404Error();

        // Add some interactive elements
        document.addEventListener('mousemove', function(e) {
            const icons = document.querySelectorAll('[data-lucide]');
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            icons.forEach((icon, index) => {
                const rect = icon.getBoundingClientRect();
                const iconX = rect.left + rect.width / 2;
                const iconY = rect.top + rect.height / 2;
                
                const distance = Math.sqrt(Math.pow(mouseX - iconX, 2) + Math.pow(mouseY - iconY, 2));
                
                if (distance < 100) {
                    const scale = 1 + (100 - distance) / 1000;
                    icon.style.transform = `scale(${scale})`;
                } else {
                    icon.style.transform = 'scale(1)';
                }
            });
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Press 'h' to go home
            if (e.key === 'h' && !e.ctrlKey && !e.metaKey && e.target.tagName !== 'INPUT') {
                window.location.href = 'index.html';
            }
            
            // Press 's' to go to shop
            if (e.key === 's' && !e.ctrlKey && !e.metaKey && e.target.tagName !== 'INPUT') {
                window.location.href = 'shop.html';
            }
            
            // Press '/' to focus search
            if (e.key === '/') {
                e.preventDefault();
                document.getElementById('search-input').focus();
            }
        });

        // Show keyboard shortcuts hint
        setTimeout(() => {
            const hint = document.createElement('div');
            hint.className = 'fixed bottom-4 left-4 bg-gray-800 text-white text-xs px-3 py-2 rounded-lg opacity-75 z-30';
            hint.innerHTML = 'Tip: Press <kbd class="bg-gray-700 px-1 rounded">H</kbd> for Home, <kbd class="bg-gray-700 px-1 rounded">S</kbd> for Shop, <kbd class="bg-gray-700 px-1 rounded">/</kbd> to search';
            document.body.appendChild(hint);
            
            // Hide hint after 5 seconds
            setTimeout(() => {
                hint.style.opacity = '0';
                setTimeout(() => hint.remove(), 300);
            }, 5000);
        }, 2000);
    </script>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
        
        kbd {
            font-family: monospace;
            font-size: 0.8em;
        }
    </style>
</body>
</html>
