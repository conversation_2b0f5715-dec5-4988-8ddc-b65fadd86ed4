{"name": "pify", "version": "2.3.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": "sindresorhus/pify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava && npm run optimization-test", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "v8-natives": "0.0.2", "xo": "*"}}