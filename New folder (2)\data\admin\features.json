{"liveChat": {"enabled": false, "name": "Live Chat Support", "description": "Real-time customer support with WhatsApp integration", "category": "Customer Support", "settings": {"whatsappNumber": "+447447186806", "autoResponse": true, "businessHours": "9:00-18:00"}}, "productRecommendations": {"enabled": false, "name": "Smart Product Recommendations", "description": "AI-powered \"You might also like\" suggestions", "category": "AI Features", "settings": {"algorithm": "collaborative_filtering", "maxRecommendations": 6, "confidenceThreshold": 0.7}}, "loyaltyProgram": {"enabled": false, "name": "Loyalty & Rewards Program", "description": "Points, VIP tiers, and exclusive benefits", "category": "Customer Engagement", "settings": {"pointsPerDollar": 1, "welcomeBonus": 100, "referralBonus": 50}}, "arTryOn": {"enabled": false, "name": "AR Try-On Experience", "description": "Virtual try-on for clothes and accessories", "category": "Advanced Features", "settings": {"supportedCategories": ["clothing", "accessories", "jewelry"], "qualityLevel": "high"}}, "voiceSearch": {"enabled": false, "name": "Voice Search", "description": "Search products using voice commands", "category": "Advanced Features", "settings": {"language": "en-US", "sensitivity": "medium"}}, "socialCommerce": {"enabled": false, "name": "Social Commerce Integration", "description": "Share and buy directly from social media", "category": "Marketing", "settings": {"platforms": ["facebook", "instagram", "tiktok"], "autoPost": false}}, "pushNotifications": {"enabled": false, "name": "Push Notifications", "description": "Real-time updates and promotional alerts", "category": "Marketing", "settings": {"orderUpdates": true, "promotions": true, "backInStock": true}}, "oneClickReorder": {"enabled": false, "name": "<PERSON>-<PERSON><PERSON>", "description": "Instantly reorder previous purchases", "category": "Customer Experience", "settings": {"showInAccount": true, "emailReminders": true}}}