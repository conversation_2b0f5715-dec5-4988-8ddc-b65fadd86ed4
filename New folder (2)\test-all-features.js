#!/usr/bin/env node

// Comprehensive Feature Testing Script
const fs = require('fs');
const path = require('path');

console.log('🧪 DEAL4U COMPREHENSIVE FEATURE TESTING');
console.log('=====================================\n');

// Test Results Storage
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, status, details = '') {
  const result = status ? '✅ PASS' : '❌ FAIL';
  console.log(`${result} ${name}`);
  if (details) console.log(`   ${details}`);
  
  testResults.tests.push({ name, status, details });
  if (status) testResults.passed++;
  else testResults.failed++;
}

// 1. TEST FILE STRUCTURE
console.log('📁 Testing File Structure...');

const requiredFiles = [
  'app/admin/dashboard/page.js',
  'app/api/admin/dashboard-stats/route.js',
  'app/api/admin/toggle-feature/route.js',
  'app/api/woocommerce-cart/route.js',
  'lib/featuresManager.js',
  'lib/woocommerceFeatures.js',
  'components/ClientScripts.js',
  '.env.local'
];

requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  logTest(`File exists: ${file}`, exists);
});

// 2. TEST ENVIRONMENT CONFIGURATION
console.log('\n🔧 Testing Environment Configuration...');

try {
  const envPath = path.join(__dirname, '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const hasWooCommerceUrl = envContent.includes('NEXT_PUBLIC_WOOCOMMERCE_URL');
    const hasConsumerKey = envContent.includes('WOOCOMMERCE_CONSUMER_KEY');
    const hasConsumerSecret = envContent.includes('WOOCOMMERCE_CONSUMER_SECRET');
    
    logTest('WooCommerce URL configured', hasWooCommerceUrl);
    logTest('Consumer Key configured', hasConsumerKey);
    logTest('Consumer Secret configured', hasConsumerSecret);
    
    if (hasWooCommerceUrl && hasConsumerKey && hasConsumerSecret) {
      logTest('Environment configuration complete', true);
    } else {
      logTest('Environment configuration complete', false, 'Missing WooCommerce credentials');
    }
  } else {
    logTest('Environment file exists', false, '.env.local not found');
  }
} catch (error) {
  logTest('Environment configuration test', false, error.message);
}

// 3. TEST PACKAGE DEPENDENCIES
console.log('\n📦 Testing Package Dependencies...');

try {
  const packagePath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const requiredDeps = [
      '@woocommerce/woocommerce-rest-api',
      'axios',
      'next',
      'react',
      'lucide-react'
    ];
    
    requiredDeps.forEach(dep => {
      const hasDepInDeps = packageJson.dependencies && packageJson.dependencies[dep];
      const hasDepInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
      logTest(`Dependency: ${dep}`, hasDepInDeps || hasDepInDevDeps);
    });
    
    logTest('Package.json structure valid', true);
  } else {
    logTest('Package.json exists', false);
  }
} catch (error) {
  logTest('Package dependencies test', false, error.message);
}

// 4. TEST ADMIN DASHBOARD STRUCTURE
console.log('\n🎛️ Testing Admin Dashboard...');

try {
  const adminPagePath = path.join(__dirname, 'app/admin/dashboard/page.js');
  if (fs.existsSync(adminPagePath)) {
    const adminContent = fs.readFileSync(adminPagePath, 'utf8');
    
    const hasAuthentication = adminContent.includes('admin') && adminContent.includes('OTP');
    const hasFeatureToggle = adminContent.includes('toggleFeature');
    const hasDashboardStats = adminContent.includes('dashboard-stats');
    const hasSecureLogin = adminContent.includes('generateOTP');
    
    logTest('Admin authentication system', hasAuthentication);
    logTest('Feature toggle functionality', hasFeatureToggle);
    logTest('Dashboard statistics integration', hasDashboardStats);
    logTest('Secure login with OTP', hasSecureLogin);
  } else {
    logTest('Admin dashboard file exists', false);
  }
} catch (error) {
  logTest('Admin dashboard test', false, error.message);
}

// 5. TEST WOOCOMMERCE INTEGRATION
console.log('\n🛍️ Testing WooCommerce Integration...');

try {
  const wooFeaturesPath = path.join(__dirname, 'lib/woocommerceFeatures.js');
  if (fs.existsSync(wooFeaturesPath)) {
    const wooContent = fs.readFileSync(wooFeaturesPath, 'utf8');
    
    const hasProductRecommendations = wooContent.includes('getProductRecommendations');
    const hasLoyaltyProgram = wooContent.includes('getLoyaltyPoints');
    const hasReorderFunction = wooContent.includes('getReorderableOrders');
    const hasInventoryAlerts = wooContent.includes('getInventoryAlerts');
    const hasCartIntegration = wooContent.includes('addToCart');
    const hasCustomerAnalytics = wooContent.includes('getCustomerAnalytics');
    
    logTest('Product recommendations integration', hasProductRecommendations);
    logTest('Loyalty program integration', hasLoyaltyProgram);
    logTest('One-click reorder integration', hasReorderFunction);
    logTest('Inventory alerts integration', hasInventoryAlerts);
    logTest('Cart management integration', hasCartIntegration);
    logTest('Customer analytics integration', hasCustomerAnalytics);
  } else {
    logTest('WooCommerce features file exists', false);
  }
} catch (error) {
  logTest('WooCommerce integration test', false, error.message);
}

// 6. TEST FEATURES MANAGER
console.log('\n⚡ Testing Features Manager...');

try {
  const featuresPath = path.join(__dirname, 'lib/featuresManager.js');
  if (fs.existsSync(featuresPath)) {
    const featuresContent = fs.readFileSync(featuresPath, 'utf8');
    
    const hasLiveChat = featuresContent.includes('enableLiveChat');
    const hasProductRecs = featuresContent.includes('enableProductRecommendations');
    const hasLoyalty = featuresContent.includes('enableLoyaltyProgram');
    const hasPushNotifications = featuresContent.includes('enablePushNotifications');
    const hasSocialCommerce = featuresContent.includes('enableSocialCommerce');
    const hasOneClickReorder = featuresContent.includes('enableOneClickReorder');
    const hasVoiceSearch = featuresContent.includes('enableVoiceSearch');
    const hasARTryOn = featuresContent.includes('enableARTryOn');
    const hasWooCommerceIntegration = featuresContent.includes('wooCommerceFeatures');
    
    logTest('Live chat feature', hasLiveChat);
    logTest('Product recommendations feature', hasProductRecs);
    logTest('Loyalty program feature', hasLoyalty);
    logTest('Push notifications feature', hasPushNotifications);
    logTest('Social commerce feature', hasSocialCommerce);
    logTest('One-click reorder feature', hasOneClickReorder);
    logTest('Voice search feature', hasVoiceSearch);
    logTest('AR try-on feature', hasARTryOn);
    logTest('WooCommerce integration in features', hasWooCommerceIntegration);
  } else {
    logTest('Features manager file exists', false);
  }
} catch (error) {
  logTest('Features manager test', false, error.message);
}

// 7. TEST API ENDPOINTS
console.log('\n🔌 Testing API Endpoints...');

const apiEndpoints = [
  'app/api/admin/dashboard-stats/route.js',
  'app/api/admin/toggle-feature/route.js',
  'app/api/woocommerce-cart/route.js',
  'app/api/sync-wordpress-products/route.js'
];

apiEndpoints.forEach(endpoint => {
  const exists = fs.existsSync(path.join(__dirname, endpoint));
  if (exists) {
    try {
      const content = fs.readFileSync(path.join(__dirname, endpoint), 'utf8');
      const hasGET = content.includes('export async function GET');
      const hasPOST = content.includes('export async function POST');
      const hasErrorHandling = content.includes('try') && content.includes('catch');
      
      logTest(`${endpoint} - Structure`, hasGET || hasPOST);
      logTest(`${endpoint} - Error handling`, hasErrorHandling);
    } catch (error) {
      logTest(`${endpoint} - Content check`, false, error.message);
    }
  } else {
    logTest(`${endpoint} - Exists`, false);
  }
});

// 8. TEST CLIENT-SIDE INTEGRATION
console.log('\n🖥️ Testing Client-Side Integration...');

try {
  const clientScriptsPath = path.join(__dirname, 'components/ClientScripts.js');
  if (fs.existsSync(clientScriptsPath)) {
    const clientContent = fs.readFileSync(clientScriptsPath, 'utf8');
    
    const hasFeaturesManagerImport = clientContent.includes('featuresManager');
    const hasAdminAccess = clientContent.includes('admin');
    const hasKeyboardShortcut = clientContent.includes('Ctrl+Shift+A');
    
    logTest('Features manager client integration', hasFeaturesManagerImport);
    logTest('Admin access integration', hasAdminAccess);
    logTest('Keyboard shortcut for admin', hasKeyboardShortcut);
  } else {
    logTest('Client scripts file exists', false);
  }
} catch (error) {
  logTest('Client-side integration test', false, error.message);
}

// 9. TEST CONFIGURATION FILES
console.log('\n⚙️ Testing Configuration...');

try {
  // Test if config directory would be created
  const configDir = path.join(__dirname, 'config');
  logTest('Config directory structure ready', true, 'Will be created automatically');
  
  // Test features configuration structure
  const toggleFeaturePath = path.join(__dirname, 'app/api/admin/toggle-feature/route.js');
  if (fs.existsSync(toggleFeaturePath)) {
    const toggleContent = fs.readFileSync(toggleFeaturePath, 'utf8');
    const hasDefaultFeatures = toggleContent.includes('DEFAULT_FEATURES');
    const hasFeatureStorage = toggleContent.includes('features.json');
    
    logTest('Default features configuration', hasDefaultFeatures);
    logTest('Feature storage system', hasFeatureStorage);
  }
} catch (error) {
  logTest('Configuration test', false, error.message);
}

// 10. GENERATE TEST REPORT
console.log('\n📊 TEST SUMMARY');
console.log('===============');
console.log(`✅ Passed: ${testResults.passed}`);
console.log(`❌ Failed: ${testResults.failed}`);
console.log(`📊 Total: ${testResults.tests.length}`);
console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);

if (testResults.failed > 0) {
  console.log('\n❌ FAILED TESTS:');
  testResults.tests
    .filter(test => !test.status)
    .forEach(test => {
      console.log(`   • ${test.name}${test.details ? ` - ${test.details}` : ''}`);
    });
}

// 11. NEXT STEPS RECOMMENDATIONS
console.log('\n🚀 NEXT STEPS:');

if (testResults.passed >= testResults.tests.length * 0.8) {
  console.log('✅ System is ready for testing!');
  console.log('');
  console.log('🎯 TO TEST YOUR FEATURES:');
  console.log('1. Start development server: npm run dev');
  console.log('2. Open: http://localhost:3000/admin/dashboard');
  console.log('3. Login: admin / admin + OTP');
  console.log('4. Toggle features ON in the Features tab');
  console.log('5. Visit your main website to see features in action');
  console.log('');
  console.log('⌨️ QUICK ACCESS:');
  console.log('• Press Ctrl+Shift+A anywhere on your site for admin access');
  console.log('• Look for the ⚙️ button in bottom-left corner');
  console.log('• Features activate instantly when toggled ON');
} else {
  console.log('⚠️ Some issues need to be resolved before testing.');
  console.log('');
  console.log('🔧 RECOMMENDED FIXES:');
  if (testResults.tests.find(t => t.name.includes('Environment') && !t.status)) {
    console.log('• Check your .env.local file has correct WooCommerce credentials');
  }
  if (testResults.tests.find(t => t.name.includes('Dependencies') && !t.status)) {
    console.log('• Run: npm install to install missing dependencies');
  }
  if (testResults.tests.find(t => t.name.includes('File exists') && !t.status)) {
    console.log('• Some required files are missing - check the failed tests above');
  }
}

console.log('\n🎉 Testing Complete!');

// Save test results
const reportPath = path.join(__dirname, 'test-results.json');
fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
console.log(`📄 Detailed results saved to: ${reportPath}`);
