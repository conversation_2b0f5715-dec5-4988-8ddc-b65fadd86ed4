# Quick cPanel Deployment Checklist

## Before You Start
- [ ] Ensure you have cPanel access with Node.js support
- [ ] Check your hosting provider's supported Node.js versions
- [ ] Have your domain/subdomain ready

## Step 1: Prepare Your Application
```bash
# 1. Check for issues
npm run deploy:check

# 2. Fix any critical issues found

# 3. Prepare deployment files
npm run deploy:prepare
```

## Step 2: cPanel Setup
- [ ] Login to cPanel
- [ ] Go to "Node.js Apps" or "Node.js Selector"
- [ ] Click "Create Application"
- [ ] Fill in the details:
  - **Node.js Version**: 18.x (or latest supported)
  - **Application Root**: `/public_html/your-app-name`
  - **Application URL**: `your-domain.com/your-app-name`
  - **Startup File**: `server.js`
- [ ] Click "Create"

## Step 3: Upload Files
- [ ] Go to File Manager in cPanel
- [ ] Navigate to your Application Root directory
- [ ] Upload ALL files from the `cpanel-ready` folder
- [ ] Extract if uploaded as ZIP

## Step 4: Install Dependencies
**Option A - Via cPanel Interface:**
- [ ] Go back to Node.js Apps
- [ ] Click on your application
- [ ] Go to "Package.json" tab
- [ ] Click "Run NPM Install"

**Option B - Via Terminal (if available):**
```bash
cd /home/<USER>/public_html/your-app-name
npm install --production
```

## Step 5: Configure Environment
- [ ] In Node.js Apps, click on your app
- [ ] Go to "Environment Variables" tab
- [ ] Add: `NODE_ENV` = `production`
- [ ] Save changes

## Step 6: Start Application
- [ ] Click "Start App" button
- [ ] Wait for status to show "Running"
- [ ] Check logs for any errors

## Step 7: Test Your Application
- [ ] Visit your application URL
- [ ] Test main pages and functionality
- [ ] Check browser console for errors
- [ ] Test on mobile devices

## Common Issues & Quick Fixes

### ❌ "Application failed to start"
- Check Node.js version compatibility
- Verify startup file is set to `server.js`
- Check application logs for specific errors

### ❌ "Cannot find module"
- Ensure module is in `dependencies`, not `devDependencies`
- Run NPM install again
- Check if all files were uploaded correctly

### ❌ "Port already in use"
- Don't set PORT environment variable (cPanel handles this)
- Ensure server.js uses `process.env.PORT`

### ❌ "404 Not Found"
- Check if static files were uploaded to correct directory
- Verify Application Root path is correct
- Check file permissions

### ❌ "Internal Server Error"
- Check application logs in cPanel
- Verify all dependencies are installed
- Check for syntax errors in server.js

## Performance Tips
- [ ] Enable compression (already included in server.js)
- [ ] Optimize images before upload
- [ ] Use CDN for static assets if possible
- [ ] Monitor application performance in cPanel

## Security Checklist
- [ ] Use HTTPS if available
- [ ] Set secure environment variables
- [ ] Regular backups
- [ ] Keep dependencies updated

## Need Help?
1. Check the detailed guide: `CPANEL_DEPLOYMENT_GUIDE.md`
2. Run troubleshooting: `npm run deploy:check`
3. Check cPanel application logs
4. Contact your hosting provider's support

---
**Remember**: Each hosting provider may have slightly different cPanel interfaces, but the general process remains the same.
