{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "names": ["fs", "path", "Log", "tar", "WritableStream", "require", "getRegistry", "getCacheDirectory", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "process", "env", "extractFromTar", "x", "file", "join", "cwd", "strip", "existsSync", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "downloadUrl", "fetch", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "close", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "downloadNativeNextSwc", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "downloadWasmSwc", "wasmDirectory", "variant"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,SAAS,yBAAwB;AACxC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAGnC,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AAEjE,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBN,kBACrB,YACAO,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBb,IAAIc,CAAC,CAAC;YACJC,MAAMjB,KAAKkB,IAAI,CAACN,gBAAgBD;YAChCQ,KAAKV;YACLW,OAAO;QACT;IAEF,IAAI,CAACrB,GAAGsB,UAAU,CAACrB,KAAKkB,IAAI,CAACN,gBAAgBD,eAAe;QAC1DV,IAAIqB,IAAI,CAAC,CAAC,wBAAwB,EAAEZ,QAAQ,GAAG,CAAC;QAChD,MAAMX,GAAGwB,QAAQ,CAACC,KAAK,CAACZ,gBAAgB;YAAEa,WAAW;QAAK;QAC1D,MAAMC,WAAW1B,KAAKkB,IAAI,CACxBN,gBACA,CAAC,EAAED,YAAY,MAAM,EAAEgB,KAAKC,GAAG,GAAG,CAAC;QAGrC,MAAMC,WAAWxB;QAEjB,MAAMyB,cAAc,CAAC,EAAED,SAAS,EAAEnB,QAAQ,GAAG,EAAEC,YAAY,CAAC;QAE5D,MAAMoB,MAAMD,aAAaE,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBlC,IAAImC,KAAK,CAAC,CAAC,oCAAoC,EAAEN,YAAY,CAAC;YAChE;YAEA,IAAI,CAACI,IAAI;gBACP,MAAM,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,CAAC,CAAC;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,IAAIE,MAAM;YAClB;YACA,MAAME,mBAAmBxC,GAAGyC,iBAAiB,CAACd;YAC9C,OAAOS,KAAKM,MAAM,CAChB,IAAItC,eAAe;gBACjBuC,OAAMC,KAAK;oBACT,OAAO,IAAIC,QAAc,CAACC,SAASC,SACjCP,iBAAiBG,KAAK,CAACC,OAAO,CAACP;4BAC7B,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;gBACAE;oBACE,OAAO,IAAIH,QAAc,CAACC,SAASC,SACjCP,iBAAiBQ,KAAK,CAAC,CAACX;4BACtB,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;YACF;QAEJ;QACA,MAAM9C,GAAGwB,QAAQ,CAACyB,MAAM,CAACtB,UAAU1B,KAAKkB,IAAI,CAACN,gBAAgBD;IAC/D;IACA,MAAMI;IAEN,MAAMkC,aAAa,MAAMlD,GAAGwB,QAAQ,CAAC2B,OAAO,CAACtC;IAE7C,IAAIqC,WAAWE,MAAM,GAAG5C,uBAAuB;QAC7C0C,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAG5C,sBAAuB;YAClE,MAAMR,GAAGwB,QAAQ,CACdkC,MAAM,CAACzD,KAAKkB,IAAI,CAACN,gBAAgBqC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,sBACpBC,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMpD,UAAU,CAAC,UAAU,EAAEqD,OAAO,CAAC;QACrC,MAAMpD,cAAc,CAAC,EAAED,QAAQsD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMnD,kBAAkBT,KAAKkB,IAAI,CAAC2C,mBAAmBnD;QAErD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;YAAEgB,WAAW;QAAK;QAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEA,OAAO,eAAesD,gBACpBL,OAAe,EACfM,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAMzD,UAAU,CAAC,eAAe,EAAEyD,QAAQ,CAAC;IAC3C,MAAMxD,cAAc,CAAC,EAAED,QAAQsD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMnD,kBAAkBT,KAAKkB,IAAI,CAACgD,eAAexD;IAEjD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;QAAEgB,WAAW;IAAK;IAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;AAChD"}