{"version": 3, "sources": ["../../src/trace/trace.test.ts"], "names": ["describe", "beforeEach", "initializeTraceState", "lastId", "shouldSaveTraceEvents", "clearTraceEvents", "it", "tmpDir", "mkdtemp", "join", "tmpdir", "setGlobal", "root", "trace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "traceAsyncFn", "delayedPromise", "Promise", "resolve", "setTimeout", "stop", "traceEvents", "getTraceEvents", "expect", "length", "toEqual", "name", "flushAllTraces", "traceFilename", "serializedTraces", "JSON", "parse", "readFile", "toMatchObject", "id", "parentId", "startTime", "any", "Number", "timestamp", "duration", "tags", "getId", "traceState", "exportTraceState", "span", "clone", "stringify", "defaultParentSpanId", "worker1Span", "worker1Traces", "worker2Span", "worker2Traces", "recordTraceEvents", "allTraces", "firstSpan", "worker1<PERSON><PERSON>d", "worker1Root", "worker2<PERSON><PERSON>d", "worker2Root", "lastChildSpan", "rootSpan", "toBeUndefined"], "mappings": ";;;;0BAAkC;sBACb;oBACE;wBACG;uBASnB;AAEPA,SAAS,SAAS;IAChBC,WAAW;QACTC,IAAAA,2BAAoB,EAAC;YACnBC,QAAQ;YACRC,uBAAuB;QACzB;QACAC,IAAAA,uBAAgB;IAClB;IAEAL,SAAS,UAAU;QACjBM,GAAG,0BAA0B;YAC3B,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACC,IAAAA,UAAI,EAACC,IAAAA,UAAM,KAAI;YAC5CC,IAAAA,iBAAS,EAAC,WAAWJ;YACrBI,IAAAA,iBAAS,EAAC,SAAS;YAEnB,MAAMC,OAAOC,IAAAA,YAAK,EAAC,aAAaC,WAAW;gBACzC,YAAY;YACd;YACAF,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5C,MAAMJ,KAAKG,UAAU,CAAC,oBAAoBE,YAAY,CAAC;gBACrD,MAAMC,iBAAiB,IAAIC,QAAQ,CAACC;oBAClCC,WAAWD,SAAS;gBACtB;gBACA,MAAMF;YACR;YACAN,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YAEpC,4DAA4D;YAC5D,MAAME,IAAAA,qBAAc;YACpB,MAAMC,gBAAgBrB,IAAAA,UAAI,EAACF,QAAQ;YACnC,MAAMwB,mBAAmBC,KAAKC,KAAK,CACjC,MAAMC,IAAAA,kBAAQ,EAACJ,eAAe;YAEhCL,OAAOM,kBAAkBI,aAAa,CAAC;gBACrC;oBACEC,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNU,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM;wBACJ,YAAY;oBACd;gBACF;aACD;QACH;IACF;IAEA3C,SAAS,UAAU;QACjBM,GAAG,uCAAuC;YACxC,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBY,OAAOb,KAAKgC,KAAK,IAAIjB,OAAO,CAAC;YAC7B,MAAMkB,aAAaC,IAAAA,uBAAgB;YACnCrB,OAAOoB,WAAW1C,MAAM,EAAEwB,OAAO,CAAC;YAClCzB,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;YACV;YACA,MAAM4C,OAAOlC,IAAAA,YAAK,EAAC;YACnBY,OAAOsB,KAAKH,KAAK,IAAIjB,OAAO,CAAC;QAC/B;QAEArB,GAAG,0CAA0C;YAC3C,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5CJ,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnC,sEAAsE;YACtE,qEAAqE;YACrE,uBAAuB;YACvB,MAAMqB,QAAQhB,KAAKC,KAAK,CAACD,KAAKiB,SAAS,CAAC1B;YACxCE,OAAOuB,OAAOrB,OAAO,CAACJ;QACxB;QAEAjB,GAAG,sDAAsD;YACvD,mEAAmE;YACnE,yDAAyD;YACzD,mEAAmE;YACnEJ,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR+C,qBAAqB;gBACrB9C,uBAAuB;YACzB;YACA,MAAM+C,cAActC,IAAAA,YAAK,EAAC;YAC1BsC,YAAYpC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DmC,YAAY7B,IAAI;YAChB,MAAM8B,gBAAgB5B,IAAAA,qBAAc;YACpCC,OAAO2B,cAAc1B,MAAM,EAAEC,OAAO,CAAC;YAErC,8BAA8B;YAC9BtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR+C,qBAAqB;gBACrB9C,uBAAuB;YACzB;YACA,MAAMiD,cAAcxC,IAAAA,YAAK,EAAC;YAC1BwC,YAAYtC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DqC,YAAY/B,IAAI;YAChB,MAAMgC,gBAAgB9B,IAAAA,qBAAc;YACpCC,OAAO6B,cAAc5B,MAAM,EAAEC,OAAO,CAAC;YAErC,oEAAoE;YACpE,oBAAoB;YACpBtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACRC,uBAAuB;YACzB;YACA,MAAMQ,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,mBAAmBC,OAAO,CAAC,IAAM;YACjDuC,IAAAA,wBAAiB,EAACH;YAClB3B,OAAOqB,IAAAA,uBAAgB,IAAG3C,MAAM,EAAEwB,OAAO,CAAC;YAC1C4B,IAAAA,wBAAiB,EAACD;YAClB7B,OAAOqB,IAAAA,uBAAgB,IAAG3C,MAAM,EAAEwB,OAAO,CAAC;YAC1Cf,KAAKG,UAAU,CAAC,sBAAsBC,OAAO,CAAC,IAAM;YACpDJ,KAAKU,IAAI;YAET,6CAA6C;YAC7C,MAAMkC,YAAYhC,IAAAA,qBAAc;YAChCC,OAAO+B,UAAU9B,MAAM,EAAEC,OAAO,CAAC;YACjC,MAAM8B,YAAYD,SAAS,CAAC,EAAE;YAC9B/B,OAAOgC,UAAU7B,IAAI,EAAED,OAAO,CAAC;YAC/BF,OAAOgC,UAAUrB,EAAE,EAAET,OAAO,CAAC;YAC7BF,OAAOgC,UAAUpB,QAAQ,EAAEV,OAAO,CAAC;YAEnC,MAAM+B,eAAeF,SAAS,CAAC,EAAE;YACjC/B,OAAOiC,aAAa9B,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOiC,aAAatB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOiC,aAAarB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAMgC,cAAcH,SAAS,CAAC,EAAE;YAChC/B,OAAOkC,YAAY/B,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOkC,YAAYvB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOkC,YAAYtB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMiC,eAAeJ,SAAS,CAAC,EAAE;YACjC/B,OAAOmC,aAAahC,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOmC,aAAaxB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOmC,aAAavB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAMkC,cAAcL,SAAS,CAAC,EAAE;YAChC/B,OAAOoC,YAAYjC,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOoC,YAAYzB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOoC,YAAYxB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMmC,gBAAgBN,SAAS,CAAC,EAAE;YAClC/B,OAAOqC,cAAclC,IAAI,EAAED,OAAO,CAAC;YACnCF,OAAOqC,cAAc1B,EAAE,EAAET,OAAO,CAAC;YACjCF,OAAOqC,cAAczB,QAAQ,EAAEV,OAAO,CAAC;YAEvC,MAAMoC,WAAWP,SAAS,CAAC,EAAE;YAC7B/B,OAAOsC,SAASnC,IAAI,EAAED,OAAO,CAAC;YAC9BF,OAAOsC,SAAS3B,EAAE,EAAET,OAAO,CAAC;YAC5BF,OAAOsC,SAAS1B,QAAQ,EAAE2B,aAAa;QACzC;IACF;AACF"}