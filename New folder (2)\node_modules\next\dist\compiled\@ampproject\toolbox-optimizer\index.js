(()=>{var __webpack_modules__={7315:(e,t,r)=>{"use strict";e.exports={FileSystemCache:r(4669),MaxAge:r(6564),oneBehindFetch:r(8414),log:r(277),isAmp:r(3471)}},4669:(e,t,r)=>{"use strict";const i=r(7147).promises;const{existsSync:n,mkdirSync:s}=r(7147);const a=r(6113);const o=r(2037);const l=r(277);const c=r(7330);const u=r(1017);const d={log:l,maxItems:50};class FileSystemCache{static create(e={}){const t=Object.assign(d,e);try{if(!t.baseDir){t.baseDir=u.join(o.tmpdir(),"ampproject-toolbox-optimizer")}if(!n(t.baseDir)){s(t.baseDir)}}catch(e){l.debug("No filesystem access, falling back to in-memory cache only",e);return new c(t.maxItems)}return new FileSystemCache(t)}constructor(e){this.opts=e;this.cache=new c(e.maxItems)}async get(e,t=null){let r=this.cache.get(e);if(r){return r}const n=this.createCacheFileName(e);try{const t=await i.readFile(n,"utf-8");r=JSON.parse(t);this.cache.set(e,r)}catch(e){r=t}return r}async set(e,t){try{this.cache.set(e,t);const r=this.createCacheFileName(e);return i.writeFile(r,JSON.stringify(t,null,""),"utf-8")}catch(e){this.opts.log.error("Could not write cache file",e)}}async clear(){const e=this.opts.baseDir;if(!n(e)){return}let t=await i.readdir(e,{withFileTypes:true});await Promise.all(t.map((t=>{let r=u.join(e,t.name);return t.isFile()&&t.name.endsWith(".json")?i.unlink(r):Promise.resolve()})))}createCacheFileName(e){const t=a.createHash("md5").update(e).digest("hex");return u.join(this.opts.baseDir,t+".json")}async deleteDir_(e){let t=await i.readdir(e,{withFileTypes:true});await Promise.all(t.map((t=>{let r=u.join(e,t.name);return t.isDirectory()?this.deleteDir_(r):i.unlink(r)})));await i.rmdir(e)}}e.exports=FileSystemCache},3471:e=>{"use strict";const t="<html";e.exports=function isAmp(e){let r=e.indexOf(t);if(r===-1){return false}r+=t.length;const i=e.indexOf(">",r);if(i===-1){return false}let n=e.substring(r,i).split(/\s+/);n=n.map((e=>e.split("=")[0]));return n.includes("amp")||n.includes("⚡")}},277:e=>{"use strict";class Log{constructor(e="",t=false,r=console){this.tag_=e;this.verbose_=t;this.prefix_=e?this.inverse_(e):"";this.output_=r}debug(e,...t){if(!this.verbose_){return}this.log_(this.output_.log,this.dim_(e),t)}log(e,...t){this.info(e,...t)}info(e,...t){this.log_(this.output_.log,e,...t)}success(e,...t){this.log_(this.output_.log,this.green_("SUCCESS "+e),t)}warn(e,...t){this.log_(this.output_.warn,this.yellow_("WARNING "+e),t)}error(e,...t){this.output_.log("\n");this.log_(this.output_.error,this.red_("ERROR "+e),t);this.output_.log("\n")}verbose(e=true){this.verbose_=!!e}tag(e){if(this.tag_){e=this.tag_+" "+e}return new Log(e,this.verbose_,this.output_)}log_(e,t,r){if(this.prefix_){t=this.prefix_+" "+t}if(r){e(...[t].concat(r))}else{e(t)}}inverse_(e){return`[7m${e}[0m`}dim_(e){return`[36m${e}[0m`}green_(e){return`[32m${e}[0m`}yellow_(e){return`[33m${e}[0m`}red_(e){return`[31m${e}[0m`}}e.exports=new Log},6564:e=>{"use strict";const t={value:0,isExpired:()=>true};class MaxAge{static zero(){return t}static parse(e){if(!e){return MaxAge.zero()}const t=e.match(/max-age=([0-9]+)[^0-9]?/i);if(!t){return MaxAge.zero()}return MaxAge.create(t[1])}static create(e){if(!Number.isInteger(e)){e=parseInt(e,10)}return new MaxAge(Date.now(),e)}static fromObject(e,t){return new MaxAge(e,t)}constructor(e,t){this.timestampInMs_=e;this.value=t}isExpired(e=Date.now()){const t=this.value*1e3;return this.timestampInMs_+t<e}toObject(){return{timestampInMs:this.timestampInMs_,maxAge:this.maxAge}}}e.exports=MaxAge},8414:(e,t,r)=>{"use strict";let i=r(3114);const n=r(6564);const s=new Map;async function oneBehindFetch(e,t){let r=s.get(e);if(!r){r={maxAge:Promise.resolve(n.zero())};s.set(e,r)}const a=await r.maxAge;if(!a.isExpired()){const e=await r.responsePromise;return e.clone()}const o=r.responsePromise;const l=i(e,t);r={responsePromise:l,maxAge:l.then((e=>n.parse(e.headers.get("cache-control"))))};s.set(e,r);const c=o||l;const u=await c;return u.clone()}oneBehindFetch.clearCache=()=>s.clear();oneBehindFetch.setDelegate=e=>i=e;e.exports=oneBehindFetch},8483:(e,t,r)=>{"use strict";const{DomTransformer:i,TRANSFORMATIONS_AMP_FIRST:n,TRANSFORMATIONS_PAIRED_AMP:s,TRANSFORMATIONS_MINIMAL:a,DEFAULT_CONFIG:o}=r(3338);const l=r(2547);e.exports={create:(e=o)=>new i(e),TRANSFORMATIONS_AMP_FIRST:n,TRANSFORMATIONS_MINIMAL:a,TRANSFORMATIONS_PAIRED_AMP:s,DEFAULT_CONFIG:o,NodeUtils:l}},8846:(e,t,r)=>{"mode strict";const{hasAttribute:i}=r(2547);e.exports={AMP_TAGS:["amp","⚡","⚡4ads","amp4ads","⚡4email","amp4email"],AMP_CACHE_HOST:"https://cdn.ampproject.org",AMP_VALIDATION_RULES_URL:"https://cdn.ampproject.org/v0/validator.json",AMP_FORMATS:["AMP","AMP4EMAIL","AMP4ADS"],AMP_RUNTIME_CSS_PATH:"/v0.css",appendRuntimeVersion:(e,t)=>e+"/rtv/"+t,isTemplate:e=>{if(!e){return false}if(e.tagName==="template"){return true}if(e.tagName==="script"&&i(e,"template")&&e.attribs.template==="amp-mustache"){return true}return false},isAmpStory:e=>{for(const t of e.children){if(isAmpScriptImport(t)&&t.attribs["custom-element"]==="amp-story"){return true}}return false}};function isAmpScriptImport(e){if(e.tagName!=="script"){return false}if(!e.attribs){return false}const t=e.attribs["custom-element"]||e.attribs["custom-template"]||"";if(!t.startsWith("amp-")){return false}return true}},3338:(e,t,r)=>{function __ncc_wildcard$0(e){if(e==="AddAmpLink")return r(4311);else if(e==="AddBlurryImagePlaceholders")return r(4949);else if(e==="AddMandatoryTags")return r(8256);else if(e==="AddTransformedFlag")return r(9494);else if(e==="AmpBoilerplateErrorHandler")return r(4975);else if(e==="AmpBoilerplateTransformer")return r(9097);else if(e==="AmpScriptCsp")return r(5557);else if(e==="ApplyCommonAttributes")return r(6070);else if(e==="ApplyLayout")return r(9464);else if(e==="AutoExtensionImporter")return r(5414);else if(e==="GoogleFontsPreconnect")return r(5141);else if(e==="Markdown")return r(9433);else if(e==="MinifyHtml")return r(1031);else if(e==="OptimizeAmpBind")return r(8046);else if(e==="OptimizeHeroImages")return r(6037);else if(e==="OptimizeImages")return r(714);else if(e==="PreloadHeroImage")return r(743);else if(e==="PreloadImages")return r(3842);else if(e==="PruneDuplicateResourceHints")return r(3144);else if(e==="RemoveAmpAttribute")return r(1865);else if(e==="RemoveCspNonce")return r(9401);else if(e==="ReorderHeadTransformer")return r(707);else if(e==="RewriteAmpUrls")return r(7185);else if(e==="SeparateKeyframes")return r(2079);else if(e==="ServerSideRendering")return r(4626)}"use strict";const i=r(3484);const n=r(255);let s=r(3114);const a=r(4178);const o=r(2412);const l=r(351);const c=["AddMandatoryTags","Markdown","AutoExtensionImporter","OptimizeImages","OptimizeHeroImages","OptimizeAmpBind","ServerSideRendering","AmpBoilerplateTransformer","RewriteAmpUrls","AmpBoilerplateErrorHandler","GoogleFontsPreconnect","PruneDuplicateResourceHints","AddBlurryImagePlaceholders","SeparateKeyframes","ReorderHeadTransformer","AddTransformedFlag","MinifyHtml","AmpScriptCsp"];const u=["AutoExtensionImporter","AddAmpLink","OptimizeImages","OptimizeHeroImages","OptimizeAmpBind","ServerSideRendering","RemoveAmpAttribute","AmpBoilerplateTransformer","RewriteAmpUrls","AmpBoilerplateErrorHandler","GoogleFontsPreconnect","PruneDuplicateResourceHints","AddBlurryImagePlaceholders","SeparateKeyframes","AddTransformedFlag","ReorderHeadTransformer","MinifyHtml","AmpScriptCsp"];const d=["OptimizeImages","OptimizeHeroImages","OptimizeAmpBind","ServerSideRendering","AmpBoilerplateTransformer","AmpBoilerplateErrorHandler","RewriteAmpUrls","GoogleFontsPreconnect","PruneDuplicateResourceHints","ReorderHeadTransformer","AddTransformedFlag"];const f={cache:l,fetch:s,log:n,profile:false,profiler:e=>{console.time(e);return()=>{console.timeEnd(e)}},transformations:c,verbose:false};class DomTransformer{constructor(e=f){this.setConfig(e)}async transformHtml(e,t){async function transform(){const r=await this.doProfile("parsing",(()=>i.parse(e)));await this.doProfile("transform",(()=>this.transformTree(r,t)));return this.doProfile("serialization",(()=>i.serialize(r)))}return await this.doProfile("overall",(()=>transform.call(this)))}async doProfile(e,t){if(!this.config.profile){return t()}const r=this.config.profiler(e);try{return await t()}finally{r()}}async transformTree(e,t={}){n.verbose(t.verbose||false);const r=await o(this.config,t);for(const t of this.transformers_){const i=this.getTransformerId(t);await this.doProfile(i,(()=>t.transform(e,r)))}}setConfig(e){this.config=Object.assign({},f,e);if(!this.config.runtimeVersion){this.config.runtimeVersion=new a(this.config.fetch)}n.verbose(this.config.verbose);this.initTransformers_(this.config)}initTransformers_(e){this.transformers_=e.transformations.map((t=>{if(typeof t==="string"){t=__ncc_wildcard$0(t)}return new t(e)}))}getTransformerId(e){return e.constructor?e.constructor.name:"custom"}}e.exports={DomTransformer:DomTransformer,DEFAULT_CONFIG:f,TRANSFORMATIONS_AMP_FIRST:c,TRANSFORMATIONS_PAIRED_AMP:u,TRANSFORMATIONS_MINIMAL:d}},9170:e=>{"mode strict";e.exports={isRenderDelayingExtension:function(e){if(e.tagName!=="script"){return false}const t=e.attribs["custom-element"];return t==="amp-dynamic-css-classes"||t==="amp-experiment"||t==="amp-story"},isCustomElement:function(e){return e.tagName&&e.tagName.startsWith("amp-")}}},7041:e=>{"use strict";function findMetaViewport(e){for(let t=e.firstChild;t!==null;t=t.nextSibling){if(t.tagName==="meta"&&t.attribs.name==="viewport"){return t}}return null}function findRuntimeScript(e){for(let t=e.firstChild;t!==null;t=t.nextSibling){if(t.tagName==="script"&&t.attribs.src&&t.attribs.src.match(/^https:\/\/.+\/v0(\.js|\.mjs)$/)){return t}}return null}function skipNodeAndChildren(e){if(!e){return null}else if(e.nextSibling){return e.nextSibling}return skipNodeAndChildren(e.parent)}e.exports={findMetaViewport:findMetaViewport,findRuntimeScript:findRuntimeScript,skipNodeAndChildren:skipNodeAndChildren}},2547:(e,t,r)=>{"use strict";const{Element:i,DataNode:n}=r(945);const{removeElement:s,append:a,prepend:o}=r(9065);const nextNode=function(e){const t=e.firstChild;if(t){return t}let r=e;while(r){const e=r.nextSibling;if(e){return e}r=r.parent}return null};const remove=function(e){s(e)};const appendChild=function(e,t){if(!t){return}t.parent=e;if(e.children.push(t)!==1){const r=e.children[e.children.length-2];r.next=t;t.prev=r;t.next=null}};const insertBefore=function(e,t,r){if(r){o(r,t);return}appendChild(e,t)};const insertAfter=function(e,t,r){if(r){a(r,t);return}appendChild(e,t)};const appendAll=function(e,t){if(!t){return}for(let r=0,i=t.length;r<i;r++){appendChild(e,t[r])}};const firstChildByTag=function(e,t){if(!e||!e.children){return null}return e.children.find((e=>e.tagName&&e.tagName===t))};const hasAttribute=function(e,t){if(!e.attribs)return false;return t in e.attribs};const setAttribute=function(e,t,r){if(!e.attribs)e.attribs={};e.attribs[t]=r};const move=function(e,t){remove(e);appendChild(t,e)};const createElement=(e,t)=>new i(e,t);const insertText=(e,t)=>{const r=new n("text",t);appendChild(e,r)};const createDocType=()=>{const e=new n("directive","!doctype html");return e};e.exports={appendChild:appendChild,appendAll:appendAll,insertAfter:insertAfter,nextNode:nextNode,remove:remove,createDocType:createDocType,createElement:createElement,insertText:insertText,insertBefore:insertBefore,hasAttribute:hasAttribute,setAttribute:setAttribute,firstChildByTag:firstChildByTag,move:move}},4056:e=>{"use strict";const t=["px","em","rem","vh","vw","vmin","vmax"];const r=["nodisplay","fixed","responsive","fixed-height","fill","container","flex-item","fluid","intrinsic"];const i=["fixed","fixed-height","responsive","fill","flex-item","intrinsic"];const n=cssLength("1",false,false);const s=cssLength("auto",true,false);const a=cssLength("44px",false,false);const o=cssLength("60px",false,false);function getLayoutClass(e){if(!e){return e}return"i-amphtml-layout-"+e}function calculateHeight(e,t,r){if((e===""||e==="fixed"||e==="fixed-height")&&!t.isSet){switch(r){case"amp-analytics":return n;case"amp-audio":return s;case"amp-pixel":return n;case"amp-social-share":return a;default:}}return t}function calculateWidth(e,t,r){if((e===""||e==="fixed")&&!t.isSet){switch(r){case"amp-analytics":return n;case"amp-audio":return s;case"amp-pixel":return n;case"amp-social-share":return o;default:}}return t}function isLayoutSizeDefined(e){return i.indexOf(e)>-1}function getCssLengthStyle(e,t){if(!e.isSet){return""}if(e.isAuto){return`${t}:auto;`}return`${t}:${e.numeral}${e.unit};`}function parseLayout(e){if(!e){return""}e=e.toLowerCase();if(r.indexOf(e)>-1){return e}return""}function calculateLayout(e,t,r,i,n){if(e!==""){return e}if(!t.isSet&&!r.isSet){return"container"}if(r.isSet&&(!t.isSet||t.isAuto)){return"fixed-height"}if(r.isSet&&t.isSet&&(i||n)){return"responsive"}return"fixed"}function cssLength(e,r=false,i=false){const n={isValid:false,isSet:false,isAuto:false,isFluid:false,numeral:Number.NaN,unit:"px"};if(e===null){n.isValid=true;return n}n.isSet=true;if(e==="auto"){n.isAuto=true;n.isValid=r;return n}if(e==="fluid"){n.isFluid=true;n.isValid=i;return n}const s=/(\d+(?:\.\d+)?)(.*)/;const a=s.exec(e);if(!a){return n}n.numeral=Number.parseFloat(a[1]);e=a[2];if(e===""){n.unit="px";n.isValid=true;return n}if(t.indexOf(e)>-1){n.isValid=true;n.unit=e}return n}function getLayoutSizeDefinedClass(){return"i-amphtml-layout-size-defined"}e.exports={parseLayout:parseLayout,cssLength:cssLength,getLayoutClass:getLayoutClass,calculateHeight:calculateHeight,calculateWidth:calculateWidth,isLayoutSizeDefined:isLayoutSizeDefined,getCssLengthStyle:getCssLengthStyle,calculateLayout:calculateLayout,getLayoutSizeDefinedClass:getLayoutSizeDefinedClass}},1150:(e,t,r)=>{"use strict";const i=r(7310).URL;const{join:n,resolve:s}=r(1017);const a="https://example.com";class PathResolver{constructor(e=""){if(typeof e==="function"){this.implementation=e}else{this.implementation=this.createStaticResolver(e)}}resolve(e,t){return this.implementation(e,t)}createStaticResolver(e){let t=false;try{new i(e);t=true}catch(e){}return r=>{try{if(t){return new i(r,e).toString()}else{return new i(r).toString()}}catch(e){}r=new i(r,a).pathname.substring(1);return s(n(e,r))}}}e.exports=PathResolver},859:(e,t,r)=>{"use strict";const{AMP_CACHE_HOST:i,appendRuntimeVersion:n}=r(8846);function calculateHost({ampUrlPrefix:e=i,ampRuntimeVersion:t,lts:r=false,rtv:s=false}){if(r&&s){throw new Error("lts flag is not compatible with runtime version parameter")}e=e.replace(/\/$/,"");if(t&&s){e=n(e,t)}else if(r){e+="/lts"}return e}e.exports={calculateHost:calculateHost}},3484:(e,t,r)=>{"use strict";const{Parser:i}=r(250);const{DomHandler:n,NodeWithChildren:s}=r(945);const{appendAll:a}=r(2547);const o=r(4404)["default"];const l={decodeEntities:false};class TreeParser{constructor(e){this.options=e}parse(e){return new Promise(((t,r)=>{const o=new n(((e,i)=>{if(e){r(e)}else{const e=new s("root",[]);a(e,i);t(e)}}),this.options);const l=new i(o,this.options);l.write(e.trim());l.end()}))}serialize(e){return o(e,{})}}e.exports=new TreeParser(l);e.exports.TreeParser=TreeParser},3741:e=>{"use strict";const isValidImageSrcURL=e=>{try{return new URL(e,"https://example.com").protocol.startsWith("http")}catch(e){return false}};e.exports={isValidImageSrcURL:isValidImageSrcURL}},351:(e,t,r)=>{"use strict";const{FileSystemCache:i}=r(7315);const n=r(1017);const s=r(255);const a=i.create({log:s,baseDir:n.join(__dirname,"..",".cache")});e.exports=a},5900:(e,t,r)=>{"use strict";const i=r(8653);const n=/^https?:\/\/|^\/\//i;function fetchImageDimensions(e){if(n.test(e)){return fetchImageDimensionsFromUrl(e)}return fetchImageDimensionsFromFile(e)}function fetchImageDimensionsFromUrl(e){return probe(e)}async function fetchImageDimensionsFromFile(e){if(!i("fs")){throw new Error("No access to the file system")}const t=r(7147);if(!t.existsSync(e)){throw new Error("Could not resolve file: "+e)}const n=t.createReadStream(e);return probe(n)}function probe(e){if(!i("probe-image-size")){throw new Error("Missing optional dependency: probe-image-size")}return r(5250)(e)}e.exports={fetchImageDimensions:fetchImageDimensions,fetchImageDimensionsFromFile:fetchImageDimensionsFromFile,fetchImageDimensionsFromUrl:fetchImageDimensionsFromUrl}},2412:(e,t,r)=>{"mode strict";const i="https://raw.githubusercontent.com/ampproject/amphtml/main/build-system/compile/bundles.config.extensions.json";const n=r(4934);const{MaxAge:s}=r(7315);const{AMP_CACHE_HOST:a,AMP_RUNTIME_CSS_PATH:o,AMP_VALIDATION_RULES_URL:l,appendRuntimeVersion:c}=r(8846);const u="validator-rules";const d=10*60;let f=false;async function fetchRuntimeParameters(e,t={}){const r=Object.assign({},t);r.verbose=t.verbose||e.verbose||false;await initRuntimeVersion(r,t,e);await initRuntimeStyles(r,e);await initValidatorRules(r,t,e);return r}async function initValidatorRules(e,t,r){try{e.validatorRules=t.validatorRules||r.validatorRules||await fetchValidatorRulesFromCache_(r)}catch(e){r.log.error("Could not fetch validator rules",e)}try{e.componentVersions=t.componentVersions||r.componentVersions||await fetchComponentVersionsFromCache_(r,e)}catch(t){r.log.error("Could not fetch latest component versions from amp.dev");r.log.verbose(t);e.componentVersions=[]}}async function fetchComponentVersionsFromCache_(e,t){const i=`component-versions-${t.ampRuntimeVersion}`;let n=await readFromCache_(e,i);if(!n){try{n=await fetchComponentVersions_(e,t);writeToCache_(e,i,n)}catch(t){e.log.warn(t.message);n=r(8649)}}return n}async function fetchComponentVersions_(e,t){const r=t.ampRuntimeVersion.substring(2);const n=`https://raw.githubusercontent.com/ampproject/amphtml/${r}/build-system/compile/bundles.config.extensions.json`;const s=await e.fetch(n);if(!s.ok){throw new Error(`Failed fetching latest component versions from ${i} with status: ${s.status}`)}return s.json()}async function fetchValidatorRulesFromCache_(e){let t=await readFromCache_(e,"validator-rules");let r;if(!t){r=await fetchValidatorRules_(e);e.log.debug("Downloaded AMP validation rules");writeToCache_(e,u,r.raw)}else{r=await n.fetch({rules:t})}return r}async function fetchValidatorRules_(e){const t=await e.fetch(l);if(!t.ok){return null}return n.fetch({rules:await t.json()})}async function initRuntimeStyles(e,t){try{e.ampRuntimeStyles=e.ampRuntimeStyles||await fetchAmpRuntimeStyles_(t,e.ampUrlPrefix,e.ampRuntimeVersion)}catch(e){t.log.error("Could not fetch AMP runtime CSS",e)}}async function initRuntimeVersion(e,t,r){e.lts=t.lts||r.lts||false;e.rtv=t.rtv||r.rtv||false;let{ampUrlPrefix:i,ampRuntimeVersion:n,lts:s}=e;if(s&&n){r.log.warn("`ampRuntimeVersion` and `lts` cannot be defined at the same time. Using LTS version.");n=""}try{e.ampRuntimeVersion=n||await fetchAmpRuntimeVersion_({config:r,ampUrlPrefix:i,lts:s})}catch(e){r.log.error("Could not fetch latest AMP runtime version",e)}}async function fetchAmpRuntimeVersion_(e){const t=`version-${e.ampUrlPrefix}-${e.lts}`;let r=await readFromCache_(e.config,t);if(!r){r=await fetchLatestRuntimeData_(e,t);e.config.log.debug("Downloaded AMP runtime v"+r.version)}else if(s.fromObject(r.maxAge).isExpired()){fetchLatestRuntimeData_(t,e)}return r.version}async function fetchLatestRuntimeData_({config:e,ampUrlPrefix:t,lts:r},i=null){let n;n={version:await e.runtimeVersion.currentVersion({ampUrlPrefix:t,lts:r}),maxAge:s.create(d).toObject()};if(!n.version&&t!==a){e.log.error(`Could not download runtime version from ${t}. Falling back to ${a}`);n=await fetchLatestRuntimeData_({config:e,ampUrlPrefix:a,lts:r},i)}else if(n.version&&i){writeToCache_(e,i,n)}return n}async function fetchAmpRuntimeStyles_(e,t,r){if(t&&!isAbsoluteUrl_(t)){e.log.warn(`AMP runtime styles cannot be fetched from relative ampUrlPrefix, please use the 'ampRuntimeStyles' parameter to provide the correct runtime style. Falling back to latest v0.css on ${a}`);t=a;r=r||await e.runtimeVersion.currentVersion()}const i=c(t||a,r)+o;const n=await downloadAmpRuntimeStyles_(e,i);if(!n){e.log.error(`Could not download ${i}. Falling back to latest v0.css.`);if(t||r){return fetchAmpRuntimeStyles_(e,a,await e.runtimeVersion.currentVersion())}else{return""}}return n}async function downloadAmpRuntimeStyles_(e,t){let r;if(e.cache!==false){r=await readFromCache_(e,t)}if(!r){const i=await e.fetch(t);if(!i.ok){return null}r=await i.text();if(!r.includes("i-amphtml-ssr")){r+=`amp-img[i-amphtml-ssr]:not(.i-amphtml-element):not([layout=container])>*{display: block;}`}e.log.debug(`Downloaded AMP runtime styles from ${t}`);if(e.cache!==false){writeToCache_(e,t,r)}}return r}function isAbsoluteUrl_(e){try{new URL(e);return true}catch(e){return false}}function readFromCache_(e,t){if(e.cache===false){return null}try{return e.cache.get(t)}catch(t){if(!f){e.log.warn("Could not read from cache",t);f=true}}}function writeToCache_(e,t,r){if(e.cache===false){return}try{e.cache.set(t,r)}catch(t){if(!f){e.log.warn("Could not write to cache",t);f=true}}}e.exports=fetchRuntimeParameters},8084:e=>{const t={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"};const r=/[&><\u2028\u2029]/g;e.exports=function htmlEscapeJsonString(e){return e.replace(r,(e=>t[e]))}},8653:e=>{function isDependencyInstalled(e){try{require.resolve(e);return true}catch(e){return false}}e.exports=isDependencyInstalled},255:(e,t,r)=>{"use strict";const{log:i}=r(7315);e.exports=i.tag("AMP Optimizer")},7199:e=>{"use strict";let t;let r;const parseSizes=e=>{const t={defaultValue:"",values:[]};if(!e){return t}const r=e.trim().split(/\s*,\s*/);for(let i=0;i<r.length;i++){const n=r[i];if(i===r.length-1){t.defaultValue=n.trim()}else{const r=n.split(/\)\s+/);if(r.length!==2){throw new Error(`Invalid sizes definition '${e}'`)}t.values.push({media:`${r[0]})`,size:r[1]})}}return t};e.exports=parseSizes},4311:(e,t,r)=>{"use strict";const{firstChildByTag:i,createElement:n,appendChild:s}=r(2547);class AddAmpLink{transform(e,t){if(!t.ampUrl)return;const r=i(e,"html");if(!r)return;const a=i(r,"head");if(!a)return;const o=n("link",{rel:"amphtml",href:t.ampUrl});s(a,o)}}e.exports=AddAmpLink},4949:(e,t,r)=>{const i=r(8653);const{createElement:n,appendChild:s,nextNode:a,firstChildByTag:o}=r(2547);const{URL:l}=r(7310);const{skipNodeAndChildren:c}=r(7041);const u=r(1150);const d=60;const f=100;const p=30;const h=-1;const{isTemplate:m}=r(8846);const g={"#":"%23","%":"%25",":":"%3A","<":"%3C",">":"%3E",'"':"'"};const b=new RegExp(Object.keys(g).join("|"),"g");function escaper(e){return g[e]}class AddBlurryImagePlaceholders{constructor(e){this.log_=e.log.tag("AddBlurryImagePlaceholders");this.blurredPlaceholders_=!!e.blurredPlaceholders;if(!this.blurredPlaceholders_){this.log_.debug("disabled")}this.missingDependencies_=false;if(!i("jimp")||!i("lru-cache")){if(this.blurredPlaceholders_){this.log_.warn("jimp and lru-cache need to be installed via `npm install jimp lru-cache` "+"for this transformer to work")}this.blurredPlaceholders_=false;this.missingDependencies_=true;return}this.jimp=r(4645);this.maxBlurredPlaceholders_=e.maxBlurredPlaceholders||f;this.pathResolver_=new u(e.imageBasePath);const t=e.blurredPlaceholdersCacheSize||p;if(t===h){this.log_.debug("caching all placeholders");this.cache_=new Map}else if(t>0){const e=r(7330);this.log_.debug("using LRU cache for regularily used placeholders",t);this.cache_=new e({max:t})}else{this.log_.debug("caching disabled")}}transform(e,t){if(!t.blurredPlaceholders&&!this.blurredPlaceholders_||this.missingDependencies_){return}const r=o(e,"html");const i=o(r,"body");const n=[];let l=0;for(let e=i;e!==null;e=a(e)){const{tagName:r}=e;let i;if(m(e)){e=c(e);continue}if(r==="amp-img"){i=e.attribs.src}if(r==="amp-video"&&e.attribs.poster){i=e.attribs.poster}if(this.shouldAddBlurryPlaceholder_(e,i,r)){l++;const r=this.addBlurryPlaceholder_(i,t).then((t=>{e.attribs["i-amphtml-ssr"]="";e.attribs.noloading="";s(e,t)}));n.push(r);if(l>=this.maxBlurredPlaceholders_){break}}}return Promise.all(n)}async addBlurryPlaceholder_(e,t){const r=n("img",{class:"i-amphtml-blurry-placeholder",placeholder:"",src:e,alt:""});try{const i=await this.getCachedDataURI(e,t);let n=`<svg xmlns="http://www.w3.org/2000/svg"\n                      xmlns:xlink="http://www.w3.org/1999/xlink"\n                      viewBox="0 0 ${i.width} ${i.height}">\n                      <filter id="b" color-interpolation-filters="sRGB">\n                        <feGaussianBlur stdDeviation=".5"></feGaussianBlur>\n                        <feComponentTransfer>\n                          <feFuncA type="discrete" tableValues="1 1"></feFuncA>\n                        </feComponentTransfer>\n                      </filter>\n                      <image filter="url(#b)" x="0" y="0"\n                        height="100%" width="100%"\n                        xlink:href="${i.src}">\n                      </image>\n                    </svg>`;n=n.replace(/\s+/g," ");n=n.replace(/> </g,"><");n=n.replace(b,escaper);r.attribs.src="data:image/svg+xml;charset=utf-8,"+n;this.log_.debug(e,"[SUCCESS]")}catch(t){this.log_.debug(e,"[FAIL]");this.log_.error(t.message)}return r}getCachedDataURI(e,t){const r=this.pathResolver_.resolve(e,t);if(this.cache_){const t=this.cache_.get(r);if(t){this.log_.debug(e,"[CACHE HIT]");return t}this.log_.debug(e,"[CACHE MISS]")}const i=this.getDataURI_(r);if(this.cache_){this.cache_.set(r,i)}return i}async getDataURI_(e){const t=/^https?:\/\//.test(e)?{headers:{"User-Agent":"AMP Optimizer"},url:e}:e;const r=await this.jimp.read(t);const i=this.getBitmapDimensions_(r.bitmap.width,r.bitmap.height);r.resize(i.width,i.height,this.jimp.RESIZE_BEZIER);const n={src:await r.getBase64Async("image/png"),width:i.width,height:i.height};return n}getBitmapDimensions_(e,t){const r=e/t;let i=d/r;i=Math.sqrt(i);const n=d/i;return{width:Math.round(n),height:Math.round(i)}}hasPlaceholder_(e){return e.childNodes.find((e=>e.attribs&&e.attribs.placeholder!==undefined))!==undefined}shouldAddBlurryPlaceholder_(e,t,r){if(!t){return false}if(this.hasPlaceholder_(e)){return false}const i=new l(t,"https://example.com");if(!i.pathname.endsWith(".jpg")&&!i.pathname.endsWith("jpeg")){return false}if(e.attribs.noloading!=null){return false}const n=r=="amp-video";const s=r=="amp-img"&&(e.attribs.layout=="intrinsic"||e.attribs.layout=="responsive"||e.attribs.layout=="fill");return n||s}}e.exports=AddBlurryImagePlaceholders},8256:(e,t,r)=>{"use strict";const{move:i,insertText:n,appendChild:s,insertBefore:a,createDocType:o,createElement:l,firstChildByTag:c}=r(2547);const{AMP_FORMATS:u,AMP_TAGS:d}=r(8846);const f="AMP";const p="data-auto";const h={AMP:[{matcher:{tagName:"meta",attribs:{charset:"utf-8"}},node:{tagName:"meta",attribs:{charset:"utf-8"}}},{matcher:{tagName:"meta",attribs:{name:"viewport"}},node:{tagName:"meta",attribs:{name:"viewport",content:"width=device-width,minimum-scale=1,initial-scale=1"}}},{matcher:{tagName:"noscript"},node:{tagName:"noscript",children:[{tagName:"style",attribs:{"amp-boilerplate":""},text:"body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}"}]}},{matcher:{tagName:"style",attribs:{"amp-boilerplate":""}},node:{tagName:"style",attribs:{"amp-boilerplate":""},text:"body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}"}},{matcher:{tagName:"script",attribs:{src:/^https:\/\/.+\/v0\.js$/}},node:{tagName:"script",attribs:{async:"",src:"https://cdn.ampproject.org/v0.js"}}},{matcher:{tagName:"link",attribs:{rel:"canonical"}},node:{tagName:"link",attribs:{rel:"canonical",href:(e,t)=>{if(!e.canonical){t.warn("No canonical param is given. Setting canonical href to `.`");e.canonical="."}return e.canonical}}}}]};class AddMandatoryTags{constructor(e){this.enabled=e.autoAddBoilerplate!==false&&e.autoAddMandatoryTags!==false;this.format=e.format||f;this.log_=e.log.tag("AddMandatoryTags")}async transform(e,t){if(!this.enabled){return}if(!u.includes(this.format)){this.log_.error("Unknown AMPHTML format",this.format);return}const r=h[this.format];if(!r){this.log_.info("Unsupported AMP format",this.format);return}let i=c(e,"html");if(!i){i=this.createHtml5Document(e)}let n=e.children.find((e=>e.type==="directive"&&e.name==="!doctype"));if(!n){n=o();a(e,n,e.firstChild)}if(!Object.keys(i.attribs).some((e=>d.includes(e)))){i.attribs[this.format.toLowerCase()]=""}let f=c(i,"head");if(!f){f=l("head");s(i,f)}const p=new Set(r);let m=f.firstChild;while(m){if(m.tagName){p.forEach((e=>{if(this.matchSpec(e.matcher,m)){p.delete(e)}}))}m=m.nextSibling}for(const e of p){this.addNode(f,e.node,t)}}matchSpec(e,t){if(e.tagName!==t.tagName){return false}if(!e.attribs){return true}for(const[r,i]of Object.entries(e.attribs)){const e=t.attribs[r];if(i instanceof RegExp){if(!i.test(e)){return false}}else if(e!==i){return false}}return true}addNode(e,t,r){const i={};i[p]="";const n=l(t.tagName,i);this.addAttributes(t,n,r);this.addChildren(t,n,r);this.addText(t,n,r);s(e,n)}addText(e,t,r){if(!e.text){return}let i;if(typeof e.text==="function"){i=e.text(r,this.log_)}else{i=e.text}n(t,i)}addChildren(e,t,r){if(!e.children){return}for(const i of e.children){this.addNode(t,i,r)}}addAttributes(e,t,r){if(!e.attribs){return}for(const[i,n]of Object.entries(e.attribs)){if(typeof n==="function"){t.attribs[i]=n(r,this.log_)}else{t.attribs[i]=n}}}createHtml5Document(e){const t=l("html",{});const r=this.createOrMoveElement(e,t,"head");const i=this.createOrMoveElement(e,t,"body");this.copyTagsToHeadAndBody(e,r,i);s(e,t);return t}createOrMoveElement(e,t,r){const n=c(e,r)||l(r);i(n,t);return n}copyTagsToHeadAndBody(e,t,r){let n=e.firstChild;while(n){const e=n;n=e.next;if(e.type==="directive"){}else if(e.tagName==="title"){i(e,t)}else{i(e,r)}}}}e.exports=AddMandatoryTags},9494:(e,t,r)=>{"use strict";const{firstChildByTag:i}=r(2547);class AddTransformedFlag{transform(e){const t=i(e,"html");if(!t)return;t.attribs.transformed="self;v=1"}}e.exports=AddTransformedFlag},4975:(e,t,r)=>{"use strict";const{appendChild:i,createElement:n,hasAttribute:s,firstChildByTag:a,insertText:o}=r(2547);const l="document.querySelector(\"script[src*='/v0.js']\").onerror=function(){"+"document.querySelector('style[amp-boilerplate]').textContent=''}";const c="[].slice.call(document.querySelectorAll("+"\"script[src*='/v0.js'],script[src*='/v0.mjs']\")).forEach("+"function(s){s.onerror="+"function(){"+"document.querySelector('style[amp-boilerplate]').textContent=''"+"}})";class AmpBoilerplateErrorHandler{transform(e,t){const r=a(e,"html");if(!r){return}if(s(r,"i-amphtml-no-boilerplate")){return}const u=a(r,"head");if(!u){return}const d=n("script",{"amp-onerror":""});if(t.esmModulesEnabled){o(d,c)}else{o(d,l)}i(u,d)}}e.exports=AmpBoilerplateErrorHandler},9097:(e,t,r)=>{"use strict";const{insertText:i,hasAttribute:n,firstChildByTag:s}=r(2547);class AmpBoilerplateTransformer{constructor(e){this.fetch_=e.fetch;this.runtimeVersion_=e.runtimeVersion;this.log_=e.log.tag("AmpBoilerplateTransformer")}transform(e,t){const r=s(e,"html");const n=s(r,"head");if(!n){return}const a=this._findAmpRuntimeStyle(n);if(!a){return}let{ampRuntimeVersion:o,ampRuntimeStyles:l}=t;if(!o||!l){this.log_.error("Missing parameters both ampRuntimeVersion and ampRuntimeStyles need to be present");return}a.attribs["i-amphtml-version"]=o;i(a,l)}_findAmpRuntimeStyle(e){let t=e.firstChild;while(t){if(n(t,"amp-runtime")){return t}t=t.nextSibling}return null}}e.exports=AmpBoilerplateTransformer},5557:(e,t,r)=>{"use strict";const{remove:i,appendChild:n,createElement:s,nextNode:a,firstChildByTag:o}=r(2547);const{calculateHash:l}=r(9874);class AmpScriptCsp{transform(e){const t=o(e,"html");if(!t)return;const r=o(t,"head");if(!r)return;const n=o(t,"body");if(!n)return;const s=this._findOrCreateCspMeta(r);const a=(s.attribs.content||"").trim().split(/\s+/);const c=new Set(a);c.delete("");const u=this._findAllInlineScripts(n);for(const e of u){const t=e.children[0]?e.children[0].data:"";c.add(l(t))}const d=Array.from(c).join(" ");if(d===""){i(s);return}s.attribs.content=d}_findAllInlineScripts(e){const t=[];let r=e;while(r!==null){if(r.tagName==="script"&&r.attribs.target==="amp-script"){t.push(r)}r=a(r)}return t}_findOrCreateCspMeta(e){for(let t=e.firstChild;t!==null;t=t.nextSibling){if(t.tagName==="meta"&&t.attribs.name==="amp-script-src"){return t}}const t=s("meta",{name:"amp-script-src"});n(e,t);return t}}e.exports=AmpScriptCsp},6070:(e,t,r)=>{"use strict";const i=r(7199);const{appendChild:n,createElement:s,insertText:a,hasAttribute:o}=r(2547);const{isCustomElement:l}=r(9170);const c="i-amp-";class MediaTransformer{constructor(){this.media=new Map}transform(e,t){let r=e.attribs.media.replace(/\s+/g," ");r=r.trim();if(!r){return false}if(r[0]==="("){r=`all and ${r}`}if(r.startsWith("not ")){r=r.substring(4)}else{r=`not ${r}`}this.addMedia(r,`#${t}`);return true}addMedia(e,t){let r=this.media.get(e);if(!r){r=[];this.media.set(e,r)}r.push(t)}toString(){let e="";for(const[t,r]of this.media.entries()){e+=`@media ${t}{${r.join(",")}{display:none}}`}return e}}class SizesTransformer{constructor(){this.sizes=[]}transform(e,t){if(!e.attribs.srcset){return false}const r=i(e.attribs.sizes);if(!r.defaultValue){return false}this.addSizes(t,r);return true}addSizes(e,t){this.sizes.push({id:e,defaultSize:t.defaultValue,sizes:t.values.reverse()})}toString(){let e="";for(const{sizes:t,defaultSize:r,id:i}of this.sizes){const n=`#${i}`;e+=`${n}{width:${r}}`;for(const r of t){e+=`@media ${r.media}{${n}{width:${r.size}}}`}}return e}}class HeightsTransformer{constructor(){this.heights=[]}transform(e,t){const r=i(e.attribs.heights);if(!r.defaultValue){return false}this.addHeights(t,r);return true}addHeights(e,t){this.heights.push({id:e,defaultHeight:t.defaultValue,heights:t.values.reverse()})}toString(){let e="";for(const{heights:t,defaultHeight:r,id:i}of this.heights){const n=`#${i}>:first-child`;e+=`${n}{padding-top:${r}}`;for(const r of t){e+=`@media ${r.media}{${n}{padding-top:${r.size}}}`}}return e}}class ApplyCommonAttributes{constructor(e){this.log=e;this.canRemoveBoilerplate=true;this.transformedNodesCounter=0;this.nodesToTransform=[];this.ids=new Set;this.attributeTransformations={media:new MediaTransformer,sizes:new SizesTransformer,heights:new HeightsTransformer}}addNode(e){if(!e.attribs){return}if(o(e,"id")){this.ids.add(e.attribs.id)}if(l(e)){this.nodesToTransform.push(e)}}apply(){for(const e of this.nodesToTransform){const t=this.getOrCreateId(e);let r=false;for(const[i,n]of Object.entries(this.attributeTransformations)){if(o(e,i)){try{r=r||n.transform(e,t)}catch(t){this.log.debug(`Cannot remove boilerplate. Failed transforming ${i}="${e.attribs[i]}".`,t);this.canRemoveBoilerplate=false}}}if(r){e.attribs.id=t}else{this.transformedNodesCounter--}}}applyToCustomStyles(e,t){const r=Object.values(this.attributeTransformations).join("");if(!r){return}if(!t){t=s("style",{"amp-custom":""});n(e,t)}if(t.children.length===0){a(t,"")}t.children[0].data+=r;for(const e of this.nodesToTransform){for(const t of Object.keys(this.attributeTransformations)){delete e.attribs[t]}}}getOrCreateId(e){if(o(e,"id")){return e.attribs.id}e.attribs=e.attribs||[];const t=c+this.transformedNodesCounter;this.transformedNodesCounter++;if(this.ids.has(t)){return this.getOrCreateId(e)}return t}}e.exports=ApplyCommonAttributes},9464:(e,t,r)=>{"use strict";const{hasAttribute:i,insertBefore:n,createElement:s,appendChild:a}=r(2547);const{parseLayout:o,cssLength:l,calculateHeight:c,calculateWidth:u,calculateLayout:d,getLayoutClass:f,isLayoutSizeDefined:p,getLayoutSizeDefinedClass:h}=r(4056);const m=["","nodisplay","fixed","fixed-height","responsive","container","fill","flex-item","fluid","intrinsic"];function isSupportedLayout(e){return m.indexOf(e)>-1}function getAttributeOrNull(e,t){return i(e,t)?e.attribs[t]:null}function addClass(e,t){const r=i(e,"class")?e.attribs.class.trim():"";e.attribs.class=r.length>0?r+" "+t:t}function apply(e,t,r,i){addClass(i,f(e));if(p(e)){addClass(i,h())}let n="";switch(e){case"nodisplay":i.attribs.hidden="hidden";break;case"fixed":n=`width:${t.numeral}${t.unit};height:${r.numeral}${r.unit};`;break;case"fixed-height":n=`height:${r.numeral}${r.unit};`;break;case"responsive":break;case"intrinsic":break;case"fill":case"container":break;case"fluid":n="width:100%;height:0;";addClass(i,"i-amphtml-layout-awaiting-size");break;case"flex-item":if(t.isSet){n=`width:${t.numeral}${t.unit};`}if(r.isSet){n+=`height:${r.numeral}${r.unit};`}break;default:}i.attribs.style=n+(i.attribs.style?i.attribs.style:"");if(i.attribs.style===""){delete i.attribs.style}i.attribs["i-amphtml-layout"]=e}function maybeAddSizerInto(e,t,r,i){if(!r.isSet||r.numeral===0||!i.isSet||r.unit!==i.unit){return}let s=null;if(t==="responsive"){s=createResponsiveSizer(r,i)}else if(t==="intrinsic"){s=createIntrinsicSizer(r,i)}if(s){const t=e.firstChild;n(e,s,t)}}function createResponsiveSizer(e,t){const r=t.numeral/e.numeral*100;const i=s("i-amphtml-sizer",{style:`display:block;padding-top:${parseFloat(r.toFixed(4))}%`});return i}function createIntrinsicSizer(e,t){const r=s("i-amphtml-sizer",{class:"i-amphtml-sizer"});const i=s("img",{alt:"","aria-hidden":"true",class:"i-amphtml-intrinsic-sizer",role:"presentation",src:"data:image/svg+xml;base64,"+Buffer.from(`<svg height="${t.numeral}" width="${e.numeral}" xmlns="http://www.w3.org/2000/svg" version="1.1"/>`).toString("base64")});a(r,i);return r}e.exports={applyLayout:function(e,t){const r=o(e.attribs.layout);const i=getAttributeOrNull(e,"width");const n=l(i,true,false);if(!n.isValid){t.debug("cannot perform SSR: invalid input width\n",i);return false}const s=getAttributeOrNull(e,"height");const a=l(s,true,r==="fluid");if(!a.isValid){t.debug("cannot perform SSR: invalid input height\n",s);return false}const f=c(r,a,e.tagName);const p=u(r,n,e.tagName);const h=d(r,p,f,getAttributeOrNull(e,"sizes"),getAttributeOrNull(e,"heights"));if(!isSupportedLayout(h)){t.debug("cannot perform SSR: unsupported layout",h);return false}if(h&&h==="responsive"){e.attribs.layout=h}apply(h,p,f,e);maybeAddSizerInto(e,h,p,f);return true}}},5414:(e,t,r)=>{"use strict";const{nextNode:i,insertAfter:n,createElement:s,firstChildByTag:a,hasAttribute:o}=r(2547);const{findMetaViewport:l,findRuntimeScript:c}=r(7041);const{AMP_FORMATS:u,AMP_CACHE_HOST:d}=r(8846);const f="bind";const p="data-amp-bind-";const h="AMP";const m=new Map([["lightbox","amp-lightbox-gallery"]]);const g=Array.from(m.values());class AutoExtensionImporter{constructor(e){this.enabled=e.autoExtensionImport!==false;this.format=e.format||h;this.log_=e.log.tag("AutoExtensionImporter");this.experimentBindAttributeEnabled=e.experimentBindAttribute===true;this.extensionVersions=e.extensionVersions||{}}createExtensionsSpec(e){const t=e.validatorRules;const r=new Map;for(const e of t.extensions){if(e.htmlFormat.includes(this.format)){r.set(e.name,{name:e.name,type:e.extensionType==="CUSTOM_TEMPLATE"?"custom-template":"custom-element",version:e.version.filter((e=>e!=="latest"))})}}const i=new Map;const n=new Map;const s=new Map;for(const e of t.getTagsForFormat(this.format)){const t=e.tagName.toLowerCase();if(t.startsWith("amp-")){let r=e.requiresExtension||[];r=r.filter((e=>!g.includes(e)));i.set(t,r)}const r=new Set;for(const i of e.attrs){if(m.has(i.name)){i.requiresExtension=[m.get(i.name)]}if(i.requiresExtension&&i.requiresExtension.length>0){const e=n.get(t)||[];e.push(i);n.set(t,e)}if(i.name.startsWith("[")){r.add(i.name.substring(1,i.name.length-1))}}s.set(t,r)}return{extensionsMap:r,tagToExtensionsMapping:i,tagToAttributeMapping:n,tagToBindAttributeMapping:s}}async transform(e,t){if(!this.enabled){return}if(!t.validatorRules){this.log_.error("Missing validation rules, cannot auto import extensions");return}if(!this.componentVersions){this.componentVersions={};for(const e of t.componentVersions){this.componentVersions[e.name]=e.latestVersion}}if(!this.extensionSpec_){this.extensionSpec_=this.createExtensionsSpec(t)}if(!u.includes(this.format)){this.log_.error("Unsupported AMPHTML format",this.format);return}const r=a(e,"html");if(!r)return;const i=a(r,"head");if(!i)return;const o=a(r,"body");if(!o)return;const f=new Set;const p=new Set;this.findExistingExtensionsAndExtensionsToImportInHead_(i,f,p);await this.findExtensionsToImportInBody_(o,f);if(f.length===0){return}let h=c(i);if(!h){h=l(i)}const m=d;for(const e of f){if(p.has(e)){continue}const t=this.extensionSpec_.extensionsMap.get(e.trim());this.log_.debug("auto importing",e);let r=this.calculateVersion(t,e);const a={async:"",src:`${m}/v0/${e}-${r}.js`};a[t.type]=e;const o=s("script",a);n(i,o,h);h=o}}calculateVersion(e,t){const r=this.extensionVersions[t];if(r){this.log_.debug("using custom version for",t,r);return r}const i=this.componentVersions[t];const n=e.version[e.version.length-1];if(n<i){return n}return i}findExistingExtensionsAndExtensionsToImportInHead_(e,t,r){let n=e;while(n){const e=this.getCustomElement_(n);if(e){r.add(e)}else if(n.tagName==="script"&&n.attribs["id"]==="amp-access"){t.add("amp-access");t.add("amp-analytics");const e=this.getJson(n);if(e.vendor==="laterpay"){t.add("amp-access-laterpay")}}else if(n.tagName==="script"&&n.attribs["id"]==="amp-subscriptions"){t.add("amp-subscriptions");t.add("amp-analytics");const e=this.getJson(n);if(e.services&&e.services.length){for(const r of e.services){if(r.serviceId==="subscribe.google.com"){t.add("amp-subscriptions-google")}}}}n=i(n)}}getJson(e){for(const t of e.children||[]){if(!t.data){continue}try{return JSON.parse(t.data)}catch(e){this.log_.error('Could not parse JSON in <script id="amp-access">',e.message)}}return{}}async findExtensionsToImportInBody_(e,t){let r=e;while(r!==null){if(r.tagName){this.addRequiredExtensionByTag_(r,t);this.addRequiredExtensionByAttributes_(r,t)}r=i(r)}}addRequiredExtensionByTag_(e,t){const r=this.extensionSpec_.tagToExtensionsMapping.get(e.tagName);if(r){r.forEach((e=>t.add(e)))}if(e.tagName==="template"&&e.attribs.type){t.add(e.attribs.type)}else if(e.tagName==="script"&&o(e,"template")){t.add(e.attribs.template)}else if(e.tagName==="input"&&o(e,"mask")){t.add("amp-inputmask")}}addRequiredExtensionByAttributes_(e,t){if(!e.tagName||!e.attribs){return}const r=this.extensionSpec_.tagToAttributeMapping;const i=r.get(e.tagName)||[];i.forEach((r=>{if(e.attribs[r.name]!==undefined){r.requiresExtension.forEach((e=>{t.add(e)}))}}));if(e.tagName==="form"){t.add("amp-form")}const n=this.extensionSpec_.tagToBindAttributeMapping;const s=Object.keys(e.attribs);if(s.some((e=>e.startsWith("[")||e.startsWith(p)))){t.add("amp-bind")}if(this.experimentBindAttributeEnabled){const r=n.get(e.tagName);let i=false;for(const n of s){if(!n.startsWith(f)){continue}const s=n.substring(f.length);if(r.has(s)){const t=`${p}${s}`;e.attribs[t]=e.attribs[n];delete e.attribs[n];i=true}if(i){t.add("amp-bind")}}}}getCustomElement_(e){if(e.tagName!=="script"){return""}const t=e.attribs["custom-element"]||e.attribs["custom-template"]||"";if(!t){return""}if(!t.startsWith("amp-")){return""}return t}}e.exports=AutoExtensionImporter},5141:(e,t,r)=>{"use strict";const{insertAfter:i,createElement:n,firstChildByTag:s}=r(2547);const{findMetaViewport:a}=r(7041);class GoogleFontsPreconnect{constructor(e){this.log_=e.log.tag("GoogleFontsPreconnect")}transform(e){const t=s(e,"html");if(!t){return}const r=s(t,"head");if(!r){return}for(let e=r.firstChild;e!==null;e=e.nextSibling){if(this.isGoogleFontsLinkNode_(e)){const e=n("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossorigin:""});const t=n("link",{rel:"dns-prefetch",href:"https://fonts.gstatic.com"});const s=a(r);i(r,e,s);this.log_.debug('adding <link rel="preconnect" href="'+e.attribs.href+'" crossorigin>');i(r,t,e);this.log_.debug('adding <link rel="dns-prefetch" href="'+t.attribs.href+'">');return}}}isGoogleFontsLinkNode_(e){return e.tagName==="link"&&e.attribs.rel==="stylesheet"&&e.attribs.href.startsWith("https://fonts.googleapis.com")}}e.exports=GoogleFontsPreconnect},9433:(e,t,r)=>{"use strict";const i=r(1150);const{fetchImageDimensions:n}=r(5900);const{remove:s,insertAfter:a,createElement:o,firstChildByTag:l,nextNode:c}=r(2547);const u="intrinsic";const d=320;class Markdown{constructor(e){this.log=e.log;this.enabled=!!e.markdown;this.pathResolver=new i(e.imageBasePath)}async transform(e,t){if(!this.enabled){return}const r=l(e,"html");if(!r){return}const i=l(r,"body");if(!i){return}let n=i;const s=[];while(n){const e=c(n);if(n.tagName==="img"){s.push(this.transformImg(n,t))}n=e}return Promise.all(s)}async transformImg(e,t){const r=e.attribs&&e.attribs.src;if(!r){return}const i=this.pathResolver.resolve(r,t);let o;try{o=await n(i)}catch(e){this.log.warn(e.message);return}const l=this.createAmpImgOrAmpAnim(o,e);a(e.parent,l,e);s(e)}createAmpImgOrAmpAnim(e,t){const r=e.type==="gif"?"amp-anim":"amp-img";const i=o(r,t.attribs);i.attribs.width=t.attribs.width||String(e.width);i.attribs.height=t.attribs.height||String(e.height);this.addLayout(i,e);return i}addLayout(e,t){if(t.width<d){return}e.attribs.layout=u}}e.exports=Markdown},1031:(e,t,r)=>{"use strict";const{minify:i}=r(6794);const{remove:n}=r(2547);const s=r(5438);const a=r(8084);const o=/^\s*__[a-bA-Z0-9_-]+__\s*$/;class MinifyHtml{constructor(e){this.opts={minify:e.minify!==false,minifyAmpScript:true,minifyJSON:true,collapseWhitespace:true,removeComments:true,canCollapseWhitespace:true,inBody:false,commentIgnorePattern:o};this.log=e.log.tag("MinifyHtml")}async transform(e){if(!this.opts.minify){return}const t=[];await this.minifyNode(e,this.opts,t);for(const e of t){n(e)}}async minifyNode(e,t,r){if(e.type==="text"){this.minifyTextNode(e,t,r)}else if(e.type==="comment"){this.minifyCommentNode(e,t,r)}else if(e.tagName==="script"){await this.minifyScriptNode(e,t)}const i=Object.assign({},t);if(t.canCollapseWhitespace&&!this.canCollapseWhitespace(e.tagName)){i.canCollapseWhitespace=false}if(e.tagName==="head"||e.tagName==="html"){i.inBody=false}else if(e.tagName==="body"){i.inBody=true}const n=[];for(const t of e.children||[]){n.push(this.minifyNode(t,i,r))}return Promise.all(n)}minifyTextNode(e,t,r){if(!e.data||!t.collapseWhitespace){return}if(t.canCollapseWhitespace){e.data=s(e.data)}if(!t.inBody){e.data=e.data.trim()}if(e.data.length===0){r.push(e)}}minifyCommentNode(e,t,r){if(!e.data||!t.removeComments){return}if(t.commentIgnorePattern.test(e.data)){return}r.push(e)}async minifyScriptNode(e,t){const r=this.isJson(e);const i=!r&&this.isInlineAmpScript(e);for(const n of e.children||[]){if(!n.data){continue}if(r&&t.minifyJSON){this.minifyJson(n)}else if(i&&t.minifyAmpScript){await this.minifyAmpScript(n)}}}async minifyAmpScript(e){try{const t=await i(e.data,{});if(t.error){this.log.warn("Could not minify inline amp-script",e.data,`${t.error.name}: ${t.error.message}`);return}e.data=t.code}catch(e){this.log.warn("Failed minifying inline amp-script",e)}}minifyJson(e){try{let t=JSON.stringify(JSON.parse(e.data),null,"");t=a(t);e.data=t}catch(t){this.log.warn("Invalid JSON",e.data)}}isInlineAmpScript(e){return e.attribs&&e.attribs.type==="text/plain"&&e.attribs.target==="amp-script"}isJson(e){return e.attribs&&(e.attribs.type==="application/json"||e.attribs.type==="application/ld+json")}canCollapseWhitespace(e){return"script"!==e&&"style"!==e&&"pre"!==e&&"textarea"!==e}canTrimWhitespace(e){return e!=="pre"&&e!=="textarea"}}e.exports=MinifyHtml},8046:(e,t,r)=>{const{nextNode:i,firstChildByTag:n,setAttribute:s}=r(2547);const{skipNodeAndChildren:a}=r(7041);const{isTemplate:o}=r(8846);class OptimizeAmpBind{constructor(e){this.log_=e.log.tag("OptimizeAmpBind");this.enabled_=e.optimizeAmpBind!==false;if(!this.enabled_){this.log_.debug("disabled")}}transform(e){if(!this.enabled_)return;const t=n(e,"html");if(!t)return;const r=n(t,"head");if(!r)return;if(!hasAmpBindScriptNode(r))return;s(t,"i-amphtml-binding","");for(let e=t;e!==null;e=i(e)){if(o(e)){e=a(e);continue}const{attribs:t}=e;if(!t)continue;for(const r in t){if(r.startsWith("data-amp-bind-")||r.startsWith("[")&&r.endsWith("]")){s(e,"i-amphtml-binding","");break}}}}}function hasAmpBindScriptNode(e){for(let t=e.firstChild;t!==null;t=t.nextSibling){if(t.tagName!=="script")continue;if(!t.attribs)continue;if(t.attribs["custom-element"]!=="amp-bind")continue;return true}return false}e.exports=OptimizeAmpBind},6037:(e,t,r)=>{"use strict";const{appendChild:i,createElement:n,hasAttribute:s,remove:a,insertAfter:o,nextNode:l,firstChildByTag:c}=r(2547);const{findMetaViewport:u,skipNodeAndChildren:d}=r(7041);const{isValidImageSrcURL:f}=r(3741);const{isTemplate:p,isAmpStory:h}=r(8846);const m=150;const g=2;class OptimizeHeroImage{constructor(e){this.log=e.log;this.enabled=e.optimizeHeroImages!==false||e.preloadHeroImage!==false;this.maxHeroImageCount=e.maxHeroImageCount||g;if(e.preloadHeroImage){this.log.info("`preloadHeroImage` option has been deprecated. Use `optimizeHeroImages` instead")}}async transform(e,t){if(t.preloadHeroImage){this.log.info("`preloadHeroImage` option has been deprecated. Use `optimizeHeroImages` instead")}if(!this.enabled||t.optimizeHeroImages===false||t.preloadHeroImage===false){return}const r=t.maxHeroImageCount||this.maxHeroImageCount;const i=c(e,"html");const n=c(i,"head");const s=c(i,"body");if(!s||!n)return;const a=this.findHeroImages(s);let o=u(n);let l=a.length;if(l>r){this.log.warn(`Found ${l} hero elements on the page, but the maximum is set to ${r}. The limit can be configured via the 'maxHeroImage' parameter.`);l=r}const d=h(n);for(let e=0;e<l;e++){const t=a[e];this.generatePreload(t,n,o);if(!d){this.generateImg(t.ampImg)}}}generatePreload(e,t,r){if(e.srcset){this.log.debug("Could not preload hero image as it's using srcset, which is currently only supported Chromium-based browsers (see https://web.dev/preload-responsive-images/).",e.src);return}if(this.hasExistingImagePreload(t,e.src)){return}const i=n("link",{rel:"preload",href:e.src,as:"image","data-hero":""});if(!e.media){return}i.attribs.media=e.media;o(t,i,r)}hasExistingImagePreload(e,t){return e.children.some((e=>{if(e.tagName!=="link"){return false}if(!s(e,"rel")){return false}if(e.attribs.rel!=="preload"){return false}if(e.attribs.as!=="image"){return false}return e.attribs.href===t}))}findHeroImages(e){let t=null;let r=[];let i=e;let n=0;while(i!==null){if(i.tagName==="p"){n++}this.addImageWithDataHero(i,r);if(!t&&n<2&&r.length===0){t=this.isCandidateHeroImage(i)}if(p(i)){i=d(i)}else{i=l(i)}}if(r.length>0){return r}if(t){return[t]}return[]}addImageWithDataHero(e,t){if(e.tagName==="amp-img"&&s(e,"data-hero")){const{src:r,media:i,srcset:n}=e.attribs;t.push({ampImg:e,src:r,media:i,srcset:n})}else if(this.isAmpIframe(e)&&s(e,"data-hero")){const r=this.getPlaceholderImage(e);if(r){t.push(r)}}}isCandidateHeroImage(e){if(!e.tagName){return null}const t=e.attribs?e.attribs.layout:"";if(t==="nodisplay"){return null}if(e.tagName==="amp-img"){return this.isCandidateImageForPreloading(e)}if(e.tagName==="amp-video"){return this.isCandidateVideoPosterImage(e)}if(this.isAmpIframe(e)){return this.isCandidateIframePlaceholderImage(e)}return null}isAmpIframe(e){return e.tagName==="amp-iframe"||e.tagName==="amp-video-iframe"}isCandidateVideoPosterImage(e){const t=e.attribs.poster;if(!t)return null;if(!f(t)){return null}const{layout:r,width:i,height:n,media:s}=e.attribs;if(this.isTinyNode(r,i,n)){return null}return{src:t,media:s,srcset:""}}isCandidateIframePlaceholderImage(e){if(!e.children||e.children.length===0){return null}const{layout:t,width:r,height:i}=e.attribs;if(this.isTinyNode(t,r,i))return null;return this.getPlaceholderImage(e)}getPlaceholderImage(e){for(const t of e.children){if(t.tagName==="amp-img"&&s(t,"placeholder")&&f(t.attribs.src)){return{ampImg:t,src:t.attribs.src,media:e.attribs.media,srcset:t.attribs.srcset||""}}}return null}isCandidateImageForPreloading(e){const t=e.attribs.src;if(!t){return null}if(!f(t)){return null}let{width:r,height:i,srcset:n,layout:s,media:a}=e.attribs;if(!r&&!i){if(s==="fill"){({width:r,height:i}=this.nodeDimensionsFromParent(e))}else{return null}}if(this.isTinyNode(s,r,i)){return null}return{ampImg:e,src:t,srcset:n,media:a}}isTinyNode(e,t,r){if(t<=0||r<=0)return true;if(e==="responsive"){return false}return t<m||r<m}nodeDimensionsFromParent(e){while(e.parent){e=e.parent;if(!e.attribs){continue}const t=e.attribs.width;const r=e.attribs.height;if(!t&&!r){continue}return{width:t,height:r}}return{width:0,height:0}}generateImg(e){if(!e){return}e.attribs["i-amphtml-ssr"]="";const t=n("img",{class:"i-amphtml-fill-content i-amphtml-replaced-content",decoding:"async"});const r=this.getNoscriptFallbackImage(e);if(r){if(s(r,"loading")){t.attribs.loading=r.attribs.loading}a(r.parent)}else if(!this.isMarkedAsHeroImage(e)){t.attribs["loading"]="lazy"}if(!s(e.attribs,"data-hero")){e.attribs["data-hero"]=""}const o=["alt","attribution","referrerpolicy","src","srcset","sizes","title"];for(const r of o){if(s(e,r)){t.attribs[r]=e.attribs[r]}}const l=[];const c=e.attribs["object-fit"];if(c){l.push(`object-fit:${c}`)}const u=e.attribs["object-position"];if(u){l.push(`object-position:${u}`)}if(l.length>0){t.attribs.style=l.join(";")}i(e,t)}getNoscriptFallbackImage(e){const t=c(e,"noscript");if(!t){return null}return c(t,"img")}isMarkedAsHeroImage(e){while(e){if(!e.tagName){e=e.parent;continue}if(s(e,"data-hero")){return true}if(e.tagName==="body"||e.tagName==="html"){return false}e=e.parent}return false}}e.exports=OptimizeHeroImage},714:(e,t,r)=>{"use strict";const{hasAttribute:i,nextNode:n,firstChildByTag:s}=r(2547);const{skipNodeAndChildren:a}=r(7041);const{isValidImageSrcURL:o}=r(3741);const{isTemplate:l}=r(8846);const c=100;const u=[39,47,56,68,82,100,120,150,180,220,270,330,390,470,560,680,820,1e3,1200,1440,1750,2e3,2500];const d=u[u-1];const f=3;class SrcsetWidth{constructor(e,t=-1,r=f){this.widthList_=[];this.setBaseWidth(e,t,r)}setBaseWidth(e,t=-1,r=f){this.widthList_.length=0;let i=-1;if(t>0&&e>t){return}for(let n=r;n>0;--n){let r=this.roundUp(e*n);if(t>0&&r>t){r=t}if(r!=i){this.widthList_.push(r)}i=r}}moreWidth(){return this.widthList_.length>0}nextWidth(){return this.widthList_.pop()}isValid(){return this.widthList_.length>1}roundUp(e){for(const t of u){if(t>e){return t}}return u[u.length-1]}}class OptimizeImages{constructor(e){this.log=e.log;this.imageOptimizer=e.imageOptimizer;this.maxImageWidth=d;this.maxSrcsetValues=f}async transform(e){if(!this.imageOptimizer){return}const t=s(e,"html");const r=s(t,"body");let i=r;const o=[];while(i!==null){if(l(i)){i=a(i)}else{if(i.tagName==="amp-img"){o.push(this.optimizeImage(i))}i=n(i)}}return Promise.all(o)}async optimizeImage(e){if(i(e,"srcset")){return}if(!i(e,"src")){return}const t=e.attribs.src;if(!o(t)){return}if(t.endsWith(",")){return}const r=e.attribs.width;if(isNaN(Number.parseInt(r))){return}const{layout:n,height:s,sizes:a}=e.attribs;const l=n==="responsive"||!n&&s&&a;if(l&&r<c){return}const u=new SrcsetWidth(r,this.maxImageWidth,this.maxSrcsetValues);if(!u.isValid()){return}let d="";while(u.moreWidth()){const e=u.nextWidth();try{const r=await this.imageOptimizer(t,e);if(r){if(!u.moreWidth()){d+=r}else{d+=`${r} ${e}w, `}}}catch(e){this.log.error("Exception when optimizing image",t,e)}}if(d){e.attribs.srcset=d;this.log.debug("Generating img srcset",t,e.attribs.srcset)}}}e.exports=OptimizeImages},743:(e,t,r)=>{"use strict";e.exports=r(6037)},3842:(e,t,r)=>{"use strict";const{createElement:i,nextNode:n,insertAfter:s,firstChildByTag:a}=r(2547);const{findMetaViewport:o,skipNodeAndChildren:l}=r(7041);const{isTemplate:c}=r(8846);const u=5;class PreloadImages{transform(e,t){const r=t.imagePreloadCount||u;const i=a(e,"html");const d=a(i,"head");const f=a(i,"body");const p=new Map;let h=f;while(h!==null){if(p.size>=r){break}if(c(h)){h=l(h)}else{this.addImage(p,h);h=n(h)}}let m=o(d);for(const e of p.values()){s(d,e,m);m=e}}addImage(e,t){const r=this.extractImageUrl(t);if(!r){return}if(t.attribs.srcset){return}e.set(r,this.createPreload(r,t.attribs.media))}extractImageUrl(e){if(!e.attribs){return null}if(e.tagName==="amp-img"){return e.attribs.src}if(e.tagName==="amp-video"){return e.attribs.poster}return null}createPreload(e,t){const r=i("link",{rel:"preload",href:e,as:"image"});if(t){r.attribs.media=t}return r}}e.exports=PreloadImages},3144:(e,t,r)=>{"use strict";const{firstChildByTag:i}=r(2547);const n=new Set(["dns-prefetch","preconnect","prefetch","preload","prerender"]);class PruneDuplicateResourceHints{transform(e){const t=new Map;const r=i(e,"html");if(!r){return}const n=i(r,"head");if(!n){return}const s=[];for(let e=n.firstChild;e!==null;e=e.nextSibling){if(this._notPruneableHintLink(e)){s.push(e)}else if(!this._alreadyLoaded(e,t)){this._markPreloaded(e,t);s.push(e)}}n.childNodes=s}_notPruneableHintLink(e){if(e.tagName!=="link"){return true}if(!e.attribs){return true}if(!e.attribs.rel){return true}if(!e.attribs.href){return true}if(e.attribs.rel==="preload"&&!e.attribs.as){return true}return!n.has(e.attribs.rel)}_alreadyLoaded(e,t){const r=e.attribs.rel;const i=e.attribs.href;if(!t.has(i)){return false}const n=t.get(i);return n.has(r)}_markPreloaded(e,t){const r=e.attribs.rel;const i=e.attribs.href;let n=t.get(i);if(!n){n=new Set;t.set(i,n)}n.add(r)}}e.exports=PruneDuplicateResourceHints},1865:(e,t,r)=>{"use strict";const{firstChildByTag:i}=r(2547);const{AMP_TAGS:n}=r(8846);class RemoveAmpAttribute{transform(e){const t=i(e,"html");if(!t){return}for(let e=0,r=n.length;e<r;e++){delete t.attribs[n[e]]}}}e.exports=RemoveAmpAttribute},9401:(e,t,r)=>{"use strict";const{hasAttribute:i,firstChildByTag:n,nextNode:s}=r(2547);class RemoveCspNonce{transform(e){const t=n(e,"html");if(!t){return}let r=t;while(r){if(r.tagName==="script"){if(i(r,"nonce")){delete r.attribs["nonce"]}}r=s(r)}}}e.exports=RemoveCspNonce},707:(e,t,r)=>{"use strict";const{appendChild:i,appendAll:n,hasAttribute:s,firstChildByTag:a}=r(2547);const{isRenderDelayingExtension:o}=r(9170);class HeadNodes{constructor(){this._styleAmpRuntime=null;this._linkStyleAmpRuntime=null;this._metaCharset=null;this._metaViewport=null;this._scriptAmpEngine=[];this._metaOther=[];this._resourceHintLinks=[];this._scriptRenderDelayingExtensions=new Map;this._scriptNonRenderDelayingExtensions=new Map;this._linkIcons=[];this._styleAmpCustom=null;this._linkStylesheetsBeforeAmpCustom=[];this._others=[];this._styleAmpBoilerplate=null;this._noscript=null}register(e){e.forEach(this._registerNode.bind(this))}uniquifyAndSortCustomElements(){this._scriptRenderDelayingExtensions=this._sortExtensions(this._scriptRenderDelayingExtensions);this._scriptNonRenderDelayingExtensions=this._sortExtensions(this._scriptNonRenderDelayingExtensions)}_sortExtensions(e){const t=new Map([...e].sort(((e,t)=>e[0].localeCompare(t[0]))));return[].concat.apply([],Array.from(t.values()))}appendToHead(e){i(e,this._metaCharset);i(e,this._metaViewport);n(e,this._resourceHintLinks);n(e,this._metaOther);i(e,this._linkStyleAmpRuntime);i(e,this._styleAmpRuntime);n(e,this._scriptAmpEngine);n(e,this._scriptRenderDelayingExtensions);n(e,this._scriptNonRenderDelayingExtensions);i(e,this._styleAmpCustom);i(e,this._styleAmpBoilerplate);i(e,this._noscript);n(e,this._linkIcons);n(e,this._linkStylesheetsBeforeAmpCustom);n(e,this._others)}_registerNode(e){if(e.tagName==="meta"){this._registerMeta(e)}else if(e.tagName==="script"){this._registerScript(e)}else if(e.tagName==="style"){this._registerStyle(e)}else if(e.tagName==="link"){this._registerLink(e)}else if(e.tagName==="noscript"){this._noscript=e}else if(e.tagName){this._others.push(e)}}_registerMeta(e){if(e.attribs.charset){this._metaCharset=e;return}if(e.attribs.name=="viewport"){this._metaViewport=e;return}this._metaOther.push(e)}_registerScript(e){const t=s(e,"nomodule")?1:0;const r=this._getName(e);if(s(e,"src")&&!r){this._scriptAmpEngine[t]=e;return}if(s(e,"custom-element")){if(o(e)){this._registerExtension(this._scriptRenderDelayingExtensions,r,t,e);return}this._registerExtension(this._scriptNonRenderDelayingExtensions,r,t,e);return}if(s(e,"custom-template")){this._registerExtension(this._scriptNonRenderDelayingExtensions,r,t,e);return}this._others.push(e)}_registerExtension(e,t,r,i){const n=e.get(t)||[];n[r]=i;e.set(t,n)}_registerStyle(e){if(s(e,"amp-runtime")){this._styleAmpRuntime=e;return}if(s("node, amp-custom")){this._styleAmpCustom=e;return}if(s(e,"amp-boilerplate")||s(e,"amp4ads-boilerplate")){this._styleAmpBoilerplate=e;return}this._others.push(e)}_registerLink(e){const t=e.attribs.rel;if(t==="stylesheet"){if(e.attribs.href.endsWith("/v0.css")){this._linkStyleAmpRuntime=e;return}if(!this._styleAmpCustom){this._linkStylesheetsBeforeAmpCustom.push(e);return}}if(t==="icon"||t==="shortcut icon"||t==="icon shortcut"){this._linkIcons.push(e);return}if(t==="preload"||t==="prefetch"||t==="dns-prefetch"||t==="preconnect"||t=="modulepreload"){this._resourceHintLinks.push(e);return}this._others.push(e)}_getName(e){return e.attribs["custom-element"]||e.attribs["custom-template"]}}class ReorderHeadTransformer{transform(e){const t=a(e,"html");if(!t){return}const r=a(t,"head");if(!r){return}if(!r.children){return}const i=new HeadNodes;i.register(r.children);i.uniquifyAndSortCustomElements();r.children=[];i.appendToHead(r)}}e.exports=ReorderHeadTransformer},7185:(e,t,r)=>{"use strict";const{createElement:i,hasAttribute:n,firstChildByTag:s,insertAfter:a,insertBefore:o,remove:l}=r(2547);const{AMP_CACHE_HOST:c}=r(8846);const{findMetaViewport:u}=r(7041);const{calculateHost:d}=r(859);class RewriteAmpUrls{constructor(e){this.esmModulesEnabled=e.esmModulesEnabled!==false;this.log=e.log}transform(e,t){const r=s(e,"html");const i=s(r,"head");if(!i)return;const o=d(t);let c=i.firstChild;let f=u(i);const p=this.esmModulesEnabled||t.esmModulesEnabled;t.esmModulesEnabled=p;const h=!n(r,"i-amphtml-no-boilerplate");const m=[];while(c){if(c.tagName==="script"&&this._usesAmpCacheUrl(c.attribs.src)){c.attribs.src=this._replaceUrl(c.attribs.src,o);if(p){const e=this._addEsm(c,h);if(h&&e){m.push(e)}}else if(h){m.push(this._createPreload(c.attribs.src,"script"))}}else if(c.tagName==="link"&&c.attribs.rel==="stylesheet"&&this._usesAmpCacheUrl(c.attribs.href)){c.attribs.href=this._replaceUrl(c.attribs.href,o);if(h){m.push(this._createPreload(c.attribs.href,"style"))}}else if(c.tagName==="link"&&c.attribs.rel==="preload"&&this._usesAmpCacheUrl(c.attribs.href)){if(p&&this._shouldPreload(c.attribs.href)){l(c)}else{c.attribs.href=this._replaceUrl(c.attribs.href,o)}}c=c.nextSibling}for(const e of m){if(e){a(i,e,f)}}if(!this._usesAmpCacheUrl(o)&&!t.lts){try{const e=new URL(o);this._addMeta(i,"runtime-host",e.origin)}catch(e){this.log.warn("ampUrlPrefix must be an absolute URL")}}if(t.geoApiUrl&&!t.lts){this._addMeta(i,"amp-geo-api",t.geoApiUrl)}}_usesAmpCacheUrl(e){if(!e){return}return e.startsWith(c)}_replaceUrl(e,t){return t+e.substring(c.length)}_addEsm(e,t){let r=null;const n=e.attribs.src.replace(/\.js$/,".mjs");if(t&&this._shouldPreload(e.attribs.src)){const e=i("link",{as:"script",crossorigin:"anonymous",href:n,rel:"modulepreload"});r=e}const s=i("script",{async:"",nomodule:"",src:e.attribs.src,crossorigin:"anonymous"});const a=e.attribs["custom-element"];if(a){s.attribs["custom-element"]=a}const l=e.attribs["custom-template"];if(l){s.attribs["custom-template"]=l}o(e.parent,s,e);e.attribs.type="module";e.attribs.crossorigin="anonymous";e.attribs.src=n;return r}_createPreload(e,t){if(!this._shouldPreload(e)){return null}return i("link",{rel:"preload",href:e,as:t})}_shouldPreload(e){return e.endsWith("v0.js")||e.endsWith("v0.css")}_addMeta(e,t,r){const n=i("meta",{name:t,content:r});o(e,n,s(e,"script"))}isAbsoluteUrl_(e){try{new URL(e);return true}catch(e){return false}}}e.exports=RewriteAmpUrls},2079:(e,t,r)=>{"use strict";const{insertText:i,createElement:n,hasAttribute:s,firstChildByTag:a}=r(2547);const o=r(7595);const l=r(977);const c=r(8937);const u=new Set(["animation-timing-function","offset-distance","opacity","visibility","transform","-webkit-transform","-moz-transform","-o-transform","-ms-transform"]);class SeparateKeyframes{constructor(e){this.log_=e.log.tag("SeparateKeyframes");this.minify=e.minify!==false}async transform(e){const t=a(e,"html");if(!t)return;const r=a(t,"head");if(!r)return;const d=a(t,"body")||r;if(this.isAmpStory(d)){return}let f;let p;r.children=r.children.filter((e=>{if(e.tagName!=="style")return true;if(!p&&s(e,"amp-keyframes")){p=e;return false}if(!f&&s(e,"amp-custom")){f=e}return true}));const h=this.minify?[c]:[];if(!f)return;let m=f.children[0];if(!m||!m.data)return;m=m.data;const g=l.parse("");const isInvalidKeyframe=e=>{let t;for(const r of e.nodes){for(const e of r.nodes){if(!u.has(e.prop)){t=e.prop;break}}if(t)break}return t};const keyframesPlugin=()=>{const e=this.logInvalid.bind(this);return{postcssPlugin:"postcss-amp-keyframes-mover",Once(t){t.nodes=t.nodes.filter((t=>{if(t.name==="keyframes"){const r=isInvalidKeyframe(t);if(r){e(t.name,r);return true}g.nodes.push(t);return false}if(t.name==="media"||t.name==="supports"){const r=Object.assign({},t,{nodes:[]});t.nodes=t.nodes.filter((t=>{if(t.name!=="keyframes")return true;const i=isInvalidKeyframe(t);if(i){e(t.name,i);return true}r.nodes.push(t)}));if(r.nodes.length){g.nodes.push(r)}return t.nodes.length}return true}))}}};keyframesPlugin.postcss=true;const{css:b}=await l([...h,keyframesPlugin]).process(m,{from:undefined,parser:o}).catch((e=>{this.log_.warn(`Failed to process CSS`,e.message);return{css:m}}));if(g.nodes.length===0){f.children[0].data=b;return}if(!p){d.children=d.children.filter((e=>{if(e.tagName==="style"&&s(e,"amp-keyframes")){p=e;return false}return true}));if(!p){p=n("style",{"amp-keyframes":""})}}const v=p.children[0];const y=l.parse(v&&v.data||"");y.nodes=g.nodes.concat(y.nodes);let x="";l.stringify(y,(e=>{x+=e}));if(h.length>0){const e=await l(h).process(x,{from:undefined,parser:o});x=e.css}if(!v){i(p,x)}else{v.data=x}d.children.push(p);f.children[0].data=b}logInvalid(e,t){this.log_.warn(`Found invalid keyframe property '${t}' in '${e}' not moving to style[amp-keyframes]`)}isAmpStory(e){return e.children.some((e=>e.tagName==="amp-story"))}}e.exports=SeparateKeyframes},4626:(e,t,r)=>{"use strict";const{hasAttribute:i,remove:n,createElement:s,insertBefore:a,nextNode:o,firstChildByTag:l}=r(2547);const{skipNodeAndChildren:c}=r(7041);const{isRenderDelayingExtension:u,isCustomElement:d}=r(9170);const{applyLayout:f}=r(9464);const{isTemplate:p}=r(8846);const h=r(6070);class ServerSideRendering{constructor(e){this.log_=e.log.tag("ServerSideRendering")}_hasAncestorWithTag(e,t){for(let r=e.parent;r!==null;r=r.parent){if(r.tagName===t){return true}}return false}transform(e){const t=new h(this.log_);const r=l(e,"html");if(!r){return}const o=l(r,"body");const c=l(r,"head");if(typeof r.attribs["i-amphtml-layout"]!=="undefined"&&r.attribs["i-amphtml-layout"]!==null){return}r.attribs["i-amphtml-layout"]="";let p=true;for(let e=o;e;e=this.nextNonTemplateNode(e)){t.addNode(e);if(!d(e)){continue}if(e.tagName==="amp-experiment"&&this.isAmpExperimentUsed(e)){p=false;this.log_.debug("cannot remove boilerplate: amp-experiment")}if(e.tagName==="amp-audio"){p=false;this.log_.debug("cannot remove boilerplate: amp-audio");continue}if(!f(e,this.log_)){this.log_.debug("cannot remove boilerplate: unsupported layout");p=false;continue}}t.apply();const m=s("style",{"amp-runtime":""});const g=c.children&&c.children.length?c.children[0]:null;a(c,m,g);let b;for(let e=c.firstChild;e;e=e.nextSibling){if(e.tagName==="script"&&i(e,"custom-element")&&e.attribs["custom-element"]==="amp-experiment"){continue}if(u(e)){this.log_.debug("cannot remove boilerplate because of a render delaying extension: ",e.tagName);p=false}if(i(e,"amp-custom")){b=e}}t.applyToCustomStyles(c,b);if(!t.canRemoveBoilerplate){p=false}if(!p){return}r.attribs["i-amphtml-no-boilerplate"]="";const v=[];for(let e=c.firstChild;e;e=e.nextSibling){if(e.tagName==="noscript"||e.tagName==="style"&&i(e,"amp-boilerplate")){v.push(e)}}for(const e of v){n(e)}}isAmpExperimentUsed(e){let t;for(const r of e.children||[]){if(r.tagName==="script"&&r.attribs&&r.attribs["type"]==="application/json"){t=r;break}}if(!t){return false}if(t.children.length!==1){return false}const r=t.firstChild;if(r.type!=="text"){return false}try{const e=JSON.parse(r.data);return typeof e==="object"&&Object.keys(e).length>0}catch(e){return false}}nextNonTemplateNode(e){if(p(e)){return c(e)}else{return o(e)}}}e.exports=ServerSideRendering},4178:(e,t,r)=>{"use strict";const i=r(7315).log.tag("AMP Runtime Version");const n="https://cdn.ampproject.org";const s="/rtv/metadata";const a="/version.txt";const o={canary:0,prod:1,lts:2};class RuntimeVersion{constructor(e){this.fetch_=e}async currentVersion(e={}){if(e.ampUrlPrefix&&!this.isAbsoluteUrl_(e.ampUrlPrefix)){throw new Error("host must be an absolute URL")}if(e.canary&&e.lts){throw new Error("lts flag is not compatible with canary flag")}let t=o.prod;if(e.canary){t=o.canary}else if(e.lts){t=o.lts}const r=e.ampUrlPrefix?e.ampUrlPrefix.replace(/\/$/,""):n;let i=await this.getVersionFromRuntimeMetadata_(r,t);if(!i&&t===o.prod){i=await this.getVersionFromVersionTxt_(r,t)}return i}async getVersionFromRuntimeMetadata_(e,t){const r=e+s;i.debug(`Fetching version from ${r}`);let n;try{n=await this.fetch_(r)}catch(e){}if(!n||!n.ok){i.debug("RTV metadata endpoint did not respond with a successful status code");return}let a;try{a=await n.json()}catch(e){i.debug("RTV metadata JSON malformed");return}let l;if(t===o.canary){if(Array.isArray(a.diversions)&&a.diversions[0]&&a.diversions[0].startsWith(this.getRtvConfigCode_(t))){l=a.diversions[0]}if(!l){i.debug("RTV metadata JSON malformed, canary version not in diversions array")}}else if(t===o.lts){l=a.ltsRuntimeVersion;if(!l){i.debug("RTV metadata JSON malformed, lts version not in ltsRuntimeVersion")}}else if(t===o.prod){l=a.ampRuntimeVersion;if(!l){i.debug("RTV metadata JSON malformed, production version not in ampRuntimeVersion")}}return l}async getVersionFromVersionTxt_(e,t){if(t!==o.prod){i.debug(`version.txt lookup only supported for prod releases`);return}let r=e+a;i.debug(`Falling back to ${r}`);let n;try{n=await this.fetch_(r)}catch(e){}if(!n||!n.ok){i.debug("version.txt endpoint did not respond with a successful status code");return}let s;try{s=(await n.text()).trim();if(s!==encodeURIComponent(s)){throw new Error}}catch(e){i.debug("Version string malformed, not URL compatible");return}return this.getRtvConfigCode_(t)+s}getRtvConfigCode_(e){if(e===o.canary){return"00"}return"01"}isAbsoluteUrl_(e){try{new URL(e);return true}catch(e){return false}}}e.exports=RuntimeVersion},9874:(e,t,r)=>{"use strict";const{calculateHash:i}=r(649);e.exports={calculateHash:i}},649:(e,t,r)=>{const i=r(6113);const n=new Set(["sha384"]);const s="sha384";function calculateHash(e,{algorithm:t=s}={}){const r=t.toLowerCase();if(!n.has(r)){throw new Error(`Unsupported algorithm for CSP: ${r}`)}if(typeof e==="string"){e=Buffer.from(e,"utf8")}const a=i.createHash(r);const o=a.update(e);const l=base64URLFormat(o.digest("base64"));return`${r}-${l}`}function base64URLFormat(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}e.exports={calculateHash:calculateHash}},4934:(e,t,r)=>{"use strict";const i=r(1379);const n=r(1822);let s=null;async function fetch(e={}){if(!e.noCache&&s){return s}let t=e.rules;if(!t){t=await i(e)}s=new n(t);return s}e.exports={fetch:fetch}},1822:e=>{class AmpValidatorRules{constructor(e){this.raw=e;this.tags=[];this.extensions=[];this.errors={};this.extensionCache_={};this.initRules_(e)}getTagsForFormat(e,t=false){e=e.toLowerCase();return this.tags.filter((r=>r.htmlFormat.includes(e.toUpperCase())&&this.checkEntityFormat_(r,e)&&this.checkEntityTransformed_(r,t))).map((r=>{r=Object.assign({},r);r.attrs=r.attrs.filter((r=>this.checkEntityFormat_(r,e)&&this.checkEntityTransformed_(r,t)));return r}))}getExtension(e,t){e=e.toLowerCase();t=t.toLowerCase();const r=`${e}|${t}`;return this.extensionCache_[r]||null}checkEntityTransformed_(e,t){const r=this.isEnabled_(e,"transformed");const i=this.isDisabled_(e,"transformed");if(t){return r!==false&&i!==true}return r!==true&&i!==false}checkEntityFormat_(e,t){t=t.toLowerCase();const r=this.isEnabled_(e,t);const i=this.isDisabled_(e,t);return r!==false&&i!==true}isEnabled_(e,t){if(!e.enabledBy){return null}return e.enabledBy.includes(t)}isDisabled_(e,t){if(!e.disabledBy){return null}return e.disabledBy.includes(t)}initRules_(e){this.initErrors_(e);this.initAttrLists_(e);this.initTags_(e);this.initExtensions_(e)}initErrors_(e){this.errors={};for(const t of e.errorFormats){const e=this.errors[t.code]||{};e.format=t.format;this.errors[t.code]=e}for(const t of e.errorSpecificity){const e=this.errors[t.code]||{};e.specificity=t.specificity;this.errors[t.code]=e}}initAttrLists_(e){this.attrLists_={};this.specialAttrLists_={};for(const{name:t,attrs:r}of e.attrLists){if(t.startsWith("$")){this.specialAttrLists_[t]=r}else{this.attrLists_[t]=r}}this.specialAttrLists_.$AMP_LAYOUT_ATTRS.forEach((e=>e.layout=true));this.specialAttrLists_.$GLOBAL_ATTRS.forEach((e=>e.global=true))}initTags_(e){this.tags=e.tags.filter((e=>!e.extensionSpec)).map((e=>{e.attrs=e.attrs||[];if(e.attrLists){for(const t of e.attrLists){e.attrs.push(...this.attrLists_[t])}delete e.attrLists}if(e.ampLayout){e.attrs.push(...this.specialAttrLists_.$AMP_LAYOUT_ATTRS)}e.attrs.push(...this.specialAttrLists_.$GLOBAL_ATTRS);return e}))}initExtensions_(e){this.extensions=e.tags.filter((e=>e.extensionSpec)).map((e=>Object.assign({},e.extensionSpec,{htmlFormat:e.htmlFormat})));for(const e of this.extensions){const t=e.name.toLowerCase();for(let r of e.htmlFormat){r=r.toLowerCase();const i=`${r}|${t}`;this.extensionCache_[i]=e}}}}e.exports=AmpValidatorRules},1379:(e,t,r)=>{const i=r(3114);const n="https://cdn.ampproject.org/v0/validator.json";async function loadRemote(e){const t=await i(e);return t.json()}async function loadRules({url:e}){e=e||n;return loadRemote(e)}e.exports=loadRules},3114:(e,t,r)=>{const i=r(4429);const n=i.default||i;const fetch=function(e,t){if(/^\/\//.test(e)){e="https:"+e}return n.call(this,e,t)};fetch.ponyfill=true;e.exports=t=fetch;t.fetch=fetch;t.Headers=i.Headers;t.Request=i.Request;t.Response=i.Response;t["default"]=fetch},6375:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.attributeNames=t.elementNames=void 0;t.elementNames=new Map([["altglyph","altGlyph"],["altglyphdef","altGlyphDef"],["altglyphitem","altGlyphItem"],["animatecolor","animateColor"],["animatemotion","animateMotion"],["animatetransform","animateTransform"],["clippath","clipPath"],["feblend","feBlend"],["fecolormatrix","feColorMatrix"],["fecomponenttransfer","feComponentTransfer"],["fecomposite","feComposite"],["feconvolvematrix","feConvolveMatrix"],["fediffuselighting","feDiffuseLighting"],["fedisplacementmap","feDisplacementMap"],["fedistantlight","feDistantLight"],["fedropshadow","feDropShadow"],["feflood","feFlood"],["fefunca","feFuncA"],["fefuncb","feFuncB"],["fefuncg","feFuncG"],["fefuncr","feFuncR"],["fegaussianblur","feGaussianBlur"],["feimage","feImage"],["femerge","feMerge"],["femergenode","feMergeNode"],["femorphology","feMorphology"],["feoffset","feOffset"],["fepointlight","fePointLight"],["fespecularlighting","feSpecularLighting"],["fespotlight","feSpotLight"],["fetile","feTile"],["feturbulence","feTurbulence"],["foreignobject","foreignObject"],["glyphref","glyphRef"],["lineargradient","linearGradient"],["radialgradient","radialGradient"],["textpath","textPath"]]);t.attributeNames=new Map([["definitionurl","definitionURL"],["attributename","attributeName"],["attributetype","attributeType"],["basefrequency","baseFrequency"],["baseprofile","baseProfile"],["calcmode","calcMode"],["clippathunits","clipPathUnits"],["diffuseconstant","diffuseConstant"],["edgemode","edgeMode"],["filterunits","filterUnits"],["glyphref","glyphRef"],["gradienttransform","gradientTransform"],["gradientunits","gradientUnits"],["kernelmatrix","kernelMatrix"],["kernelunitlength","kernelUnitLength"],["keypoints","keyPoints"],["keysplines","keySplines"],["keytimes","keyTimes"],["lengthadjust","lengthAdjust"],["limitingconeangle","limitingConeAngle"],["markerheight","markerHeight"],["markerunits","markerUnits"],["markerwidth","markerWidth"],["maskcontentunits","maskContentUnits"],["maskunits","maskUnits"],["numoctaves","numOctaves"],["pathlength","pathLength"],["patterncontentunits","patternContentUnits"],["patterntransform","patternTransform"],["patternunits","patternUnits"],["pointsatx","pointsAtX"],["pointsaty","pointsAtY"],["pointsatz","pointsAtZ"],["preservealpha","preserveAlpha"],["preserveaspectratio","preserveAspectRatio"],["primitiveunits","primitiveUnits"],["refx","refX"],["refy","refY"],["repeatcount","repeatCount"],["repeatdur","repeatDur"],["requiredextensions","requiredExtensions"],["requiredfeatures","requiredFeatures"],["specularconstant","specularConstant"],["specularexponent","specularExponent"],["spreadmethod","spreadMethod"],["startoffset","startOffset"],["stddeviation","stdDeviation"],["stitchtiles","stitchTiles"],["surfacescale","surfaceScale"],["systemlanguage","systemLanguage"],["tablevalues","tableValues"],["targetx","targetX"],["targety","targetY"],["textlength","textLength"],["viewbox","viewBox"],["viewtarget","viewTarget"],["xchannelselector","xChannelSelector"],["ychannelselector","yChannelSelector"],["zoomandpan","zoomAndPan"]])},4404:function(e,t,r){"use strict";var i=this&&this.__assign||function(){i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n))e[n]=t[n]}return e};return i.apply(this,arguments)};var n=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);s(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});var o=a(r(9391));var l=r(9280);var c=r(6375);var u=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function formatAttributes(e,t){if(!e)return;return Object.keys(e).map((function(r){var i,n;var s=(i=e[r])!==null&&i!==void 0?i:"";if(t.xmlMode==="foreign"){r=(n=c.attributeNames.get(r))!==null&&n!==void 0?n:r}if(!t.emptyAttrs&&!t.xmlMode&&s===""){return r}return r+'="'+(t.decodeEntities?l.encodeXML(s):s.replace(/"/g,"&quot;"))+'"'})).join(" ")}var d=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function render(e,t){if(t===void 0){t={}}var r=Array.isArray(e)||e.cheerio?e:[e];var i="";for(var n=0;n<r.length;n++){i+=renderNode(r[n],t)}return i}t["default"]=render;function renderNode(e,t){switch(e.type){case o.Root:return render(e.children,t);case o.Directive:case o.Doctype:return renderDirective(e);case o.Comment:return renderComment(e);case o.CDATA:return renderCdata(e);case o.Script:case o.Style:case o.Tag:return renderTag(e,t);case o.Text:return renderText(e,t)}}var f=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]);var p=new Set(["svg","math"]);function renderTag(e,t){var r;if(t.xmlMode==="foreign"){e.name=(r=c.elementNames.get(e.name))!==null&&r!==void 0?r:e.name;if(e.parent&&f.has(e.parent.name)){t=i(i({},t),{xmlMode:false})}}if(!t.xmlMode&&p.has(e.name)){t=i(i({},t),{xmlMode:"foreign"})}var n="<"+e.name;var s=formatAttributes(e.attribs,t);if(s){n+=" "+s}if(e.children.length===0&&(t.xmlMode?t.selfClosingTags!==false:t.selfClosingTags&&d.has(e.name))){if(!t.xmlMode)n+=" ";n+="/>"}else{n+=">";if(e.children.length>0){n+=render(e.children,t)}if(t.xmlMode||!d.has(e.name)){n+="</"+e.name+">"}}return n}function renderDirective(e){return"<"+e.data+">"}function renderText(e,t){var r=e.data||"";if(t.decodeEntities&&!(e.parent&&u.has(e.parent.name))){r=l.encodeXML(r)}return r}function renderCdata(e){return"<![CDATA["+e.children[0].data+"]]>"}function renderComment(e){return"\x3c!--"+e.data+"--\x3e"}},7537:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.attributeNames=t.elementNames=void 0;t.elementNames=new Map([["altglyph","altGlyph"],["altglyphdef","altGlyphDef"],["altglyphitem","altGlyphItem"],["animatecolor","animateColor"],["animatemotion","animateMotion"],["animatetransform","animateTransform"],["clippath","clipPath"],["feblend","feBlend"],["fecolormatrix","feColorMatrix"],["fecomponenttransfer","feComponentTransfer"],["fecomposite","feComposite"],["feconvolvematrix","feConvolveMatrix"],["fediffuselighting","feDiffuseLighting"],["fedisplacementmap","feDisplacementMap"],["fedistantlight","feDistantLight"],["fedropshadow","feDropShadow"],["feflood","feFlood"],["fefunca","feFuncA"],["fefuncb","feFuncB"],["fefuncg","feFuncG"],["fefuncr","feFuncR"],["fegaussianblur","feGaussianBlur"],["feimage","feImage"],["femerge","feMerge"],["femergenode","feMergeNode"],["femorphology","feMorphology"],["feoffset","feOffset"],["fepointlight","fePointLight"],["fespecularlighting","feSpecularLighting"],["fespotlight","feSpotLight"],["fetile","feTile"],["feturbulence","feTurbulence"],["foreignobject","foreignObject"],["glyphref","glyphRef"],["lineargradient","linearGradient"],["radialgradient","radialGradient"],["textpath","textPath"]]);t.attributeNames=new Map([["definitionurl","definitionURL"],["attributename","attributeName"],["attributetype","attributeType"],["basefrequency","baseFrequency"],["baseprofile","baseProfile"],["calcmode","calcMode"],["clippathunits","clipPathUnits"],["diffuseconstant","diffuseConstant"],["edgemode","edgeMode"],["filterunits","filterUnits"],["glyphref","glyphRef"],["gradienttransform","gradientTransform"],["gradientunits","gradientUnits"],["kernelmatrix","kernelMatrix"],["kernelunitlength","kernelUnitLength"],["keypoints","keyPoints"],["keysplines","keySplines"],["keytimes","keyTimes"],["lengthadjust","lengthAdjust"],["limitingconeangle","limitingConeAngle"],["markerheight","markerHeight"],["markerunits","markerUnits"],["markerwidth","markerWidth"],["maskcontentunits","maskContentUnits"],["maskunits","maskUnits"],["numoctaves","numOctaves"],["pathlength","pathLength"],["patterncontentunits","patternContentUnits"],["patterntransform","patternTransform"],["patternunits","patternUnits"],["pointsatx","pointsAtX"],["pointsaty","pointsAtY"],["pointsatz","pointsAtZ"],["preservealpha","preserveAlpha"],["preserveaspectratio","preserveAspectRatio"],["primitiveunits","primitiveUnits"],["refx","refX"],["refy","refY"],["repeatcount","repeatCount"],["repeatdur","repeatDur"],["requiredextensions","requiredExtensions"],["requiredfeatures","requiredFeatures"],["specularconstant","specularConstant"],["specularexponent","specularExponent"],["spreadmethod","spreadMethod"],["startoffset","startOffset"],["stddeviation","stdDeviation"],["stitchtiles","stitchTiles"],["surfacescale","surfaceScale"],["systemlanguage","systemLanguage"],["tablevalues","tableValues"],["targetx","targetX"],["targety","targetY"],["textlength","textLength"],["viewbox","viewBox"],["viewtarget","viewTarget"],["xchannelselector","xChannelSelector"],["ychannelselector","yChannelSelector"],["zoomandpan","zoomAndPan"]])},4614:function(e,t,r){"use strict";var i=this&&this.__assign||function(){i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n))e[n]=t[n]}return e};return i.apply(this,arguments)};var n=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);s(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});var o=a(r(9391));var l=r(9280);var c=r(7537);var u=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function formatAttributes(e,t){if(!e)return;return Object.keys(e).map((function(r){var i,n;var s=(i=e[r])!==null&&i!==void 0?i:"";if(t.xmlMode==="foreign"){r=(n=c.attributeNames.get(r))!==null&&n!==void 0?n:r}if(!t.emptyAttrs&&!t.xmlMode&&s===""){return r}return r+'="'+(t.decodeEntities!==false?l.encodeXML(s):s.replace(/"/g,"&quot;"))+'"'})).join(" ")}var d=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function render(e,t){if(t===void 0){t={}}var r=Array.isArray(e)||e.cheerio?e:[e];var i="";for(var n=0;n<r.length;n++){i+=renderNode(r[n],t)}return i}t["default"]=render;function renderNode(e,t){switch(e.type){case o.Root:return render(e.children,t);case o.Directive:case o.Doctype:return renderDirective(e);case o.Comment:return renderComment(e);case o.CDATA:return renderCdata(e);case o.Script:case o.Style:case o.Tag:return renderTag(e,t);case o.Text:return renderText(e,t)}}var f=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]);var p=new Set(["svg","math"]);function renderTag(e,t){var r;if(t.xmlMode==="foreign"){e.name=(r=c.elementNames.get(e.name))!==null&&r!==void 0?r:e.name;if(e.parent&&f.has(e.parent.name)){t=i(i({},t),{xmlMode:false})}}if(!t.xmlMode&&p.has(e.name)){t=i(i({},t),{xmlMode:"foreign"})}var n="<"+e.name;var s=formatAttributes(e.attribs,t);if(s){n+=" "+s}if(e.children.length===0&&(t.xmlMode?t.selfClosingTags!==false:t.selfClosingTags&&d.has(e.name))){if(!t.xmlMode)n+=" ";n+="/>"}else{n+=">";if(e.children.length>0){n+=render(e.children,t)}if(t.xmlMode||!d.has(e.name)){n+="</"+e.name+">"}}return n}function renderDirective(e){return"<"+e.data+">"}function renderText(e,t){var r=e.data||"";if(t.decodeEntities!==false&&!(!t.xmlMode&&e.parent&&u.has(e.parent.name))){r=l.encodeXML(r)}return r}function renderCdata(e){return"<![CDATA["+e.children[0].data+"]]>"}function renderComment(e){return"\x3c!--"+e.data+"--\x3e"}},9391:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0;var r;(function(e){e["Root"]="root";e["Text"]="text";e["Directive"]="directive";e["Comment"]="comment";e["Script"]="script";e["Style"]="style";e["Tag"]="tag";e["CDATA"]="cdata";e["Doctype"]="doctype"})(r=t.ElementType||(t.ElementType={}));function isTag(e){return e.type===r.Tag||e.type===r.Script||e.type===r.Style}t.isTag=isTag;t.Root=r.Root;t.Text=r.Text;t.Directive=r.Directive;t.Comment=r.Comment;t.Script=r.Script;t.Style=r.Style;t.Tag=r.Tag;t.CDATA=r.CDATA;t.Doctype=r.Doctype},945:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var n=this&&this.__exportStar||function(e,t){for(var r in e)if(r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r))i(t,e,r)};Object.defineProperty(t,"__esModule",{value:true});t.DomHandler=void 0;var s=r(274);n(r(274),t);var a=/\s+/g;var o={normalizeWhitespace:false,withStartIndices:false,withEndIndices:false};var l=function(){function DomHandler(e,t,r){this.dom=[];this.root=new s.Document(this.dom);this.done=false;this.tagStack=[this.root];this.lastNode=null;this.parser=null;if(typeof t==="function"){r=t;t=o}if(typeof e==="object"){t=e;e=undefined}this.callback=e!==null&&e!==void 0?e:null;this.options=t!==null&&t!==void 0?t:o;this.elementCB=r!==null&&r!==void 0?r:null}DomHandler.prototype.onparserinit=function(e){this.parser=e};DomHandler.prototype.onreset=function(){var e;this.dom=[];this.root=new s.Document(this.dom);this.done=false;this.tagStack=[this.root];this.lastNode=null;this.parser=(e=this.parser)!==null&&e!==void 0?e:null};DomHandler.prototype.onend=function(){if(this.done)return;this.done=true;this.parser=null;this.handleCallback(null)};DomHandler.prototype.onerror=function(e){this.handleCallback(e)};DomHandler.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();if(this.options.withEndIndices){e.endIndex=this.parser.endIndex}if(this.elementCB)this.elementCB(e)};DomHandler.prototype.onopentag=function(e,t){var r=new s.Element(e,t);this.addNode(r);this.tagStack.push(r)};DomHandler.prototype.ontext=function(e){var t=this.options.normalizeWhitespace;var r=this.lastNode;if(r&&r.type==="text"){if(t){r.data=(r.data+e).replace(a," ")}else{r.data+=e}}else{if(t){e=e.replace(a," ")}var i=new s.Text(e);this.addNode(i);this.lastNode=i}};DomHandler.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type==="comment"){this.lastNode.data+=e;return}var t=new s.Comment(e);this.addNode(t);this.lastNode=t};DomHandler.prototype.oncommentend=function(){this.lastNode=null};DomHandler.prototype.oncdatastart=function(){var e=new s.Text("");var t=new s.NodeWithChildren("cdata",[e]);this.addNode(t);e.parent=t;this.lastNode=e};DomHandler.prototype.oncdataend=function(){this.lastNode=null};DomHandler.prototype.onprocessinginstruction=function(e,t){var r=new s.ProcessingInstruction(e,t);this.addNode(r)};DomHandler.prototype.handleCallback=function(e){if(typeof this.callback==="function"){this.callback(e,this.dom)}else if(e){throw e}};DomHandler.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1];var r=t.children[t.children.length-1];if(this.options.withStartIndices){e.startIndex=this.parser.startIndex}if(this.options.withEndIndices){e.endIndex=this.parser.endIndex}t.children.push(e);if(r){e.prev=r;r.next=e}e.parent=t;this.lastNode=null};DomHandler.prototype.addDataNode=function(e){this.addNode(e);this.lastNode=e};return DomHandler}();t.DomHandler=l;t["default"]=l},274:function(e,t){"use strict";var r=this&&this.__extends||function(){var extendStatics=function(e,t){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r))e[r]=t[r]};return extendStatics(e,t)};return function(e,t){extendStatics(e,t);function __(){this.constructor=e}e.prototype=t===null?Object.create(t):(__.prototype=t.prototype,new __)}}();var i=this&&this.__assign||function(){i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n))e[n]=t[n]}return e};return i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:true});t.cloneNode=t.Element=t.Document=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var n=new Map([["tag",1],["script",1],["style",1],["directive",1],["text",3],["cdata",4],["comment",8],["root",9]]);var s=function(){function Node(e){this.type=e;this.parent=null;this.prev=null;this.next=null;this.startIndex=null;this.endIndex=null}Object.defineProperty(Node.prototype,"nodeType",{get:function(){var e;return(e=n.get(this.type))!==null&&e!==void 0?e:1},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:false,configurable:true});Node.prototype.cloneNode=function(e){if(e===void 0){e=false}return cloneNode(this,e)};return Node}();t.Node=s;var a=function(e){r(DataNode,e);function DataNode(t,r){var i=e.call(this,t)||this;i.data=r;return i}Object.defineProperty(DataNode.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:false,configurable:true});return DataNode}(s);t.DataNode=a;var o=function(e){r(Text,e);function Text(t){return e.call(this,"text",t)||this}return Text}(a);t.Text=o;var l=function(e){r(Comment,e);function Comment(t){return e.call(this,"comment",t)||this}return Comment}(a);t.Comment=l;var c=function(e){r(ProcessingInstruction,e);function ProcessingInstruction(t,r){var i=e.call(this,"directive",r)||this;i.name=t;return i}return ProcessingInstruction}(a);t.ProcessingInstruction=c;var u=function(e){r(NodeWithChildren,e);function NodeWithChildren(t,r){var i=e.call(this,t)||this;i.children=r;return i}Object.defineProperty(NodeWithChildren.prototype,"firstChild",{get:function(){var e;return(e=this.children[0])!==null&&e!==void 0?e:null},enumerable:false,configurable:true});Object.defineProperty(NodeWithChildren.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:false,configurable:true});Object.defineProperty(NodeWithChildren.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:false,configurable:true});return NodeWithChildren}(s);t.NodeWithChildren=u;var d=function(e){r(Document,e);function Document(t){return e.call(this,"root",t)||this}return Document}(u);t.Document=d;var f=function(e){r(Element,e);function Element(t,r,i){if(i===void 0){i=[]}var n=e.call(this,t==="script"?"script":t==="style"?"style":"tag",i)||this;n.name=t;n.attribs=r;n.attribs=r;return n}Object.defineProperty(Element.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:false,configurable:true});Object.defineProperty(Element.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var r,i;return{name:t,value:e.attribs[t],namespace:(r=e["x-attribsNamespace"])===null||r===void 0?void 0:r[t],prefix:(i=e["x-attribsPrefix"])===null||i===void 0?void 0:i[t]}}))},enumerable:false,configurable:true});return Element}(u);t.Element=f;function cloneNode(e,t){if(t===void 0){t=false}var r;switch(e.type){case"text":r=new o(e.data);break;case"directive":{var n=e;r=new c(n.name,n.data);if(n["x-name"]!=null){r["x-name"]=n["x-name"];r["x-publicId"]=n["x-publicId"];r["x-systemId"]=n["x-systemId"]}break}case"comment":r=new l(e.data);break;case"tag":case"script":case"style":{var s=e;var a=t?cloneChildren(s.children):[];var p=new f(s.name,i({},s.attribs),a);a.forEach((function(e){return e.parent=p}));if(s["x-attribsNamespace"]){p["x-attribsNamespace"]=i({},s["x-attribsNamespace"])}if(s["x-attribsPrefix"]){p["x-attribsPrefix"]=i({},s["x-attribsPrefix"])}r=p;break}case"cdata":{var h=e;var a=t?cloneChildren(h.children):[];var m=new u(e.type,a);a.forEach((function(e){return e.parent=m}));r=m;break}case"root":{var g=e;var a=t?cloneChildren(g.children):[];var b=new d(a);a.forEach((function(e){return e.parent=b}));if(g["x-mode"]){b["x-mode"]=g["x-mode"]}r=b;break}case"doctype":{throw new Error("Not implemented yet: ElementType.Doctype case")}}r.startIndex=e.startIndex;r.endIndex=e.endIndex;return r}t.cloneNode=cloneNode;function cloneChildren(e){var t=e.map((function(e){return cloneNode(e,true)}));for(var r=1;r<t.length;r++){t[r].prev=t[r-1];t[r-1].next=t[r]}return t}},7847:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var n=this&&this.__exportStar||function(e,t){for(var r in e)if(r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r))i(t,e,r)};Object.defineProperty(t,"__esModule",{value:true});t.DomHandler=void 0;var s=r(9391);var a=r(3527);n(r(3527),t);var o=/\s+/g;var l={normalizeWhitespace:false,withStartIndices:false,withEndIndices:false};var c=function(){function DomHandler(e,t,r){this.dom=[];this.root=new a.Document(this.dom);this.done=false;this.tagStack=[this.root];this.lastNode=null;this.parser=null;if(typeof t==="function"){r=t;t=l}if(typeof e==="object"){t=e;e=undefined}this.callback=e!==null&&e!==void 0?e:null;this.options=t!==null&&t!==void 0?t:l;this.elementCB=r!==null&&r!==void 0?r:null}DomHandler.prototype.onparserinit=function(e){this.parser=e};DomHandler.prototype.onreset=function(){var e;this.dom=[];this.root=new a.Document(this.dom);this.done=false;this.tagStack=[this.root];this.lastNode=null;this.parser=(e=this.parser)!==null&&e!==void 0?e:null};DomHandler.prototype.onend=function(){if(this.done)return;this.done=true;this.parser=null;this.handleCallback(null)};DomHandler.prototype.onerror=function(e){this.handleCallback(e)};DomHandler.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();if(this.options.withEndIndices){e.endIndex=this.parser.endIndex}if(this.elementCB)this.elementCB(e)};DomHandler.prototype.onopentag=function(e,t){var r=this.options.xmlMode?s.ElementType.Tag:undefined;var i=new a.Element(e,t,undefined,r);this.addNode(i);this.tagStack.push(i)};DomHandler.prototype.ontext=function(e){var t=this.options.normalizeWhitespace;var r=this.lastNode;if(r&&r.type===s.ElementType.Text){if(t){r.data=(r.data+e).replace(o," ")}else{r.data+=e}}else{if(t){e=e.replace(o," ")}var i=new a.Text(e);this.addNode(i);this.lastNode=i}};DomHandler.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===s.ElementType.Comment){this.lastNode.data+=e;return}var t=new a.Comment(e);this.addNode(t);this.lastNode=t};DomHandler.prototype.oncommentend=function(){this.lastNode=null};DomHandler.prototype.oncdatastart=function(){var e=new a.Text("");var t=new a.NodeWithChildren(s.ElementType.CDATA,[e]);this.addNode(t);e.parent=t;this.lastNode=e};DomHandler.prototype.oncdataend=function(){this.lastNode=null};DomHandler.prototype.onprocessinginstruction=function(e,t){var r=new a.ProcessingInstruction(e,t);this.addNode(r)};DomHandler.prototype.handleCallback=function(e){if(typeof this.callback==="function"){this.callback(e,this.dom)}else if(e){throw e}};DomHandler.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1];var r=t.children[t.children.length-1];if(this.options.withStartIndices){e.startIndex=this.parser.startIndex}if(this.options.withEndIndices){e.endIndex=this.parser.endIndex}t.children.push(e);if(r){e.prev=r;r.next=e}e.parent=t;this.lastNode=null};return DomHandler}();t.DomHandler=c;t["default"]=c},3527:function(e,t,r){"use strict";var i=this&&this.__extends||function(){var extendStatics=function(e,t){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r))e[r]=t[r]};return extendStatics(e,t)};return function(e,t){if(typeof t!=="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");extendStatics(e,t);function __(){this.constructor=e}e.prototype=t===null?Object.create(t):(__.prototype=t.prototype,new __)}}();var n=this&&this.__assign||function(){n=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n))e[n]=t[n]}return e};return n.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:true});t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var s=r(9391);var a=new Map([[s.ElementType.Tag,1],[s.ElementType.Script,1],[s.ElementType.Style,1],[s.ElementType.Directive,1],[s.ElementType.Text,3],[s.ElementType.CDATA,4],[s.ElementType.Comment,8],[s.ElementType.Root,9]]);var o=function(){function Node(e){this.type=e;this.parent=null;this.prev=null;this.next=null;this.startIndex=null;this.endIndex=null}Object.defineProperty(Node.prototype,"nodeType",{get:function(){var e;return(e=a.get(this.type))!==null&&e!==void 0?e:1},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:false,configurable:true});Object.defineProperty(Node.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:false,configurable:true});Node.prototype.cloneNode=function(e){if(e===void 0){e=false}return cloneNode(this,e)};return Node}();t.Node=o;var l=function(e){i(DataNode,e);function DataNode(t,r){var i=e.call(this,t)||this;i.data=r;return i}Object.defineProperty(DataNode.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:false,configurable:true});return DataNode}(o);t.DataNode=l;var c=function(e){i(Text,e);function Text(t){return e.call(this,s.ElementType.Text,t)||this}return Text}(l);t.Text=c;var u=function(e){i(Comment,e);function Comment(t){return e.call(this,s.ElementType.Comment,t)||this}return Comment}(l);t.Comment=u;var d=function(e){i(ProcessingInstruction,e);function ProcessingInstruction(t,r){var i=e.call(this,s.ElementType.Directive,r)||this;i.name=t;return i}return ProcessingInstruction}(l);t.ProcessingInstruction=d;var f=function(e){i(NodeWithChildren,e);function NodeWithChildren(t,r){var i=e.call(this,t)||this;i.children=r;return i}Object.defineProperty(NodeWithChildren.prototype,"firstChild",{get:function(){var e;return(e=this.children[0])!==null&&e!==void 0?e:null},enumerable:false,configurable:true});Object.defineProperty(NodeWithChildren.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:false,configurable:true});Object.defineProperty(NodeWithChildren.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:false,configurable:true});return NodeWithChildren}(o);t.NodeWithChildren=f;var p=function(e){i(Document,e);function Document(t){return e.call(this,s.ElementType.Root,t)||this}return Document}(f);t.Document=p;var h=function(e){i(Element,e);function Element(t,r,i,n){if(i===void 0){i=[]}if(n===void 0){n=t==="script"?s.ElementType.Script:t==="style"?s.ElementType.Style:s.ElementType.Tag}var a=e.call(this,n,i)||this;a.name=t;a.attribs=r;return a}Object.defineProperty(Element.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:false,configurable:true});Object.defineProperty(Element.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var r,i;return{name:t,value:e.attribs[t],namespace:(r=e["x-attribsNamespace"])===null||r===void 0?void 0:r[t],prefix:(i=e["x-attribsPrefix"])===null||i===void 0?void 0:i[t]}}))},enumerable:false,configurable:true});return Element}(f);t.Element=h;function isTag(e){return s.isTag(e)}t.isTag=isTag;function isCDATA(e){return e.type===s.ElementType.CDATA}t.isCDATA=isCDATA;function isText(e){return e.type===s.ElementType.Text}t.isText=isText;function isComment(e){return e.type===s.ElementType.Comment}t.isComment=isComment;function isDirective(e){return e.type===s.ElementType.Directive}t.isDirective=isDirective;function isDocument(e){return e.type===s.ElementType.Root}t.isDocument=isDocument;function hasChildren(e){return Object.prototype.hasOwnProperty.call(e,"children")}t.hasChildren=hasChildren;function cloneNode(e,t){if(t===void 0){t=false}var r;if(isText(e)){r=new c(e.data)}else if(isComment(e)){r=new u(e.data)}else if(isTag(e)){var i=t?cloneChildren(e.children):[];var a=new h(e.name,n({},e.attribs),i);i.forEach((function(e){return e.parent=a}));if(e["x-attribsNamespace"]){a["x-attribsNamespace"]=n({},e["x-attribsNamespace"])}if(e["x-attribsPrefix"]){a["x-attribsPrefix"]=n({},e["x-attribsPrefix"])}r=a}else if(isCDATA(e)){var i=t?cloneChildren(e.children):[];var o=new f(s.ElementType.CDATA,i);i.forEach((function(e){return e.parent=o}));r=o}else if(isDocument(e)){var i=t?cloneChildren(e.children):[];var l=new p(i);i.forEach((function(e){return e.parent=l}));if(e["x-mode"]){l["x-mode"]=e["x-mode"]}r=l}else if(isDirective(e)){var m=new d(e.name,e.data);if(e["x-name"]!=null){m["x-name"]=e["x-name"];m["x-publicId"]=e["x-publicId"];m["x-systemId"]=e["x-systemId"]}r=m}else{throw new Error("Not implemented yet: "+e.type)}r.startIndex=e.startIndex;r.endIndex=e.endIndex;return r}t.cloneNode=cloneNode;function cloneChildren(e){var t=e.map((function(e){return cloneNode(e,true)}));for(var r=1;r<t.length;r++){t[r].prev=t[r-1];t[r-1].next=t[r]}return t}},3425:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.uniqueSort=t.compareDocumentPosition=t.removeSubsets=void 0;var i=r(964);function removeSubsets(e){var t=e.length;while(--t>=0){var r=e[t];if(t>0&&e.lastIndexOf(r,t-1)>=0){e.splice(t,1);continue}for(var i=r.parent;i;i=i.parent){if(e.includes(i)){e.splice(t,1);break}}}return e}t.removeSubsets=removeSubsets;function compareDocumentPosition(e,t){var r=[];var n=[];if(e===t){return 0}var s=i.hasChildren(e)?e:e.parent;while(s){r.unshift(s);s=s.parent}s=i.hasChildren(t)?t:t.parent;while(s){n.unshift(s);s=s.parent}var a=Math.min(r.length,n.length);var o=0;while(o<a&&r[o]===n[o]){o++}if(o===0){return 1}var l=r[o-1];var c=l.children;var u=r[o];var d=n[o];if(c.indexOf(u)>c.indexOf(d)){if(l===t){return 4|16}return 4}if(l===e){return 2|8}return 2}t.compareDocumentPosition=compareDocumentPosition;function uniqueSort(e){e=e.filter((function(e,t,r){return!r.includes(e,t+1)}));e.sort((function(e,t){var r=compareDocumentPosition(e,t);if(r&2){return-1}else if(r&4){return 1}return 0}));return e}t.uniqueSort=uniqueSort},9065:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var n=this&&this.__exportStar||function(e,t){for(var r in e)if(r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r))i(t,e,r)};Object.defineProperty(t,"__esModule",{value:true});n(r(6576),t);n(r(5428),t);n(r(4443),t);n(r(4236),t);n(r(4274),t);n(r(3425),t);n(r(964),t)},4274:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.getElementsByTagType=t.getElementsByTagName=t.getElementById=t.getElements=t.testElement=void 0;var i=r(4236);var n=r(964);var s={tag_name:function(e){if(typeof e==="function"){return function(t){return n.isTag(t)&&e(t.name)}}else if(e==="*"){return n.isTag}return function(t){return n.isTag(t)&&t.name===e}},tag_type:function(e){if(typeof e==="function"){return function(t){return e(t.type)}}return function(t){return t.type===e}},tag_contains:function(e){if(typeof e==="function"){return function(t){return n.isText(t)&&e(t.data)}}return function(t){return n.isText(t)&&t.data===e}}};function getAttribCheck(e,t){if(typeof t==="function"){return function(r){return n.isTag(r)&&t(r.attribs[e])}}return function(r){return n.isTag(r)&&r.attribs[e]===t}}function combineFuncs(e,t){return function(r){return e(r)||t(r)}}function compileTest(e){var t=Object.keys(e).map((function(t){var r=e[t];return t in s?s[t](r):getAttribCheck(t,r)}));return t.length===0?null:t.reduce(combineFuncs)}function testElement(e,t){var r=compileTest(e);return r?r(t):true}t.testElement=testElement;function getElements(e,t,r,n){if(n===void 0){n=Infinity}var s=compileTest(e);return s?i.filter(s,t,r,n):[]}t.getElements=getElements;function getElementById(e,t,r){if(r===void 0){r=true}if(!Array.isArray(t))t=[t];return i.findOne(getAttribCheck("id",e),t,r)}t.getElementById=getElementById;function getElementsByTagName(e,t,r,n){if(r===void 0){r=true}if(n===void 0){n=Infinity}return i.filter(s.tag_name(e),t,r,n)}t.getElementsByTagName=getElementsByTagName;function getElementsByTagType(e,t,r,n){if(r===void 0){r=true}if(n===void 0){n=Infinity}return i.filter(s.tag_type(e),t,r,n)}t.getElementsByTagType=getElementsByTagType},4443:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.prepend=t.prependChild=t.append=t.appendChild=t.replaceElement=t.removeElement=void 0;function removeElement(e){if(e.prev)e.prev.next=e.next;if(e.next)e.next.prev=e.prev;if(e.parent){var t=e.parent.children;t.splice(t.lastIndexOf(e),1)}}t.removeElement=removeElement;function replaceElement(e,t){var r=t.prev=e.prev;if(r){r.next=t}var i=t.next=e.next;if(i){i.prev=t}var n=t.parent=e.parent;if(n){var s=n.children;s[s.lastIndexOf(e)]=t}}t.replaceElement=replaceElement;function appendChild(e,t){removeElement(t);t.next=null;t.parent=e;if(e.children.push(t)>1){var r=e.children[e.children.length-2];r.next=t;t.prev=r}else{t.prev=null}}t.appendChild=appendChild;function append(e,t){removeElement(t);var r=e.parent;var i=e.next;t.next=i;t.prev=e;e.next=t;t.parent=r;if(i){i.prev=t;if(r){var n=r.children;n.splice(n.lastIndexOf(i),0,t)}}else if(r){r.children.push(t)}}t.append=append;function prependChild(e,t){removeElement(t);t.parent=e;t.prev=null;if(e.children.unshift(t)!==1){var r=e.children[1];r.prev=t;t.next=r}else{t.next=null}}t.prependChild=prependChild;function prepend(e,t){removeElement(t);var r=e.parent;if(r){var i=r.children;i.splice(i.indexOf(e),0,t)}if(e.prev){e.prev.next=t}t.parent=r;t.prev=e.prev;t.next=e;e.prev=t}t.prepend=prepend},4236:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.findAll=t.existsOne=t.findOne=t.findOneChild=t.find=t.filter=void 0;var i=r(964);function filter(e,t,r,i){if(r===void 0){r=true}if(i===void 0){i=Infinity}if(!Array.isArray(t))t=[t];return find(e,t,r,i)}t.filter=filter;function find(e,t,r,n){var s=[];for(var a=0,o=t;a<o.length;a++){var l=o[a];if(e(l)){s.push(l);if(--n<=0)break}if(r&&i.hasChildren(l)&&l.children.length>0){var c=find(e,l.children,r,n);s.push.apply(s,c);n-=c.length;if(n<=0)break}}return s}t.find=find;function findOneChild(e,t){return t.find(e)}t.findOneChild=findOneChild;function findOne(e,t,r){if(r===void 0){r=true}var n=null;for(var s=0;s<t.length&&!n;s++){var a=t[s];if(!i.isTag(a)){continue}else if(e(a)){n=a}else if(r&&a.children.length>0){n=findOne(e,a.children)}}return n}t.findOne=findOne;function existsOne(e,t){return t.some((function(t){return i.isTag(t)&&(e(t)||t.children.length>0&&existsOne(e,t.children))}))}t.existsOne=existsOne;function findAll(e,t){var r;var n=[];var s=t.filter(i.isTag);var a;while(a=s.shift()){var o=(r=a.children)===null||r===void 0?void 0:r.filter(i.isTag);if(o&&o.length>0){s.unshift.apply(s,o)}if(e(a))n.push(a)}return n}t.findAll=findAll},6576:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.getText=t.getInnerHTML=t.getOuterHTML=void 0;var n=r(964);var s=i(r(4614));function getOuterHTML(e,t){return s.default(e,t)}t.getOuterHTML=getOuterHTML;function getInnerHTML(e,t){return n.hasChildren(e)?e.children.map((function(e){return getOuterHTML(e,t)})).join(""):""}t.getInnerHTML=getInnerHTML;function getText(e){if(Array.isArray(e))return e.map(getText).join("");if(n.isTag(e))return e.name==="br"?"\n":getText(e.children);if(n.isCDATA(e))return getText(e.children);if(n.isText(e))return e.data;return""}t.getText=getText},964:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.hasChildren=t.isComment=t.isText=t.isCDATA=t.isTag=void 0;var i=r(9391);function isTag(e){return i.isTag(e)}t.isTag=isTag;function isCDATA(e){return e.type==="cdata"}t.isCDATA=isCDATA;function isText(e){return e.type==="text"}t.isText=isText;function isComment(e){return e.type==="comment"}t.isComment=isComment;function hasChildren(e){return Object.prototype.hasOwnProperty.call(e,"children")}t.hasChildren=hasChildren},5428:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.prevElementSibling=t.nextElementSibling=t.getName=t.hasAttrib=t.getAttributeValue=t.getSiblings=t.getParent=t.getChildren=void 0;var i=r(964);var n=[];function getChildren(e){var t;return(t=e.children)!==null&&t!==void 0?t:n}t.getChildren=getChildren;function getParent(e){return e.parent||null}t.getParent=getParent;function getSiblings(e){var t,r;var i=getParent(e);if(i!=null)return getChildren(i);var n=[e];var s=e.prev,a=e.next;while(s!=null){n.unshift(s);t=s,s=t.prev}while(a!=null){n.push(a);r=a,a=r.next}return n}t.getSiblings=getSiblings;function getAttributeValue(e,t){var r;return(r=e.attribs)===null||r===void 0?void 0:r[t]}t.getAttributeValue=getAttributeValue;function hasAttrib(e,t){return e.attribs!=null&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&e.attribs[t]!=null}t.hasAttrib=hasAttrib;function getName(e){return e.name}t.getName=getName;function nextElementSibling(e){var t;var r=e.next;while(r!==null&&!i.isTag(r))t=r,r=t.next;return r}t.nextElementSibling=nextElementSibling;function prevElementSibling(e){var t;var r=e.prev;while(r!==null&&!i.isTag(r))t=r,r=t.prev;return r}t.prevElementSibling=prevElementSibling},8641:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.uniqueSort=t.compareDocumentPosition=t.removeSubsets=void 0;var i=r(7847);function removeSubsets(e){var t=e.length;while(--t>=0){var r=e[t];if(t>0&&e.lastIndexOf(r,t-1)>=0){e.splice(t,1);continue}for(var i=r.parent;i;i=i.parent){if(e.includes(i)){e.splice(t,1);break}}}return e}t.removeSubsets=removeSubsets;function compareDocumentPosition(e,t){var r=[];var n=[];if(e===t){return 0}var s=i.hasChildren(e)?e:e.parent;while(s){r.unshift(s);s=s.parent}s=i.hasChildren(t)?t:t.parent;while(s){n.unshift(s);s=s.parent}var a=Math.min(r.length,n.length);var o=0;while(o<a&&r[o]===n[o]){o++}if(o===0){return 1}var l=r[o-1];var c=l.children;var u=r[o];var d=n[o];if(c.indexOf(u)>c.indexOf(d)){if(l===t){return 4|16}return 4}if(l===e){return 2|8}return 2}t.compareDocumentPosition=compareDocumentPosition;function uniqueSort(e){e=e.filter((function(e,t,r){return!r.includes(e,t+1)}));e.sort((function(e,t){var r=compareDocumentPosition(e,t);if(r&2){return-1}else if(r&4){return 1}return 0}));return e}t.uniqueSort=uniqueSort},7459:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var n=this&&this.__exportStar||function(e,t){for(var r in e)if(r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r))i(t,e,r)};Object.defineProperty(t,"__esModule",{value:true});t.hasChildren=t.isDocument=t.isComment=t.isText=t.isCDATA=t.isTag=void 0;n(r(6894),t);n(r(3597),t);n(r(8315),t);n(r(9956),t);n(r(4996),t);n(r(8641),t);var s=r(7847);Object.defineProperty(t,"isTag",{enumerable:true,get:function(){return s.isTag}});Object.defineProperty(t,"isCDATA",{enumerable:true,get:function(){return s.isCDATA}});Object.defineProperty(t,"isText",{enumerable:true,get:function(){return s.isText}});Object.defineProperty(t,"isComment",{enumerable:true,get:function(){return s.isComment}});Object.defineProperty(t,"isDocument",{enumerable:true,get:function(){return s.isDocument}});Object.defineProperty(t,"hasChildren",{enumerable:true,get:function(){return s.hasChildren}})},4996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.getElementsByTagType=t.getElementsByTagName=t.getElementById=t.getElements=t.testElement=void 0;var i=r(7847);var n=r(9956);var s={tag_name:function(e){if(typeof e==="function"){return function(t){return i.isTag(t)&&e(t.name)}}else if(e==="*"){return i.isTag}return function(t){return i.isTag(t)&&t.name===e}},tag_type:function(e){if(typeof e==="function"){return function(t){return e(t.type)}}return function(t){return t.type===e}},tag_contains:function(e){if(typeof e==="function"){return function(t){return i.isText(t)&&e(t.data)}}return function(t){return i.isText(t)&&t.data===e}}};function getAttribCheck(e,t){if(typeof t==="function"){return function(r){return i.isTag(r)&&t(r.attribs[e])}}return function(r){return i.isTag(r)&&r.attribs[e]===t}}function combineFuncs(e,t){return function(r){return e(r)||t(r)}}function compileTest(e){var t=Object.keys(e).map((function(t){var r=e[t];return t in s?s[t](r):getAttribCheck(t,r)}));return t.length===0?null:t.reduce(combineFuncs)}function testElement(e,t){var r=compileTest(e);return r?r(t):true}t.testElement=testElement;function getElements(e,t,r,i){if(i===void 0){i=Infinity}var s=compileTest(e);return s?n.filter(s,t,r,i):[]}t.getElements=getElements;function getElementById(e,t,r){if(r===void 0){r=true}if(!Array.isArray(t))t=[t];return n.findOne(getAttribCheck("id",e),t,r)}t.getElementById=getElementById;function getElementsByTagName(e,t,r,i){if(r===void 0){r=true}if(i===void 0){i=Infinity}return n.filter(s.tag_name(e),t,r,i)}t.getElementsByTagName=getElementsByTagName;function getElementsByTagType(e,t,r,i){if(r===void 0){r=true}if(i===void 0){i=Infinity}return n.filter(s.tag_type(e),t,r,i)}t.getElementsByTagType=getElementsByTagType},8315:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.prepend=t.prependChild=t.append=t.appendChild=t.replaceElement=t.removeElement=void 0;function removeElement(e){if(e.prev)e.prev.next=e.next;if(e.next)e.next.prev=e.prev;if(e.parent){var t=e.parent.children;t.splice(t.lastIndexOf(e),1)}}t.removeElement=removeElement;function replaceElement(e,t){var r=t.prev=e.prev;if(r){r.next=t}var i=t.next=e.next;if(i){i.prev=t}var n=t.parent=e.parent;if(n){var s=n.children;s[s.lastIndexOf(e)]=t}}t.replaceElement=replaceElement;function appendChild(e,t){removeElement(t);t.next=null;t.parent=e;if(e.children.push(t)>1){var r=e.children[e.children.length-2];r.next=t;t.prev=r}else{t.prev=null}}t.appendChild=appendChild;function append(e,t){removeElement(t);var r=e.parent;var i=e.next;t.next=i;t.prev=e;e.next=t;t.parent=r;if(i){i.prev=t;if(r){var n=r.children;n.splice(n.lastIndexOf(i),0,t)}}else if(r){r.children.push(t)}}t.append=append;function prependChild(e,t){removeElement(t);t.parent=e;t.prev=null;if(e.children.unshift(t)!==1){var r=e.children[1];r.prev=t;t.next=r}else{t.next=null}}t.prependChild=prependChild;function prepend(e,t){removeElement(t);var r=e.parent;if(r){var i=r.children;i.splice(i.indexOf(e),0,t)}if(e.prev){e.prev.next=t}t.parent=r;t.prev=e.prev;t.next=e;e.prev=t}t.prepend=prepend},9956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.findAll=t.existsOne=t.findOne=t.findOneChild=t.find=t.filter=void 0;var i=r(7847);function filter(e,t,r,i){if(r===void 0){r=true}if(i===void 0){i=Infinity}if(!Array.isArray(t))t=[t];return find(e,t,r,i)}t.filter=filter;function find(e,t,r,n){var s=[];for(var a=0,o=t;a<o.length;a++){var l=o[a];if(e(l)){s.push(l);if(--n<=0)break}if(r&&i.hasChildren(l)&&l.children.length>0){var c=find(e,l.children,r,n);s.push.apply(s,c);n-=c.length;if(n<=0)break}}return s}t.find=find;function findOneChild(e,t){return t.find(e)}t.findOneChild=findOneChild;function findOne(e,t,r){if(r===void 0){r=true}var n=null;for(var s=0;s<t.length&&!n;s++){var a=t[s];if(!i.isTag(a)){continue}else if(e(a)){n=a}else if(r&&a.children.length>0){n=findOne(e,a.children)}}return n}t.findOne=findOne;function existsOne(e,t){return t.some((function(t){return i.isTag(t)&&(e(t)||t.children.length>0&&existsOne(e,t.children))}))}t.existsOne=existsOne;function findAll(e,t){var r;var n=[];var s=t.filter(i.isTag);var a;while(a=s.shift()){var o=(r=a.children)===null||r===void 0?void 0:r.filter(i.isTag);if(o&&o.length>0){s.unshift.apply(s,o)}if(e(a))n.push(a)}return n}t.findAll=findAll},6894:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.getText=t.getInnerHTML=t.getOuterHTML=void 0;var n=r(7847);var s=i(r(4614));function getOuterHTML(e,t){return s.default(e,t)}t.getOuterHTML=getOuterHTML;function getInnerHTML(e,t){return n.hasChildren(e)?e.children.map((function(e){return getOuterHTML(e,t)})).join(""):""}t.getInnerHTML=getInnerHTML;function getText(e){if(Array.isArray(e))return e.map(getText).join("");if(n.isTag(e))return e.name==="br"?"\n":getText(e.children);if(n.isCDATA(e))return getText(e.children);if(n.isText(e))return e.data;return""}t.getText=getText},3597:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.prevElementSibling=t.nextElementSibling=t.getName=t.hasAttrib=t.getAttributeValue=t.getSiblings=t.getParent=t.getChildren=void 0;var i=r(7847);var n=[];function getChildren(e){var t;return(t=e.children)!==null&&t!==void 0?t:n}t.getChildren=getChildren;function getParent(e){return e.parent||null}t.getParent=getParent;function getSiblings(e){var t,r;var i=getParent(e);if(i!=null)return getChildren(i);var n=[e];var s=e.prev,a=e.next;while(s!=null){n.unshift(s);t=s,s=t.prev}while(a!=null){n.push(a);r=a,a=r.next}return n}t.getSiblings=getSiblings;function getAttributeValue(e,t){var r;return(r=e.attribs)===null||r===void 0?void 0:r[t]}t.getAttributeValue=getAttributeValue;function hasAttrib(e,t){return e.attribs!=null&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&e.attribs[t]!=null}t.hasAttrib=hasAttrib;function getName(e){return e.name}t.getName=getName;function nextElementSibling(e){var t;var r=e.next;while(r!==null&&!i.isTag(r))t=r,r=t.next;return r}t.nextElementSibling=nextElementSibling;function prevElementSibling(e){var t;var r=e.prev;while(r!==null&&!i.isTag(r))t=r,r=t.prev;return r}t.prevElementSibling=prevElementSibling},6674:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});var n=i(r(1859));var s=i(r(2128));var a=i(r(4931));var o=i(r(9281));t.decodeXML=getStrictDecoder(a.default);t.decodeHTMLStrict=getStrictDecoder(n.default);function getStrictDecoder(e){var t=Object.keys(e).join("|");var r=getReplacer(e);t+="|#[xX][\\da-fA-F]+|#\\d+";var i=new RegExp("&(?:"+t+");","g");return function(e){return String(e).replace(i,r)}}var sorter=function(e,t){return e<t?1:-1};t.decodeHTML=function(){var e=Object.keys(s.default).sort(sorter);var t=Object.keys(n.default).sort(sorter);for(var r=0,i=0;r<t.length;r++){if(e[i]===t[r]){t[r]+=";?";i++}else{t[r]+=";"}}var a=new RegExp("&(?:"+t.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g");var o=getReplacer(n.default);function replacer(e){if(e.substr(-1)!==";")e+=";";return o(e)}return function(e){return String(e).replace(a,replacer)}}();function getReplacer(e){return function replace(t){if(t.charAt(1)==="#"){if(t.charAt(2)==="X"||t.charAt(2)==="x"){return o.default(parseInt(t.substr(3),16))}return o.default(parseInt(t.substr(2),10))}return e[t.slice(1,-1)]}}},9281:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});var n=i(r(7971));function decodeCodePoint(e){if(e>=55296&&e<=57343||e>1114111){return"�"}if(e in n.default){e=n.default[e]}var t="";if(e>65535){e-=65536;t+=String.fromCharCode(e>>>10&1023|55296);e=56320|e&1023}t+=String.fromCharCode(e);return t}t["default"]=decodeCodePoint},6032:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});var n=i(r(4931));var s=getInverseObj(n.default);var a=getInverseReplacer(s);t.encodeXML=getInverse(s,a);var o=i(r(1859));var l=getInverseObj(o.default);var c=getInverseReplacer(l);t.encodeHTML=getInverse(l,c);function getInverseObj(e){return Object.keys(e).sort().reduce((function(t,r){t[e[r]]="&"+r+";";return t}),{})}function getInverseReplacer(e){var t=[];var r=[];Object.keys(e).forEach((function(e){return e.length===1?t.push("\\"+e):r.push(e)}));r.unshift("["+t.join("")+"]");return new RegExp(r.join("|"),"g")}var u=/[^\0-\x7F]/g;var d=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;function singleCharReplacer(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}function astralReplacer(e,t){var r=e.charCodeAt(0);var i=e.charCodeAt(1);var n=(r-55296)*1024+i-56320+65536;return"&#x"+n.toString(16).toUpperCase()+";"}function getInverse(e,t){return function(r){return r.replace(t,(function(t){return e[t]})).replace(d,astralReplacer).replace(u,singleCharReplacer)}}var f=getInverseReplacer(s);function escape(e){return e.replace(f,singleCharReplacer).replace(d,astralReplacer).replace(u,singleCharReplacer)}t.escape=escape},9280:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=r(6674);var n=r(6032);function decode(e,t){return(!t||t<=0?i.decodeXML:i.decodeHTML)(e)}t.decode=decode;function decodeStrict(e,t){return(!t||t<=0?i.decodeXML:i.decodeHTMLStrict)(e)}t.decodeStrict=decodeStrict;function encode(e,t){return(!t||t<=0?n.encodeXML:n.encodeHTML)(e)}t.encode=encode;var s=r(6032);t.encodeXML=s.encodeXML;t.encodeHTML=s.encodeHTML;t.escape=s.escape;t.encodeHTML4=s.encodeHTML;t.encodeHTML5=s.encodeHTML;var a=r(6674);t.decodeXML=a.decodeXML;t.decodeHTML=a.decodeHTML;t.decodeHTMLStrict=a.decodeHTMLStrict;t.decodeHTML4=a.decodeHTML;t.decodeHTML5=a.decodeHTML;t.decodeHTML4Strict=a.decodeHTMLStrict;t.decodeHTML5Strict=a.decodeHTMLStrict;t.decodeXMLStrict=a.decodeXML},3350:function(e,t,r){"use strict";var i=this&&this.__extends||function(){var extendStatics=function(e,t){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r))e[r]=t[r]};return extendStatics(e,t)};return function(e,t){if(typeof t!=="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");extendStatics(e,t);function __(){this.constructor=e}e.prototype=t===null?Object.create(t):(__.prototype=t.prototype,new __)}}();var n=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);s(t,e);return t};var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.parseFeed=t.FeedHandler=void 0;var l=o(r(7847));var c=a(r(7459));var u=r(9825);var d;(function(e){e[e["image"]=0]="image";e[e["audio"]=1]="audio";e[e["video"]=2]="video";e[e["document"]=3]="document";e[e["executable"]=4]="executable"})(d||(d={}));var f;(function(e){e[e["sample"]=0]="sample";e[e["full"]=1]="full";e[e["nonstop"]=2]="nonstop"})(f||(f={}));var p=function(e){i(FeedHandler,e);function FeedHandler(t,r){var i=this;if(typeof t==="object"){t=undefined;r=t}i=e.call(this,t,r)||this;return i}FeedHandler.prototype.onend=function(){var e,t;var r=getOneElement(isValidFeed,this.dom);if(!r){this.handleCallback(new Error("couldn't find root of feed"));return}var i={};if(r.name==="feed"){var n=r.children;i.type="atom";addConditionally(i,"id","id",n);addConditionally(i,"title","title",n);var s=getAttribute("href",getOneElement("link",n));if(s){i.link=s}addConditionally(i,"description","subtitle",n);var a=fetch("updated",n);if(a){i.updated=new Date(a)}addConditionally(i,"author","email",n,true);i.items=getElements("entry",n).map((function(e){var t={};var r=e.children;addConditionally(t,"id","id",r);addConditionally(t,"title","title",r);var i=getAttribute("href",getOneElement("link",r));if(i){t.link=i}var n=fetch("summary",r)||fetch("content",r);if(n){t.description=n}var s=fetch("updated",r);if(s){t.pubDate=new Date(s)}t.media=getMediaElements(r);return t}))}else{var n=(t=(e=getOneElement("channel",r.children))===null||e===void 0?void 0:e.children)!==null&&t!==void 0?t:[];i.type=r.name.substr(0,3);i.id="";addConditionally(i,"title","title",n);addConditionally(i,"link","link",n);addConditionally(i,"description","description",n);var a=fetch("lastBuildDate",n);if(a){i.updated=new Date(a)}addConditionally(i,"author","managingEditor",n,true);i.items=getElements("item",r.children).map((function(e){var t={};var r=e.children;addConditionally(t,"id","guid",r);addConditionally(t,"title","title",r);addConditionally(t,"link","link",r);addConditionally(t,"description","description",r);var i=fetch("pubDate",r);if(i)t.pubDate=new Date(i);t.media=getMediaElements(r);return t}))}this.feed=i;this.handleCallback(null)};return FeedHandler}(l.default);t.FeedHandler=p;function getMediaElements(e){return getElements("media:content",e).map((function(e){var t={medium:e.attribs.medium,isDefault:!!e.attribs.isDefault};if(e.attribs.url){t.url=e.attribs.url}if(e.attribs.fileSize){t.fileSize=parseInt(e.attribs.fileSize,10)}if(e.attribs.type){t.type=e.attribs.type}if(e.attribs.expression){t.expression=e.attribs.expression}if(e.attribs.bitrate){t.bitrate=parseInt(e.attribs.bitrate,10)}if(e.attribs.framerate){t.framerate=parseInt(e.attribs.framerate,10)}if(e.attribs.samplingrate){t.samplingrate=parseInt(e.attribs.samplingrate,10)}if(e.attribs.channels){t.channels=parseInt(e.attribs.channels,10)}if(e.attribs.duration){t.duration=parseInt(e.attribs.duration,10)}if(e.attribs.height){t.height=parseInt(e.attribs.height,10)}if(e.attribs.width){t.width=parseInt(e.attribs.width,10)}if(e.attribs.lang){t.lang=e.attribs.lang}return t}))}function getElements(e,t){return c.getElementsByTagName(e,t,true)}function getOneElement(e,t){return c.getElementsByTagName(e,t,true,1)[0]}function fetch(e,t,r){if(r===void 0){r=false}return c.getText(c.getElementsByTagName(e,t,r,1)).trim()}function getAttribute(e,t){if(!t){return null}var r=t.attribs;return r[e]}function addConditionally(e,t,r,i,n){if(n===void 0){n=false}var s=fetch(r,i,n);if(s)e[t]=s}function isValidFeed(e){return e==="rss"||e==="feed"||e==="rdf:RDF"}function parseFeed(e,t){if(t===void 0){t={xmlMode:true}}var r=new p(t);new u.Parser(r,t).end(e);return r.feed}t.parseFeed=parseFeed},9825:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.Parser=void 0;var n=i(r(3562));var s=new Set(["input","option","optgroup","select","button","datalist","textarea"]);var a=new Set(["p"]);var o={tr:new Set(["tr","th","td"]),th:new Set(["th"]),td:new Set(["thead","th","td"]),body:new Set(["head","link","script"]),li:new Set(["li"]),p:a,h1:a,h2:a,h3:a,h4:a,h5:a,h6:a,select:s,input:s,output:s,button:s,datalist:s,textarea:s,option:new Set(["option"]),optgroup:new Set(["optgroup","option"]),dd:new Set(["dt","dd"]),dt:new Set(["dt","dd"]),address:a,article:a,aside:a,blockquote:a,details:a,div:a,dl:a,fieldset:a,figcaption:a,figure:a,footer:a,form:a,header:a,hr:a,main:a,nav:a,ol:a,pre:a,section:a,table:a,ul:a,rt:new Set(["rt","rp"]),rp:new Set(["rt","rp"]),tbody:new Set(["thead","tbody"]),tfoot:new Set(["thead","tbody"])};var l=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);var c=new Set(["math","svg"]);var u=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]);var d=/\s|\//;var f=function(){function Parser(e,t){if(t===void 0){t={}}var r,i,s,a,o;this.startIndex=0;this.endIndex=null;this.tagname="";this.attribname="";this.attribvalue="";this.attribs=null;this.stack=[];this.foreignContext=[];this.options=t;this.cbs=e!==null&&e!==void 0?e:{};this.lowerCaseTagNames=(r=t.lowerCaseTags)!==null&&r!==void 0?r:!t.xmlMode;this.lowerCaseAttributeNames=(i=t.lowerCaseAttributeNames)!==null&&i!==void 0?i:!t.xmlMode;this.tokenizer=new((s=t.Tokenizer)!==null&&s!==void 0?s:n.default)(this.options,this);(o=(a=this.cbs).onparserinit)===null||o===void 0?void 0:o.call(a,this)}Parser.prototype.updatePosition=function(e){if(this.endIndex===null){if(this.tokenizer.sectionStart<=e){this.startIndex=0}else{this.startIndex=this.tokenizer.sectionStart-e}}else{this.startIndex=this.endIndex+1}this.endIndex=this.tokenizer.getAbsoluteIndex()};Parser.prototype.ontext=function(e){var t,r;this.updatePosition(1);this.endIndex--;(r=(t=this.cbs).ontext)===null||r===void 0?void 0:r.call(t,e)};Parser.prototype.onopentagname=function(e){var t,r;if(this.lowerCaseTagNames){e=e.toLowerCase()}this.tagname=e;if(!this.options.xmlMode&&Object.prototype.hasOwnProperty.call(o,e)){var i=void 0;while(this.stack.length>0&&o[e].has(i=this.stack[this.stack.length-1])){this.onclosetag(i)}}if(this.options.xmlMode||!l.has(e)){this.stack.push(e);if(c.has(e)){this.foreignContext.push(true)}else if(u.has(e)){this.foreignContext.push(false)}}(r=(t=this.cbs).onopentagname)===null||r===void 0?void 0:r.call(t,e);if(this.cbs.onopentag)this.attribs={}};Parser.prototype.onopentagend=function(){var e,t;this.updatePosition(1);if(this.attribs){(t=(e=this.cbs).onopentag)===null||t===void 0?void 0:t.call(e,this.tagname,this.attribs);this.attribs=null}if(!this.options.xmlMode&&this.cbs.onclosetag&&l.has(this.tagname)){this.cbs.onclosetag(this.tagname)}this.tagname=""};Parser.prototype.onclosetag=function(e){this.updatePosition(1);if(this.lowerCaseTagNames){e=e.toLowerCase()}if(c.has(e)||u.has(e)){this.foreignContext.pop()}if(this.stack.length&&(this.options.xmlMode||!l.has(e))){var t=this.stack.lastIndexOf(e);if(t!==-1){if(this.cbs.onclosetag){t=this.stack.length-t;while(t--){this.cbs.onclosetag(this.stack.pop())}}else this.stack.length=t}else if(e==="p"&&!this.options.xmlMode){this.onopentagname(e);this.closeCurrentTag()}}else if(!this.options.xmlMode&&(e==="br"||e==="p")){this.onopentagname(e);this.closeCurrentTag()}};Parser.prototype.onselfclosingtag=function(){if(this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]){this.closeCurrentTag()}else{this.onopentagend()}};Parser.prototype.closeCurrentTag=function(){var e,t;var r=this.tagname;this.onopentagend();if(this.stack[this.stack.length-1]===r){(t=(e=this.cbs).onclosetag)===null||t===void 0?void 0:t.call(e,r);this.stack.pop()}};Parser.prototype.onattribname=function(e){if(this.lowerCaseAttributeNames){e=e.toLowerCase()}this.attribname=e};Parser.prototype.onattribdata=function(e){this.attribvalue+=e};Parser.prototype.onattribend=function(e){var t,r;(r=(t=this.cbs).onattribute)===null||r===void 0?void 0:r.call(t,this.attribname,this.attribvalue,e);if(this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)){this.attribs[this.attribname]=this.attribvalue}this.attribname="";this.attribvalue=""};Parser.prototype.getInstructionName=function(e){var t=e.search(d);var r=t<0?e:e.substr(0,t);if(this.lowerCaseTagNames){r=r.toLowerCase()}return r};Parser.prototype.ondeclaration=function(e){if(this.cbs.onprocessinginstruction){var t=this.getInstructionName(e);this.cbs.onprocessinginstruction("!"+t,"!"+e)}};Parser.prototype.onprocessinginstruction=function(e){if(this.cbs.onprocessinginstruction){var t=this.getInstructionName(e);this.cbs.onprocessinginstruction("?"+t,"?"+e)}};Parser.prototype.oncomment=function(e){var t,r,i,n;this.updatePosition(4);(r=(t=this.cbs).oncomment)===null||r===void 0?void 0:r.call(t,e);(n=(i=this.cbs).oncommentend)===null||n===void 0?void 0:n.call(i)};Parser.prototype.oncdata=function(e){var t,r,i,n,s,a;this.updatePosition(1);if(this.options.xmlMode||this.options.recognizeCDATA){(r=(t=this.cbs).oncdatastart)===null||r===void 0?void 0:r.call(t);(n=(i=this.cbs).ontext)===null||n===void 0?void 0:n.call(i,e);(a=(s=this.cbs).oncdataend)===null||a===void 0?void 0:a.call(s)}else{this.oncomment("[CDATA["+e+"]]")}};Parser.prototype.onerror=function(e){var t,r;(r=(t=this.cbs).onerror)===null||r===void 0?void 0:r.call(t,e)};Parser.prototype.onend=function(){var e,t;if(this.cbs.onclosetag){for(var r=this.stack.length;r>0;this.cbs.onclosetag(this.stack[--r]));}(t=(e=this.cbs).onend)===null||t===void 0?void 0:t.call(e)};Parser.prototype.reset=function(){var e,t,r,i;(t=(e=this.cbs).onreset)===null||t===void 0?void 0:t.call(e);this.tokenizer.reset();this.tagname="";this.attribname="";this.attribs=null;this.stack=[];(i=(r=this.cbs).onparserinit)===null||i===void 0?void 0:i.call(r,this)};Parser.prototype.parseComplete=function(e){this.reset();this.end(e)};Parser.prototype.write=function(e){this.tokenizer.write(e)};Parser.prototype.end=function(e){this.tokenizer.end(e)};Parser.prototype.pause=function(){this.tokenizer.pause()};Parser.prototype.resume=function(){this.tokenizer.resume()};Parser.prototype.parseChunk=function(e){this.write(e)};Parser.prototype.done=function(e){this.end(e)};return Parser}();t.Parser=f},3562:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});var n=i(r(9281));var s=i(r(1859));var a=i(r(2128));var o=i(r(4931));function whitespace(e){return e===" "||e==="\n"||e==="\t"||e==="\f"||e==="\r"}function isASCIIAlpha(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"}function ifElseState(e,t,r){var i=e.toLowerCase();if(e===i){return function(e,n){if(n===i){e._state=t}else{e._state=r;e._index--}}}return function(n,s){if(s===i||s===e){n._state=t}else{n._state=r;n._index--}}}function consumeSpecialNameChar(e,t){var r=e.toLowerCase();return function(i,n){if(n===r||n===e){i._state=t}else{i._state=3;i._index--}}}var l=ifElseState("C",24,16);var c=ifElseState("D",25,16);var u=ifElseState("A",26,16);var d=ifElseState("T",27,16);var f=ifElseState("A",28,16);var p=consumeSpecialNameChar("R",35);var h=consumeSpecialNameChar("I",36);var m=consumeSpecialNameChar("P",37);var g=consumeSpecialNameChar("T",38);var b=ifElseState("R",40,1);var v=ifElseState("I",41,1);var y=ifElseState("P",42,1);var x=ifElseState("T",43,1);var _=consumeSpecialNameChar("Y",45);var w=consumeSpecialNameChar("L",46);var C=consumeSpecialNameChar("E",47);var T=ifElseState("Y",49,1);var S=ifElseState("L",50,1);var A=ifElseState("E",51,1);var N=consumeSpecialNameChar("I",54);var E=consumeSpecialNameChar("T",55);var k=consumeSpecialNameChar("L",56);var D=consumeSpecialNameChar("E",57);var O=ifElseState("I",58,1);var P=ifElseState("T",59,1);var I=ifElseState("L",60,1);var V=ifElseState("E",61,1);var M=ifElseState("#",63,64);var L=ifElseState("X",66,65);var R=function(){function Tokenizer(e,t){var r;this._state=1;this.buffer="";this.sectionStart=0;this._index=0;this.bufferOffset=0;this.baseState=1;this.special=1;this.running=true;this.ended=false;this.cbs=t;this.xmlMode=!!(e===null||e===void 0?void 0:e.xmlMode);this.decodeEntities=(r=e===null||e===void 0?void 0:e.decodeEntities)!==null&&r!==void 0?r:true}Tokenizer.prototype.reset=function(){this._state=1;this.buffer="";this.sectionStart=0;this._index=0;this.bufferOffset=0;this.baseState=1;this.special=1;this.running=true;this.ended=false};Tokenizer.prototype.write=function(e){if(this.ended)this.cbs.onerror(Error(".write() after done!"));this.buffer+=e;this.parse()};Tokenizer.prototype.end=function(e){if(this.ended)this.cbs.onerror(Error(".end() after done!"));if(e)this.write(e);this.ended=true;if(this.running)this.finish()};Tokenizer.prototype.pause=function(){this.running=false};Tokenizer.prototype.resume=function(){this.running=true;if(this._index<this.buffer.length){this.parse()}if(this.ended){this.finish()}};Tokenizer.prototype.getAbsoluteIndex=function(){return this.bufferOffset+this._index};Tokenizer.prototype.stateText=function(e){if(e==="<"){if(this._index>this.sectionStart){this.cbs.ontext(this.getSection())}this._state=2;this.sectionStart=this._index}else if(this.decodeEntities&&e==="&"&&(this.special===1||this.special===4)){if(this._index>this.sectionStart){this.cbs.ontext(this.getSection())}this.baseState=1;this._state=62;this.sectionStart=this._index}};Tokenizer.prototype.stateBeforeTagName=function(e){if(e==="/"){this._state=5}else if(e==="<"){this.cbs.ontext(this.getSection());this.sectionStart=this._index}else if(e===">"||this.special!==1||whitespace(e)){this._state=1}else if(e==="!"){this._state=15;this.sectionStart=this._index+1}else if(e==="?"){this._state=17;this.sectionStart=this._index+1}else if(!isASCIIAlpha(e)){this._state=1}else{this._state=!this.xmlMode&&(e==="s"||e==="S")?32:!this.xmlMode&&(e==="t"||e==="T")?52:3;this.sectionStart=this._index}};Tokenizer.prototype.stateInTagName=function(e){if(e==="/"||e===">"||whitespace(e)){this.emitToken("onopentagname");this._state=8;this._index--}};Tokenizer.prototype.stateBeforeClosingTagName=function(e){if(whitespace(e)){}else if(e===">"){this._state=1}else if(this.special!==1){if(this.special!==4&&(e==="s"||e==="S")){this._state=33}else if(this.special===4&&(e==="t"||e==="T")){this._state=53}else{this._state=1;this._index--}}else if(!isASCIIAlpha(e)){this._state=20;this.sectionStart=this._index}else{this._state=6;this.sectionStart=this._index}};Tokenizer.prototype.stateInClosingTagName=function(e){if(e===">"||whitespace(e)){this.emitToken("onclosetag");this._state=7;this._index--}};Tokenizer.prototype.stateAfterClosingTagName=function(e){if(e===">"){this._state=1;this.sectionStart=this._index+1}};Tokenizer.prototype.stateBeforeAttributeName=function(e){if(e===">"){this.cbs.onopentagend();this._state=1;this.sectionStart=this._index+1}else if(e==="/"){this._state=4}else if(!whitespace(e)){this._state=9;this.sectionStart=this._index}};Tokenizer.prototype.stateInSelfClosingTag=function(e){if(e===">"){this.cbs.onselfclosingtag();this._state=1;this.sectionStart=this._index+1;this.special=1}else if(!whitespace(e)){this._state=8;this._index--}};Tokenizer.prototype.stateInAttributeName=function(e){if(e==="="||e==="/"||e===">"||whitespace(e)){this.cbs.onattribname(this.getSection());this.sectionStart=-1;this._state=10;this._index--}};Tokenizer.prototype.stateAfterAttributeName=function(e){if(e==="="){this._state=11}else if(e==="/"||e===">"){this.cbs.onattribend(undefined);this._state=8;this._index--}else if(!whitespace(e)){this.cbs.onattribend(undefined);this._state=9;this.sectionStart=this._index}};Tokenizer.prototype.stateBeforeAttributeValue=function(e){if(e==='"'){this._state=12;this.sectionStart=this._index+1}else if(e==="'"){this._state=13;this.sectionStart=this._index+1}else if(!whitespace(e)){this._state=14;this.sectionStart=this._index;this._index--}};Tokenizer.prototype.handleInAttributeValue=function(e,t){if(e===t){this.emitToken("onattribdata");this.cbs.onattribend(t);this._state=8}else if(this.decodeEntities&&e==="&"){this.emitToken("onattribdata");this.baseState=this._state;this._state=62;this.sectionStart=this._index}};Tokenizer.prototype.stateInAttributeValueDoubleQuotes=function(e){this.handleInAttributeValue(e,'"')};Tokenizer.prototype.stateInAttributeValueSingleQuotes=function(e){this.handleInAttributeValue(e,"'")};Tokenizer.prototype.stateInAttributeValueNoQuotes=function(e){if(whitespace(e)||e===">"){this.emitToken("onattribdata");this.cbs.onattribend(null);this._state=8;this._index--}else if(this.decodeEntities&&e==="&"){this.emitToken("onattribdata");this.baseState=this._state;this._state=62;this.sectionStart=this._index}};Tokenizer.prototype.stateBeforeDeclaration=function(e){this._state=e==="["?23:e==="-"?18:16};Tokenizer.prototype.stateInDeclaration=function(e){if(e===">"){this.cbs.ondeclaration(this.getSection());this._state=1;this.sectionStart=this._index+1}};Tokenizer.prototype.stateInProcessingInstruction=function(e){if(e===">"){this.cbs.onprocessinginstruction(this.getSection());this._state=1;this.sectionStart=this._index+1}};Tokenizer.prototype.stateBeforeComment=function(e){if(e==="-"){this._state=19;this.sectionStart=this._index+1}else{this._state=16}};Tokenizer.prototype.stateInComment=function(e){if(e==="-")this._state=21};Tokenizer.prototype.stateInSpecialComment=function(e){if(e===">"){this.cbs.oncomment(this.buffer.substring(this.sectionStart,this._index));this._state=1;this.sectionStart=this._index+1}};Tokenizer.prototype.stateAfterComment1=function(e){if(e==="-"){this._state=22}else{this._state=19}};Tokenizer.prototype.stateAfterComment2=function(e){if(e===">"){this.cbs.oncomment(this.buffer.substring(this.sectionStart,this._index-2));this._state=1;this.sectionStart=this._index+1}else if(e!=="-"){this._state=19}};Tokenizer.prototype.stateBeforeCdata6=function(e){if(e==="["){this._state=29;this.sectionStart=this._index+1}else{this._state=16;this._index--}};Tokenizer.prototype.stateInCdata=function(e){if(e==="]")this._state=30};Tokenizer.prototype.stateAfterCdata1=function(e){if(e==="]")this._state=31;else this._state=29};Tokenizer.prototype.stateAfterCdata2=function(e){if(e===">"){this.cbs.oncdata(this.buffer.substring(this.sectionStart,this._index-2));this._state=1;this.sectionStart=this._index+1}else if(e!=="]"){this._state=29}};Tokenizer.prototype.stateBeforeSpecialS=function(e){if(e==="c"||e==="C"){this._state=34}else if(e==="t"||e==="T"){this._state=44}else{this._state=3;this._index--}};Tokenizer.prototype.stateBeforeSpecialSEnd=function(e){if(this.special===2&&(e==="c"||e==="C")){this._state=39}else if(this.special===3&&(e==="t"||e==="T")){this._state=48}else this._state=1};Tokenizer.prototype.stateBeforeSpecialLast=function(e,t){if(e==="/"||e===">"||whitespace(e)){this.special=t}this._state=3;this._index--};Tokenizer.prototype.stateAfterSpecialLast=function(e,t){if(e===">"||whitespace(e)){this.special=1;this._state=6;this.sectionStart=this._index-t;this._index--}else this._state=1};Tokenizer.prototype.parseFixedEntity=function(e){if(e===void 0){e=this.xmlMode?o.default:s.default}if(this.sectionStart+1<this._index){var t=this.buffer.substring(this.sectionStart+1,this._index);if(Object.prototype.hasOwnProperty.call(e,t)){this.emitPartial(e[t]);this.sectionStart=this._index+1}}};Tokenizer.prototype.parseLegacyEntity=function(){var e=this.sectionStart+1;var t=Math.min(this._index-e,6);while(t>=2){var r=this.buffer.substr(e,t);if(Object.prototype.hasOwnProperty.call(a.default,r)){this.emitPartial(a.default[r]);this.sectionStart+=t+1;return}t--}};Tokenizer.prototype.stateInNamedEntity=function(e){if(e===";"){this.parseFixedEntity();if(this.baseState===1&&this.sectionStart+1<this._index&&!this.xmlMode){this.parseLegacyEntity()}this._state=this.baseState}else if((e<"0"||e>"9")&&!isASCIIAlpha(e)){if(this.xmlMode||this.sectionStart+1===this._index){}else if(this.baseState!==1){if(e!=="="){this.parseFixedEntity(a.default)}}else{this.parseLegacyEntity()}this._state=this.baseState;this._index--}};Tokenizer.prototype.decodeNumericEntity=function(e,t,r){var i=this.sectionStart+e;if(i!==this._index){var s=this.buffer.substring(i,this._index);var a=parseInt(s,t);this.emitPartial(n.default(a));this.sectionStart=r?this._index+1:this._index}this._state=this.baseState};Tokenizer.prototype.stateInNumericEntity=function(e){if(e===";"){this.decodeNumericEntity(2,10,true)}else if(e<"0"||e>"9"){if(!this.xmlMode){this.decodeNumericEntity(2,10,false)}else{this._state=this.baseState}this._index--}};Tokenizer.prototype.stateInHexEntity=function(e){if(e===";"){this.decodeNumericEntity(3,16,true)}else if((e<"a"||e>"f")&&(e<"A"||e>"F")&&(e<"0"||e>"9")){if(!this.xmlMode){this.decodeNumericEntity(3,16,false)}else{this._state=this.baseState}this._index--}};Tokenizer.prototype.cleanup=function(){if(this.sectionStart<0){this.buffer="";this.bufferOffset+=this._index;this._index=0}else if(this.running){if(this._state===1){if(this.sectionStart!==this._index){this.cbs.ontext(this.buffer.substr(this.sectionStart))}this.buffer="";this.bufferOffset+=this._index;this._index=0}else if(this.sectionStart===this._index){this.buffer="";this.bufferOffset+=this._index;this._index=0}else{this.buffer=this.buffer.substr(this.sectionStart);this._index-=this.sectionStart;this.bufferOffset+=this.sectionStart}this.sectionStart=0}};Tokenizer.prototype.parse=function(){while(this._index<this.buffer.length&&this.running){var e=this.buffer.charAt(this._index);if(this._state===1){this.stateText(e)}else if(this._state===12){this.stateInAttributeValueDoubleQuotes(e)}else if(this._state===9){this.stateInAttributeName(e)}else if(this._state===19){this.stateInComment(e)}else if(this._state===20){this.stateInSpecialComment(e)}else if(this._state===8){this.stateBeforeAttributeName(e)}else if(this._state===3){this.stateInTagName(e)}else if(this._state===6){this.stateInClosingTagName(e)}else if(this._state===2){this.stateBeforeTagName(e)}else if(this._state===10){this.stateAfterAttributeName(e)}else if(this._state===13){this.stateInAttributeValueSingleQuotes(e)}else if(this._state===11){this.stateBeforeAttributeValue(e)}else if(this._state===5){this.stateBeforeClosingTagName(e)}else if(this._state===7){this.stateAfterClosingTagName(e)}else if(this._state===32){this.stateBeforeSpecialS(e)}else if(this._state===21){this.stateAfterComment1(e)}else if(this._state===14){this.stateInAttributeValueNoQuotes(e)}else if(this._state===4){this.stateInSelfClosingTag(e)}else if(this._state===16){this.stateInDeclaration(e)}else if(this._state===15){this.stateBeforeDeclaration(e)}else if(this._state===22){this.stateAfterComment2(e)}else if(this._state===18){this.stateBeforeComment(e)}else if(this._state===33){this.stateBeforeSpecialSEnd(e)}else if(this._state===53){O(this,e)}else if(this._state===39){b(this,e)}else if(this._state===40){v(this,e)}else if(this._state===41){y(this,e)}else if(this._state===34){p(this,e)}else if(this._state===35){h(this,e)}else if(this._state===36){m(this,e)}else if(this._state===37){g(this,e)}else if(this._state===38){this.stateBeforeSpecialLast(e,2)}else if(this._state===42){x(this,e)}else if(this._state===43){this.stateAfterSpecialLast(e,6)}else if(this._state===44){_(this,e)}else if(this._state===29){this.stateInCdata(e)}else if(this._state===45){w(this,e)}else if(this._state===46){C(this,e)}else if(this._state===47){this.stateBeforeSpecialLast(e,3)}else if(this._state===48){T(this,e)}else if(this._state===49){S(this,e)}else if(this._state===50){A(this,e)}else if(this._state===51){this.stateAfterSpecialLast(e,5)}else if(this._state===52){N(this,e)}else if(this._state===54){E(this,e)}else if(this._state===55){k(this,e)}else if(this._state===56){D(this,e)}else if(this._state===57){this.stateBeforeSpecialLast(e,4)}else if(this._state===58){P(this,e)}else if(this._state===59){I(this,e)}else if(this._state===60){V(this,e)}else if(this._state===61){this.stateAfterSpecialLast(e,5)}else if(this._state===17){this.stateInProcessingInstruction(e)}else if(this._state===64){this.stateInNamedEntity(e)}else if(this._state===23){l(this,e)}else if(this._state===62){M(this,e)}else if(this._state===24){c(this,e)}else if(this._state===25){u(this,e)}else if(this._state===30){this.stateAfterCdata1(e)}else if(this._state===31){this.stateAfterCdata2(e)}else if(this._state===26){d(this,e)}else if(this._state===27){f(this,e)}else if(this._state===28){this.stateBeforeCdata6(e)}else if(this._state===66){this.stateInHexEntity(e)}else if(this._state===65){this.stateInNumericEntity(e)}else if(this._state===63){L(this,e)}else{this.cbs.onerror(Error("unknown _state"),this._state)}this._index++}this.cleanup()};Tokenizer.prototype.finish=function(){if(this.sectionStart<this._index){this.handleTrailingData()}this.cbs.onend()};Tokenizer.prototype.handleTrailingData=function(){var e=this.buffer.substr(this.sectionStart);if(this._state===29||this._state===30||this._state===31){this.cbs.oncdata(e)}else if(this._state===19||this._state===21||this._state===22){this.cbs.oncomment(e)}else if(this._state===64&&!this.xmlMode){this.parseLegacyEntity();if(this.sectionStart<this._index){this._state=this.baseState;this.handleTrailingData()}}else if(this._state===65&&!this.xmlMode){this.decodeNumericEntity(2,10,false);if(this.sectionStart<this._index){this._state=this.baseState;this.handleTrailingData()}}else if(this._state===66&&!this.xmlMode){this.decodeNumericEntity(3,16,false);if(this.sectionStart<this._index){this._state=this.baseState;this.handleTrailingData()}}else if(this._state!==3&&this._state!==8&&this._state!==11&&this._state!==10&&this._state!==9&&this._state!==13&&this._state!==12&&this._state!==14&&this._state!==6){this.cbs.ontext(e)}};Tokenizer.prototype.getSection=function(){return this.buffer.substring(this.sectionStart,this._index)};Tokenizer.prototype.emitToken=function(e){this.cbs[e](this.getSection());this.sectionStart=-1};Tokenizer.prototype.emitPartial=function(e){if(this.baseState!==1){this.cbs.onattribdata(e)}else{this.cbs.ontext(e)}};return Tokenizer}();t["default"]=R},250:function(e,t,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){if(i===undefined)i=r;Object.defineProperty(e,i,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,i){if(i===undefined)i=r;e[i]=t[r]});var n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))i(t,e,r);n(t,e);return t};var a=this&&this.__exportStar||function(e,t){for(var r in e)if(r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r))i(t,e,r)};var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.RssHandler=t.DefaultHandler=t.DomUtils=t.ElementType=t.Tokenizer=t.createDomStream=t.parseDOM=t.parseDocument=t.DomHandler=t.Parser=void 0;var l=r(9825);Object.defineProperty(t,"Parser",{enumerable:true,get:function(){return l.Parser}});var c=r(7847);Object.defineProperty(t,"DomHandler",{enumerable:true,get:function(){return c.DomHandler}});Object.defineProperty(t,"DefaultHandler",{enumerable:true,get:function(){return c.DomHandler}});function parseDocument(e,t){var r=new c.DomHandler(undefined,t);new l.Parser(r,t).end(e);return r.root}t.parseDocument=parseDocument;function parseDOM(e,t){return parseDocument(e,t).children}t.parseDOM=parseDOM;function createDomStream(e,t,r){var i=new c.DomHandler(e,t,r);return new l.Parser(i,t)}t.createDomStream=createDomStream;var u=r(3562);Object.defineProperty(t,"Tokenizer",{enumerable:true,get:function(){return o(u).default}});var d=s(r(9391));t.ElementType=d;a(r(3350),t);t.DomUtils=s(r(7459));var f=r(3350);Object.defineProperty(t,"RssHandler",{enumerable:true,get:function(){return f.FeedHandler}})},5438:e=>{"use strict";const t=/[\f\n\r\t\v ]{2,}/g;const r=" ";const normalize=e=>e.replace(t,r);e.exports=normalize},4645:module=>{module.exports=eval("require")("jimp")},5250:module=>{module.exports=eval("require")("probe-image-size")},6113:e=>{"use strict";e.exports=require("crypto")},7147:e=>{"use strict";e.exports=require("fs")},8937:e=>{"use strict";e.exports=require("next/dist/compiled/cssnano-simple")},7330:e=>{"use strict";e.exports=require("next/dist/compiled/lru-cache")},4429:e=>{"use strict";e.exports=require("next/dist/compiled/node-fetch")},7595:e=>{"use strict";e.exports=require("next/dist/compiled/postcss-safe-parser")},6794:e=>{"use strict";e.exports=require("next/dist/compiled/terser")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},977:e=>{"use strict";e.exports=require("postcss")},7310:e=>{"use strict";e.exports=require("url")},8649:e=>{"use strict";e.exports=JSON.parse('[{"name":"amp-3d-gltf","version":"0.1","latestVersion":"0.1"},{"name":"amp-3q-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-access","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-access-laterpay","version":["0.1","0.2"],"latestVersion":"0.2","options":{"hasCss":true}},{"name":"amp-access-poool","version":"0.1","latestVersion":"0.1"},{"name":"amp-access-scroll","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-accordion","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-accordion","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-action-macro","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-ad-custom","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-exit","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-adsense-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-adzerk-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-doubleclick-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-fake-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-nws-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-ad-network-valueimpression-impl","version":"0.1","latestVersion":"0.1"},{"name":"amp-addthis","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-analytics","version":"0.1","latestVersion":"0.1"},{"name":"amp-anim","version":"0.1","latestVersion":"0.1"},{"name":"amp-animation","version":"0.1","latestVersion":"0.1"},{"name":"amp-animation-polyfill","version":"0.1","latestVersion":"0.1"},{"name":"amp-apester-media","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-app-banner","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-audio","version":"0.1","latestVersion":"0.1"},{"name":"amp-auto-ads","version":"0.1","latestVersion":"0.1"},{"name":"amp-auto-lightbox","version":"0.1","latestVersion":"0.1"},{"name":"amp-autocomplete","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-base-carousel","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-base-carousel","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-beopinion","version":"0.1","latestVersion":"0.1"},{"name":"amp-bind","version":"0.1","latestVersion":"0.1"},{"name":"amp-bodymovin-animation","version":"0.1","latestVersion":"0.1"},{"name":"amp-brid-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-brightcove","version":"0.1","latestVersion":"0.1"},{"name":"amp-byside-content","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-cache-url","version":"0.1","latestVersion":"0.1"},{"name":"amp-call-tracking","version":"0.1","latestVersion":"0.1"},{"name":"amp-carousel","version":["0.1","0.2"],"latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-connatix-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-consent","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-crypto-polyfill","version":"0.1","latestVersion":"0.1"},{"name":"amp-dailymotion","version":"0.1","latestVersion":"0.1"},{"name":"amp-date-countdown","version":"0.1","latestVersion":"0.1"},{"name":"amp-date-countdown","version":"1.0","latestVersion":"0.1","options":{"npm":true}},{"name":"amp-date-display","version":"0.1","latestVersion":"0.1"},{"name":"amp-date-display","version":"1.0","latestVersion":"0.1","options":{"npm":true}},{"name":"amp-date-picker","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-delight-player","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-dynamic-css-classes","version":"0.1","latestVersion":"0.1"},{"name":"amp-embedly-card","version":"0.1","latestVersion":"0.1"},{"name":"amp-experiment","version":["0.1","1.0"],"latestVersion":"0.1"},{"name":"amp-facebook","version":"0.1","latestVersion":"0.1"},{"name":"amp-facebook-comments","version":["0.1","1.0"],"latestVersion":"0.1"},{"name":"amp-facebook-like","version":"0.1","latestVersion":"0.1"},{"name":"amp-facebook-page","version":"0.1","latestVersion":"0.1"},{"name":"amp-fit-text","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-fit-text","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-font","version":"0.1","latestVersion":"0.1"},{"name":"amp-form","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-fx-collection","version":"0.1","latestVersion":"0.1"},{"name":"amp-fx-flying-carpet","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-geo","version":"0.1","latestVersion":"0.1"},{"name":"amp-gfycat","version":"0.1","latestVersion":"0.1"},{"name":"amp-gist","version":"0.1","latestVersion":"0.1"},{"name":"amp-google-assistant-assistjs","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-google-document-embed","version":"0.1","latestVersion":"0.1"},{"name":"amp-gwd-animation","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-hulu","version":"0.1","latestVersion":"0.1"},{"name":"amp-iframe","version":"0.1","latestVersion":"0.1"},{"name":"amp-ima-video","version":"0.1","latestVersion":"0.1"},{"name":"amp-image-lightbox","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-image-slider","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-image-viewer","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-imgur","version":"0.1","latestVersion":"0.1"},{"name":"amp-inline-gallery","version":"0.1","latestVersion":"0.1","options":{"hasCss":true,"cssBinaries":["amp-inline-gallery","amp-inline-gallery-captions","amp-inline-gallery-pagination","amp-inline-gallery-slide","amp-inline-gallery-thumbnails"]}},{"name":"amp-inline-gallery","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"cssBinaries":["amp-inline-gallery-pagination"],"npm":true}},{"name":"amp-inputmask","version":"0.1","latestVersion":"0.1"},{"name":"amp-instagram","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-instagram","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-install-serviceworker","version":"0.1","latestVersion":"0.1"},{"name":"amp-intersection-observer-polyfill","version":"0.1","latestVersion":"0.1"},{"name":"amp-izlesene","version":"0.1","latestVersion":"0.1"},{"name":"amp-jwplayer","version":"0.1","latestVersion":"0.1"},{"name":"amp-kaltura-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-lightbox","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-lightbox","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-lightbox-gallery","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-link-rewriter","version":"0.1","latestVersion":"0.1"},{"name":"amp-list","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-live-list","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-loader","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-mathml","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-mega-menu","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-megaphone","version":"0.1","latestVersion":"0.1"},{"name":"amp-minute-media-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-mowplayer","version":"0.1","latestVersion":"0.1"},{"name":"amp-mraid","version":"0.1","latestVersion":"0.1"},{"name":"amp-mustache","version":["0.1","0.2"],"latestVersion":"0.2"},{"name":"amp-nested-menu","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-next-page","version":["0.1","1.0"],"latestVersion":"1.0","options":{"hasCss":true}},{"name":"amp-nexxtv-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-o2-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-onetap-google","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-ooyala-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-orientation-observer","version":"0.1","latestVersion":"0.1"},{"name":"amp-pan-zoom","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-pinterest","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-playbuzz","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-position-observer","version":"0.1","latestVersion":"0.1"},{"name":"amp-powr-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-reach-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-recaptcha-input","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-redbull-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-reddit","version":"0.1","latestVersion":"0.1"},{"name":"amp-render","version":"1.0","latestVersion":"1.0"},{"name":"amp-resize-observer-polyfill","version":"0.1","latestVersion":"0.1"},{"name":"amp-riddle-quiz","version":"0.1","latestVersion":"0.1"},{"name":"amp-script","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-selector","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-selector","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-shadow-dom-polyfill","version":"0.1","latestVersion":"0.1","options":{"noWrapper":true}},{"name":"amp-sidebar","version":["0.1","0.2","1.0"],"latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-skimlinks","version":"0.1","latestVersion":"0.1"},{"// 1":"`amp-slides` is deprecated","// 2":"Please use `<amp-carousel type=slides>` instead.","name":"amp-slides","version":"0.1","latestVersion":"0.1"},{"name":"amp-smartlinks","version":"0.1","latestVersion":"0.1"},{"name":"amp-social-share","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-social-share","version":"1.0","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-soundcloud","version":"0.1","latestVersion":"0.1"},{"name":"amp-springboard-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-standalone","version":"0.1","latestVersion":"0.1"},{"name":"amp-sticky-ad","version":"1.0","latestVersion":"1.0","options":{"hasCss":true}},{"name":"amp-story","version":"1.0","latestVersion":"1.0","options":{"hasCss":true,"cssBinaries":["amp-story-consent","amp-story-draggable-drawer-header","amp-story-hint","amp-story-info-dialog","amp-story-open-page-attachment","amp-story-share","amp-story-share-menu","amp-story-system-layer","amp-story-tooltip","amp-story-unsupported-browser-layer","amp-story-viewport-warning-layer"]}},{"name":"amp-story-360","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-story-auto-ads","version":"0.1","latestVersion":"0.1","options":{"hasCss":true,"cssBinaries":["amp-story-auto-ads-ad-badge","amp-story-auto-ads-attribution","amp-story-auto-ads-cta-button","amp-story-auto-ads-inabox","amp-story-auto-ads-progress-bar","amp-story-auto-ads-shared"]}},{"name":"amp-story-auto-analytics","version":"0.1","latestVersion":"0.1"},{"name":"amp-story-dev-tools","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-story-education","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-story-interactive","version":"0.1","latestVersion":"0.1","options":{"hasCss":true,"cssBinaries":["amp-story-interactive-binary-poll","amp-story-interactive-poll","amp-story-interactive-quiz","amp-story-interactive-results"]}},{"name":"amp-story-panning-media","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-story-player","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-stream-gallery","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-stream-gallery","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}},{"name":"amp-subscriptions","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-subscriptions-google","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-tiktok","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-timeago","version":"0.1","latestVersion":"0.1"},{"name":"amp-timeago","version":"1.0","latestVersion":"0.1","options":{"npm":true}},{"name":"amp-truncate-text","version":"0.1","latestVersion":"0.1","options":{"hasCss":true,"cssBinaries":["amp-truncate-text","amp-truncate-text-shadow"]}},{"name":"amp-twitter","version":"0.1","latestVersion":"0.1"},{"name":"amp-twitter","version":"1.0","latestVersion":"0.1","options":{"npm":true}},{"name":"amp-user-notification","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-video","version":"0.1","latestVersion":"0.1"},{"name":"amp-video","version":"1.0","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-video-docking","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-video-iframe","version":"0.1","latestVersion":"0.1"},{"name":"amp-video-iframe","version":"1.0","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-viewer-integration","version":"0.1","latestVersion":"0.1","options":{"// 1":"The viewer integration code needs to run asap, so that viewers","// 2":"can influence document state asap. Otherwise the document may ","// 3":"take a long time to learn that it should start process other","// 4":"extensions faster.","loadPriority":"high"}},{"name":"amp-vimeo","version":"0.1","latestVersion":"0.1"},{"name":"amp-vimeo","version":"1.0","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-vine","version":"0.1","latestVersion":"0.1"},{"name":"amp-viqeo-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-viz-vega","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-vk","version":"0.1","latestVersion":"0.1"},{"name":"amp-web-push","version":"0.1","latestVersion":"0.1","options":{"hasCss":true}},{"name":"amp-wistia-player","version":"0.1","latestVersion":"0.1"},{"name":"amp-yotpo","version":"0.1","latestVersion":"0.1"},{"name":"amp-youtube","version":"0.1","latestVersion":"0.1"},{"name":"amp-youtube","version":"1.0","latestVersion":"0.1","options":{"hasCss":true,"npm":true}}]')},7971:e=>{"use strict";e.exports=JSON.parse('{"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376}')},1859:e=>{"use strict";e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')},2128:e=>{"use strict";e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Acirc":"Â","acirc":"â","acute":"´","AElig":"Æ","aelig":"æ","Agrave":"À","agrave":"à","amp":"&","AMP":"&","Aring":"Å","aring":"å","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","brvbar":"¦","Ccedil":"Ç","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","COPY":"©","curren":"¤","deg":"°","divide":"÷","Eacute":"É","eacute":"é","Ecirc":"Ê","ecirc":"ê","Egrave":"È","egrave":"è","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","GT":">","Iacute":"Í","iacute":"í","Icirc":"Î","icirc":"î","iexcl":"¡","Igrave":"Ì","igrave":"ì","iquest":"¿","Iuml":"Ï","iuml":"ï","laquo":"«","lt":"<","LT":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","Ntilde":"Ñ","ntilde":"ñ","Oacute":"Ó","oacute":"ó","Ocirc":"Ô","ocirc":"ô","Ograve":"Ò","ograve":"ò","ordf":"ª","ordm":"º","Oslash":"Ø","oslash":"ø","Otilde":"Õ","otilde":"õ","Ouml":"Ö","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","QUOT":"\\"","raquo":"»","reg":"®","REG":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","THORN":"Þ","thorn":"þ","times":"×","Uacute":"Ú","uacute":"ú","Ucirc":"Û","ucirc":"û","Ugrave":"Ù","ugrave":"ù","uml":"¨","Uuml":"Ü","uuml":"ü","Yacute":"Ý","yacute":"ý","yen":"¥","yuml":"ÿ"}')},4931:e=>{"use strict";e.exports=JSON.parse('{"amp":"&","apos":"\'","gt":">","lt":"<","quot":"\\""}')}};var __webpack_module_cache__={};function __nccwpck_require__(e){var t=__webpack_module_cache__[e];if(t!==undefined){return t.exports}var r=__webpack_module_cache__[e]={exports:{}};var i=true;try{__webpack_modules__[e].call(r.exports,r,r.exports,__nccwpck_require__);i=false}finally{if(i)delete __webpack_module_cache__[e]}return r.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var __webpack_exports__=__nccwpck_require__(8483);module.exports=__webpack_exports__})();