{"version": 3, "sources": ["../../../src/lib/metadata/clone-metadata.ts"], "names": ["TYPE_URL", "replacer", "_key", "val", "URL", "_type", "value", "href", "reviver", "cloneMetadata", "metadata", "jsonString", "JSON", "stringify", "parse"], "mappings": "AAEA,MAAMA,WAAW;AAEjB,SAASC,SAASC,IAAY,EAAEC,GAAQ;IACtC,4CAA4C;IAC5C,IAAIA,eAAeC,KAAK;QACtB,OAAO;YAAEC,OAAOL;YAAUM,OAAOH,IAAII,IAAI;QAAC;IAC5C;IACA,OAAOJ;AACT;AAEA,SAASK,QAAQN,IAAY,EAAEC,GAAQ;IACrC,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQA,IAAIE,KAAK,KAAKL,UAAU;QACrE,OAAO,IAAII,IAAID,IAAIG,KAAK;IAC1B;IACA,OAAOH;AACT;AAEA,OAAO,SAASM,cAAcC,QAA0B;IACtD,MAAMC,aAAaC,KAAKC,SAAS,CAACH,UAAUT;IAC5C,OAAOW,KAAKE,KAAK,CAACH,YAAYH;AAChC"}