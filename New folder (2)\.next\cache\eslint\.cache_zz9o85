[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\about\\page.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\accessibility\\page.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\account\\page.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\automation\\page.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\dashboard\\page.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\debug\\page.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\layout.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\master-control\\page.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\master-control\\test.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\page.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\simple-sync\\page.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\dashboard-stats\\route.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\orders\\route.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\products\\route.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\settings\\route.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\toggle-feature\\route.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\alidrop-importer\\jobs\\route.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\alidrop-importer\\stop\\route.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\auto-sync\\route.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\clean-resync\\route.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\debug-images\\route.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\diagnose-products\\route.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\direct-products\\route.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\fast-sync\\route.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\image-search\\route.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\mock-woocommerce\\products\\route.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\push-subscribe\\route.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\push-unsubscribe\\route.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\reviews\\route.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\reviews\\[reviewId]\\route.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\smart-categorize\\route.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\sync-wordpress-products\\route.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\categories\\route.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\products\\route.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\route.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\test\\route.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce-cart\\route.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\auth\\callback\\page.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\checkout\\page.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\checkout\\success\\page.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\page.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\categories\\page.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\contact\\page.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cookies\\page.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\debug-products\\page.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\demo-products\\page.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\error.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\faq\\page.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\forgot-password\\page.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\layout.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\loading.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\login\\page.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\not-found.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\offline\\page.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\page.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\privacy\\page.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\register\\page.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\shop\\page.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\shop\\product\\[id]\\page.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\terms\\page.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\track-order\\page.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\wishlist\\page.js": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ClientScripts.js": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ClientScripts.jsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\FeaturesSection.js": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\HeroSection.js": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\SimpleProductCard.js": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\StatsSection.js": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Footer.js": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Header.js": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\postcss.config.js": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\EnhancedProductCard.js": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductCard.js": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductDetail.js": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductDetailComponent.js": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductFilter.js": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductGrid.js": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\QuickViewModal.jsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\RelatedProducts.js": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\PWAInstallPrompt.jsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\RecentReviews.jsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\ReviewForm.jsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\ReviewList.jsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\StarRating.jsx": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\shop\\ShopHeader.js": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\SocialLoginButtons.jsx": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\BackToTopButton.js": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\LoadingSkeleton.js": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\WhatsAppButton.js": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\featuresManager.js": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\pwa.js": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\reviews.js": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\smartCategorizer.js": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\socialAuth.js": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\utils.js": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\woocommerce.js": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\woocommerceFeatures.js": "97"}, {"size": 15469, "mtime": 1748704039604, "results": "98", "hashOfConfig": "99"}, {"size": 10523, "mtime": 1748956852753, "results": "100", "hashOfConfig": "99"}, {"size": 20017, "mtime": 1748646700769, "results": "101", "hashOfConfig": "99"}, {"size": 11175, "mtime": 1749841056687, "results": "102", "hashOfConfig": "99"}, {"size": 104044, "mtime": 1749989171736, "results": "103", "hashOfConfig": "99"}, {"size": 5756, "mtime": 1749769040160, "results": "104", "hashOfConfig": "99"}, {"size": 2144, "mtime": 1749842387100, "results": "105", "hashOfConfig": "99"}, {"size": 5084, "mtime": 1749849758489, "results": "106", "hashOfConfig": "99"}, {"size": 413, "mtime": 1749844352098, "results": "107", "hashOfConfig": "99"}, {"size": 7286, "mtime": 1749939859387, "results": "108", "hashOfConfig": "99"}, {"size": 11217, "mtime": 1749801106366, "results": "109", "hashOfConfig": "99"}, {"size": 7675, "mtime": 1749979747996, "results": "110", "hashOfConfig": "99"}, {"size": 847, "mtime": 1749978636709, "results": "111", "hashOfConfig": "99"}, {"size": 2048, "mtime": 1749980479784, "results": "112", "hashOfConfig": "99"}, {"size": 3299, "mtime": 1749985177497, "results": "113", "hashOfConfig": "99"}, {"size": 8953, "mtime": 1749972433852, "results": "114", "hashOfConfig": "99"}, {"size": 796, "mtime": 1749841469929, "results": "115", "hashOfConfig": "99"}, {"size": 1057, "mtime": 1749841482013, "results": "116", "hashOfConfig": "99"}, {"size": 8580, "mtime": 1749840293595, "results": "117", "hashOfConfig": "99"}, {"size": 10224, "mtime": 1749839326081, "results": "118", "hashOfConfig": "99"}, {"size": 3263, "mtime": 1749807114352, "results": "119", "hashOfConfig": "99"}, {"size": 3876, "mtime": 1749811211909, "results": "120", "hashOfConfig": "99"}, {"size": 7098, "mtime": 1749807691570, "results": "121", "hashOfConfig": "99"}, {"size": 13551, "mtime": 1749847890344, "results": "122", "hashOfConfig": "99"}, {"size": 1896, "mtime": 1748860307506, "results": "123", "hashOfConfig": "99"}, {"size": 11427, "mtime": 1749590387848, "results": "124", "hashOfConfig": "99"}, {"size": 3302, "mtime": 1749587049837, "results": "125", "hashOfConfig": "99"}, {"size": 2844, "mtime": 1749587067053, "results": "126", "hashOfConfig": "99"}, {"size": 3687, "mtime": 1749587281816, "results": "127", "hashOfConfig": "99"}, {"size": 2666, "mtime": 1749587298465, "results": "128", "hashOfConfig": "99"}, {"size": 7944, "mtime": 1749642257396, "results": "129", "hashOfConfig": "99"}, {"size": 5329, "mtime": 1749845667849, "results": "130", "hashOfConfig": "99"}, {"size": 1479, "mtime": 1749917061613, "results": "131", "hashOfConfig": "99"}, {"size": 2294, "mtime": 1749800148681, "results": "132", "hashOfConfig": "99"}, {"size": 1878, "mtime": 1749319965273, "results": "133", "hashOfConfig": "99"}, {"size": 2363, "mtime": 1749800161303, "results": "134", "hashOfConfig": "99"}, {"size": 8083, "mtime": 1749940693647, "results": "135", "hashOfConfig": "99"}, {"size": 2906, "mtime": 1748707483290, "results": "136", "hashOfConfig": "99"}, {"size": 24217, "mtime": 1748647149279, "results": "137", "hashOfConfig": "99"}, {"size": 10488, "mtime": 1748647202898, "results": "138", "hashOfConfig": "99"}, {"size": 14032, "mtime": 1748783315285, "results": "139", "hashOfConfig": "99"}, {"size": 4632, "mtime": 1748786428708, "results": "140", "hashOfConfig": "99"}, {"size": 13119, "mtime": 1749972403494, "results": "141", "hashOfConfig": "99"}, {"size": 10498, "mtime": 1748956803389, "results": "142", "hashOfConfig": "99"}, {"size": 4386, "mtime": 1749801510761, "results": "143", "hashOfConfig": "99"}, {"size": 9130, "mtime": 1749686564716, "results": "144", "hashOfConfig": "99"}, {"size": 6052, "mtime": 1748647555369, "results": "145", "hashOfConfig": "99"}, {"size": 17491, "mtime": 1749584771123, "results": "146", "hashOfConfig": "99"}, {"size": 7026, "mtime": 1748864769832, "results": "147", "hashOfConfig": "99"}, {"size": 7680, "mtime": 1749989074357, "results": "148", "hashOfConfig": "99"}, {"size": 1881, "mtime": 1748692534481, "results": "149", "hashOfConfig": "99"}, {"size": 9491, "mtime": 1749584991657, "results": "150", "hashOfConfig": "99"}, {"size": 5661, "mtime": 1748693548938, "results": "151", "hashOfConfig": "99"}, {"size": 4930, "mtime": 1750081355174, "results": "152", "hashOfConfig": "99"}, {"size": 12668, "mtime": 1750080723086, "results": "153", "hashOfConfig": "99"}, {"size": 9894, "mtime": 1748946728946, "results": "154", "hashOfConfig": "99"}, {"size": 21061, "mtime": 1749585166217, "results": "155", "hashOfConfig": "99"}, {"size": 8844, "mtime": 1750080644937, "results": "156", "hashOfConfig": "99"}, {"size": 3198, "mtime": 1749552176320, "results": "157", "hashOfConfig": "99"}, {"size": 10815, "mtime": 1748946828742, "results": "158", "hashOfConfig": "99"}, {"size": 21352, "mtime": 1748987637348, "results": "159", "hashOfConfig": "99"}, {"size": 17303, "mtime": 1748965972457, "results": "160", "hashOfConfig": "99"}, {"size": 1688, "mtime": 1749973818609, "results": "161", "hashOfConfig": "162"}, {"size": 1478, "mtime": 1749586991129, "results": "163", "hashOfConfig": "162"}, {"size": 2451, "mtime": 1748852675721, "results": "164", "hashOfConfig": "162"}, {"size": 7882, "mtime": 1748942572360, "results": "165", "hashOfConfig": "162"}, {"size": 1824, "mtime": 1749324817559, "results": "166", "hashOfConfig": "162"}, {"size": 2197, "mtime": 1748852699727, "results": "167", "hashOfConfig": "162"}, {"size": 15507, "mtime": 1749584605465, "results": "168", "hashOfConfig": "162"}, {"size": 19041, "mtime": 1748987082792, "results": "169", "hashOfConfig": "162"}, {"size": 96, "mtime": 1748647817872, "results": "170", "hashOfConfig": "162"}, {"size": 7435, "mtime": 1749751594243, "results": "171", "hashOfConfig": "162"}, {"size": 8355, "mtime": 1749801968271, "results": "172", "hashOfConfig": "162"}, {"size": 31721, "mtime": 1749754886984, "results": "173", "hashOfConfig": "162"}, {"size": 2759, "mtime": 1748646956419, "results": "174", "hashOfConfig": "162"}, {"size": 12899, "mtime": 1748855962756, "results": "175", "hashOfConfig": "162"}, {"size": 5296, "mtime": 1749767887488, "results": "176", "hashOfConfig": "162"}, {"size": 12153, "mtime": 1749754942731, "results": "177", "hashOfConfig": "162"}, {"size": 2759, "mtime": 1748647008768, "results": "178", "hashOfConfig": "162"}, {"size": 7941, "mtime": 1749586842936, "results": "179", "hashOfConfig": "162"}, {"size": 7813, "mtime": 1749587682247, "results": "180", "hashOfConfig": "162"}, {"size": 11027, "mtime": 1749587372296, "results": "181", "hashOfConfig": "162"}, {"size": 14918, "mtime": 1749587442339, "results": "182", "hashOfConfig": "162"}, {"size": 6110, "mtime": 1749587328363, "results": "183", "hashOfConfig": "162"}, {"size": 9458, "mtime": 1748860676661, "results": "184", "hashOfConfig": "162"}, {"size": 2046, "mtime": 1748707544199, "results": "185", "hashOfConfig": "162"}, {"size": 1087, "mtime": 1748692730442, "results": "186", "hashOfConfig": "162"}, {"size": 7311, "mtime": 1748692504442, "results": "187", "hashOfConfig": "162"}, {"size": 1613, "mtime": 1750059811273, "results": "188", "hashOfConfig": "162"}, {"size": 32908, "mtime": 1749972368370, "results": "189", "hashOfConfig": "99"}, {"size": 8521, "mtime": 1749586877863, "results": "190", "hashOfConfig": "99"}, {"size": 9139, "mtime": 1749587261496, "results": "191", "hashOfConfig": "99"}, {"size": 11985, "mtime": 1749642195058, "results": "192", "hashOfConfig": "99"}, {"size": 3886, "mtime": 1748707440510, "results": "193", "hashOfConfig": "99"}, {"size": 12079, "mtime": 1748647601287, "results": "194", "hashOfConfig": "99"}, {"size": 49303, "mtime": 1750068610175, "results": "195", "hashOfConfig": "99"}, {"size": 16646, "mtime": 1749940713197, "results": "196", "hashOfConfig": "99"}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "57i<PERSON><PERSON>", {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d4fmka", {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\about\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\accessibility\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\account\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\automation\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\dashboard\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\debug\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\layout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\master-control\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\master-control\\test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\admin\\simple-sync\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\dashboard-stats\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\orders\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\settings\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\admin\\toggle-feature\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\alidrop-importer\\jobs\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\alidrop-importer\\stop\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\auto-sync\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\clean-resync\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\debug-images\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\diagnose-products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\direct-products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\fast-sync\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\image-search\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\mock-woocommerce\\products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\push-subscribe\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\push-unsubscribe\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\reviews\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\reviews\\[reviewId]\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\smart-categorize\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\sync-wordpress-products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\categories\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\products\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce\\test\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\api\\woocommerce-cart\\route.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\auth\\callback\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\checkout\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\checkout\\success\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cart\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\categories\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\contact\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\cookies\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\debug-products\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\demo-products\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\error.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\faq\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\forgot-password\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\layout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\loading.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\login\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\not-found.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\offline\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\privacy\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\register\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\shop\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\shop\\product\\[id]\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\terms\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\track-order\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\wishlist\\page.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ClientScripts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ClientScripts.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\FeaturesSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\SimpleProductCard.js", ["488"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\StatsSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Header.js", ["489"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\postcss.config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\EnhancedProductCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductDetailComponent.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductFilter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductGrid.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\QuickViewModal.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\RelatedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\PWAInstallPrompt.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\RecentReviews.jsx", ["490", "491"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\ReviewForm.jsx", ["492"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\ReviewList.jsx", ["493", "494", "495"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\StarRating.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\shop\\ShopHeader.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\SocialLoginButtons.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\BackToTopButton.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\LoadingSkeleton.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\WhatsAppButton.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\featuresManager.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\pwa.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\reviews.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\smartCategorizer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\socialAuth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\woocommerce.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\lib\\woocommerceFeatures.js", [], [], {"ruleId": "496", "severity": 1, "message": "497", "line": 11, "column": 11, "nodeType": "498", "endLine": 15, "endColumn": 13}, {"ruleId": "496", "severity": 1, "message": "497", "line": 312, "column": 19, "nodeType": "498", "endLine": 316, "endColumn": 21}, {"ruleId": "499", "severity": 1, "message": "500", "line": 18, "column": 6, "nodeType": "501", "endLine": 18, "endColumn": 13, "suggestions": "502"}, {"ruleId": "496", "severity": 1, "message": "497", "line": 160, "column": 11, "nodeType": "498", "endLine": 164, "endColumn": 13}, {"ruleId": "496", "severity": 1, "message": "497", "line": 299, "column": 23, "nodeType": "498", "endLine": 303, "endColumn": 25}, {"ruleId": "499", "severity": 1, "message": "503", "line": 71, "column": 6, "nodeType": "501", "endLine": 71, "endColumn": 26, "suggestions": "504"}, {"ruleId": "496", "severity": 1, "message": "497", "line": 334, "column": 9, "nodeType": "498", "endLine": 338, "endColumn": 11}, {"ruleId": "496", "severity": 1, "message": "497", "line": 404, "column": 17, "nodeType": "498", "endLine": 409, "endColumn": 19}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRecentReviews'. Either include it or remove the dependency array.", "ArrayExpression", ["505"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["506"], {"desc": "507", "fix": "508"}, {"desc": "509", "fix": "510"}, "Update the dependencies array to be: [fetchRecentReviews, limit]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [productId, filters, fetchReviews]", {"range": "513", "text": "514"}, [497, 504], "[fetchRecentReviews, limit]", [1672, 1692], "[productId, filters, fetchReviews]"]