{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["createSelfSignedCertificate", "WritableStream", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "getCacheDirectory", "binaryPath", "path", "join", "fs", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "Log", "info", "response", "fetch", "ok", "body", "status", "binaryWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "error", "close", "chmod", "err", "host", "certDir", "resolvedCertDir", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "execSync", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": ";;;;+BAiGsBA;;;eAAAA;;;2DAjGP;6DACE;mCACiB;6DACb;+BACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACzB,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAInC,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBC,IAAAA,oCAAiB,EAAC;QACzC,MAAMC,aAAaC,aAAI,CAACC,IAAI,CAACJ,gBAAgBD;QAE7C,IAAIM,WAAE,CAACC,UAAU,CAACJ,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMK,cAAc,CAAC,wDAAwD,EAAEf,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMM,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACT,gBAAgB;YAAEU,WAAW;QAAK;QAE1DC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMP;QAE7B,IAAI,CAACM,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,IAAInB,MAAM,CAAC,2BAA2B,EAAEgB,SAASI,MAAM,CAAC,CAAC;QACjE;QAEAN,KAAIC,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,oBAAoBb,WAAE,CAACc,iBAAiB,CAACjB;QAE/C,MAAMW,SAASG,IAAI,CAACI,MAAM,CACxB,IAAI9B,eAAe;YACjB+B,OAAMC,KAAK;gBACT,OAAO,IAAIC,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBG,KAAK,CAACC,OAAO,CAACI;wBAC9B,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;YACAG;gBACE,OAAO,IAAIJ,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBS,KAAK,CAAC,CAACD;wBACvB,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;QACF;QAGF,MAAMnB,WAAE,CAACG,QAAQ,CAACoB,KAAK,CAAC1B,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAO2B,KAAK;QACZlB,KAAIe,KAAK,CAAC,6BAA6BG;IACzC;AACF;AAEO,eAAexC,4BACpByC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAM7B,aAAa,MAAMJ;QACzB,IAAI,CAACI,YAAY,MAAM,IAAIL,MAAM;QAEjC,MAAMmC,kBAAkB7B,aAAI,CAACqB,OAAO,CAAC7B,QAAQsC,GAAG,IAAI,CAAC,EAAE,EAAEF,QAAQ,CAAC;QAElE,MAAM1B,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACuB,iBAAiB;YACvCtB,WAAW;QACb;QAEA,MAAMwB,UAAU/B,aAAI,CAACqB,OAAO,CAACQ,iBAAiB;QAC9C,MAAMG,WAAWhC,aAAI,CAACqB,OAAO,CAACQ,iBAAiB;QAE/CrB,KAAIC,IAAI,CACN;QAGF,MAAMwB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJP,QAAQ,CAACM,aAAaE,QAAQ,CAACR,QAC3B;eAAIM;YAAcN;SAAK,GACvBM;QAENG,IAAAA,uBAAQ,EACN,CAAC,CAAC,EAAErC,WAAW,sBAAsB,EAAEgC,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAMjC,IAAI,CACpF,KACA,CAAC,EACH;YAAEoC,OAAO;QAAS;QAGpB,MAAMC,aAAaF,IAAAA,uBAAQ,EAAC,CAAC,CAAC,EAAErC,WAAW,SAAS,CAAC,EAAEwC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACtC,WAAE,CAACC,UAAU,CAAC4B,YAAY,CAAC7B,WAAE,CAACC,UAAU,CAAC6B,WAAW;YACvD,MAAM,IAAItC,MAAM;QAClB;QAEAc,KAAIC,IAAI,CAAC,CAAC,+BAA+B,EAAE6B,WAAW,CAAC;QACvD9B,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEoB,gBAAgB,CAAC;QAErD,MAAMY,gBAAgBzC,aAAI,CAACqB,OAAO,CAAC7B,QAAQsC,GAAG,IAAI;QAElD,IAAI5B,WAAE,CAACC,UAAU,CAACsC,gBAAgB;YAChC,MAAMC,YAAY,MAAMxC,WAAE,CAACG,QAAQ,CAACsC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUP,QAAQ,CAACP,UAAU;gBAChCpB,KAAIC,IAAI,CAAC;gBAET,MAAMP,WAAE,CAACG,QAAQ,CAACuC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEb,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLiB,KAAKd;YACLe,MAAMd;YACNe,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOZ,KAAK;QACZlB,KAAIe,KAAK,CACP,qEACAG;IAEJ;AACF"}