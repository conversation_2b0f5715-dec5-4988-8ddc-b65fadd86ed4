// WooCommerce Features Integration
import { wooCommerceApi } from './woocommerce.js';

class WooCommerceFeatures {
  constructor() {
    this.initialized = false;
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  async initialize() {
    try {
      console.log('🔗 Initializing WooCommerce Features Integration...');
      
      // Test WooCommerce connection
      const testConnection = await wooCommerceApi.getProducts({ per_page: 1 });
      if (testConnection) {
        this.initialized = true;
        console.log('✅ WooCommerce Features Integration ready');
        return true;
      }
      
      throw new Error('WooCommerce connection failed');
    } catch (error) {
      console.error('❌ WooCommerce Features Integration failed:', error);
      return false;
    }
  }

  // Cache management
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  getCache(key) {
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  // 1. PRODUCT RECOMMENDATIONS - Real WooCommerce Integration
  async getProductRecommendations(productId, limit = 4) {
    try {
      const cacheKey = `recommendations_${productId}_${limit}`;
      const cached = this.getCache(cacheKey);
      if (cached) return cached;

      console.log(`🎯 Getting recommendations for product ${productId}...`);

      // Get current product details
      const currentProduct = await wooCommerceApi.getProduct(productId);
      if (!currentProduct) {
        throw new Error('Product not found');
      }

      // Get products from same categories
      const categoryIds = currentProduct.categories?.map(cat => cat.id) || [];
      let recommendations = [];

      if (categoryIds.length > 0) {
        // Get products from same categories
        const categoryProducts = await wooCommerceApi.getProducts({
          category: categoryIds.join(','),
          per_page: limit * 2,
          exclude: [productId],
          status: 'publish',
          orderby: 'popularity'
        });

        recommendations = categoryProducts || [];
      }

      // If not enough recommendations, get popular products
      if (recommendations.length < limit) {
        const popularProducts = await wooCommerceApi.getProducts({
          per_page: limit * 2,
          exclude: [productId],
          status: 'publish',
          orderby: 'popularity'
        });

        // Merge and deduplicate
        const existingIds = recommendations.map(p => p.id);
        const additionalProducts = (popularProducts || []).filter(p => !existingIds.includes(p.id));
        recommendations = [...recommendations, ...additionalProducts];
      }

      // Format recommendations
      const formattedRecommendations = recommendations.slice(0, limit).map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
        regular_price: product.regular_price,
        sale_price: product.sale_price,
        image: product.images?.[0]?.src || '/placeholder.jpg',
        permalink: product.permalink,
        categories: product.categories?.map(cat => cat.name) || [],
        rating: product.average_rating || 0,
        rating_count: product.rating_count || 0
      }));

      this.setCache(cacheKey, formattedRecommendations);
      console.log(`✅ Found ${formattedRecommendations.length} recommendations`);
      
      return formattedRecommendations;

    } catch (error) {
      console.error('❌ Error getting product recommendations:', error);
      return [];
    }
  }

  // 2. LOYALTY PROGRAM - WooCommerce Orders Integration
  async getLoyaltyPoints(customerId) {
    try {
      const cacheKey = `loyalty_${customerId}`;
      const cached = this.getCache(cacheKey);
      if (cached) return cached;

      console.log(`🎁 Calculating loyalty points for customer ${customerId}...`);

      // Get customer orders
      const orders = await wooCommerceApi.getOrders({
        customer: customerId,
        status: 'completed',
        per_page: 100
      });

      if (!orders || orders.length === 0) {
        return { points: 0, tier: 'Bronze', totalSpent: 0, orderCount: 0 };
      }

      // Calculate points (10 points per £1 spent)
      const totalSpent = orders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0);
      const points = Math.floor(totalSpent * 10);
      
      // Determine tier
      let tier = 'Bronze';
      if (totalSpent >= 1000) tier = 'Platinum';
      else if (totalSpent >= 500) tier = 'Gold';
      else if (totalSpent >= 200) tier = 'Silver';

      const loyaltyData = {
        points,
        tier,
        totalSpent,
        orderCount: orders.length,
        nextTierThreshold: tier === 'Bronze' ? 200 : tier === 'Silver' ? 500 : tier === 'Gold' ? 1000 : null,
        benefits: this.getTierBenefits(tier)
      };

      this.setCache(cacheKey, loyaltyData);
      console.log(`✅ Customer has ${points} points (${tier} tier)`);
      
      return loyaltyData;

    } catch (error) {
      console.error('❌ Error calculating loyalty points:', error);
      return { points: 0, tier: 'Bronze', totalSpent: 0, orderCount: 0 };
    }
  }

  getTierBenefits(tier) {
    const benefits = {
      Bronze: ['5% birthday discount', 'Free shipping on orders over £50'],
      Silver: ['10% birthday discount', 'Free shipping on orders over £30', 'Early access to sales'],
      Gold: ['15% birthday discount', 'Free shipping on all orders', 'Early access to sales', 'Priority customer support'],
      Platinum: ['20% birthday discount', 'Free shipping on all orders', 'Early access to sales', 'Priority customer support', 'Exclusive products access']
    };
    return benefits[tier] || benefits.Bronze;
  }

  // 3. ONE-CLICK REORDER - WooCommerce Order History
  async getReorderableOrders(customerId, limit = 10) {
    try {
      const cacheKey = `reorder_${customerId}_${limit}`;
      const cached = this.getCache(cacheKey);
      if (cached) return cached;

      console.log(`⚡ Getting reorderable orders for customer ${customerId}...`);

      // Get customer's completed orders
      const orders = await wooCommerceApi.getOrders({
        customer: customerId,
        status: 'completed',
        per_page: limit,
        orderby: 'date',
        order: 'desc'
      });

      if (!orders || orders.length === 0) {
        return [];
      }

      // Format orders for reordering
      const reorderableOrders = orders.map(order => ({
        id: order.id,
        date: order.date_created,
        total: order.total,
        items: order.line_items?.map(item => ({
          product_id: item.product_id,
          variation_id: item.variation_id || null,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          image: item.image?.src || '/placeholder.jpg'
        })) || [],
        itemCount: order.line_items?.length || 0
      }));

      this.setCache(cacheKey, reorderableOrders);
      console.log(`✅ Found ${reorderableOrders.length} reorderable orders`);
      
      return reorderableOrders;

    } catch (error) {
      console.error('❌ Error getting reorderable orders:', error);
      return [];
    }
  }

  // 4. INVENTORY ALERTS - WooCommerce Stock Management
  async getInventoryAlerts() {
    try {
      const cacheKey = 'inventory_alerts';
      const cached = this.getCache(cacheKey);
      if (cached) return cached;

      console.log('📦 Checking inventory alerts...');

      // Get all products with stock management
      const products = await wooCommerceApi.getProducts({
        per_page: 100,
        manage_stock: true,
        status: 'publish'
      });

      if (!products || products.length === 0) {
        return { lowStock: [], outOfStock: [], total: 0 };
      }

      const lowStockThreshold = 10;
      const lowStock = [];
      const outOfStock = [];

      products.forEach(product => {
        const stock = parseInt(product.stock_quantity || 0);
        
        if (product.stock_status === 'outofstock' || stock === 0) {
          outOfStock.push({
            id: product.id,
            name: product.name,
            stock: stock,
            sku: product.sku
          });
        } else if (stock <= lowStockThreshold) {
          lowStock.push({
            id: product.id,
            name: product.name,
            stock: stock,
            sku: product.sku
          });
        }
      });

      const alerts = {
        lowStock,
        outOfStock,
        total: lowStock.length + outOfStock.length,
        lastChecked: new Date().toISOString()
      };

      this.setCache(cacheKey, alerts);
      console.log(`✅ Found ${alerts.total} inventory alerts`);
      
      return alerts;

    } catch (error) {
      console.error('❌ Error checking inventory alerts:', error);
      return { lowStock: [], outOfStock: [], total: 0 };
    }
  }

  // 5. CUSTOMER ANALYTICS - WooCommerce Customer Data
  async getCustomerAnalytics(customerId) {
    try {
      const cacheKey = `analytics_${customerId}`;
      const cached = this.getCache(cacheKey);
      if (cached) return cached;

      console.log(`📊 Getting customer analytics for ${customerId}...`);

      // Get customer data
      const customer = await wooCommerceApi.getCustomer(customerId);
      const orders = await wooCommerceApi.getOrders({
        customer: customerId,
        per_page: 100
      });

      if (!customer || !orders) {
        throw new Error('Customer data not found');
      }

      // Calculate analytics
      const totalSpent = orders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0);
      const avgOrderValue = orders.length > 0 ? totalSpent / orders.length : 0;
      const lastOrderDate = orders.length > 0 ? orders[0].date_created : null;
      
      // Get favorite categories
      const categoryCount = {};
      orders.forEach(order => {
        order.line_items?.forEach(item => {
          // You'd need to get product details to get categories
          // This is a simplified version
        });
      });

      const analytics = {
        customerId,
        email: customer.email,
        firstName: customer.first_name,
        lastName: customer.last_name,
        totalOrders: orders.length,
        totalSpent,
        avgOrderValue,
        lastOrderDate,
        registrationDate: customer.date_created,
        favoriteCategories: Object.keys(categoryCount).slice(0, 3)
      };

      this.setCache(cacheKey, analytics);
      console.log(`✅ Customer analytics calculated`);
      
      return analytics;

    } catch (error) {
      console.error('❌ Error getting customer analytics:', error);
      return null;
    }
  }

  // 6. PUSH NOTIFICATIONS - WooCommerce Order Updates
  async getOrderUpdates(customerId, lastCheck = null) {
    try {
      console.log(`🔔 Checking order updates for customer ${customerId}...`);

      const params = {
        customer: customerId,
        per_page: 10,
        orderby: 'date',
        order: 'desc'
      };

      if (lastCheck) {
        params.modified_after = lastCheck;
      }

      const orders = await wooCommerceApi.getOrders(params);

      if (!orders || orders.length === 0) {
        return [];
      }

      // Format notifications
      const notifications = orders.map(order => ({
        id: order.id,
        type: 'order_update',
        title: `Order #${order.number} ${order.status}`,
        message: `Your order is now ${order.status}`,
        status: order.status,
        total: order.total,
        date: order.date_modified || order.date_created,
        actionUrl: `/account/orders/${order.id}`
      }));

      console.log(`✅ Found ${notifications.length} order updates`);
      return notifications;

    } catch (error) {
      console.error('❌ Error getting order updates:', error);
      return [];
    }
  }

  // 7. PRODUCT SEARCH - Enhanced WooCommerce Search
  async searchProducts(query, filters = {}) {
    try {
      console.log(`🔍 Searching products: "${query}"...`);

      const searchParams = {
        search: query,
        per_page: filters.limit || 20,
        status: 'publish'
      };

      // Add filters
      if (filters.category) searchParams.category = filters.category;
      if (filters.min_price) searchParams.min_price = filters.min_price;
      if (filters.max_price) searchParams.max_price = filters.max_price;
      if (filters.on_sale) searchParams.on_sale = filters.on_sale;

      const products = await wooCommerceApi.getProducts(searchParams);

      if (!products || products.length === 0) {
        return { products: [], total: 0, query };
      }

      // Format search results
      const formattedProducts = products.map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
        regular_price: product.regular_price,
        sale_price: product.sale_price,
        image: product.images?.[0]?.src || '/placeholder.jpg',
        permalink: product.permalink,
        categories: product.categories?.map(cat => cat.name) || [],
        rating: product.average_rating || 0,
        stock_status: product.stock_status,
        on_sale: product.on_sale
      }));

      console.log(`✅ Found ${formattedProducts.length} products`);
      
      return {
        products: formattedProducts,
        total: formattedProducts.length,
        query,
        filters
      };

    } catch (error) {
      console.error('❌ Error searching products:', error);
      return { products: [], total: 0, query };
    }
  }

  // 8. CART INTEGRATION - WooCommerce Cart Management
  async addToCart(productId, quantity = 1, variationId = null, customerId = null) {
    try {
      console.log(`🛒 Adding product ${productId} to cart...`);

      const response = await fetch('/api/woocommerce-cart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add_to_cart',
          productId,
          quantity,
          variationId,
          customerId
        })
      });

      const data = await response.json();

      if (data.success) {
        // Trigger cart update event
        window.dispatchEvent(new CustomEvent('cartUpdated', {
          detail: {
            action: 'add',
            product: data.product,
            quantity
          }
        }));

        console.log(`✅ Product added to cart: ${data.product?.name}`);
        return data;
      } else {
        throw new Error(data.error);
      }

    } catch (error) {
      console.error('❌ Error adding to cart:', error);
      return { success: false, error: error.message };
    }
  }

  // Bulk add to cart (for reorder functionality)
  async bulkAddToCart(items, customerId = null) {
    try {
      console.log(`🛒 Bulk adding ${items.length} items to cart...`);

      const response = await fetch('/api/woocommerce-cart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulk_add_to_cart',
          items,
          customerId
        })
      });

      const data = await response.json();

      if (data.success) {
        // Trigger cart update event
        window.dispatchEvent(new CustomEvent('cartUpdated', {
          detail: {
            action: 'bulk_add',
            results: data.results
          }
        }));

        console.log(`✅ Bulk add complete: ${data.results.total_added} items added`);
        return data;
      } else {
        throw new Error(data.error);
      }

    } catch (error) {
      console.error('❌ Error bulk adding to cart:', error);
      return { success: false, error: error.message };
    }
  }

  // Get cart contents
  async getCart(customerId = null) {
    try {
      const params = customerId ? `?customer_id=${customerId}` : '';
      const response = await fetch(`/api/woocommerce-cart${params}`);
      const data = await response.json();

      if (data.success) {
        return data.cart;
      } else {
        throw new Error(data.error);
      }

    } catch (error) {
      console.error('❌ Error getting cart:', error);
      return null;
    }
  }
}

// Create global instance
const wooCommerceFeatures = new WooCommerceFeatures();

// Auto-initialize
if (typeof window !== 'undefined') {
  wooCommerceFeatures.initialize();
}

export default wooCommerceFeatures;
