#!/usr/bin/env node

// Quick Admin Dashboard Access Test
const http = require('http');

console.log('🧪 Testing Admin Dashboard Access...\n');

// Test if server is running
function testServerRunning() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      console.log('✅ Development server is running');
      console.log(`   Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log('❌ Development server is not running');
      console.log('   Please start with: npm run dev');
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('⏱️ Server response timeout');
      req.destroy();
      resolve(false);
    });
  });
}

// Test admin dashboard accessibility
function testAdminDashboard() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000/admin/dashboard', (res) => {
      console.log('✅ Admin dashboard is accessible');
      console.log(`   Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log('❌ Admin dashboard is not accessible');
      console.log(`   Error: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('⏱️ Admin dashboard response timeout');
      req.destroy();
      resolve(false);
    });
  });
}

// Test API endpoints
function testAPIEndpoint(endpoint, name) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:3000${endpoint}`, (res) => {
      console.log(`✅ ${name} API is accessible`);
      console.log(`   Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log(`❌ ${name} API is not accessible`);
      console.log(`   Error: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log(`⏱️ ${name} API response timeout`);
      req.destroy();
      resolve(false);
    });
  });
}

// Run all tests
async function runTests() {
  console.log('🔍 Testing server accessibility...');
  
  const serverRunning = await testServerRunning();
  
  if (!serverRunning) {
    console.log('\n🚨 ISSUE: Development server is not running');
    console.log('\n🔧 TO FIX:');
    console.log('1. Open a terminal');
    console.log('2. Navigate to your project folder');
    console.log('3. Run: npm run dev');
    console.log('4. Wait for "Ready" message');
    console.log('5. Then test again');
    return;
  }
  
  console.log('\n🔍 Testing admin dashboard...');
  await testAdminDashboard();
  
  console.log('\n🔍 Testing API endpoints...');
  await testAPIEndpoint('/api/admin/dashboard-stats', 'Dashboard Stats');
  await testAPIEndpoint('/api/admin/toggle-feature', 'Feature Toggle');
  await testAPIEndpoint('/api/woocommerce-cart', 'WooCommerce Cart');
  
  console.log('\n🎯 MANUAL TESTING STEPS:');
  console.log('=====================================');
  console.log('');
  console.log('1. 🌐 OPEN ADMIN DASHBOARD:');
  console.log('   → http://localhost:3000/admin/dashboard');
  console.log('');
  console.log('2. 🔐 LOGIN:');
  console.log('   → Username: admin');
  console.log('   → Password: admin');
  console.log('   → Enter OTP from alert popup');
  console.log('');
  console.log('3. 🎛️ TEST FEATURES:');
  console.log('   → Click "Features" in sidebar');
  console.log('   → Toggle features ON/OFF');
  console.log('   → Features activate instantly');
  console.log('');
  console.log('4. 🛍️ TEST ON MAIN WEBSITE:');
  console.log('   → Go to: http://localhost:3000');
  console.log('   → Look for enabled features:');
  console.log('     • 💬 Live chat widget (bottom-right)');
  console.log('     • 🎯 Product recommendations');
  console.log('     • 🎁 Loyalty points widget');
  console.log('     • ⚡ Reorder buttons');
  console.log('     • 📱 Social share buttons');
  console.log('     • 🎤 Voice search (microphone icon)');
  console.log('');
  console.log('5. ⌨️ QUICK ACCESS:');
  console.log('   → Press Ctrl+Shift+A anywhere for admin');
  console.log('   → Click ⚙️ button in bottom-left corner');
  console.log('');
  console.log('6. 🧪 TEST WOOCOMMERCE INTEGRATION:');
  console.log('   → Product recommendations show YOUR products');
  console.log('   → Loyalty program uses YOUR order data');
  console.log('   → Reorder shows YOUR customer orders');
  console.log('   → All data comes from YOUR WooCommerce store');
  console.log('');
  console.log('🎉 READY TO TEST!');
  console.log('Your comprehensive admin system is live and ready for testing.');
}

runTests().catch(console.error);
