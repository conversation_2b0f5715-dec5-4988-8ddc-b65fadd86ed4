// Products Management System
class ProductsManager {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.currentCategory = 'all';
        this.currentPage = 1;
        this.productsPerPage = CONFIG.PRODUCTS.PER_PAGE;
        this.isLoading = false;
        this.currentProduct = null;
        this.currentImageIndex = 0;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadProducts();
    }

    bindEvents() {
        // Category filter buttons
        const filterBtns = $$('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                this.filterByCategory(category);
            });
        });

        // Search functionality
        const searchInput = $('#search-input');
        if (searchInput) {
            const debouncedSearch = debounce((query) => {
                this.searchProducts(query);
            }, 300);

            searchInput.addEventListener('input', (e) => {
                debouncedSearch(e.target.value);
            });
        }

        // Load more button
        const loadMoreBtn = $('#load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProducts();
            });
        }

        // Modal events
        this.bindModalEvents();
    }

    bindModalEvents() {
        // Product modal close
        const modalClose = $('.modal-close');
        if (modalClose) {
            modalClose.addEventListener('click', () => this.closeProductModal());
        }

        // Modal background click
        const productModal = $('#product-modal');
        if (productModal) {
            productModal.addEventListener('click', (e) => {
                if (e.target === productModal) {
                    this.closeProductModal();
                }
            });
        }

        // Image navigation
        const sliderPrev = $('.slider-prev');
        const sliderNext = $('.slider-next');
        
        if (sliderPrev) sliderPrev.addEventListener('click', () => this.prevImage());
        if (sliderNext) sliderNext.addEventListener('click', () => this.nextImage());

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            const modal = $('#product-modal');
            if (modal && modal.classList.contains('active')) {
                switch (e.key) {
                    case 'Escape':
                        this.closeProductModal();
                        break;
                    case 'ArrowLeft':
                        this.prevImage();
                        break;
                    case 'ArrowRight':
                        this.nextImage();
                        break;
                }
            }
        });
    }

    // Load products from API
    async loadProducts(category = 'all') {
        if (this.isLoading) return;

        this.isLoading = true;
        showLoading('#featured-products', 'Loading amazing deals from your WooCommerce store...');

        try {
            // Try to load from WooCommerce API first
            try {
                this.products = await loadProducts(category === 'all' ? null : category);
            } catch (apiError) {
                console.warn('WooCommerce API not available, using demo products:', apiError);
                // Fallback to demo products
                this.products = this.getDemoProducts();
            }

            this.filteredProducts = [...this.products];
            this.renderProducts();
            this.updateLoadMoreButton();

            // Load best sellers after products are loaded
            this.loadBestSellers();
        } catch (error) {
            console.error('Error loading products:', error);
            // Use demo products as final fallback
            this.products = this.getDemoProducts();
            this.filteredProducts = [...this.products];
            this.renderProducts();
            this.updateLoadMoreButton();
            this.loadBestSellers();
        } finally {
            this.isLoading = false;
        }
    }

    // Demo products for when WooCommerce API is not available
    getDemoProducts() {
        return [
            {
                id: 1,
                name: "Premium Wireless Headphones",
                price: "89.99",
                regular_price: "129.99",
                short_description: "High-quality wireless headphones with noise cancellation",
                description: "Experience premium sound quality with these wireless headphones featuring active noise cancellation, 30-hour battery life, and premium comfort.",
                images: [
                    { src: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop" }
                ],
                featured: true,
                status: "publish",
                total_sales: 150,
                categories: [{ name: "Electronics" }]
            },
            {
                id: 2,
                name: "Smart Fitness Watch",
                price: "199.99",
                regular_price: "249.99",
                short_description: "Advanced fitness tracking with heart rate monitor",
                description: "Track your fitness goals with this advanced smartwatch featuring GPS, heart rate monitoring, sleep tracking, and 7-day battery life.",
                images: [
                    { src: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop" }
                ],
                featured: false,
                status: "publish",
                total_sales: 89,
                categories: [{ name: "Electronics" }]
            },
            {
                id: 3,
                name: "Elegant Women's Dress",
                price: "79.99",
                regular_price: "99.99",
                short_description: "Beautiful summer dress perfect for any occasion",
                description: "Elegant and comfortable dress made from premium materials. Perfect for both casual and formal occasions.",
                images: [
                    { src: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=400&fit=crop" }
                ],
                featured: true,
                status: "publish",
                total_sales: 120,
                categories: [{ name: "Fashion" }]
            },
            {
                id: 4,
                name: "Professional Camera Lens",
                price: "299.99",
                regular_price: "399.99",
                short_description: "High-quality 50mm lens for professional photography",
                description: "Professional grade camera lens with superior optics and build quality. Perfect for portrait and landscape photography.",
                images: [
                    { src: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop" }
                ],
                featured: false,
                status: "publish",
                total_sales: 45,
                categories: [{ name: "Electronics" }]
            },
            {
                id: 5,
                name: "Luxury Leather Handbag",
                price: "149.99",
                regular_price: "199.99",
                short_description: "Premium leather handbag with elegant design",
                description: "Handcrafted leather handbag with multiple compartments and premium hardware. Perfect for everyday use or special occasions.",
                images: [
                    { src: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop" }
                ],
                featured: true,
                status: "publish",
                total_sales: 78,
                categories: [{ name: "Accessories" }]
            },
            {
                id: 6,
                name: "Gaming Mechanical Keyboard",
                price: "129.99",
                regular_price: "159.99",
                short_description: "RGB mechanical keyboard for gaming enthusiasts",
                description: "High-performance mechanical keyboard with RGB backlighting, programmable keys, and premium switches for the ultimate gaming experience.",
                images: [
                    { src: "https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop" }
                ],
                featured: false,
                status: "publish",
                total_sales: 92,
                categories: [{ name: "Electronics" }]
            },
            {
                id: 7,
                name: "Stylish Sunglasses",
                price: "59.99",
                regular_price: "79.99",
                short_description: "UV protection sunglasses with modern design",
                description: "Protect your eyes in style with these premium sunglasses featuring 100% UV protection and durable frame construction.",
                images: [
                    { src: "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&fit=crop" }
                ],
                featured: false,
                status: "publish",
                total_sales: 156,
                categories: [{ name: "Accessories" }]
            },
            {
                id: 8,
                name: "Cozy Winter Sweater",
                price: "69.99",
                regular_price: "89.99",
                short_description: "Warm and comfortable sweater for cold weather",
                description: "Stay warm and stylish with this premium wool blend sweater. Perfect for layering and available in multiple colors.",
                images: [
                    { src: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=400&fit=crop" }
                ],
                featured: true,
                status: "publish",
                total_sales: 134,
                categories: [{ name: "Fashion" }]
            }
        ];
    }

    // Load best sellers (top 4 products by popularity/sales)
    loadBestSellers() {
        try {
            // Get top 4 products (can be based on sales, views, or featured status)
            const bestSellers = this.products
                .filter(product => product.status === 'publish' || product.status === 'private')
                .sort((a, b) => {
                    // Sort by featured status first, then by total sales or random
                    if (a.featured && !b.featured) return -1;
                    if (!a.featured && b.featured) return 1;
                    return (b.total_sales || 0) - (a.total_sales || 0);
                })
                .slice(0, 4);

            if (bestSellers.length > 0) {
                this.renderBestSellers(bestSellers);
                document.getElementById('best-sellers-section').style.display = 'block';
            }
        } catch (error) {
            console.error('Error loading best sellers:', error);
        }
    }

    // Render best sellers section
    renderBestSellers(bestSellers) {
        const container = document.getElementById('best-sellers-grid');
        if (!container) return;

        container.innerHTML = bestSellers.map((product, index) => {
            return `
                <div class="relative">
                    <div class="absolute top-2 left-2 z-10">
                        <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            #${index + 1} Best Seller
                        </span>
                    </div>
                    ${this.createSimpleProductCard(product)}
                </div>
            `;
        }).join('');

        // Add click events to best seller cards
        container.querySelectorAll('.simple-product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (e.target.closest('.add-to-cart-btn')) return;

                const productId = parseInt(card.dataset.id);
                this.openProductModal(productId);
            });
        });
    }

    // Create simple product card for best sellers (matches original SimpleProductCard)
    createSimpleProductCard(product) {
        const rating = (Math.random() * 1.5 + 3.5).toFixed(1);
        const reviewCount = Math.floor(Math.random() * 100) + 10;

        return `
            <div class="simple-product-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer" data-id="${product.id}">
                <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
                    <img
                        src="${getImageUrl(product)}"
                        alt="${product.name}"
                        class="h-48 w-full object-cover object-center group-hover:opacity-75"
                        loading="lazy"
                    />
                </div>
                <div class="p-4">
                    <h3 class="text-sm font-medium text-gray-900 line-clamp-2 mb-2">
                        ${truncate(product.name, 50)}
                    </h3>
                    <div class="flex items-center mb-2">
                        <div class="flex items-center">
                            ${'★'.repeat(Math.floor(rating))}${'☆'.repeat(5 - Math.floor(rating))}
                        </div>
                        <span class="ml-1 text-sm text-gray-600">
                            (${reviewCount})
                        </span>
                    </div>
                    <div>
                        <span class="text-lg font-bold text-gray-900">
                            ${formatPrice(product.price)}
                        </span>
                        ${product.regular_price && product.regular_price !== product.price ? `
                            <span class="ml-2 text-sm text-gray-500 line-through">
                                ${formatPrice(product.regular_price)}
                            </span>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // Filter products by category
    async filterByCategory(category) {
        if (this.currentCategory === category) return;

        this.currentCategory = category;
        this.currentPage = 1;

        // Update active filter button
        $$('.filter-btn').forEach(btn => {
            removeClass(btn, 'active');
            if (btn.dataset.category === category) {
                addClass(btn, 'active');
            }
        });

        await this.loadProducts(category);
    }

    // Search products
    async searchProducts(query) {
        if (!query.trim()) {
            this.filteredProducts = [...this.products];
            this.renderProducts();
            return;
        }

        try {
            const searchResults = await searchProductsGlobal(query);
            this.filteredProducts = searchResults;
            this.renderProducts();
        } catch (error) {
            console.error('Error searching products:', error);
            // Fallback to client-side search
            this.filteredProducts = this.products.filter(product =>
                product.name.toLowerCase().includes(query.toLowerCase()) ||
                (product.description && product.description.toLowerCase().includes(query.toLowerCase()))
            );
            this.renderProducts();
        }
    }

    // Render products grid
    renderProducts() {
        const loadingElement = $('#products-loading');
        const gridContainer = $('#featured-products-grid');
        const noProductsElement = $('#no-products');
        const viewAllContainer = $('#view-all-container');

        if (!gridContainer) return;

        // Hide loading
        if (loadingElement) loadingElement.classList.add('hidden');

        if (this.filteredProducts.length === 0) {
            if (gridContainer) gridContainer.classList.add('hidden');
            if (noProductsElement) noProductsElement.classList.remove('hidden');
            if (viewAllContainer) viewAllContainer.style.display = 'none';
            return;
        }

        // Show products grid
        if (gridContainer) gridContainer.classList.remove('hidden');
        if (noProductsElement) noProductsElement.classList.add('hidden');
        if (viewAllContainer) viewAllContainer.style.display = 'block';

        const productsToShow = this.filteredProducts.slice(0, this.currentPage * this.productsPerPage);

        gridContainer.innerHTML = productsToShow.map(product => this.createModernProductCard(product)).join('');

        // Add click events to product cards
        $$('.product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't open modal if clicking on add to cart button
                if (e.target.closest('.add-to-cart-btn')) return;

                const productId = parseInt(card.dataset.id);
                this.openProductModal(productId);
            });
        });
    }

    // Create modern product card HTML
    createModernProductCard(product) {
        const discount = calculateDiscount(product.regular_price, product.price);
        const category = getCategoryByKeywords(product.name, product.short_description);
        const categoryInfo = CONFIG.CATEGORIES[category];
        const rating = (Math.random() * 1.5 + 3.5).toFixed(1); // Random rating between 3.5-5.0
        const reviewCount = Math.floor(Math.random() * 500) + 50; // Random review count

        return `
            <div class="product-card bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer transform hover:-translate-y-2" data-id="${product.id}">
                <div class="relative overflow-hidden">
                    ${discount > 0 ? `
                        <div class="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold z-10">
                            ${discount}% OFF
                        </div>
                    ` : ''}

                    ${product.featured ? `
                        <div class="absolute top-3 right-3 bg-yellow-400 text-purple-900 px-2 py-1 rounded-full text-xs font-bold z-10">
                            Featured
                        </div>
                    ` : ''}

                    <div class="aspect-square overflow-hidden bg-gray-100">
                        <img
                            src="${getImageUrl(product)}"
                            alt="${product.name}"
                            loading="lazy"
                            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                    </div>

                    <!-- Quick action buttons -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div class="flex space-x-2">
                            <button
                                onclick="addToWishlist(${product.id}); event.stopPropagation();"
                                class="bg-white text-gray-700 p-2 rounded-full hover:bg-red-500 hover:text-white transition-colors"
                                title="Add to Wishlist"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </button>
                            <button
                                onclick="quickView(${product.id}); event.stopPropagation();"
                                class="bg-white text-gray-700 p-2 rounded-full hover:bg-blue-500 hover:text-white transition-colors"
                                title="Quick View"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <div class="mb-2">
                        <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            ${categoryInfo.name}
                        </span>
                    </div>

                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        ${truncate(product.name, 60)}
                    </h3>

                    <div class="flex items-center mb-2">
                        <div class="flex text-yellow-400 text-sm">
                            ${'★'.repeat(Math.floor(rating))}${'☆'.repeat(5 - Math.floor(rating))}
                        </div>
                        <span class="text-xs text-gray-500 ml-1">(${reviewCount})</span>
                    </div>

                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-blue-600">${formatPrice(product.price)}</span>
                            ${product.regular_price && product.regular_price !== product.price ?
                                `<span class="text-sm text-gray-500 line-through">${formatPrice(product.regular_price)}</span>` : ''}
                        </div>
                        ${discount > 0 ? `
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                                Save ${discount}%
                            </span>
                        ` : ''}
                    </div>

                    <button
                        onclick="addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')}, 1); event.stopPropagation();"
                        class="add-to-cart-btn w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        <span>Add to Cart</span>
                    </button>
                </div>
            </div>
        `;
    }

    // Legacy method for backward compatibility
    createProductCard(product) {
        return this.createModernProductCard(product);
    }

    // Load more products
    loadMoreProducts() {
        this.currentPage++;
        this.renderProducts();
        this.updateLoadMoreButton();
    }

    // Update load more button visibility
    updateLoadMoreButton() {
        const loadMoreBtn = $('#load-more-btn');
        if (!loadMoreBtn) return;

        const totalShown = this.currentPage * this.productsPerPage;
        const hasMore = totalShown < this.filteredProducts.length;

        if (hasMore) {
            loadMoreBtn.style.display = 'block';
            loadMoreBtn.textContent = `Load More (${this.filteredProducts.length - totalShown} remaining)`;
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }

    // Open product modal
    async openProductModal(productId) {
        try {
            const product = this.products.find(p => p.id === productId) || 
                           await getProductDetails(productId);
            
            if (!product) {
                showToast('Product not found', 'error');
                return;
            }

            this.currentProduct = product;
            this.currentImageIndex = 0;
            this.renderProductModal(product);
            
            const modal = $('#product-modal');
            if (modal) {
                addClass(modal, 'active');
                document.body.style.overflow = 'hidden';
            }
        } catch (error) {
            console.error('Error opening product modal:', error);
            showToast('Error loading product details', 'error');
        }
    }

    // Close product modal
    closeProductModal() {
        const modal = $('#product-modal');
        if (modal) {
            removeClass(modal, 'active');
            document.body.style.overflow = '';
        }
        this.currentProduct = null;
    }

    // Render product modal content
    renderProductModal(product) {
        const modalTitle = $('#modal-product-title');
        const modalPrice = $('#modal-product-price');
        const modalDescription = $('#modal-product-description');
        const modalImage = $('#modal-main-image');
        const modalRating = $('#modal-product-rating');

        if (modalTitle) modalTitle.textContent = product.name;
        if (modalPrice) modalPrice.textContent = formatPrice(product.price);
        if (modalDescription) {
            modalDescription.innerHTML = product.short_description || 
                                       stripHtml(product.description) || 
                                       'No description available.';
        }

        // Set main image
        if (modalImage && product.images && product.images.length > 0) {
            modalImage.src = product.images[this.currentImageIndex].src;
            modalImage.alt = product.name;
        }

        // Update rating
        if (modalRating) {
            modalRating.innerHTML = `
                <div class="stars">${'★'.repeat(5)}</div>
                <span class="rating-text">(${Math.floor(Math.random() * 100) + 50} reviews)</span>
            `;
        }

        // Update image dots
        this.updateImageDots(product);
        this.updateImageCounter(product);
    }

    // Update image dots
    updateImageDots(product) {
        const dotsContainer = $('#image-dots');
        if (!dotsContainer || !product.images || product.images.length <= 1) {
            if (dotsContainer) dotsContainer.innerHTML = '';
            return;
        }

        dotsContainer.innerHTML = product.images.map((_, index) => 
            `<div class="image-dot ${index === this.currentImageIndex ? 'active' : ''}" 
                  onclick="productsManager.setImageIndex(${index})"></div>`
        ).join('');
    }

    // Update image counter
    updateImageCounter(product) {
        const currentImage = $('#current-image');
        const totalImages = $('#total-images');
        
        if (currentImage) currentImage.textContent = this.currentImageIndex + 1;
        if (totalImages) totalImages.textContent = product.images ? product.images.length : 1;
    }

    // Navigate to previous image
    prevImage() {
        if (!this.currentProduct || !this.currentProduct.images) return;
        
        this.currentImageIndex = this.currentImageIndex > 0 ? 
            this.currentImageIndex - 1 : 
            this.currentProduct.images.length - 1;
        
        this.updateModalImage();
    }

    // Navigate to next image
    nextImage() {
        if (!this.currentProduct || !this.currentProduct.images) return;
        
        this.currentImageIndex = this.currentImageIndex < this.currentProduct.images.length - 1 ? 
            this.currentImageIndex + 1 : 
            0;
        
        this.updateModalImage();
    }

    // Set specific image index
    setImageIndex(index) {
        if (!this.currentProduct || !this.currentProduct.images) return;
        
        this.currentImageIndex = index;
        this.updateModalImage();
    }

    // Update modal image
    updateModalImage() {
        const modalImage = $('#modal-main-image');
        if (modalImage && this.currentProduct && this.currentProduct.images) {
            modalImage.src = this.currentProduct.images[this.currentImageIndex].src;
            this.updateImageDots(this.currentProduct);
            this.updateImageCounter(this.currentProduct);
        }
    }

    // Show error message
    showError(message) {
        const container = $('#featured-products');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ef4444; margin-bottom: 1rem;"></i>
                    <h3>Oops! Something went wrong</h3>
                    <p>${message}</p>
                    <button class="btn-primary" onclick="productsManager.loadProducts()">Try Again</button>
                </div>
            `;
        }
    }
}

// Global products manager instance
const productsManager = new ProductsManager();

// Global functions for product operations
function addToCartFromModal() {
    if (productsManager.currentProduct) {
        addToCart(productsManager.currentProduct, 1);
    }
}

function addToWishlist() {
    if (productsManager.currentProduct) {
        // Implement wishlist functionality
        showToast('Added to wishlist!', 'success');
    }
}

function searchProducts() {
    const searchInput = $('#search-input');
    if (searchInput) {
        productsManager.searchProducts(searchInput.value);
    }
}

function prevImage() {
    productsManager.prevImage();
}

function nextImage() {
    productsManager.nextImage();
}

function closeProductModal() {
    productsManager.closeProductModal();
}

// Global helper functions for new features
function addToWishlist(productId) {
    // Get product data
    const product = productsManager.products.find(p => p.id === productId);
    if (!product) return;

    // Get current wishlist
    let wishlist = getStorage('deal4u_wishlist', []);

    // Check if already in wishlist
    if (wishlist.find(item => item.id === productId)) {
        showToast('Product already in wishlist!', 'info');
        return;
    }

    // Add to wishlist
    wishlist.push({
        id: product.id,
        name: product.name,
        price: product.price,
        image: getImageUrl(product),
        addedAt: new Date().toISOString()
    });

    setStorage('deal4u_wishlist', wishlist);
    showToast('Added to wishlist!', 'success');
}

function quickView(productId) {
    productsManager.openProductModal(productId);
}

function removeFromWishlist(productId) {
    let wishlist = getStorage('deal4u_wishlist', []);
    wishlist = wishlist.filter(item => item.id !== productId);
    setStorage('deal4u_wishlist', wishlist);
    showToast('Removed from wishlist', 'info');
}

function getWishlistCount() {
    const wishlist = getStorage('deal4u_wishlist', []);
    return wishlist.length;
}

function isInWishlist(productId) {
    const wishlist = getStorage('deal4u_wishlist', []);
    return wishlist.some(item => item.id === productId);
}

// Utility functions for products
function getImageUrl(product) {
    if (product.images && product.images.length > 0) {
        return product.images[0].src;
    }
    return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&q=80';
}

function formatPrice(price) {
    const numPrice = parseFloat(price) || 0;
    return `£${numPrice.toFixed(2)}`;
}

function calculateDiscount(regularPrice, salePrice) {
    if (!regularPrice || !salePrice || regularPrice <= salePrice) return 0;
    const regular = parseFloat(regularPrice);
    const sale = parseFloat(salePrice);
    return Math.round(((regular - sale) / regular) * 100);
}

function truncate(text, length) {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
}

function stripHtml(html) {
    if (!html) return '';
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

function showLoading(selector, message = 'Loading...') {
    const element = document.querySelector(selector);
    if (element) {
        element.innerHTML = `
            <div class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-4 text-gray-600">${message}</p>
            </div>
        `;
    }
}

// Global functions for cart (placeholders)
function addToCart(product, quantity = 1) {
    console.log('Adding to cart:', product.name, 'Quantity:', quantity);
    showToast(`${product.name} added to cart!`, 'success');
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

    switch (type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ProductsManager,
        productsManager,
        addToCartFromModal,
        addToWishlist,
        quickView,
        removeFromWishlist,
        getWishlistCount,
        isInWishlist,
        searchProducts,
        prevImage,
        nextImage,
        closeProductModal,
        getImageUrl,
        formatPrice,
        calculateDiscount,
        truncate,
        stripHtml,
        showLoading,
        addToCart,
        showToast
    };
}
