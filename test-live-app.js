const axios = require('axios');

async function testLiveApp() {
  console.log('🔍 Testing Live Application...\n');
  
  try {
    console.log('1. Testing homepage...');
    const homeResponse = await axios.get('http://localhost:3000', { 
      timeout: 15000 
    });
    console.log(`✅ Homepage: ${homeResponse.status} OK`);
    
    console.log('\n2. Testing shop page...');
    const shopResponse = await axios.get('http://localhost:3000/shop', { 
      timeout: 15000 
    });
    console.log(`✅ Shop page: ${shopResponse.status} OK`);
    console.log(`   Content size: ${shopResponse.data.length} characters`);
    
    // Check what's actually in the page
    const content = shopResponse.data;
    
    // Look for signs of successful product loading
    const hasProducts = content.includes('product') && !content.includes('No products');
    const hasGrid = content.includes('grid-cols') || content.includes('ProductGrid');
    const hasError = content.includes('error') || content.includes('Error');
    const hasLoading = content.includes('Loading') || content.includes('animate-spin');
    const hasReactData = content.includes('__NEXT_DATA__');
    
    console.log('\n📊 Page Analysis:');
    console.log(`   Has products: ${hasProducts ? 'Yes' : 'No'}`);
    console.log(`   Has grid layout: ${hasGrid ? 'Yes' : 'No'}`);
    console.log(`   Has errors: ${hasError ? 'Yes' : 'No'}`);
    console.log(`   Has loading state: ${hasLoading ? 'Yes' : 'No'}`);
    console.log(`   Has React data: ${hasReactData ? 'Yes' : 'No'}`);
    
    // Look for specific product data
    if (content.includes('__NEXT_DATA__')) {
      console.log('\n🔍 Checking React data...');
      try {
        const nextDataMatch = content.match(/<script id="__NEXT_DATA__" type="application\/json">(.*?)<\/script>/);
        if (nextDataMatch) {
          const nextData = JSON.parse(nextDataMatch[1]);
          const pageProps = nextData?.props?.pageProps;
          
          if (pageProps) {
            console.log(`   Page props found: ${Object.keys(pageProps).join(', ')}`);
            
            if (pageProps.products) {
              console.log(`   Products in data: ${pageProps.products.length}`);
            }
            
            if (pageProps.error) {
              console.log(`   Error in data: ${pageProps.error}`);
            }
          }
        }
      } catch (parseError) {
        console.log('   Could not parse React data');
      }
    }
    
    // Test a specific product page
    console.log('\n3. Testing product page...');
    try {
      const productResponse = await axios.get('http://localhost:3000/shop/product/13619', { 
        timeout: 15000 
      });
      console.log(`✅ Product page: ${productResponse.status} OK`);
      
      const productContent = productResponse.data;
      const hasProductData = productContent.includes('Ladies') || productContent.includes('T-Shirt');
      const hasModernDesign = productContent.includes('gradient') && productContent.includes('rounded');
      
      console.log(`   Has product data: ${hasProductData ? 'Yes' : 'No'}`);
      console.log(`   Has modern design: ${hasModernDesign ? 'Yes' : 'No'}`);
      
    } catch (productError) {
      console.log(`❌ Product page failed: ${productError.response?.status || 'No response'}`);
    }
    
    console.log('\n🎯 SUMMARY:');
    if (hasReactData && hasProducts && hasGrid) {
      console.log('🎉 SUCCESS: Application is working with modern grid layout!');
    } else if (hasReactData && !hasProducts) {
      console.log('⚠️  App loaded but no products - API issue during server render');
    } else if (!hasReactData) {
      console.log('⚠️  Static HTML only - React not hydrating');
    } else {
      console.log('⚠️  Mixed results - partial functionality');
    }
    
  } catch (error) {
    console.log('❌ Error testing live app:');
    console.log(`   ${error.message}`);
  }
}

testLiveApp();
