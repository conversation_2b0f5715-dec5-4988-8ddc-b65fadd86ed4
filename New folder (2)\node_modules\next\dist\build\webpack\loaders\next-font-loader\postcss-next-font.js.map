{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/postcss-next-font.ts"], "names": ["postcssNextFontPlugin", "exports", "fontFamilyHash", "fallbackFonts", "adjustFontFallback", "variable", "weight", "style", "postcssPlugin", "Once", "root", "fontFamily", "normalizeFamily", "family", "replace", "formatFamily", "node", "nodes", "type", "name", "familyNode", "find", "decl", "prop", "value", "Error", "adjustFontFallbackFamily", "fallbackFontFace", "postcss", "atRule", "fallbackFont", "ascentOverride", "descentOverride", "lineGapOverride", "sizeAdjust", "Declaration", "push", "isRange", "trim", "includes", "formattedFontFamilies", "join", "classRule", "Rule", "selector", "varialbeRule", "fontWeight", "Number", "isNaN", "undefined", "fontStyle"], "mappings": ";;;;+BAoMA;;;eAAA;;;gEAlMoB;;;;;;AAEpB;;;;;;;;;;;;;;;CAeC,GACD,MAAMA,wBAAwB,CAAC,EAC7BC,SAAAA,QAAO,EACPC,cAAc,EACdC,gBAAgB,EAAE,EAClBC,kBAAkB,EAClBC,QAAQ,EACRC,MAAM,EACNC,KAAK,EASN;IACC,OAAO;QACLC,eAAe;QACfC,MAAKC,IAAS;YACZ,IAAIC;YAEJ,MAAMC,kBAAkB,CAACC;gBACvB,OAAOA,OAAOC,OAAO,CAAC,SAAS;YACjC;YAEA,MAAMC,eAAe,CAACF;gBACpB,6DAA6D;gBAC7D,OAAO,CAAC,GAAG,EAAEA,OAAOC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAEZ,eAAe,CAAC,CAAC;YAC7D;YAEA,yBAAyB;YACzB,KAAK,MAAMc,QAAQN,KAAKO,KAAK,CAAE;gBAC7B,IAAID,KAAKE,IAAI,KAAK,YAAYF,KAAKG,IAAI,KAAK,aAAa;oBACvD,MAAMC,aAAaJ,KAAKC,KAAK,CAACI,IAAI,CAChC,CAACC,OAAsBA,KAAKC,IAAI,KAAK;oBAEvC,IAAI,CAACH,YAAY;wBACf;oBACF;oBAEA,IAAI,CAACT,YAAY;wBACfA,aAAaC,gBAAgBQ,WAAWI,KAAK;oBAC/C;oBAEAJ,WAAWI,KAAK,GAAGT,aAAaJ;gBAClC;YACF;YAEA,IAAI,CAACA,YAAY;gBACf,MAAM,IAAIc,MAAM;YAClB;YAEA,4DAA4D;YAC5D,IAAIC;YACJ,IAAItB,oBAAoB;gBACtBsB,2BAA2BX,aAAa,CAAC,EAAEJ,WAAW,SAAS,CAAC;gBAChE,MAAMgB,mBAAmBC,gBAAO,CAACC,MAAM,CAAC;oBAAEV,MAAM;gBAAY;gBAC5D,MAAM,EACJW,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,UAAU,EACX,GAAG9B;gBACJuB,iBAAiBV,KAAK,GAAG;oBACvB,IAAIW,gBAAO,CAACO,WAAW,CAAC;wBACtBZ,MAAM;wBACNC,OAAOE;oBACT;oBACA,IAAIE,gBAAO,CAACO,WAAW,CAAC;wBACtBZ,MAAM;wBACNC,OAAO,CAAC,OAAO,EAAEM,aAAa,EAAE,CAAC;oBACnC;uBACIC,iBACA;wBACE,IAAIH,gBAAO,CAACO,WAAW,CAAC;4BACtBZ,MAAM;4BACNC,OAAOO;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAIJ,gBAAO,CAACO,WAAW,CAAC;4BACtBZ,MAAM;4BACNC,OAAOQ;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAIL,gBAAO,CAACO,WAAW,CAAC;4BACtBZ,MAAM;4BACNC,OAAOS;wBACT;qBACD,GACD,EAAE;uBACFC,aACA;wBACE,IAAIN,gBAAO,CAACO,WAAW,CAAC;4BACtBZ,MAAM;4BACNC,OAAOU;wBACT;qBACD,GACD,EAAE;iBACP;gBACDxB,KAAKO,KAAK,CAACmB,IAAI,CAACT;YAClB;YAEA,6CAA6C;YAC7C,MAAMU,UAAU,CAACb,QAAkBA,MAAMc,IAAI,GAAGC,QAAQ,CAAC;YAEzD,iDAAiD;YACjD,MAAMC,wBAAwB;gBAC5BzB,aAAaJ;mBACTe,2BAA2B;oBAACA;iBAAyB,GAAG,EAAE;mBAC3DvB;aACJ,CAACsC,IAAI,CAAC;YAEP,0CAA0C;YAC1C,MAAMC,YAAY,IAAId,gBAAO,CAACe,IAAI,CAAC;gBAAEC,UAAU;YAAa;YAC5DF,UAAUzB,KAAK,GAAG;gBAChB,IAAIW,gBAAO,CAACO,WAAW,CAAC;oBACtBZ,MAAM;oBACNC,OAAOgB;gBACT;gBACA,uEAAuE;mBACnElC,UAAU,CAAC+B,QAAQ/B,UACnB;oBACE,IAAIsB,gBAAO,CAACO,WAAW,CAAC;wBACtBZ,MAAM;wBACNC,OAAOlB;oBACT;iBACD,GACD,EAAE;mBACFC,SAAS,CAAC8B,QAAQ9B,SAClB;oBACE,IAAIqB,gBAAO,CAACO,WAAW,CAAC;wBACtBZ,MAAM;wBACNC,OAAOjB;oBACT;iBACD,GACD,EAAE;aACP;YACDG,KAAKO,KAAK,CAACmB,IAAI,CAACM;YAEhB,+DAA+D;YAC/D,IAAIrC,UAAU;gBACZ,MAAMwC,eAAe,IAAIjB,gBAAO,CAACe,IAAI,CAAC;oBAAEC,UAAU;gBAAY;gBAC9DC,aAAa5B,KAAK,GAAG;oBACnB,IAAIW,gBAAO,CAACO,WAAW,CAAC;wBACtBZ,MAAMlB;wBACNmB,OAAOgB;oBACT;iBACD;gBACD9B,KAAKO,KAAK,CAACmB,IAAI,CAACS;YAClB;YAEA,iCAAiC;YACjC5C,SAAQmC,IAAI,CAAC;gBACXjB,MAAM;gBACNK,OAAO;oBACLb,YAAY6B;oBACZM,YAAY,CAACC,OAAOC,KAAK,CAACD,OAAOzC,WAC7ByC,OAAOzC,UACP2C;oBACJC,WAAW3C,SAAS,CAAC8B,QAAQ9B,SAASA,QAAQ0C;gBAChD;YACF;QACF;IACF;AACF;AAEAjD,sBAAsB4B,OAAO,GAAG;MAEhC,WAAe5B"}