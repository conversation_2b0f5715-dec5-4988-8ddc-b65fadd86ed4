'use client';

import { useState } from 'react';
import EnhancedProductCard from '@/components/product/EnhancedProductCard';
import ProductDetail from '@/components/product/ProductDetail';
import { ArrowLeft, Grid, List } from 'lucide-react';

// Demo products with enhanced images
const demoProducts = [
  {
    id: 1,
    name: "Premium Wireless Headphones with Active Noise Cancellation",
    price: 199.99,
    regular_price: 249.99,
    images: [
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=600&h=600&fit=crop"
    ],
    rating: 4.8,
    reviews: 124,
    inStock: true,
    stock_quantity: 15,
    featured: true,
    description: `
      <h3>Experience Premium Audio Quality</h3>
      <p>These premium wireless headphones deliver exceptional sound quality with active noise cancellation technology. Perfect for music lovers, professionals, and travelers who demand the best audio experience.</p>
      
      <h4>Key Features:</h4>
      <ul>
        <li>Active Noise Cancellation (ANC) technology</li>
        <li>30-hour battery life with quick charge</li>
        <li>Premium comfort padding for extended wear</li>
        <li>High-resolution audio support</li>
        <li>Touch controls and voice assistant integration</li>
      </ul>
      
      <h4>What's in the Box:</h4>
      <ul>
        <li>Premium Wireless Headphones</li>
        <li>USB-C Charging Cable</li>
        <li>3.5mm Audio Cable</li>
        <li>Carrying Case</li>
        <li>User Manual</li>
      </ul>
    `,
    attributes: [
      { name: "Color", options: ["Black", "White", "Silver"] },
      { name: "Connectivity", options: ["Bluetooth 5.0", "USB-C", "3.5mm Jack"] },
      { name: "Battery Life", value: "30 hours" },
      { name: "Weight", value: "250g" }
    ]
  },
  {
    id: 2,
    name: "Smart Fitness Watch with Heart Rate Monitor",
    price: 299.99,
    regular_price: 299.99,
    images: [
      "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-**********-31a4b719223d?w=600&h=600&fit=crop"
    ],
    rating: 4.6,
    reviews: 89,
    inStock: true,
    stock_quantity: 8,
    description: `
      <h3>Advanced Fitness Tracking</h3>
      <p>Monitor your health and fitness goals with this advanced smartwatch featuring comprehensive health tracking, GPS, and smart notifications.</p>
      
      <h4>Health Features:</h4>
      <ul>
        <li>24/7 Heart Rate Monitoring</li>
        <li>Sleep Quality Analysis</li>
        <li>Blood Oxygen Level Tracking</li>
        <li>Stress Level Monitoring</li>
        <li>50+ Workout Modes</li>
      </ul>
    `,
    attributes: [
      { name: "Display", value: "1.4\" AMOLED" },
      { name: "Battery", value: "7 days" },
      { name: "Water Resistance", value: "5ATM" },
      { name: "GPS", value: "Built-in" }
    ]
  },
  {
    id: 3,
    name: "Portable Bluetooth Speaker with 360° Sound",
    price: 79.99,
    regular_price: 99.99,
    images: [
      "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-**********-3531b543be5d?w=600&h=600&fit=crop"
    ],
    rating: 4.7,
    reviews: 156,
    inStock: true,
    stock_quantity: 25,
    description: `
      <h3>Immersive 360° Audio Experience</h3>
      <p>Enjoy rich, room-filling sound with this portable Bluetooth speaker featuring 360-degree audio technology and waterproof design.</p>
      
      <h4>Audio Features:</h4>
      <ul>
        <li>360-degree surround sound</li>
        <li>Deep bass enhancement</li>
        <li>Crystal clear highs</li>
        <li>12-hour battery life</li>
        <li>IPX7 waterproof rating</li>
      </ul>
    `,
    attributes: [
      { name: "Power Output", value: "20W" },
      { name: "Bluetooth", value: "5.0" },
      { name: "Battery", value: "12 hours" },
      { name: "Waterproof", value: "IPX7" }
    ]
  }
];

export default function DemoProductsPage() {
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [viewMode, setViewMode] = useState('grid');

  if (selectedProduct) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <button
            onClick={() => setSelectedProduct(null)}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-700 mb-6 font-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Products
          </button>
          <ProductDetail product={selectedProduct} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Enhanced Product Display Demo
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our AliExpress-inspired product display with enhanced images, 
            interactive galleries, and eye-catching designs that boost conversions.
          </p>
        </div>

        {/* View Toggle */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-900">Featured Products</h2>
          <div className="flex items-center gap-2 bg-white rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-blue-500 text-white' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' 
                  ? 'bg-blue-500 text-white' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {demoProducts.map((product) => (
            <div key={product.id} onClick={() => setSelectedProduct(product)}>
              <EnhancedProductCard 
                product={product} 
                viewMode={viewMode}
              />
            </div>
          ))}
        </div>

        {/* Features Section */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-sm">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Enhanced Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🖼️</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Image Gallery</h4>
              <p className="text-gray-600 text-sm">Interactive image galleries with thumbnails and smooth transitions</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Smart Pricing</h4>
              <p className="text-gray-600 text-sm">Dynamic pricing display with discount badges and savings calculation</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⭐</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Social Proof</h4>
              <p className="text-gray-600 text-sm">Star ratings, review counts, and sales numbers to build trust</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Modern Design</h4>
              <p className="text-gray-600 text-sm">Clean, modern interface with smooth animations and hover effects</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
