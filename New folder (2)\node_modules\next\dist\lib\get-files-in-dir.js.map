{"version": 3, "sources": ["../../src/lib/get-files-in-dir.ts"], "names": ["getFilesInDir", "path", "dir", "fs", "opendir", "results", "file", "resolvedFile", "isSymbolicLink", "stat", "join", "name", "isFile", "push"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;sBAJD;iEACN;;;;;;AAGR,eAAeA,cAAcC,IAAY;IAC9C,MAAMC,MAAM,MAAMC,iBAAE,CAACC,OAAO,CAACH;IAC7B,MAAMI,UAAU,EAAE;IAElB,WAAW,MAAMC,QAAQJ,IAAK;QAC5B,IAAIK,eAA2CD;QAE/C,IAAIA,KAAKE,cAAc,IAAI;YACzBD,eAAe,MAAMJ,iBAAE,CAACM,IAAI,CAACC,IAAAA,UAAI,EAACT,MAAMK,KAAKK,IAAI;QACnD;QAEA,IAAIJ,aAAaK,MAAM,IAAI;YACzBP,QAAQQ,IAAI,CAACP,KAAKK,IAAI;QACxB;IACF;IAEA,OAAON;AACT"}