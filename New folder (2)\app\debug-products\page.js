'use client';

import { useState, useEffect } from 'react';

export default function DebugProductsPage() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      console.log('🔍 Fetching products for debug...');
      
      const response = await fetch('/api/direct-products?status=publish&per_page=5');
      const data = await response.json();
      
      console.log('📡 Raw API response:', data);
      
      if (data.success) {
        setProducts(data.products);
        console.log('✅ Products loaded:', data.products);
      } else {
        setError(data.error);
      }
    } catch (err) {
      console.error('❌ Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>;

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Product Debug Page</h1>
      
      <div className="mb-6 p-4 bg-blue-50 rounded">
        <h2 className="font-semibold mb-2">Summary:</h2>
        <p>Total Products: {products.length}</p>
        <button 
          onClick={fetchProducts}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh Data
        </button>
      </div>

      <div className="grid gap-6">
        {products.map((product, index) => (
          <div key={product.id} className="border rounded-lg p-4 bg-white shadow">
            <h3 className="font-semibold text-lg mb-2">Product #{index + 1}</h3>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Basic Info:</h4>
                <div className="text-sm space-y-1">
                  <p><strong>ID:</strong> {product.id}</p>
                  <p><strong>Name:</strong> {product.name}</p>
                  <p><strong>Status:</strong> {product.status}</p>
                  <p><strong>Price:</strong> {product.price}</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Stock Info:</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Stock Status:</strong> {product.stock_status}</p>
                  <p><strong>Stock Quantity:</strong> {product.stock_quantity}</p>
                  <p><strong>In Stock:</strong> {product.inStock ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <h4 className="font-medium mb-2">Images ({product.images?.length || 0}):</h4>
              {product.images && product.images.length > 0 ? (
                <div className="flex gap-2 flex-wrap">
                  {product.images.slice(0, 3).map((img, i) => (
                    <div key={i} className="relative">
                      <img 
                        src={img} 
                        alt={`Product ${product.id} image ${i + 1}`}
                        className="w-20 h-20 object-cover rounded border"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'block';
                        }}
                      />
                      <div 
                        className="w-20 h-20 bg-gray-200 rounded border flex items-center justify-center text-xs text-gray-500 hidden"
                      >
                        Failed
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No images</p>
              )}
            </div>
            
            <div className="mt-4">
              <h4 className="font-medium mb-2">Raw Data:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(product, null, 2)}
              </pre>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
