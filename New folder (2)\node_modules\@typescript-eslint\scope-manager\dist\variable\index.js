"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Variable = exports.ImplicitLibVariable = exports.ESLintScopeVariable = void 0;
var ESLintScopeVariable_1 = require("./ESLintScopeVariable");
Object.defineProperty(exports, "ESLintScopeVariable", { enumerable: true, get: function () { return ESLintScopeVariable_1.ESLintScopeVariable; } });
var ImplicitLibVariable_1 = require("./ImplicitLibVariable");
Object.defineProperty(exports, "ImplicitLibVariable", { enumerable: true, get: function () { return ImplicitLibVariable_1.ImplicitLibVariable; } });
var Variable_1 = require("./Variable");
Object.defineProperty(exports, "Variable", { enumerable: true, get: function () { return Variable_1.Variable; } });
//# sourceMappingURL=index.js.map