{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["defaultOverrides", "BARREL_OPTIMIZATION_PREFIX", "path", "NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "isWebpackAppLayer", "isWebpackServerOnlyLayer", "normalizePathSep", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "isResourceInPackages", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "sep", "includes", "join", "replace", "resolveExternal", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "_optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "nodeResolveOptions", "baseEsmResolveOptions", "baseResolveOptions", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolveOptions", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "makeExternalHandler", "config", "optOutBundlingPackages", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "test", "notExternalModules", "resolveNextExternal", "resolveResult", "undefined", "isOptOutBundling", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBundlePages", "bundlePagesExternals", "shouldBeBundled", "isExternal"], "mappings": "AAGA,SAASA,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,0BAA0B,QAAQ,0BAAyB;AACpE,OAAOC,UAAU,gCAA+B;AAChD,SACEC,6BAA6B,EAC7BC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QACf,mBAAkB;AACzB,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,UAAS;AACrE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAEzB,OAAO,SAASC,qBACdC,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMrB,KAAKyB,GAAG,IACxDR,SAASS,QAAQ,CACf1B,KAAKyB,GAAG,GACNzB,KAAK2B,IAAI,CAAC,gBAAgBN,EAAEO,OAAO,CAAC,OAAO5B,KAAKyB,GAAG,KACnDzB,KAAKyB,GAAG;AAGpB;AAEA,OAAO,eAAeI,gBACpBC,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,uBAAiC,EACjCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBpC,wBAAwB,EACjDqC,qBAA0BpC,oBAAoB,EAC9CqC,wBAA6BxC,6BAA6B,EAC1DyC,qBAA0BxC,yBAAyB;IAEnD,MAAMyC,eAAe,CAAC,CAACZ;IACvB,MAAMa,oBAAoBb,uBAAuB;IAEjD,IAAIc,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBAAgBT,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMc,aAAaD,iBAAkB;QACxC,MAAME,iBAAiBD,YAAYT,oBAAoBC;QAEvD,MAAMU,UAAUd,WAAWa;QAE3B,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACJ,KAAKC,MAAM,GAAG,MAAMI,QAAQlB,SAASC;QACzC,EAAE,OAAOkB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACX,kBAAkBY,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIP,iBAAiB;YACnB,OAAO;gBAAEe,UAAUf,gBAAgBQ;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIP,kBAAkB;YACpB,IAAIe;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcnB,WAClBU,QAAQL,wBAAwBC;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAYzB,KAAKG;YACjD,EAAE,OAAOkB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEA,OAAO,SAASU,oBAAoB,EAClCC,MAAM,EACNC,sBAAsB,EACtBC,0BAA0B,EAC1B7B,GAAG,EAMJ;QAE2B2B;IAD1B,IAAIG;IACJ,MAAMhB,oBAAoBa,EAAAA,uBAAAA,OAAOI,YAAY,qBAAnBJ,qBAAqBd,YAAY,MAAK;IAEhE,OAAO,eAAemB,gBACpB9B,OAAe,EACfC,OAAe,EACf8B,cAAsB,EACtBC,KAA8B,EAC9B5B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM6B,UACJhC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBvB,KAAKkE,KAAK,CAACC,UAAU,CAAClC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBmC,QAAQC,QAAQ,KAAK,WAAWrE,KAAKsE,KAAK,CAACH,UAAU,CAAClC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMsC,aAAalE,kBAAkB2D;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaO,IAAI,CAACvC,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIzB,mBAAmBgE,IAAI,CAACvC,YAAY,CAACsC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEtC,QAAQ,CAAC;YAC9B;YAEA,MAAMwC,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACvC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQP,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIO,QAAQV,UAAU,CAACxB,6BAA6B;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMmC,iBAAiB6B,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACEzD,yBAAyB0D,UACzB/B,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDiD,IAAI,CAACvC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CuC,IAAI,CAACvC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DuC,IAAI,CAChEvC,YAEF,4CAA4CuC,IAAI,CAACvC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEuC,IAAI,CACxEvC,YAEF,2CAA2CuC,IAAI,CAACvC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAOyC,oBAAoBzC;QAC7B;QAEA,6FAA6F;QAC7F,MAAM0C,gBAAgB,MAAM9C,gBAC1BC,KACA2B,OAAOI,YAAY,CAAClB,YAAY,EAChCX,SACAC,SACAC,gBACAwB,wBACAtB,YACA6B,UAAUS,sBAAsBE;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAcvB,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAInB,YAAY,oBAAoB;YAClC0C,cAAc9B,GAAG,GAAG/C,gBAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAE+C,GAAG,EAAEC,KAAK,EAAE,GAAG6B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAC9B,KAAK;YACR;QACF;QAEA,MAAMgC,mBAAmBlB,2BAA2Ba,IAAI,CAAC3B;QACzD,0CAA0C;QAC1C,4FAA4F;QAC5F,IAAI,CAACgC,oBAAoBN,YAAY;YACnC;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACrC,kBAAkBY,SAAS,CAACF,qBAAqB,CAACqB,SAAS;YAC9D,MAAM,IAAIa,MACR,CAAC,cAAc,EAAE7C,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAM8C,eAAejC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2C0B,IAAI,CAAC3B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B2B,IAAI,CAAC3B,QAChC,8BAA8B2B,IAAI,CAAC3B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIY,OAAOuB,iBAAiB,IAAI,CAACpB,6BAA6B;YAC5DA,8BAA8B,IAAIqB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAOzB,OAAOuB,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMtD,gBACnBC,KACA2B,OAAOI,YAAY,CAAClB,YAAY,EAChCX,SACAkD,MAAM,iBACNhD,gBACAwB,wBACAtB,YACA6B,UAAUS,sBAAsBE;gBAElC,IAAIO,OAAOtC,GAAG,EAAE;oBACde,4BAA4BwB,GAAG,CAACF,KAAKlF,KAAKqF,OAAO,CAACF,OAAOtC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAMyC,4BAA4BC,8BAA8B;YAC9DC,aAAa3C;YACbY;YACAG;YACAW;YACAQ;YACAF;YACA5C;QACF;QACA,IAAIqD,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACX/B,MAAM,EACNG,2BAA2B,EAC3BW,UAAU,EACVQ,YAAY,EACZF,gBAAgB,EAChB5C,OAAO,EASR;IACC,IAAIlB,iBAAiByD,IAAI,CAACgB,cAAc;QACtC,MAAMC,oBACJ,CAAClB,cACDd,OAAOI,YAAY,CAAC6B,oBAAoB,IACxC,CAACb;QACH,MAAMc,kBACJF,qBACAzE,qBACEwE,aACA/B,OAAOuB,iBAAiB,EACxBpB;QAEJ,IAAI,CAAC+B,iBAAiB;YACpB,OAAO,CAAC,EAAEZ,aAAa,CAAC,EAAE9C,QAAQ,CAAC,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAASyC,oBAAoBtB,QAAgB;IAC3C,MAAMwC,aAAa/E,gBAAgB2D,IAAI,CAACpB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAIwC,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAErF,iBACjB6C,SAASxB,OAAO,CAAC,oBAAoB,cACrC,CAAC;IACL;AACF"}