{"version": 3, "sources": ["../../../../src/server/lib/router-utils/proxy-request.ts"], "names": ["proxyRequest", "req", "res", "parsedUrl", "upgradeHead", "reqBody", "proxyTimeout", "query", "search", "stringifyQuery", "target", "url", "format", "HttpProxy", "require", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "ignore<PERSON><PERSON>", "ws", "undefined", "headers", "host", "Promise", "proxyResolve", "proxyReject", "finished", "on", "proxyReq", "destroy", "proxyRes", "destroyed", "innerReq", "innerRes", "cleanup", "err", "removeListener", "once", "console", "error", "statusCode", "end", "web", "buffer"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;4DAHN;kCACe;;;;;;AAExB,eAAeA,aACpBC,GAAoB,EACpBC,GAAmB,EACnBC,SAAiC,EACjCC,WAAiB,EACjBC,OAAa,EACbC,YAA4B;IAE5B,MAAM,EAAEC,KAAK,EAAE,GAAGJ;IAClB,OAAO,AAACA,UAAkBI,KAAK;IAC/BJ,UAAUK,MAAM,GAAGC,IAAAA,gCAAc,EAACR,KAAYM;IAE9C,MAAMG,SAASC,YAAG,CAACC,MAAM,CAACT;IAC1B,MAAMU,YACJC,QAAQ;IAEV,MAAMC,QAAQ,IAAIF,UAAU;QAC1BH;QACAM,cAAc;QACdC,YAAY;QACZC,IAAI;QACJ,4DAA4D;QAC5D,yDAAyD;QACzDZ,cAAcA,iBAAiB,OAAOa,YAAYb,gBAAgB;QAClEc,SAAS;YACP,oBAAoBnB,IAAImB,OAAO,CAACC,IAAI,IAAI;QAC1C;IACF;IAEA,MAAM,IAAIC,QAAQ,CAACC,cAAcC;QAC/B,IAAIC,WAAW;QAEf,mEAAmE;QACnE,sEAAsE;QACtE,qEAAqE;QACrE,uEAAuE;QACvE,uEAAuE;QACvE,kEAAkE;QAClE,cAAc;QACdV,MAAMW,EAAE,CAAC,YAAY,CAACC;YACpBzB,IAAIwB,EAAE,CAAC,SAAS,IAAMC,SAASC,OAAO;QACxC;QACAb,MAAMW,EAAE,CAAC,YAAY,CAACG;YACpB,IAAI3B,IAAI4B,SAAS,EAAE;gBACjBD,SAASD,OAAO;YAClB,OAAO;gBACL1B,IAAIwB,EAAE,CAAC,SAAS,IAAMG,SAASD,OAAO;YACxC;QACF;QAEAb,MAAMW,EAAE,CAAC,YAAY,CAACG,UAAUE,UAAUC;YACxC,MAAMC,UAAU,CAACC;gBACf,4DAA4D;gBAC5DL,SAASM,cAAc,CAAC,SAASF;gBACjCJ,SAASM,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBAEjC,oEAAoE;gBACpEF,SAASH,OAAO,CAACM;gBACjBL,SAASD,OAAO,CAACM;YACnB;YAEAL,SAASO,IAAI,CAAC,SAASH;YACvBJ,SAASO,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;QACzB;QAEAlB,MAAMW,EAAE,CAAC,SAAS,CAACQ;YACjBG,QAAQC,KAAK,CAAC,CAAC,gBAAgB,EAAE5B,OAAO,CAAC,EAAEwB;YAC3C,IAAI,CAACT,UAAU;gBACbA,WAAW;gBACXD,YAAYU;gBAEZ,IAAI,CAAChC,IAAI4B,SAAS,EAAE;oBAClB5B,IAAIqC,UAAU,GAAG;oBACjBrC,IAAIsC,GAAG,CAAC;gBACV;YACF;QACF;QAEA,wDAAwD;QACxD,IAAIpC,aAAa;YACfW,MAAMW,EAAE,CAAC,cAAc,CAACC;gBACtBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAR,MAAMG,EAAE,CAACjB,KAA+BC,KAAKE;YAC7CmB,aAAa;QACf,OAAO;YACLR,MAAMW,EAAE,CAAC,YAAY,CAACC;gBACpBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAR,MAAM0B,GAAG,CAACxC,KAAKC,KAAK;gBAClBwC,QAAQrC;YACV;QACF;IACF;AACF"}