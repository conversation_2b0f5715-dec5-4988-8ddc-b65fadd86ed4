{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/is-dynamic.ts"], "names": ["isDynamicRoute", "TEST_ROUTE", "route", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;oCALT;AAEP,qCAAqC;AACrC,MAAMC,aAAa;AAEZ,SAASD,eAAeE,KAAa;IAC1C,IAAIC,IAAAA,8CAA0B,EAACD,QAAQ;QACrCA,QAAQE,IAAAA,uDAAmC,EAACF,OAAOG,gBAAgB;IACrE;IAEA,OAAOJ,WAAWK,IAAI,CAACJ;AACzB"}