import Link from 'next/link';
import { 
  ArrowRight, 
  Truck, 
  Shield, 
  RefreshCw, 
  Award, 
  Star,
  TrendingUp,
  Users,
  ShoppingBag
} from 'lucide-react';
import { wooCommerceApi } from '@/lib/woocommerce';
import ProductGrid from '@/components/product/ProductGrid';
import HeroSection from '@/components/home/<USER>';
import FeaturesSection from '@/components/home/<USER>';
import StatsSection from '@/components/home/<USER>';
import dynamic from 'next/dynamic';

// Use dynamic import with ssr: true for client components
const SimpleProductCard = dynamic(() => import('@/components/home/<USER>'), {
  ssr: true
});

export const metadata = {
  title: 'Deal4u - Amazing Deals on Premium Products',
  description: 'Discover thousands of high-quality products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.',
};

async function getFeaturedProducts() {
  try {
    const products = await wooCommerceApi.getProducts({ 
      per_page: 8,
      featured: true,
      status: 'publish'
    });
    return products;
  } catch (error) {
    console.error('Error fetching featured products:', error);
    // Return mock data if API fails
    return [];
  }
}

async function getBestSellers() {
  try {
    const products = await wooCommerceApi.getProducts({ 
      per_page: 4,
      orderby: 'popularity',
      order: 'desc'
    });
    return products;
  } catch (error) {
    console.error('Error fetching best sellers:', error);
    return [];
  }
}

export default async function HomePage() {
  const [featuredProducts, bestSellers] = await Promise.all([
    getFeaturedProducts(),
    getBestSellers()
  ]);

  return (
    <div className="min-h-screen" id="main-content">
      {/* Summer Sale Banner - Above Hero Section */}
      <div className="bg-yellow-400 text-purple-900 py-4 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-purple-900 text-yellow-400 text-xl font-extrabold p-2 rounded-lg rotate-3 shadow-lg">
                50% OFF
              </div>
              <div>
                <h3 className="text-lg md:text-xl font-bold">SUMMER MEGA SALE</h3>
                <p className="text-sm">Limited time offer on all products!</p>
              </div>
            </div>
            <Link 
              href="/shop?sale=true" 
              className="bg-purple-900 hover:bg-purple-800 text-white font-bold py-2 px-6 rounded-lg shadow-md transition-colors"
            >
              Shop Now
            </Link>
          </div>
        </div>
        {/* Decorative elements */}
        <div className="absolute -right-8 top-0 bottom-0 w-32 bg-purple-900 opacity-20 rotate-12"></div>
        <div className="absolute left-1/3 top-0 bottom-0 w-8 bg-purple-900 opacity-10 -rotate-12"></div>
      </div>

      {/* Hero Section */}
      <HeroSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Stats Section */}
      <StatsSection />

      {/* Featured Products Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Featured Products
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our hand-picked selection of premium products
            </p>
          </div>
          
          {featuredProducts.length > 0 ? (
            <>
              <ProductGrid products={featuredProducts} />
              
              <div className="text-center mt-12">
                <Link
                  href="/shop"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  View All Products
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No featured products available
              </h3>
              <p className="mt-2 text-gray-600">
                {wooCommerceApi.isConfigured() 
                  ? 'Please check your WooCommerce store setup.' 
                  : 'Connect your WooCommerce store to display real products.'
                }
              </p>
              <div className="mt-6">
                <Link
                  href="/shop"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Browse All Products
                </Link>
              </div>
            </div>
          )}

          {/* Demo mode notice removed */}
        </div>
      </section>

      {/* Best Sellers Section */}
      {bestSellers.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Best Sellers
              </h2>
              <p className="text-lg text-gray-600">
                Our most popular products loved by customers
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {bestSellers.map((product, index) => (
                <div key={product.id} className="relative">
                  <div className="absolute top-2 left-2 z-10">
                    <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                      #{index + 1} Best Seller
                    </span>
                  </div>
                  <SimpleProductCard product={product} />
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Shop by Category
            </h2>
            <p className="text-lg text-gray-600">
              Find exactly what you're looking for
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {[
              { name: 'Electronics', icon: '📱', href: '/shop?category=electronics' },
              { name: 'Fashion', icon: '👕', href: '/shop?category=fashion' },
              { name: 'Home & Living', icon: '🏠', href: '/shop?category=home' },
              { name: 'Sports', icon: '⚽', href: '/shop?category=sports' },
              { name: 'Beauty', icon: '💄', href: '/shop?category=beauty' },
              { name: 'Books', icon: '📚', href: '/shop?category=books' },
            ].map((category) => (
              <Link
                key={category.name}
                href={category.href}
                className="group bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 hover:shadow-md transition-all duration-300"
              >
                <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">
                  {category.icon}
                </div>
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                  {category.name}
                </h3>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Promotional Banner */}
      <section className="py-16 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 overflow-hidden relative">
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80)', backgroundSize: 'cover', backgroundPosition: 'center' }}></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="md:flex items-center justify-between">
            <div className="md:w-1/2 text-center md:text-left mb-8 md:mb-0">
              <span className="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm text-white text-xs font-semibold rounded-full mb-3">LIMITED TIME OFFER</span>
              <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 leading-tight">
                Summer Sale is<br /><span className="text-yellow-300">Now Live!</span>
              </h2>
              <p className="text-xl text-white text-opacity-90 mb-8 max-w-md">
                Enjoy up to 50% off on selected items across all categories. Don't miss out on these incredible deals!  
              </p>
              <Link
                href="/shop?sale=true"
                className="inline-flex items-center px-8 py-4 rounded-lg bg-white text-purple-600 font-bold text-lg hover:bg-yellow-300 hover:text-purple-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Shop Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="w-64 h-64 sm:w-80 sm:h-80 relative rounded-full bg-white bg-opacity-20 backdrop-blur-sm p-2 transform rotate-3 hover:rotate-6 transition-transform">
                <div className="w-full h-full rounded-full overflow-hidden border-4 border-white border-opacity-40">
                  <div className="w-full h-full bg-gradient-to-br from-yellow-400 to-pink-500 flex items-center justify-center text-white text-opacity-90 text-center p-6">
                    <div>
                      <p className="text-4xl font-extrabold">50%</p>
                      <p className="text-xl font-bold">OFF</p>
                      <p className="text-sm mt-2 font-medium">Selected Items</p>
                      <p className="text-xs mt-3 font-bold">Use code: SUMMER50</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-12 bg-gray-50 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div className="flex flex-col items-center">
              <Truck className="h-8 w-8 text-blue-600 mb-3" />
              <h3 className="text-lg font-semibold text-gray-900">Free Shipping</h3>
              <p className="text-sm text-gray-600">On orders over $50</p>
            </div>
            <div className="flex flex-col items-center">
              <Shield className="h-8 w-8 text-green-600 mb-3" />
              <h3 className="text-lg font-semibold text-gray-900">Secure Payment</h3>
              <p className="text-sm text-gray-600">SSL protected checkout</p>
            </div>
            <div className="flex flex-col items-center">
              <RefreshCw className="h-8 w-8 text-purple-600 mb-3" />
              <h3 className="text-lg font-semibold text-gray-900">Easy Returns</h3>
              <p className="text-sm text-gray-600">30-day return policy</p>
            </div>
            <div className="flex flex-col items-center">
              <Award className="h-8 w-8 text-orange-600 mb-3" />
              <h3 className="text-lg font-semibold text-gray-900">Quality Guarantee</h3>
              <p className="text-sm text-gray-600">Authentic products only</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

// SimpleProductCard is imported at the top of the file