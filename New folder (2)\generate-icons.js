#!/usr/bin/env node

/**
 * Icon Generator for PWA
 * Creates placeholder icons for the Deal4u PWA
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 Generating PWA Icons for Deal4u...\n');

// Create icons directory
const iconsDir = path.join(__dirname, 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
  console.log('✅ Created icons directory');
}

// Icon sizes needed for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create SVG template for icons
const createSVGIcon = (size) => {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="${size}" height="${size}" fill="url(#grad)" rx="${size * 0.1}"/>
  
  <!-- Deal4u Logo -->
  <g transform="translate(${size * 0.15}, ${size * 0.2})">
    <!-- Shopping bag icon -->
    <path d="M${size * 0.2} ${size * 0.15} L${size * 0.6} ${size * 0.15} L${size * 0.65} ${size * 0.55} L${size * 0.15} ${size * 0.55} Z" 
          fill="white" stroke="white" stroke-width="${size * 0.02}"/>
    
    <!-- Bag handles -->
    <path d="M${size * 0.25} ${size * 0.15} C${size * 0.25} ${size * 0.1} ${size * 0.3} ${size * 0.05} ${size * 0.4} ${size * 0.05} 
             C${size * 0.5} ${size * 0.05} ${size * 0.55} ${size * 0.1} ${size * 0.55} ${size * 0.15}" 
          fill="none" stroke="white" stroke-width="${size * 0.025}"/>
  </g>
  
  <!-- Deal4u Text -->
  <text x="${size * 0.5}" y="${size * 0.8}" 
        font-family="Arial, sans-serif" 
        font-size="${size * 0.12}" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="white">Deal4u</text>
</svg>`;
};

// Generate icons
iconSizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`✅ Generated ${filename}`);
});

// Create shortcut icons
const shortcuts = [
  { name: 'shop-shortcut.svg', icon: '🛍️', label: 'Shop' },
  { name: 'cart-shortcut.svg', icon: '🛒', label: 'Cart' },
  { name: 'wishlist-shortcut.svg', icon: '❤️', label: 'Wishlist' },
  { name: 'account-shortcut.svg', icon: '👤', label: 'Account' }
];

shortcuts.forEach(shortcut => {
  const svgContent = `<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
  <rect width="96" height="96" fill="#3b82f6" rx="12"/>
  <text x="48" y="60" font-size="40" text-anchor="middle" fill="white">${shortcut.icon}</text>
</svg>`;
  
  const filepath = path.join(iconsDir, shortcut.name);
  fs.writeFileSync(filepath, svgContent);
  console.log(`✅ Generated ${shortcut.name}`);
});

// Create favicon
const faviconSVG = `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" fill="#3b82f6" rx="4"/>
  <path d="M8 10 L24 10 L25 22 L7 22 Z" fill="white"/>
  <path d="M10 10 C10 8 12 6 16 6 C20 6 22 8 22 10" fill="none" stroke="white" stroke-width="1.5"/>
</svg>`;

fs.writeFileSync(path.join(__dirname, 'public', 'icon.svg'), faviconSVG);
console.log('✅ Generated favicon (icon.svg)');

// Create apple-touch-icon
const appleTouchIcon = createSVGIcon(180);
fs.writeFileSync(path.join(__dirname, 'public', 'apple-touch-icon.svg'), appleTouchIcon);
console.log('✅ Generated apple-touch-icon.svg');

// Create README for icons
const iconReadme = `# PWA Icons for Deal4u

This directory contains all the icons needed for the Deal4u Progressive Web App (PWA).

## Generated Icons:

### App Icons:
- icon-72x72.svg - Small app icon
- icon-96x96.svg - Medium app icon  
- icon-128x128.svg - Large app icon
- icon-144x144.svg - Windows tile icon
- icon-152x152.svg - iOS app icon
- icon-192x192.svg - Android app icon
- icon-384x384.svg - Large Android icon
- icon-512x512.svg - Splash screen icon

### Shortcut Icons:
- shop-shortcut.svg - Shop shortcut icon
- cart-shortcut.svg - Cart shortcut icon
- wishlist-shortcut.svg - Wishlist shortcut icon
- account-shortcut.svg - Account shortcut icon

### Favicon:
- icon.svg - Main favicon (in public root)
- apple-touch-icon.svg - Apple touch icon (in public root)

## Converting to PNG:

To convert these SVG icons to PNG format (recommended for better compatibility):

1. Use an online converter like https://convertio.co/svg-png/
2. Or use ImageMagick: \`convert icon.svg icon.png\`
3. Or use Node.js with sharp: \`npm install sharp\` then use sharp to convert

## Icon Guidelines:

- Icons should be square (1:1 aspect ratio)
- Use high contrast colors
- Keep designs simple and recognizable
- Test on different backgrounds
- Ensure icons look good at small sizes

## Updating Icons:

To update the icons, modify the \`generate-icons.js\` script and run:
\`\`\`bash
node generate-icons.js
\`\`\`
`;

fs.writeFileSync(path.join(iconsDir, 'README.md'), iconReadme);
console.log('✅ Generated README.md for icons');

console.log('\n🎉 PWA Icon generation complete!');
console.log('\n📋 Next Steps:');
console.log('1. Convert SVG icons to PNG format for better compatibility');
console.log('2. Update manifest.json if you change icon names');
console.log('3. Test icons on different devices and browsers');
console.log('4. Consider creating custom icons with your brand colors');

console.log('\n💡 Tip: You can use online tools to convert SVG to PNG:');
console.log('   - https://convertio.co/svg-png/');
console.log('   - https://cloudconvert.com/svg-to-png');

console.log('\n🚀 Your PWA is ready to be installed!');
