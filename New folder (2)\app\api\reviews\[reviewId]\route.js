import { NextResponse } from 'next/server';
import { updateReviewHelpfulness, deleteReview } from '@/lib/reviews';

/**
 * PATCH /api/reviews/[reviewId] - Update review helpfulness
 */
export async function PATCH(request, { params }) {
  try {
    const { reviewId } = params;
    const { action, userId } = await request.json();

    if (!reviewId) {
      return NextResponse.json(
        { error: 'Review ID is required' },
        { status: 400 }
      );
    }

    if (action === 'helpful' || action === 'not-helpful') {
      const isHelpful = action === 'helpful';
      const updatedReview = await updateReviewHelpfulness(reviewId, isHelpful);

      return NextResponse.json({
        success: true,
        message: `Review marked as ${action}`,
        review: updatedReview
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "helpful" or "not-helpful"' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in PATCH /api/reviews/[reviewId]:', error);
    
    if (error.message === 'Review not found') {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to update review',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reviews/[reviewId] - Delete a review
 */
export async function DELETE(request, { params }) {
  try {
    const { reviewId } = params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const isAdmin = searchParams.get('admin') === 'true';

    if (!reviewId) {
      return NextResponse.json(
        { error: 'Review ID is required' },
        { status: 400 }
      );
    }

    if (!userId && !isAdmin) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    await deleteReview(reviewId, userId, isAdmin);

    return NextResponse.json({
      success: true,
      message: 'Review deleted successfully'
    });

  } catch (error) {
    console.error('Error in DELETE /api/reviews/[reviewId]:', error);
    
    if (error.message === 'Review not found') {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    if (error.message === 'Unauthorized to delete this review') {
      return NextResponse.json(
        { error: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to delete review',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
