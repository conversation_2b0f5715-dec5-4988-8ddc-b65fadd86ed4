const axios = require('axios');

async function testRestart() {
  console.log('🔄 Testing Fresh Project Restart...\n');
  
  try {
    console.log('1. Testing homepage...');
    const homeResponse = await axios.get('http://localhost:3000', { 
      timeout: 30000 
    });
    console.log(`✅ Homepage loaded (${homeResponse.status})`);
    
    console.log('\n2. Testing shop page...');
    const shopResponse = await axios.get('http://localhost:3000/shop', { 
      timeout: 30000 
    });
    console.log(`✅ Shop page loaded (${shopResponse.status})`);
    console.log(`   Content size: ${shopResponse.data.length} characters`);
    
    // Quick check for key elements
    const content = shopResponse.data;
    const hasProducts = content.includes('product') || content.includes('Product');
    const hasModernDesign = content.includes('grid') || content.includes('rounded');
    const hasReactData = content.includes('__NEXT_DATA__');
    
    console.log('\n📊 Quick Analysis:');
    console.log(`   Has products: ${hasProducts ? 'Yes' : 'No'}`);
    console.log(`   Modern design: ${hasModernDesign ? 'Yes' : 'No'}`);
    console.log(`   React hydration: ${hasReactData ? 'Yes' : 'No'}`);
    
    if (hasReactData) {
      console.log('\n🎉 SUCCESS: Project restarted successfully!');
      console.log('   React is hydrating properly');
      console.log('   Modern grid layout should be visible');
    } else {
      console.log('\n⚠️  Server-side only - React hydration pending');
    }
    
    console.log('\n🌐 Your website is now live at:');
    console.log('   Homepage: http://localhost:3000');
    console.log('   Shop: http://localhost:3000/shop');
    
  } catch (error) {
    console.log('❌ Error testing restart:');
    console.log(`   ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Server not ready yet - try again in a few seconds');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   Server is slow to respond - this is normal on first load');
    }
  }
}

testRestart();
