DEAL4U CPANEL DEPLOYMENT INSTRUCTIONS 
======================================= 
 
🔄 REAL-TIME WOOCOMMERCE SYNC ENABLED 
Your app will fetch fresh product data from WooCommerce on every page load! 
 
STEP 1: CREATE NODE.JS APP IN CPANEL 
===================================== 
1. Login to your cPanel 
2. Find "Node.js Apps" (or "Node.js Selector") 
3. Click "Create Application" 
4. Fill in these settings: 
   - Node.js Version: 18.x (or latest available) 
   - Application Root: /public_html/deal4u 
   - Application URL: yourdomain.com/deal4u 
   - Startup File: server.js 
5. Click "Create" 
 
STEP 2: UPLOAD ALL FILES 
======================== 
1. Go to cPanel File Manager 
2. Navigate to /public_html/deal4u 
3. Upload ALL files and folders from this Deal4u_cpanel folder 
4. Make sure ALL folders are uploaded completely 
 
STEP 3: INSTALL DEPENDENCIES 
============================ 
1. Go back to cPanel Node.js Apps 
2. Click on your Deal4u application 
3. Go to "Package.json" tab 
4. Click "Run NPM Install" 
5. Wait 2-3 minutes for installation 
 
STEP 4: SET ENVIRONMENT 
====================== 
1. Go to "Environment Variables" tab 
2. Add: NODE_ENV = production 
 
STEP 5: START YOUR APP 
==================== 
1. Click "Start App" button 
2. Wait for "Running" status 
3. Visit your website URL 
 
STEP 6: TEST DYNAMIC SYNC 
========================= 
1. Add a new product to your WooCommerce store 
2. Refresh your website 
3. New product should appear automatically! 
