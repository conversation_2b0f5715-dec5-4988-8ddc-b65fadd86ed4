const axios = require('axios');

async function debugAPIKeys() {
  console.log('🔍 Debugging WooCommerce API Keys Issue...\n');
  
  const consumerKey = 'ck_d0674fe5dd2fec3d1f959f75e082d71475db9fea';
  const consumerSecret = 'cs_9a290c537308b80cf61c7314ac267c09db85c5be';
  const baseURL = 'https://deal4u.co/wp-json/wc/v3';
  
  console.log('📋 Current API Configuration:');
  console.log(`   Base URL: ${baseURL}`);
  console.log(`   Consumer Key: ${consumerKey.substring(0, 10)}...${consumerKey.substring(consumerKey.length - 10)}`);
  console.log(`   Consumer Secret: ${consumerSecret.substring(0, 10)}...${consumerSecret.substring(consumerSecret.length - 10)}`);
  
  // Test different endpoints to understand the issue
  const tests = [
    {
      name: 'WooCommerce API Status',
      url: `${baseURL}`,
      auth: false
    },
    {
      name: 'Products List (with auth)',
      url: `${baseURL}/products?per_page=1`,
      auth: true
    },
    {
      name: 'Categories List (with auth)',
      url: `${baseURL}/products/categories?per_page=1`,
      auth: true
    },
    {
      name: 'System Status (with auth)',
      url: `${baseURL}/system_status`,
      auth: true
    }
  ];
  
  for (const test of tests) {
    console.log(`\n🧪 Testing: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    try {
      const config = {
        timeout: 10000,
        headers: {
          'User-Agent': 'Deal4u-Website/1.0'
        }
      };
      
      if (test.auth) {
        config.auth = {
          username: consumerKey,
          password: consumerSecret
        };
      }
      
      const response = await axios.get(test.url, config);
      
      console.log(`   ✅ Success: ${response.status} ${response.statusText}`);
      console.log(`   📊 Response size: ${JSON.stringify(response.data).length} characters`);
      
      if (test.name.includes('Products') && Array.isArray(response.data)) {
        console.log(`   📦 Products found: ${response.data.length}`);
        if (response.data.length > 0) {
          console.log(`   📝 First product: ${response.data[0].name || 'No name'}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.response?.status || 'No response'} ${error.response?.statusText || error.message}`);
      
      if (error.response?.data) {
        console.log(`   📄 Error details: ${JSON.stringify(error.response.data)}`);
      }
      
      // Analyze the error
      if (error.response?.status === 401) {
        console.log(`   🔍 Analysis: Authentication failed`);
        if (error.response.data?.code === 'woocommerce_rest_cannot_view') {
          console.log(`   💡 Possible causes:`);
          console.log(`      - API key permissions insufficient`);
          console.log(`      - API key expired or revoked`);
          console.log(`      - WordPress user permissions changed`);
          console.log(`      - WooCommerce plugin settings changed`);
        }
      } else if (error.response?.status === 404) {
        console.log(`   🔍 Analysis: Endpoint not found - WooCommerce may be disabled`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`   🔍 Analysis: Cannot connect to server`);
      }
    }
  }
  
  // Check if WooCommerce is active
  console.log(`\n🔌 Testing WordPress REST API (without auth):`);
  try {
    const wpResponse = await axios.get('https://deal4u.co/wp-json/wp/v2', { timeout: 5000 });
    console.log(`   ✅ WordPress REST API working: ${wpResponse.status}`);
  } catch (error) {
    console.log(`   ❌ WordPress REST API failed: ${error.message}`);
  }
  
  // Test WooCommerce plugin status
  console.log(`\n🛒 Testing WooCommerce Plugin Status:`);
  try {
    const wcResponse = await axios.get('https://deal4u.co/wp-json/wc/v3', { timeout: 5000 });
    console.log(`   ✅ WooCommerce plugin active: ${wcResponse.status}`);
  } catch (error) {
    console.log(`   ❌ WooCommerce plugin issue: ${error.response?.status || error.message}`);
    if (error.response?.status === 401) {
      console.log(`   💡 WooCommerce is active but requires authentication`);
    }
  }
  
  console.log(`\n📋 DIAGNOSIS:`);
  console.log(`   Your API keys were working yesterday, so the issue could be:`);
  console.log(`   1. 🔄 WordPress/WooCommerce plugin updated overnight`);
  console.log(`   2. 🔒 Security plugin blocked API access`);
  console.log(`   3. 👤 User permissions changed`);
  console.log(`   4. ⚙️  WooCommerce settings modified`);
  console.log(`   5. 🌐 Server configuration changed`);
  console.log(`   6. 🔑 API keys were regenerated by someone else`);
  
  console.log(`\n🔧 RECOMMENDED ACTIONS:`);
  console.log(`   1. Check WordPress admin for any plugin updates`);
  console.log(`   2. Verify WooCommerce settings haven't changed`);
  console.log(`   3. Check if security plugins are blocking API access`);
  console.log(`   4. Confirm the API keys still exist in WooCommerce settings`);
}

debugAPIKeys();
