'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, Search, MessageCircle } from 'lucide-react';

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState({});

  const faqCategories = [
    {
      category: 'Orders & Shipping',
      icon: '📦',
      faqs: [
        {
          question: 'How long does shipping take?',
          answer: 'Standard shipping typically takes 5-7 business days within the United States. Express shipping is available and takes 2-3 business days. International shipping times vary by location but generally take 10-21 business days. You can track your order using the tracking number provided in your shipping confirmation email.'
        },
        {
          question: 'How can I track my order?',
          answer: 'Once your order ships, you\'ll receive a tracking number via email. You can use this number to track your package on our website by visiting the "Track Your Order" page, or directly on the carrier\'s website (UPS, FedEx, USPS). You can also track orders from your account dashboard.'
        },
        {
          question: 'Do you ship internationally?',
          answer: 'Yes! We ship to most countries worldwide. International shipping rates and delivery times are calculated at checkout based on your location. Please note that international orders may be subject to customs duties and taxes, which are the responsibility of the customer.'
        },
        {
          question: 'What are the shipping costs?',
          answer: 'Shipping is free for orders over $50 within the US. For orders under $50, standard shipping is $5.99. Express shipping costs $12.99. International shipping rates vary by destination and are calculated at checkout based on weight and location.'
        },
        {
          question: 'Can I change my shipping address after placing an order?',
          answer: 'You can change your shipping address within 1 hour of placing your order by contacting our customer service team. After this time, we may not be able to modify the address as your order may have already entered our fulfillment process.'
        }
      ]
    },
    {
      category: 'Returns & Refunds',
      icon: '↩️',
      faqs: [
        {
          question: 'What is your return policy?',
          answer: 'We offer a 30-day return policy for all products. Items must be unused, in their original packaging, and in the same condition as received. Some restrictions apply to certain product categories like personalized items, perishables, and intimate apparel.'
        },
        {
          question: 'How do I return an item?',
          answer: 'To initiate a return, log into your account and go to your order history. Select the item you want to return and follow the instructions. We\'ll provide a prepaid return label via email. Package the item securely and drop it off at any authorized shipping location.'
        },
        {
          question: 'When will I receive my refund?',
          answer: 'Refunds are processed within 5-7 business days after we receive your return. It may take an additional 3-5 business days for the refund to appear in your account, depending on your bank or credit card company. You\'ll receive an email confirmation once the refund is processed.'
        },
        {
          question: 'Can I exchange an item instead of returning it?',
          answer: 'Currently, we don\'t offer direct exchanges. To get a different size or color, please return the original item and place a new order for the item you want. This ensures you get exactly what you need and helps us process your request faster.'
        },
        {
          question: 'Who pays for return shipping?',
          answer: 'We provide free return shipping labels for defective items or our errors. For returns due to change of mind or ordering the wrong size, the customer is responsible for return shipping costs, which will be deducted from your refund.'
        }
      ]
    },
    {
      category: 'Products & Inventory',
      icon: '🏷️',
      faqs: [
        {
          question: 'Are all products authentic?',
          answer: 'Yes, we guarantee 100% authenticity for all products sold on Deal4u. We work directly with authorized distributors and manufacturers. All products come with proper documentation and warranties. If you ever receive a counterfeit item, we\'ll provide a full refund and investigate the issue immediately.'
        },
        {
          question: 'When will out-of-stock items be available?',
          answer: 'Restock times vary by product and supplier. You can sign up for email notifications on the product page to be alerted when an item is back in stock. We typically receive new inventory weekly, but popular items may take 2-4 weeks to restock.'
        },
        {
          question: 'Can I reserve items?',
          answer: 'Unfortunately, we cannot reserve items. Products are available on a first-come, first-served basis. We recommend adding items to your cart and purchasing as soon as possible. Items in your cart are not reserved and may become unavailable if stock runs out.'
        },
        {
          question: 'Do you offer product warranties?',
          answer: 'Yes, all products come with their standard manufacturer warranties. We also offer extended warranty options for electronics and appliances at checkout. Warranty terms vary by product and manufacturer. You can find specific warranty information on each product page.'
        },
        {
          question: 'How do I know if a product is compatible with my device?',
          answer: 'Product compatibility information is listed in the product description and specifications. If you\'re unsure, please contact our customer service team with your device model, and we\'ll help you find compatible products.'
        }
      ]
    },
    {
      category: 'Account & Payment',
      icon: '💳',
      faqs: [
        {
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards (Visa, MasterCard, American Express, Discover), PayPal, Apple Pay, and Google Pay. All transactions are secured with SSL encryption and we never store your credit card details on our servers.'
        },
        {
          question: 'Is my payment information secure?',
          answer: 'Yes, we use industry-standard SSL encryption to protect your payment information. We are PCI DSS compliant and work with trusted payment processors. Your credit card information is encrypted and transmitted securely to our payment partners.'
        },
        {
          question: 'How do I create an account?',
          answer: 'Click the "Login" button in the header and select "Sign up". You can create an account with your email address or sign up using your Google or Facebook account. Having an account allows you to track orders, save addresses, and view your order history.'
        },
        {
          question: 'Can I checkout as a guest?',
          answer: 'Yes, you can checkout as a guest without creating an account. However, creating an account allows you to track your orders, save shipping addresses, and access exclusive member benefits.'
        },
        {
          question: 'How do I update my account information?',
          answer: 'Log into your account and go to "Account Settings" where you can update your personal information, shipping addresses, and communication preferences. Changes are saved automatically.'
        }
      ]
    },
    {
      category: 'Technical Support',
      icon: '🔧',
      faqs: [
        {
          question: 'I\'m having trouble placing an order. What should I do?',
          answer: 'Try clearing your browser cache and cookies, then attempt to place the order again. Make sure you\'re using a supported browser (Chrome, Firefox, Safari, Edge). If you continue to experience issues, contact our technical support team.'
        },
        {
          question: 'Why isn\'t the website loading properly?',
          answer: 'This could be due to your internet connection, browser cache, or temporary server issues. Try refreshing the page, clearing your browser cache, or using a different browser. If the problem persists, please contact our support team.'
        },
        {
          question: 'How do I reset my password?',
          answer: 'Click "Forgot Password" on the login page and enter your email address. We\'ll send you a password reset link. If you don\'t receive the email within a few minutes, check your spam folder.'
        },
        {
          question: 'Can I use the website on my mobile device?',
          answer: 'Yes, our website is fully optimized for mobile devices. You can browse, shop, and manage your account from any smartphone or tablet. We also recommend adding our website to your home screen for easy access.'
        }
      ]
    }
  ];

  const toggleItem = (category, index) => {
    const key = `${category}-${index}`;
    setOpenItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const filteredFAQs = faqCategories.map(category => ({
    ...category,
    faqs: category.faqs.filter(faq =>
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.faqs.length > 0);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <HelpCircle className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-600">
            Find answers to common questions about shopping at Deal4u
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Quick Links */}
        <div className="mb-12">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-center">Browse by Category</h2>
          <div className="flex flex-wrap justify-center gap-4">
            {faqCategories.map((category, index) => (
              <button
                key={index}
                onClick={() => {
                  const element = document.getElementById(`category-${index}`);
                  element?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="bg-white border border-gray-200 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 transition-colors"
              >
                <span className="mr-2">{category.icon}</span>
                {category.category}
              </button>
            ))}
          </div>
        </div>

        {/* FAQ Categories */}
        {filteredFAQs.length > 0 ? (
          <div className="space-y-8">
            {filteredFAQs.map((category, categoryIndex) => (
              <div 
                key={categoryIndex} 
                id={`category-${categoryIndex}`}
                className="bg-white rounded-lg shadow-md overflow-hidden"
              >
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 px-6 py-4">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                    <span className="text-2xl mr-3">{category.icon}</span>
                    {category.category}
                  </h2>
                </div>
                
                <div className="divide-y divide-gray-200">
                  {category.faqs.map((faq, faqIndex) => {
                    const isOpen = openItems[`${categoryIndex}-${faqIndex}`];
                    
                    return (
                      <div key={faqIndex} className="px-6 py-4">
                        <button
                          onClick={() => toggleItem(categoryIndex, faqIndex)}
                          className="w-full flex justify-between items-start text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                        >
                          <h3 className="text-lg font-medium text-gray-900 pr-8">
                            {faq.question}
                          </h3>
                          {isOpen ? (
                            <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          )}
                        </button>
                        
                        {isOpen && (
                          <div className="mt-3 text-gray-600 leading-relaxed animate-in slide-in-from-top">
                            {faq.answer}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white rounded-lg">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No results found</h3>
            <p className="text-gray-600">Try searching with different keywords</p>
            <button
              onClick={() => setSearchTerm('')}
              className="mt-4 text-blue-600 hover:text-blue-700 font-medium"
            >
              Clear search and view all FAQs
            </button>
          </div>
        )}

        {/* Contact Section */}
        <div className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Still have questions?</h2>
          <p className="mb-6">Our customer support team is here to help!</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => window.location.href = '/contact'}
              className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Contact Support
            </button>
            <button 
              onClick={() => alert('Live chat feature coming soon!')}
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center space-x-2"
            >
              <MessageCircle className="w-4 h-4" />
              <span>Live Chat</span>
            </button>
          </div>
        </div>

        {/* Help Resources */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📞</span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Phone Support</h3>
            <p className="text-gray-600 text-sm">Call us at +44 7447 186806</p>
            <p className="text-gray-500 text-xs">Mon-Fri, 9AM-6PM CET</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📧</span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Email Support</h3>
            <p className="text-gray-600 text-sm"><EMAIL></p>
            <p className="text-gray-500 text-xs">Response within 24 hours</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Live Chat</h3>
            <p className="text-gray-600 text-sm">Available 24/7</p>
            <p className="text-gray-500 text-xs">Instant responses</p>
          </div>
        </div>
      </div>
    </div>
  );
}