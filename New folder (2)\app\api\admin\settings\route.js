import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'data', 'admin-settings.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Default settings
const defaultSettings = {
  siteName: 'deal4u.co',
  siteEmail: '<EMAIL>',
  supportPhone: '+447447186806',
  currency: 'GBP',
  timezone: 'Europe/London',
  notifications: {
    orderEmails: true,
    stockAlerts: true,
    customerEmails: true,
    adminNotifications: true
  },
  store: {
    allowRegistration: true,
    guestCheckout: true,
    reviewsEnabled: true,
    couponsEnabled: true
  },
  lastUpdated: new Date().toISOString()
};

// GET - Load settings
export async function GET() {
  try {
    console.log('📖 Loading admin settings...');
    
    ensureDataDirectory();
    
    // Check if settings file exists
    if (!fs.existsSync(SETTINGS_FILE)) {
      console.log('📝 Creating default settings file...');
      fs.writeFileSync(SETTINGS_FILE, JSON.stringify(defaultSettings, null, 2));
      return NextResponse.json({
        success: true,
        settings: defaultSettings,
        message: 'Default settings loaded'
      });
    }
    
    // Read existing settings
    const settingsData = fs.readFileSync(SETTINGS_FILE, 'utf8');
    const settings = JSON.parse(settingsData);
    
    console.log('✅ Settings loaded successfully');
    
    return NextResponse.json({
      success: true,
      settings,
      message: 'Settings loaded successfully'
    });
    
  } catch (error) {
    console.error('❌ Error loading settings:', error);
    
    return NextResponse.json({
      success: false,
      settings: defaultSettings,
      error: error.message,
      message: 'Error loading settings, using defaults'
    }, { status: 200 });
  }
}

// POST - Save settings
export async function POST(request) {
  try {
    console.log('💾 Saving admin settings...');
    
    const newSettings = await request.json();
    
    // Validate required fields
    if (!newSettings.siteName || !newSettings.siteEmail) {
      return NextResponse.json({
        success: false,
        error: 'Site name and email are required',
        message: 'Please fill in all required fields'
      }, { status: 400 });
    }
    
    // Add timestamp
    const settingsToSave = {
      ...newSettings,
      lastUpdated: new Date().toISOString()
    };
    
    ensureDataDirectory();
    
    // Save to file
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settingsToSave, null, 2));
    
    console.log('✅ Settings saved successfully:', {
      siteName: settingsToSave.siteName,
      siteEmail: settingsToSave.siteEmail,
      lastUpdated: settingsToSave.lastUpdated
    });
    
    return NextResponse.json({
      success: true,
      settings: settingsToSave,
      message: 'Settings saved successfully!'
    });
    
  } catch (error) {
    console.error('❌ Error saving settings:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      message: 'Failed to save settings'
    }, { status: 500 });
  }
}
