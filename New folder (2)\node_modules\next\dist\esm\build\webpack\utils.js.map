{"version": 3, "sources": ["../../../src/build/webpack/utils.ts"], "names": ["isAppRouteRoute", "traverseModules", "compilation", "callback", "filterChunkGroup", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "mod", "modId", "getModuleId", "toString", "anyModule", "modules", "subMod", "forEachEntryModule", "name", "entry", "entries", "startsWith", "entryDependency", "dependencies", "request", "entryModule", "moduleGraph", "getResolvedModule", "dependency", "modRequest", "includes", "formatBarrelOptimizedResource", "resource", "matchResource", "getModuleReferencesInOrder", "module", "connections", "connection", "getOutgoingConnections", "push", "index", "getParentBlockIndex", "sort", "a", "b", "map", "c"], "mappings": "AAQA,SAASA,eAAe,QAAQ,+BAA8B;AAG9D,OAAO,SAASC,gBACdC,WAAwB,EACxBC,QAKQ,EACRC,gBAAsD;IAEtDF,YAAYG,WAAW,CAACC,OAAO,CAAC,CAACC;QAC/B,IAAIH,oBAAoB,CAACA,iBAAiBG,aAAa;YACrD;QACF;QACAA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;YACzB,MAAMC,eAAeR,YAAYS,UAAU,CAACC,uBAAuB,CACjEH;YAGF,KAAK,MAAMI,OAAOH,aAAc;oBAChBR;gBAAd,MAAMY,SAAQZ,sCAAAA,YAAYS,UAAU,CAACI,WAAW,CAACF,yBAAnCX,oCAAyCc,QAAQ;gBAC/Db,SAASU,KAAKJ,OAAOF,YAAYO;gBACjC,MAAMG,YAAYJ;gBAClB,IAAII,UAAUC,OAAO,EAAE;oBACrB,KAAK,MAAMC,UAAUF,UAAUC,OAAO,CACpCf,SAASgB,QAAQV,OAAOF,YAAYO;gBACxC;YACF;QACF;IACF;AACF;AAEA,mCAAmC;AACnC,OAAO,SAASM,mBACdlB,WAAgB,EAChBC,QAA6E;IAE7E,KAAK,MAAM,CAACkB,MAAMC,MAAM,IAAIpB,YAAYqB,OAAO,CAACA,OAAO,GAAI;YAWPD;QAVlD,gCAAgC;QAChC,IACED,KAAKG,UAAU,CAAC,aAChB,4BAA4B;QAC3BH,KAAKG,UAAU,CAAC,WAAWxB,gBAAgBqB,OAC5C;YACA;QACF;QAEA,wDAAwD;QACxD,MAAMI,mBAA4CH,sBAAAA,MAAMI,YAAY,qBAAlBJ,mBAAoB,CAAC,EAAE;QACzE,mDAAmD;QACnD,IAAI,CAACG,mBAAmB,CAACA,gBAAgBE,OAAO,EAAE;QAElD,MAAMA,UAAUF,gBAAgBE,OAAO;QAEvC,IACE,CAACA,QAAQH,UAAU,CAAC,4BACpB,CAACG,QAAQH,UAAU,CAAC,qBAEpB;QAEF,IAAII,cACF1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACL;QAE5C,IAAIE,QAAQH,UAAU,CAAC,0BAA0B;YAC/CI,YAAYF,YAAY,CAACpB,OAAO,CAAC,CAACyB;gBAChC,MAAMC,aAAiC,AAACD,WAAmBJ,OAAO;gBAClE,IAAIK,8BAAAA,WAAYC,QAAQ,CAAC,oBAAoB;oBAC3CL,cAAc1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACC;gBAC1D;YACF;QACF;QAEA5B,SAAS;YAAEkB;YAAMO;QAAY;IAC/B;AACF;AAEA,OAAO,SAASM,8BACdC,QAAgB,EAChBC,aAAqB;IAErB,OAAO,CAAC,EAAED,SAAS,CAAC,EAAEC,cAAc,CAAC;AACvC;AAEA,OAAO,SAASC,2BACdC,MAAc,EACdT,WAAwB;IAExB,MAAMU,cAAc,EAAE;IACtB,KAAK,MAAMC,cAAcX,YAAYY,sBAAsB,CAACH,QAAS;QACnE,IAAIE,WAAWT,UAAU,IAAIS,WAAWF,MAAM,EAAE;YAC9CC,YAAYG,IAAI,CAAC;gBACfF;gBACAG,OAAOd,YAAYe,mBAAmB,CAACJ,WAAWT,UAAU;YAC9D;QACF;IACF;IACAQ,YAAYM,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEH,KAAK,GAAGI,EAAEJ,KAAK;IAC5C,OAAOJ,YAAYS,GAAG,CAAC,CAACC,IAAMA,EAAET,UAAU;AAC5C"}