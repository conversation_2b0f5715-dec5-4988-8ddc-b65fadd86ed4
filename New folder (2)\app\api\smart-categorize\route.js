import { NextResponse } from 'next/server';
import smartCategorizer from '../../../lib/smartCategorizer.js';
import { wooCommerceApi } from '../../../lib/woocommerce.js';

// Smart categorization API endpoint
export async function POST(request) {
  try {
    const body = await request.json();
    const { action, productIds, autoCorrect = false } = body;

    switch (action) {
      case 'categorize-single':
        return await categorizeSingleProduct(body);
      
      case 'categorize-batch':
        return await categorizeBatchProducts(body);
      
      case 'categorize-all':
        return await categorizeAllProducts(body);
      
      case 'auto-correct':
        return await autoCorrectCategories(body);
      
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: categorize-single, categorize-batch, categorize-all, or auto-correct'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Smart categorization error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Categorize a single product
async function categorizeSingleProduct({ productId }) {
  try {
    // Get product from WooCommerce
    const product = await wooCommerceApi.getProduct(productId);
    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Analyze the product
    const analysis = await smartCategorizer.categorizeProduct(product);

    return NextResponse.json({
      success: true,
      product: {
        id: product.id,
        name: product.name
      },
      analysis
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Categorize multiple products
async function categorizeBatchProducts({ productIds }) {
  try {
    const products = [];
    
    // Get all products
    for (const id of productIds) {
      const product = await wooCommerceApi.getProduct(id);
      if (product) {
        products.push(product);
      }
    }

    // Analyze all products
    const results = await smartCategorizer.batchCategorize(products);
    const stats = smartCategorizer.getCategoryStats(results);

    return NextResponse.json({
      success: true,
      results,
      stats
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Categorize all products in the store
async function categorizeAllProducts({ limit = 100 }) {
  try {
    // Get all products from WooCommerce
    const products = await wooCommerceApi.getProducts({ 
      per_page: limit,
      status: 'publish'
    });

    if (!products || products.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No products found to categorize',
        results: [],
        stats: { total: 0, needsCorrection: 0, byCategory: {}, corrections: [] }
      });
    }

    // Analyze all products
    const results = await smartCategorizer.batchCategorize(products);
    const stats = smartCategorizer.getCategoryStats(results);

    return NextResponse.json({
      success: true,
      message: `Analyzed ${products.length} products`,
      results,
      stats
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Auto-correct categories in WooCommerce
async function autoCorrectCategories({ productIds, minConfidence = 5 }) {
  try {
    const correctionResults = [];
    
    for (const productId of productIds) {
      try {
        // Get product
        const product = await wooCommerceApi.getProduct(productId);
        if (!product) continue;

        // Analyze product
        const analysis = await smartCategorizer.categorizeProduct(product);
        
        // Only correct if confidence is high enough and correction is needed
        if (analysis.needsCorrection && analysis.confidence >= minConfidence) {
          // Find or create the category in WooCommerce
          const categoryId = await findOrCreateCategory(analysis.suggestedCategory);
          
          if (categoryId) {
            // Update product category in WooCommerce
            const updateResult = await updateProductCategory(productId, categoryId);
            
            correctionResults.push({
              productId,
              productName: product.name,
              success: updateResult.success,
              from: analysis.currentCategory,
              to: analysis.suggestedCategory,
              confidence: analysis.confidence,
              error: updateResult.error
            });
          }
        }
      } catch (error) {
        correctionResults.push({
          productId,
          success: false,
          error: error.message
        });
      }
    }

    const successCount = correctionResults.filter(r => r.success).length;
    
    return NextResponse.json({
      success: true,
      message: `Successfully corrected ${successCount} out of ${correctionResults.length} products`,
      results: correctionResults,
      summary: {
        total: correctionResults.length,
        successful: successCount,
        failed: correctionResults.length - successCount
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Helper function to find or create category in WooCommerce
async function findOrCreateCategory(categorySlug) {
  try {
    // First, try to find existing category
    const categories = await wooCommerceApi.getCategories();
    const existingCategory = categories.find(cat => 
      cat.slug === categorySlug || 
      cat.name.toLowerCase().replace(/\s+/g, '-') === categorySlug
    );
    
    if (existingCategory) {
      return existingCategory.id;
    }

    // If not found, create new category
    const categoryName = categorySlug.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');

    // Note: This would require WooCommerce API write permissions
    // For now, we'll return null and log the need to create the category
    console.log(`Category "${categoryName}" (${categorySlug}) needs to be created in WooCommerce`);
    return null;
    
  } catch (error) {
    console.error('Error finding/creating category:', error);
    return null;
  }
}

// Helper function to update product category in WooCommerce
async function updateProductCategory(productId, categoryId) {
  try {
    // Note: This would require WooCommerce API write permissions
    // For now, we'll simulate the update
    console.log(`Would update product ${productId} to category ${categoryId}`);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating product category:', error);
    return { success: false, error: error.message };
  }
}

// GET endpoint for testing
export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'test';

  if (action === 'test') {
    return NextResponse.json({
      success: true,
      message: 'Smart Categorization API is working',
      availableActions: [
        'categorize-single',
        'categorize-batch', 
        'categorize-all',
        'auto-correct'
      ],
      usage: {
        'POST /api/smart-categorize': {
          'categorize-single': { productId: 'number' },
          'categorize-batch': { productIds: 'array' },
          'categorize-all': { limit: 'number (optional)' },
          'auto-correct': { productIds: 'array', minConfidence: 'number (optional)' }
        }
      }
    });
  }

  return NextResponse.json({
    success: false,
    error: 'Invalid action'
  }, { status: 400 });
}
