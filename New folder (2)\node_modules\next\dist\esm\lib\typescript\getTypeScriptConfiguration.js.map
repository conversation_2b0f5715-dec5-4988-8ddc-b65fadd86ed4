{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptConfiguration.ts"], "names": ["bold", "cyan", "os", "path", "FatalE<PERSON>r", "isError", "getTypeScriptConfiguration", "ts", "tsConfigPath", "metaOnly", "result", "formatDiagnosticsHost", "getCanonicalFileName", "fileName", "getCurrentDirectory", "sys", "getNewLine", "EOL", "config", "error", "readConfigFile", "readFile", "formatDiagnostic", "configToParse", "parseJsonConfigFileContent", "readDirectory", "_path", "extensions", "_excludes", "_includes", "_depth", "dirname", "errors", "filter", "code", "length", "err", "name", "reason", "message"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,gBAAe;AAC1C,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AAEvB,SAASC,UAAU,QAAQ,iBAAgB;AAC3C,OAAOC,aAAa,cAAa;AAEjC,OAAO,eAAeC,2BACpBC,EAA+B,EAC/BC,YAAoB,EACpBC,QAAkB;IAElB,IAAI;YAqCEC;QApCJ,MAAMC,wBAAoE;YACxEC,sBAAsB,CAACC,WAAqBA;YAC5CC,qBAAqBP,GAAGQ,GAAG,CAACD,mBAAmB;YAC/CE,YAAY,IAAMd,GAAGe,GAAG;QAC1B;QAEA,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGZ,GAAGa,cAAc,CAACZ,cAAcD,GAAGQ,GAAG,CAACM,QAAQ;QACzE,IAAIF,OAAO;YACT,MAAM,IAAIf,WAAWG,GAAGe,gBAAgB,CAACH,OAAOR;QAClD;QAEA,IAAIY,gBAAqBL;QAEzB,MAAMR,SAASH,GAAGiB,0BAA0B,CAC1CD,eACA,qCAAqC;QACrC,wDAAwD;QACxDd,WACI;YACE,GAAGF,GAAGQ,GAAG;YACTU,eAAcC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM;gBAC3D,OAAO;oBAACH,aAAa,CAAC,IAAI,EAAEA,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;iBAAC;YAC1D;QACF,IACApB,GAAGQ,GAAG,EACVZ,KAAK4B,OAAO,CAACvB;QAGf,IAAIE,OAAOsB,MAAM,EAAE;YACjBtB,OAAOsB,MAAM,GAAGtB,OAAOsB,MAAM,CAACC,MAAM,CAClC,CAAC,EAAEC,IAAI,EAAE,GACP,sCAAsC;gBACtCA,SAAS;QAEf;QAEA,KAAIxB,iBAAAA,OAAOsB,MAAM,qBAAbtB,eAAeyB,MAAM,EAAE;YACzB,MAAM,IAAI/B,WACRG,GAAGe,gBAAgB,CAACZ,OAAOsB,MAAM,CAAC,EAAE,EAAErB;QAE1C;QAEA,OAAOD;IACT,EAAE,OAAO0B,KAAK;QACZ,IAAI/B,QAAQ+B,QAAQA,IAAIC,IAAI,KAAK,eAAe;YAC9C,MAAMC,SAAS,OAAQF,CAAAA,IAAIG,OAAO,IAAI,EAAC;YACvC,MAAM,IAAInC,WACRJ,KACE,oBACEC,KAAK,mBACL,MACA,+DACAqC;QAER;QACA,MAAMF;IACR;AACF"}