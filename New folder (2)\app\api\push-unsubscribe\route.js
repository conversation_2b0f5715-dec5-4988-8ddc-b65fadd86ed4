import { NextResponse } from 'next/server';

/**
 * Unsubscribe from push notifications
 * POST /api/push-unsubscribe
 */
export async function POST(request) {
  try {
    const subscription = await request.json();
    
    console.log('Push unsubscription request:', subscription);
    
    // Validate subscription object
    if (!subscription || !subscription.endpoint) {
      return NextResponse.json(
        { error: 'Invalid subscription object' },
        { status: 400 }
      );
    }

    // In a real application, you would:
    // 1. Find the subscription in your database
    // 2. Mark it as inactive or delete it
    // 3. Clean up any associated user preferences
    
    // Example database removal:
    /*
    await db.pushSubscriptions.updateMany({
      where: {
        endpoint: subscription.endpoint
      },
      data: {
        isActive: false,
        unsubscribedAt: new Date()
      }
    });
    */

    // Remove from memory for demo (in production, use a database)
    if (typeof global.pushSubscriptions !== 'undefined') {
      const index = global.pushSubscriptions.findIndex(
        sub => sub.endpoint === subscription.endpoint
      );
      
      if (index >= 0) {
        // Mark as inactive instead of deleting (for analytics)
        global.pushSubscriptions[index].isActive = false;
        global.pushSubscriptions[index].unsubscribedAt = new Date().toISOString();
        console.log('Marked push subscription as inactive');
      } else {
        console.log('Push subscription not found');
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully unsubscribed from push notifications'
    });

  } catch (error) {
    console.error('Error handling push unsubscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process unsubscription',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Get unsubscription statistics (admin only)
 * GET /api/push-unsubscribe
 */
export async function GET(request) {
  try {
    // In a real app, you'd check admin permissions here
    
    const subscriptions = global.pushSubscriptions || [];
    const activeCount = subscriptions.filter(sub => sub.isActive).length;
    const inactiveCount = subscriptions.filter(sub => !sub.isActive).length;
    const totalCount = subscriptions.length;
    
    return NextResponse.json({
      statistics: {
        total: totalCount,
        active: activeCount,
        inactive: inactiveCount,
        unsubscribeRate: totalCount > 0 ? (inactiveCount / totalCount * 100).toFixed(2) : 0
      }
    });

  } catch (error) {
    console.error('Error getting unsubscription statistics:', error);
    
    return NextResponse.json(
      { error: 'Failed to get statistics' },
      { status: 500 }
    );
  }
}
