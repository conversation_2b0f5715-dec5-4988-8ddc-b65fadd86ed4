{"version": 3, "sources": ["../../../../src/server/future/route-modules/checks.ts"], "names": ["isAppPageRouteModule", "isAppRouteRouteModule", "isPagesAPIRouteModule", "isPagesRouteModule", "routeModule", "definition", "kind", "RouteKind", "APP_ROUTE", "APP_PAGE", "PAGES", "PAGES_API"], "mappings": ";;;;;;;;;;;;;;;;;IAegBA,oBAAoB;eAApBA;;IANAC,qBAAqB;eAArBA;;IAkBAC,qBAAqB;eAArBA;;IANAC,kBAAkB;eAAlBA;;;2BAdU;AAEnB,SAASF,sBACdG,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,SAAS;AAC5D;AAEO,SAASR,qBACdI,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACE,QAAQ;AAC3D;AAEO,SAASN,mBACdC,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACG,KAAK;AACxD;AAEO,SAASR,sBACdE,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACI,SAAS;AAC5D"}