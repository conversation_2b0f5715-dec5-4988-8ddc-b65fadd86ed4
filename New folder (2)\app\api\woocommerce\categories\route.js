import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function GET() {
  try {
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    // Get all categories
    const response = await api.get('products/categories', {
      per_page: 100,
      orderby: 'name',
      order: 'asc'
    });

    const categories = response.data.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      count: cat.count || 0,
      parent: cat.parent || 0
    }));

    return NextResponse.json({
      success: true,
      categories: categories,
      count: categories.length
    });

  } catch (error) {
    console.error('❌ Error fetching categories:', error);

    // Return empty array instead of fake categories - only show REAL categories
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch real categories from WordPress',
      categories: [],
      count: 0,
      message: 'Only real WordPress categories will be shown. Please check your WooCommerce API connection.'
    }, { status: 500 });
  }
}
