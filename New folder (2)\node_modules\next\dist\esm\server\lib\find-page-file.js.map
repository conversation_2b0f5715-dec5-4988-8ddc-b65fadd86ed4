{"version": 3, "sources": ["../../../src/server/lib/find-page-file.ts"], "names": ["fileExists", "getPagePaths", "nonNullable", "join", "sep", "normalize", "promises", "fsPromises", "warn", "cyan", "isMetadataRouteFile", "isTrueCasePagePath", "pagePath", "pagesDir", "pageSegments", "split", "filter", "Boolean", "segmentExistsPromises", "map", "segment", "i", "segmentParentDir", "slice", "parentDirEntries", "readdir", "includes", "Promise", "all", "every", "findPageFile", "normalizedPagePath", "pageExtensions", "isAppDir", "pagePaths", "existingPath", "others", "path", "filePath", "err", "code", "length", "createValidFileMatcher", "appDirPath", "getExtensionRegexString", "extensions", "validExtensionFileRegex", "RegExp", "leafOnlyPageFileRegex", "rootNotFoundFileRegex", "isMetadataFile", "appDirRelativePath", "replace", "isAppRouterPage", "test", "isPageFile", "isRootNotFound", "startsWith", "rest"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAuB;AAClD,SAASC,YAAY,QAAQ,4CAA2C;AACxE,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,OAAM;AAC3C,SAASC,YAAYC,UAAU,QAAQ,KAAI;AAC3C,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,mBAAmB,QAAQ,uCAAsC;AAG1E,eAAeC,mBAAmBC,QAAgB,EAAEC,QAAgB;IAClE,MAAMC,eAAeT,UAAUO,UAAUG,KAAK,CAACX,KAAKY,MAAM,CAACC;IAC3D,MAAMC,wBAAwBJ,aAAaK,GAAG,CAAC,OAAOC,SAASC;QAC7D,MAAMC,mBAAmBnB,KAAKU,aAAaC,aAAaS,KAAK,CAAC,GAAGF;QACjE,MAAMG,mBAAmB,MAAMjB,WAAWkB,OAAO,CAACH;QAClD,OAAOE,iBAAiBE,QAAQ,CAACN;IACnC;IAEA,OAAO,AAAC,CAAA,MAAMO,QAAQC,GAAG,CAACV,sBAAqB,EAAGW,KAAK,CAACZ;AAC1D;AAEA;;;;;;;;CAQC,GACD,OAAO,eAAea,aACpBjB,QAAgB,EAChBkB,kBAA0B,EAC1BC,cAA8B,EAC9BC,QAAiB;IAEjB,MAAMC,YAAYjC,aAAa8B,oBAAoBC,gBAAgBC;IACnE,MAAM,CAACE,cAAc,GAAGC,OAAO,GAAG,AAChC,CAAA,MAAMT,QAAQC,GAAG,CACfM,UAAUf,GAAG,CAAC,OAAOkB;QACnB,MAAMC,WAAWnC,KAAKU,UAAUwB;QAChC,IAAI;YACF,OAAO,AAAC,MAAMrC,WAAWsC,YAAaD,OAAO;QAC/C,EAAE,OAAOE,KAAU;gBACZA;YAAL,IAAI,EAACA,wBAAAA,YAAAA,IAAKC,IAAI,qBAATD,UAAWb,QAAQ,CAAC,aAAY,MAAMa;QAC7C;QACA,OAAO;IACT,GACF,EACAvB,MAAM,CAACd;IAET,IAAI,CAACiC,cAAc;QACjB,OAAO;IACT;IAEA,IAAI,CAAE,MAAMxB,mBAAmBwB,cAActB,WAAY;QACvD,OAAO;IACT;IAEA,IAAIuB,OAAOK,MAAM,GAAG,GAAG;QACrBjC,KACE,CAAC,yBAAyB,EAAEC,KAAKN,KAAK,SAASgC,eAAe,KAAK,EAAE1B,KACnEN,KAAK,SAASiC,MAAM,CAAC,EAAE,GACvB,iBAAiB,EAAE3B,KAAKsB,oBAAoB,CAAC,CAAC;IAEpD;IAEA,OAAOI;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASO,uBACdV,cAA8B,EAC9BW,UAA8B;IAE9B,MAAMC,0BAA0B,CAACC,aAC/B,CAAC,GAAG,EAAEA,WAAW1C,IAAI,CAAC,KAAK,CAAC,CAAC;IAE/B,MAAM2C,0BAA0B,IAAIC,OAClC,QAAQH,wBAAwBZ,kBAAkB;IAEpD,MAAMgB,wBAAwB,IAAID,OAChC,CAAC,sCAAsC,EAAEH,wBACvCZ,gBACA,CAAC,CAAC;IAEN,MAAMiB,wBAAwB,IAAIF,OAChC,CAAC,aAAa,EAAEH,wBAAwBZ,gBAAgB,CAAC,CAAC;IAE5D;;;;;;;;;;GAUC,GAED;;;GAGC,GACD,SAASkB,eAAeZ,QAAgB;QACtC,MAAMa,qBAAqBR,aACvBL,SAASc,OAAO,CAACT,YAAY,MAC7BL;QAEJ,OAAO5B,oBAAoByC,oBAAoBnB,gBAAgB;IACjE;IAEA,4EAA4E;IAC5E,2CAA2C;IAC3C,SAASqB,gBAAgBf,QAAgB;QACvC,OAAOU,sBAAsBM,IAAI,CAAChB,aAAaY,eAAeZ;IAChE;IAEA,SAASiB,WAAWjB,QAAgB;QAClC,OAAOQ,wBAAwBQ,IAAI,CAAChB,aAAaY,eAAeZ;IAClE;IAEA,SAASkB,eAAelB,QAAgB;QACtC,IAAI,CAACK,YAAY;YACf,OAAO;QACT;QACA,IAAI,CAACL,SAASmB,UAAU,CAACd,aAAavC,MAAM;YAC1C,OAAO;QACT;QACA,MAAMsD,OAAOpB,SAASf,KAAK,CAACoB,WAAWF,MAAM,GAAG;QAChD,OAAOQ,sBAAsBK,IAAI,CAACI;IACpC;IAEA,OAAO;QACLH;QACAF;QACAH;QACAM;IACF;AACF"}