@echo off
echo Creating cPanel deployment package...

REM Clean previous deployment
if exist cpanel-ready rmdir /s /q cpanel-ready
mkdir cpanel-ready

REM Copy Next.js build
echo Copying Next.js build files...
xcopy /E /I /Y .next cpanel-ready\.next

REM Copy application directories
echo Copying application files...
xcopy /E /I /Y app cpanel-ready\app
xcopy /E /I /Y components cpanel-ready\components
xcopy /E /I /Y context cpanel-ready\context
xcopy /E /I /Y lib cpanel-ready\lib
xcopy /E /I /Y public cpanel-ready\public

REM Copy configuration files
echo Copying configuration files...
copy /Y server.js cpanel-ready\
copy /Y package.json cpanel-ready\
copy /Y next.config.js cpanel-ready\
copy /Y tailwind.config.js cpanel-ready\
copy /Y postcss.config.js cpanel-ready\
copy /Y jsconfig.json cpanel-ready\

REM Create deployment instructions
echo Creating deployment instructions...
echo CPANEL DEPLOYMENT INSTRUCTIONS - DYNAMIC NEXT.JS APP > cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo =================================================== >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 🔄 REAL-TIME WOOCOMMERCE SYNC ENABLED >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo Your app will fetch fresh product data from WooCommerce on every page load! >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 1. CREATE NODE.JS APP IN CPANEL: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Go to cPanel → Node.js Apps >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Click "Create Application" >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Node.js Version: 18.x (or latest supported) >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Application Root: /public_html/your-app-name >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Application URL: your-domain.com/your-app-name >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Startup File: server.js >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 2. UPLOAD FILES: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Upload ALL files from this folder >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Make sure to upload to the Application Root directory >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Ensure .next folder is uploaded (contains Next.js build) >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 3. INSTALL DEPENDENCIES: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - In cPanel Node.js Apps, click on your app >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Go to "Package.json" tab >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Click "Run NPM Install" >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 4. SET ENVIRONMENT VARIABLES: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - NODE_ENV: production >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 5. START APPLICATION: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Click "Start App" in cPanel Node.js Apps >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Wait for "Application is running" status >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo. >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo 6. TEST YOUR DYNAMIC SYNC: >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Visit your application URL >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Add a new product to your WooCommerce store >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt
echo    - Refresh your website - new product should appear! >> cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt

echo.
echo ✅ Deployment package created successfully!
echo 📁 Files ready in: cpanel-ready folder
echo 🔄 Your app will sync with WooCommerce in real-time
echo 📖 Check DEPLOYMENT_INSTRUCTIONS.txt for next steps
pause
