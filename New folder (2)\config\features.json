{"liveChat": {"enabled": false, "name": "Live Chat Support", "description": "Real-time customer support with WhatsApp integration", "category": "Customer Support", "settings": {"whatsappNumber": "+***********", "autoResponse": true, "businessHours": "9:00-18:00"}}, "productRecommendations": {"enabled": true, "name": "Smart Product Recommendations", "description": "AI-powered \"You might also like\" suggestions", "category": "AI Features", "settings": {"algorithm": "collaborative_filtering", "maxRecommendations": 6, "confidenceThreshold": 0.7}}, "loyaltyProgram": {"enabled": true, "name": "Loyalty & Rewards Program", "description": "Points, VIP tiers, and exclusive benefits", "category": "Customer Engagement", "settings": {"pointsPerPound": 10, "vipThreshold": 500, "welcomeBonus": 100}}, "arTryOn": {"enabled": true, "name": "AR Try-On Experience", "description": "Virtual try-on for clothes and accessories", "category": "Advanced Features", "settings": {"supportedCategories": ["clothing", "accessories"], "quality": "high"}}, "voiceSearch": {"enabled": false, "name": "Voice Search", "description": "Search products using voice commands", "category": "Search & Discovery", "settings": {"language": "en-GB", "sensitivity": "medium"}}, "socialCommerce": {"enabled": true, "name": "Social Commerce", "description": "Instagram integration and social sharing", "category": "Social Features", "settings": {"platforms": ["instagram", "facebook", "twitter"], "autoPost": false}}, "pushNotifications": {"enabled": true, "name": "Push Notifications", "description": "Real-time notifications for deals and updates", "category": "Engagement", "settings": {"orderUpdates": true, "promotions": true, "backInStock": true}}, "oneClickReorder": {"enabled": true, "name": "<PERSON>-<PERSON><PERSON>", "description": "Quick reorder from purchase history", "category": "User Experience", "settings": {"maxHistoryItems": 20, "showInProfile": true}}, "advancedSearch": {"enabled": true, "name": "Advanced Search & Filters", "description": "Enhanced search with filters and suggestions", "category": "Search & Discovery", "settings": {"autoComplete": true, "searchHistory": true, "visualSearch": false}}, "mobilePayments": {"enabled": true, "name": "Mobile Payment Options", "description": "Apple Pay, Google Pay, and other mobile payments", "category": "Payments", "settings": {"applePay": true, "googlePay": true, "paypal": true}}, "inventoryAlerts": {"enabled": true, "name": "Inventory Alerts", "description": "Low stock and out of stock notifications", "category": "Inventory Management", "settings": {"lowStockThreshold": 10, "emailAlerts": true, "customerNotifications": true}}, "priceDropAlerts": {"enabled": false, "name": "Price Drop Alerts", "description": "Notify customers when prices drop on wishlist items", "category": "Customer Engagement", "settings": {"emailNotifications": true, "pushNotifications": true, "threshold": 10}}}