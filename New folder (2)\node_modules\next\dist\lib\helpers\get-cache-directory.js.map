{"version": 3, "sources": ["../../../src/lib/helpers/get-cache-directory.ts"], "names": ["getCacheDirectory", "fileDirectory", "envPath", "result", "systemCacheDirectory", "process", "platform", "env", "XDG_CACHE_HOME", "path", "join", "os", "homedir", "LOCALAPPDATA", "dir", "tmpdir", "fs", "existsSync", "console", "error", "Error", "exit", "isAbsolute", "resolve", "cwd"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;2DAND;6DACE;2DACF;;;;;;AAIR,SAASA,kBAAkBC,aAAqB,EAAEC,OAAgB;IACvE,IAAIC;IAEJ,IAAID,SAAS;QACXC,SAASD;IACX,OAAO;QACL,IAAIE;QACJ,IAAIC,QAAQC,QAAQ,KAAK,SAAS;YAChCF,uBACEC,QAAQE,GAAG,CAACC,cAAc,IAAIC,aAAI,CAACC,IAAI,CAACC,WAAE,CAACC,OAAO,IAAI;QAC1D,OAAO,IAAIP,QAAQC,QAAQ,KAAK,UAAU;YACxCF,uBAAuBK,aAAI,CAACC,IAAI,CAACC,WAAE,CAACC,OAAO,IAAI,WAAW;QAC5D,OAAO,IAAIP,QAAQC,QAAQ,KAAK,SAAS;YACvCF,uBACEC,QAAQE,GAAG,CAACM,YAAY,IAAIJ,aAAI,CAACC,IAAI,CAACC,WAAE,CAACC,OAAO,IAAI,WAAW;QACnE,OAAO;YACL,+DAA+D;YAC/D,IAAI,CAACR,sBAAsB;gBACzB,KAAK,MAAMU,OAAO;oBAChBL,aAAI,CAACC,IAAI,CAACC,WAAE,CAACC,OAAO,IAAI;oBACxBH,aAAI,CAACC,IAAI,CAACC,WAAE,CAACI,MAAM;iBACpB,CAAE;oBACD,IAAIC,WAAE,CAACC,UAAU,CAACH,MAAM;wBACtBV,uBAAuBU;wBACvB;oBACF;gBACF;YACF;YAEA,IAAI,CAACV,sBAAsB;gBACzBc,QAAQC,KAAK,CAAC,IAAIC,MAAM,2BAA2Bf,QAAQC,QAAQ;gBACnED,QAAQgB,IAAI,CAAC;YACf;QACF;QACAlB,SAASM,aAAI,CAACC,IAAI,CAACN,sBAAsBH;IAC3C;IAEA,IAAI,CAACQ,aAAI,CAACa,UAAU,CAACnB,SAAS;QAC5B,mDAAmD;QACnD,uCAAuC;QACvC,6EAA6E;QAC7E,yEAAyE;QACzE,gDAAgD;QAChDA,SAASM,aAAI,CAACc,OAAO,CAAClB,QAAQE,GAAG,CAAC,WAAW,IAAIF,QAAQmB,GAAG,IAAIrB;IAClE;IACA,OAAOA;AACT"}