{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["getFilenameAndExtension", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "createReExportsCode", "loaderContext", "exportNames", "getLoaderModuleNamedExports", "reExportNames", "filter", "name", "length", "join", "cacheHeader", "none", "longCache", "revalidate", "filename", "path", "basename", "ext", "split", "getContentType", "imageExtMimeTypeMap", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "fs", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "hasGenerateSiteMaps", "includes", "nextMetadataRouterLoader", "isDynamic", "filePath", "getOptions", "addDependency"], "mappings": ";;;;;;;;;;;;;;;IA0SA,OAAuC;eAAvC;;IAvPgBA,uBAAuB;eAAvBA;;;2DAlDD;6DACE;0BACmB;uBACQ;;;;;;AAE5C,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,wCAAwC,GACxC,eAAeG,oBACbH,YAAoB,EACpBI,aAAyC;IAEzC,MAAMC,cAAc,MAAMC,IAAAA,kCAA2B,EACnDN,cACAI;IAEF,iDAAiD;IACjD,MAAMG,gBAAgBF,YAAYG,MAAM,CACtC,CAACC,OAASA,SAAS,aAAaA,SAAS;IAG3C,OAAOF,cAAcG,MAAM,GAAG,IAC1B,CAAC,SAAS,EAAEH,cAAcI,IAAI,CAAC,MAAM,QAAQ,EAAEV,KAAKC,SAAS,CAC3DF,cACA,EAAE,CAAC,GACL;AACN;AAEA,MAAMY,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAUO,SAASjB,wBAAwBE,YAAoB;IAC1D,MAAMgB,WAAWC,aAAI,CAACC,QAAQ,CAAClB;IAC/B,MAAM,CAACS,MAAMU,IAAI,GAAGH,SAASI,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEX;QAAMU;IAAI;AACrB;AAEA,SAASE,eAAerB,YAAoB;IAC1C,IAAI,EAAES,IAAI,EAAEU,GAAG,EAAE,GAAGrB,wBAAwBE;IAC5C,IAAImB,QAAQ,OAAOA,MAAM;IAEzB,IAAIV,SAAS,aAAaU,QAAQ,OAAO,OAAO;IAChD,IAAIV,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIU,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOG,6BAAmB,CAACH,IAAI;IACjC;IACA,OAAO;AACT;AAEA,eAAeI,wBACbvB,YAAoB,EACpBwB,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBhB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMe,OAAO,CAAC;;;;oBAII,EAAE5B,KAAKC,SAAS,CAACmB,eAAerB,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAM4B,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAAChC,aAAY,EAAGiC,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAEhC,KAAKC,SAAS,CAACuB,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,eAAeK,wBACblC,YAAoB,EACpBI,aAAyC;IAEzC,OAAO,CAAC;;;oBAGU,EAAEH,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACmB,eAAerB,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcS,IAAI,EAAE;;AAE9E,EAAEV,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;;;;;;uBASlC,EAAEH,KAAKC,SAAS,CAACU,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,eAAeoB,yBACbnC,YAAoB,EACpBI,aAAyC;IAEzC,OAAO,CAAC;;;0BAGgB,EAAEH,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;AAyBzD,CAAC;AACD;AAEA,eAAegC,2BACbpC,YAAoB,EACpBqC,IAAY,EACZjC,aAAyC;IAEzC,IAAIkC,uBAAuB;IAE3B,MAAMjC,cAAc,MAAMC,IAAAA,kCAA2B,EACnDN,cACAI;IAGF,MAAMmC,sBAAsBlC,YAAYmC,QAAQ,CAAC;IACjD,IACEd,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBW,uBACAF,KAAKG,QAAQ,CAAC,sBACd;QACAF,uBAAuB,CAAC;;;;;;;;;;;IAWxB,CAAC;IACH;IAEA,MAAMT,OAAO,CAAC;;0BAEU,EAAE5B,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACmB,eAAerB,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcS,IAAI,EAAE;;AAE9E,EAAEV,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;EAIvD,EACE,GAAG,2FAA2F,IAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAkCoB,EAAEH,KAAKC,SAAS,CAACU,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEuB,qBAAqB;AACvB,CAAC;IACC,OAAOT;AACT;AAEA,gFAAgF;AAChF,oDAAoD;AACpD,MAAMY,2BACJ;IACE,MAAM,EAAEJ,IAAI,EAAEK,SAAS,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACC,UAAU;IACrD,MAAM,EAAEnC,MAAMe,YAAY,EAAE,GAAG1B,wBAAwB6C;IACvD,IAAI,CAACE,aAAa,CAACF;IAEnB,IAAId,OAAO;IACX,IAAIa,cAAc,KAAK;QACrB,IAAIlB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAO,MAAMK,wBAAwBS,UAAU,IAAI;QACrD,OAAO,IAAInB,iBAAiB,WAAW;YACrCK,OAAO,MAAMO,2BAA2BO,UAAUN,MAAM,IAAI;QAC9D,OAAO;YACLR,OAAO,MAAMM,yBAAyBQ,UAAU,IAAI;QACtD;IACF,OAAO;QACLd,OAAO,MAAMN,wBAAwBoB,UAAUnB;IACjD;IAEA,OAAOK;AACT;MAEF,WAAeY"}