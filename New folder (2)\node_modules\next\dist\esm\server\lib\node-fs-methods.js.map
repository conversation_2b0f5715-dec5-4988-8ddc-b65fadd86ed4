{"version": 3, "sources": ["../../../src/server/lib/node-fs-methods.ts"], "names": ["fs", "nodeFs", "existsSync", "readFile", "promises", "readFileSync", "writeFile", "f", "d", "mkdir", "dir", "recursive", "stat"], "mappings": "AAEA,OAAOA,QAAQ,KAAI;AAEnB,OAAO,MAAMC,SAAkB;IAC7BC,YAAYF,GAAGE,UAAU;IACzBC,UAAUH,GAAGI,QAAQ,CAACD,QAAQ;IAC9BE,cAAcL,GAAGK,YAAY;IAC7BC,WAAW,CAACC,GAAGC,IAAMR,GAAGI,QAAQ,CAACE,SAAS,CAACC,GAAGC;IAC9CC,OAAO,CAACC,MAAQV,GAAGI,QAAQ,CAACK,KAAK,CAACC,KAAK;YAAEC,WAAW;QAAK;IACzDC,MAAM,CAACL,IAAMP,GAAGI,QAAQ,CAACQ,IAAI,CAACL;AAChC,EAAC"}