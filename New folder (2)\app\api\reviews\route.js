import { NextResponse } from 'next/server';
import { 
  getProductReviews, 
  getProductReviewStats, 
  submitReview,
  searchReviews,
  getRecentReviews
} from '@/lib/reviews';

/**
 * GET /api/reviews - Get reviews with various filters
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const sortBy = searchParams.get('sortBy') || 'newest';
    const filterRating = searchParams.get('rating');
    const query = searchParams.get('q');
    const recent = searchParams.get('recent');

    // Get recent reviews for homepage
    if (recent === 'true') {
      const recentReviews = await getRecentReviews(parseInt(recent) || 5);
      return NextResponse.json({
        success: true,
        reviews: recentReviews
      });
    }

    // Search reviews
    if (query) {
      const searchResults = await searchReviews(query, {
        productId,
        minRating: searchParams.get('minRating'),
        maxRating: searchParams.get('maxRating')
      });
      
      return NextResponse.json({
        success: true,
        reviews: searchResults,
        total: searchResults.length
      });
    }

    // Get product reviews
    if (productId) {
      const result = await getProductReviews(productId, {
        page,
        limit,
        sortBy,
        filterRating
      });

      // Also get review statistics
      const stats = await getProductReviewStats(productId);

      return NextResponse.json({
        success: true,
        ...result,
        stats
      });
    }

    return NextResponse.json(
      { error: 'Product ID is required' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in GET /api/reviews:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch reviews',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reviews - Submit a new review
 */
export async function POST(request) {
  try {
    const reviewData = await request.json();
    
    // Validate required fields
    const { productId, userId, userName, rating, comment } = reviewData;
    
    if (!productId || !userId || !userName || !rating || !comment) {
      return NextResponse.json(
        { error: 'Missing required fields: productId, userId, userName, rating, comment' },
        { status: 400 }
      );
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    if (comment.length < 10) {
      return NextResponse.json(
        { error: 'Comment must be at least 10 characters long' },
        { status: 400 }
      );
    }

    if (comment.length > 1000) {
      return NextResponse.json(
        { error: 'Comment must be less than 1000 characters' },
        { status: 400 }
      );
    }

    // Submit the review
    const newReview = await submitReview(reviewData);

    return NextResponse.json({
      success: true,
      message: 'Review submitted successfully',
      review: newReview
    }, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/reviews:', error);
    
    if (error.message === 'You have already reviewed this product') {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to submit review',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
