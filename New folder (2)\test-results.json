{"passed": 50, "failed": 1, "tests": [{"name": "File exists: app/admin/dashboard/page.js", "status": true, "details": ""}, {"name": "File exists: app/api/admin/dashboard-stats/route.js", "status": true, "details": ""}, {"name": "File exists: app/api/admin/toggle-feature/route.js", "status": true, "details": ""}, {"name": "File exists: app/api/woocommerce-cart/route.js", "status": true, "details": ""}, {"name": "File exists: lib/featuresManager.js", "status": true, "details": ""}, {"name": "File exists: lib/woocommerceFeatures.js", "status": true, "details": ""}, {"name": "File exists: components/ClientScripts.js", "status": true, "details": ""}, {"name": "File exists: .env.local", "status": true, "details": ""}, {"name": "WooCommerce URL configured", "status": true, "details": ""}, {"name": "Consumer Key configured", "status": true, "details": ""}, {"name": "Consumer Secret configured", "status": true, "details": ""}, {"name": "Environment configuration complete", "status": true, "details": ""}, {"name": "Dependency: @woocommerce/woocommerce-rest-api", "status": "^1.0.1", "details": ""}, {"name": "Dependency: axios", "status": "^1.7.9", "details": ""}, {"name": "Dependency: next", "status": "^14.2.30", "details": ""}, {"name": "Dependency: react", "status": "^18.2.0", "details": ""}, {"name": "Dependency: lucide-react", "status": "^0.292.0", "details": ""}, {"name": "Package.json structure valid", "status": true, "details": ""}, {"name": "Admin authentication system", "status": true, "details": ""}, {"name": "Feature toggle functionality", "status": true, "details": ""}, {"name": "Dashboard statistics integration", "status": false, "details": ""}, {"name": "Secure login with OTP", "status": true, "details": ""}, {"name": "Product recommendations integration", "status": true, "details": ""}, {"name": "Loyalty program integration", "status": true, "details": ""}, {"name": "One-click reorder integration", "status": true, "details": ""}, {"name": "Inventory alerts integration", "status": true, "details": ""}, {"name": "Cart management integration", "status": true, "details": ""}, {"name": "Customer analytics integration", "status": true, "details": ""}, {"name": "Live chat feature", "status": true, "details": ""}, {"name": "Product recommendations feature", "status": true, "details": ""}, {"name": "Loyalty program feature", "status": true, "details": ""}, {"name": "Push notifications feature", "status": true, "details": ""}, {"name": "Social commerce feature", "status": true, "details": ""}, {"name": "One-click reorder feature", "status": true, "details": ""}, {"name": "Voice search feature", "status": true, "details": ""}, {"name": "AR try-on feature", "status": true, "details": ""}, {"name": "WooCommerce integration in features", "status": true, "details": ""}, {"name": "app/api/admin/dashboard-stats/route.js - Structure", "status": true, "details": ""}, {"name": "app/api/admin/dashboard-stats/route.js - Error handling", "status": true, "details": ""}, {"name": "app/api/admin/toggle-feature/route.js - Structure", "status": true, "details": ""}, {"name": "app/api/admin/toggle-feature/route.js - Error handling", "status": true, "details": ""}, {"name": "app/api/woocommerce-cart/route.js - Structure", "status": true, "details": ""}, {"name": "app/api/woocommerce-cart/route.js - Error handling", "status": true, "details": ""}, {"name": "app/api/sync-wordpress-products/route.js - Structure", "status": true, "details": ""}, {"name": "app/api/sync-wordpress-products/route.js - Error handling", "status": true, "details": ""}, {"name": "Features manager client integration", "status": true, "details": ""}, {"name": "Admin access integration", "status": true, "details": ""}, {"name": "Keyboard shortcut for admin", "status": true, "details": ""}, {"name": "Config directory structure ready", "status": true, "details": "Will be created automatically"}, {"name": "Default features configuration", "status": true, "details": ""}, {"name": "Feature storage system", "status": true, "details": ""}]}