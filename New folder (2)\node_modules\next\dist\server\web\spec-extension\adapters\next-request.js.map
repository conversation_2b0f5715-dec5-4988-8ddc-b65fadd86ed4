{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/next-request.ts"], "names": ["NextRequestAdapter", "ResponseAborted", "ResponseAbortedName", "createAbortController", "signalFromNodeResponse", "Error", "name", "response", "controller", "AbortController", "once", "writableFinished", "abort", "errored", "destroyed", "AbortSignal", "signal", "fromBaseNextRequest", "request", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "getRequestMeta", "NextRequest", "headers", "fromNodeOutgoingHttpHeaders", "duplex", "aborted"], "mappings": ";;;;;;;;;;;;;;;;;;IAsDaA,kBAAkB;eAAlBA;;IA5CAC,eAAe;eAAfA;;IADAC,mBAAmB;eAAnBA;;IAYGC,qBAAqB;eAArBA;;IAuBAC,sBAAsB;eAAtBA;;;6BAvCe;uBACa;yBAChB;AAErB,MAAMF,sBAAsB;AAC5B,MAAMD,wBAAwBI;;;aACnBC,OAAOJ;;AACzB;AASO,SAASC,sBAAsBI,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIX;IACvB;IAEA,OAAOO;AACT;AAUO,SAASJ,uBAAuBG,QAAkB;IACvD,MAAM,EAAEM,OAAO,EAAEC,SAAS,EAAE,GAAGP;IAC/B,IAAIM,WAAWC,WAAW;QACxB,OAAOC,YAAYH,KAAK,CAACC,WAAW,IAAIZ;IAC1C;IAEA,MAAM,EAAEe,MAAM,EAAE,GAAGb,sBAAsBI;IACzC,OAAOS;AACT;AAEO,MAAMhB;IACX,OAAciB,oBACZC,OAAwB,EACxBF,MAAmB,EACN;QACb,oCAAoC;QACpC,IAAI,aAAaE,WAAW,AAACA,QAA2BA,OAAO,EAAE;YAC/D,OAAOlB,mBAAmBmB,kBAAkB,CAACD;QAC/C;QAEA,OAAOlB,mBAAmBoB,mBAAmB,CAC3CF,SACAF;IAEJ;IAEA,OAAcI,oBACZF,OAAwB,EACxBF,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIK,OAAwB;QAC5B,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,UAAUJ,QAAQG,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAOH,QAAQG,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIL,QAAQK,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIP,QAAQK,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAOC,IAAAA,2BAAc,EAACT,SAAS;YACrC,IAAI,CAACQ,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIP,QAAQK,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAIE,oBAAW,CAACL,KAAK;YAC1BD,QAAQJ,QAAQI,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACZ,QAAQW,OAAO;YACpD,mEAAmE;YACnEE,QAAQ;YACRf;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOgB,OAAO,GACd,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;IAEA,OAAcF,mBAAmBD,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIG,OAA8B;QAClC,IAAIH,QAAQI,MAAM,KAAK,SAASJ,QAAQI,MAAM,KAAK,QAAQ;YACzDD,OAAOH,QAAQG,IAAI;QACrB;QAEA,OAAO,IAAIO,oBAAW,CAACV,QAAQK,GAAG,EAAE;YAClCD,QAAQJ,QAAQI,MAAM;YACtBO,SAASC,IAAAA,kCAA2B,EAACZ,QAAQW,OAAO;YACpD,mEAAmE;YACnEE,QAAQ;YACRf,QAAQE,QAAQA,OAAO,CAACF,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIE,QAAQA,OAAO,CAACF,MAAM,CAACgB,OAAO,GAC9B,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;AACF"}