{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/minify.ts"], "names": ["LightningCssMinifyPlugin", "PLUGIN_NAME", "CSS_FILE_REG", "constructor", "opts", "implementation", "otherOpts", "transformCss", "TypeError", "transform", "options", "apply", "compiler", "meta", "JSON", "stringify", "name", "version", "hooks", "compilation", "tap", "chunkHash", "_", "hash", "update", "processAssets", "tapPromise", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "additionalAssets", "transformAssets", "statsPrinter", "print", "for", "minimized", "green", "formatFlag", "undefined", "devtool", "loadBindings", "require", "css", "lightning", "sourcemap", "sourceMap", "includes", "include", "exclude", "test", "testRegExp", "targets", "userTargets", "transformOptions", "assets", "getAssets", "filter", "asset", "info", "ModuleFilenameHelpers", "matchObject", "Promise", "all", "map", "source", "sourceAndMap", "sourceAsString", "toString", "code", "<PERSON><PERSON><PERSON>", "from", "getTargets", "key", "<PERSON><PERSON><PERSON><PERSON>", "minify", "result", "filename", "codeString", "updateAsset", "SourceMapSource", "parse", "RawSource"], "mappings": "AAAA,aAAa;;;;;+BAaAA;;;eAAAA;;;yBAZyB;iCAGK;2BACjB;uBAEC;wBACJ;AAEvB,MAAMC,cAAc;AACpB,MAAMC,eAAe;AAEd,MAAMF;IAIXG,YAAYC,OAAY,CAAC,CAAC,CAAE;QAC1B,MAAM,EAAEC,cAAc,EAAE,GAAGC,WAAW,GAAGF;QACzC,IAAIC,kBAAkB,OAAOA,eAAeE,YAAY,KAAK,YAAY;YACvE,MAAM,IAAIC,UACR,CAAC,+GAA+G,EAAE,OAAOH,eAAeE,YAAY,CAAC,CAAC;QAE1J;QAEA,IAAI,CAACE,SAAS,GAAGJ,kCAAAA,eAAgBE,YAAY;QAC7C,IAAI,CAACG,OAAO,GAAGJ;IACjB;IAEAK,MAAMC,QAAkB,EAAE;QACxB,MAAMC,OAAOC,KAAKC,SAAS,CAAC;YAC1BC,MAAM;YACNC,SAAS;YACTP,SAAS,IAAI,CAACA,OAAO;QACvB;QAEAE,SAASM,KAAK,CAACC,WAAW,CAACC,GAAG,CAACnB,aAAa,CAACkB;YAC3CA,YAAYD,KAAK,CAACG,SAAS,CAACD,GAAG,CAACnB,aAAa,CAACqB,GAAGC,OAC/CA,KAAKC,MAAM,CAACX;YAGdM,YAAYD,KAAK,CAACO,aAAa,CAACC,UAAU,CACxC;gBACEV,MAAMf;gBACN0B,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;gBAC7DC,kBAAkB;YACpB,GACA,UAAY,MAAM,IAAI,CAACC,eAAe,CAACb;YAGzCA,YAAYD,KAAK,CAACe,YAAY,CAACb,GAAG,CAACnB,aAAa,CAACgC;gBAC/CA,aAAaf,KAAK,CAACgB,KAAK,CACrBC,GAAG,CAAC,uBACL,aAAa;iBACZf,GAAG,CAACnB,aAAa,CAACmC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;oBACjD,aAAa;oBACb,OAAOF,YAAYC,MAAMC,WAAW,gBAAgBC;gBACtD;YACJ;QACF;IACF;IAEA,MAAcP,gBAAgBb,WAAwB,EAAiB;QACrE,MAAM,EACJT,SAAS,EAAE8B,OAAO,EAAE,EACrB,GAAGrB,YAAYP,QAAQ;QAExB,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;YACnB,MAAM,EAAEgC,YAAY,EAAE,GAAGC,QAAQ;YACjC,IAAI,CAACjC,SAAS,GAAG,AAAC,CAAA,MAAMgC,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACnC,SAAS;QACjE;QAEA,MAAMoC,YACJ,IAAI,CAACnC,OAAO,CAACoC,SAAS,KAAKP,YACrBC,WAAW,AAACA,QAAmBO,QAAQ,CAAC,gBAC1C,IAAI,CAACrC,OAAO,CAACoC,SAAS;QAE5B,MAAM,EACJE,OAAO,EACPC,OAAO,EACPC,MAAMC,UAAU,EAChBC,SAASC,WAAW,EACpB,GAAGC,kBACJ,GAAG,IAAI,CAAC5C,OAAO;QAEhB,MAAM6C,SAASpC,YAAYqC,SAAS,GAAGC,MAAM,CAC3C,CAACC,QACC,+BAA+B;YAC/B,CAACA,MAAMC,IAAI,CAACvB,SAAS,IAErB,AADA,0BAA0B;YACzBe,CAAAA,cAAcjD,YAAW,EAAGgD,IAAI,CAACQ,MAAM1C,IAAI,KAC5C4C,8BAAqB,CAACC,WAAW,CAAC;gBAAEb;gBAASC;YAAQ,GAAGS,MAAM1C,IAAI;QAGtE,MAAM8C,QAAQC,GAAG,CACfR,OAAOS,GAAG,CAAC,OAAON;YAChB,MAAM,EAAEO,MAAM,EAAED,GAAG,EAAE,GAAGN,MAAMO,MAAM,CAACC,YAAY;YACjD,MAAMC,iBAAiBF,OAAOG,QAAQ;YACtC,MAAMC,OAAO,OAAOJ,WAAW,WAAWK,cAAM,CAACC,IAAI,CAACN,UAAUA;YAChE,MAAMb,UAAUoB,IAAAA,iBAAU,EAAC;gBACzBpB,SAASC;gBACToB,KAAKC,oBAAS,CAACC,MAAM;YACvB;YAEA,MAAMC,SAAS,MAAM,IAAI,CAACnE,SAAS,CAAE;gBACnCoE,UAAUnB,MAAM1C,IAAI;gBACpBqD;gBACAM,QAAQ;gBACR7B,WAAWD;gBACXO;gBACA,GAAGE,gBAAgB;YACrB;YACA,MAAMwB,aAAaF,OAAOP,IAAI,CAACD,QAAQ;YAEvCjD,YAAY4D,WAAW,CACrBrB,MAAM1C,IAAI,EACV,aAAa;YACb6B,YACI,IAAImC,gCAAe,CACjBF,YACApB,MAAM1C,IAAI,EACVF,KAAKmE,KAAK,CAACL,OAAOZ,GAAG,CAAEI,QAAQ,KAC/BD,gBACAH,KACA,QAEF,IAAIkB,0BAAS,CAACJ,aAClB;gBACE,GAAGpB,MAAMC,IAAI;gBACbvB,WAAW;YACb;QAEJ;IAEJ;AACF"}