// Dynamic Next.js server for cPanel hosting
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOST || 'localhost';
const port = process.env.PORT || 3000;

console.log('🚀 Starting Dynamic Next.js server...');
console.log('💡 Full API and WooCommerce integration enabled');

// Initialize Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      // Set proper headers for cPanel compatibility
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.setHeader('X-Powered-By', 'Next.js');

      // Parse the URL
      const parsedUrl = parse(req.url, true);
      const { pathname, query } = parsedUrl;

      // Health check endpoint for cPanel
      if (pathname === '/health' || pathname === '/status') {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        res.end(JSON.stringify({
          status: 'OK',
          timestamp: new Date().toISOString(),
          app: 'Deal4u Next.js'
        }));
        return;
      }

      // WordPress paths - let Apache handle these
      const wpPaths = ['/wp-admin', '/wp-content', '/wp-includes', '/wp-json', '/wp-login.php'];
      if (wpPaths.some(path => pathname.startsWith(path))) {
        res.statusCode = 404;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.end('<!DOCTYPE html><html><head><title>404</title></head><body><h1>WordPress path - should be handled by Apache</h1></body></html>');
        return;
      }

      // Ensure static assets are served properly
      if (pathname.startsWith('/_next/') || pathname.startsWith('/static/')) {
        // Let Next.js handle static files
        await handle(req, res, parsedUrl);
        return;
      }

      // Let Next.js handle everything else
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.end('<!DOCTYPE html><html><head><title>Error</title></head><body><h1>Internal server error</h1></body></html>');
    }
  })
    .once('error', (err) => {
      console.error('❌ Server error:', err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(`✅ Next.js server ready on http://${hostname}:${port}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'production'}`);
      console.log(`🔧 Node.js version: ${process.version}`);
      console.log(`💾 Memory usage:`, process.memoryUsage());
    });
});
