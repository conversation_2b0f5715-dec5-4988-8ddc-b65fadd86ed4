#!/usr/bin/env node

/**
 * cPanel Deployment Troubleshooting Script
 * This script helps diagnose common deployment issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 cPanel Deployment Troubleshooting\n');

const issues = [];
const warnings = [];

// Check 1: Package.json validation
console.log('1. Checking package.json...');
if (fs.existsSync('package.json')) {
    try {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        // Check main field
        if (!pkg.main) {
            issues.push('package.json missing "main" field - should be "server.js"');
        } else if (pkg.main !== 'server.js') {
            warnings.push(`package.json main field is "${pkg.main}" - should be "server.js" for cPanel`);
        }
        
        // Check start script
        if (!pkg.scripts || !pkg.scripts.start) {
            issues.push('package.json missing "start" script');
        } else if (pkg.scripts.start !== 'node server.js') {
            warnings.push(`Start script is "${pkg.scripts.start}" - should be "node server.js"`);
        }
        
        // Check Node.js version
        if (!pkg.engines || !pkg.engines.node) {
            warnings.push('No Node.js version specified in engines field');
        }
        
        // Check for production dependencies
        const prodDeps = Object.keys(pkg.dependencies || {});
        const requiredDeps = ['express', 'compression', 'helmet'];
        const missingDeps = requiredDeps.filter(dep => !prodDeps.includes(dep));
        
        if (missingDeps.length > 0) {
            issues.push(`Missing required dependencies: ${missingDeps.join(', ')}`);
        }
        
        console.log('✅ package.json structure looks good');
    } catch (error) {
        issues.push('package.json is invalid JSON');
    }
} else {
    issues.push('package.json not found');
}

// Check 2: Server.js validation
console.log('2. Checking server.js...');
if (fs.existsSync('server.js')) {
    const serverContent = fs.readFileSync('server.js', 'utf8');
    
    // Check for hardcoded localhost
    if (serverContent.includes('127.0.0.1') || serverContent.includes('localhost')) {
        warnings.push('server.js contains hardcoded localhost/127.0.0.1 - use 0.0.0.0 for cPanel');
    }
    
    // Check for environment port usage
    if (!serverContent.includes('process.env.PORT')) {
        issues.push('server.js should use process.env.PORT for cPanel compatibility');
    }
    
    // Check for required modules
    const requiredModules = ['express', 'path', 'compression', 'helmet'];
    const missingModules = requiredModules.filter(mod => !serverContent.includes(`require('${mod}')`));
    
    if (missingModules.length > 0) {
        warnings.push(`server.js missing imports: ${missingModules.join(', ')}`);
    }
    
    console.log('✅ server.js structure looks good');
} else {
    issues.push('server.js not found - required for cPanel Node.js apps');
}

// Check 3: Next.js configuration
console.log('3. Checking Next.js configuration...');
if (fs.existsSync('next.config.js')) {
    const configContent = fs.readFileSync('next.config.js', 'utf8');
    
    if (!configContent.includes('output: \'export\'')) {
        issues.push('next.config.js missing output: "export" for static deployment');
    }
    
    if (!configContent.includes('trailingSlash: true')) {
        warnings.push('Consider adding trailingSlash: true for better cPanel compatibility');
    }
    
    console.log('✅ Next.js config looks good');
} else {
    warnings.push('next.config.js not found - may be needed for proper static export');
}

// Check 4: Build output
console.log('4. Checking build output...');
if (fs.existsSync('out')) {
    const outFiles = fs.readdirSync('out');
    
    if (!outFiles.includes('index.html')) {
        issues.push('Build output missing index.html - run "npm run build" first');
    }
    
    if (!outFiles.includes('_next')) {
        warnings.push('Build output missing _next directory - may indicate build issues');
    }
    
    console.log('✅ Build output exists');
} else {
    issues.push('Build output directory "out" not found - run "npm run build" first');
}

// Check 5: File permissions and structure
console.log('5. Checking file structure...');
const requiredFiles = ['package.json', 'server.js'];
const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));

if (missingFiles.length > 0) {
    issues.push(`Missing required files: ${missingFiles.join(', ')}`);
}

// Check 6: Node modules
console.log('6. Checking dependencies...');
if (!fs.existsSync('node_modules')) {
    warnings.push('node_modules not found - run "npm install" before deployment');
} else {
    console.log('✅ Dependencies installed');
}

// Report results
console.log('\n' + '='.repeat(50));
console.log('TROUBLESHOOTING RESULTS');
console.log('='.repeat(50));

if (issues.length === 0 && warnings.length === 0) {
    console.log('🎉 No issues found! Your app should deploy successfully to cPanel.');
} else {
    if (issues.length > 0) {
        console.log('\n❌ CRITICAL ISSUES (must fix):');
        issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    if (warnings.length > 0) {
        console.log('\n⚠️  WARNINGS (recommended to fix):');
        warnings.forEach((warning, index) => {
            console.log(`   ${index + 1}. ${warning}`);
        });
    }
}

console.log('\n📖 For detailed deployment instructions, see CPANEL_DEPLOYMENT_GUIDE.md');
console.log('🚀 To prepare deployment files, run: node deploy-to-cpanel.js');

// Exit with error code if critical issues found
if (issues.length > 0) {
    process.exit(1);
}
