'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart, Trash2, X, AlertCircle } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';

export default function WishlistPage() {
  const { addToCart } = useCart();
  const { user } = useAuth();
  const [wishlistItems, setWishlistItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [countdownTime, setCountdownTime] = useState('');
  const timerRef = useRef(null);

  // Simulated fetch of wishlist items
  // Countdown timer logic
  useEffect(() => {
    // Set initial countdown (5 hours from now)
    const updateCountdown = () => {
      const hours = 4;
      const minutes = 59;
      const seconds = Math.floor(Math.random() * 60); // Random seconds for demo
      setCountdownTime(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };
    
    // Initialize countdown
    updateCountdown();
    
    // In a real app, you would actually count down each second
    timerRef.current = setInterval(updateCountdown, 30000); // Update every 30 seconds for demo
    
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  // Fetch wishlist items
  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      // In a real app, you would fetch from API or localStorage
      // For now, we'll use mock data
      const mockWishlistItems = [
        {
          id: 'prod-001',
          name: 'Wireless Bluetooth Earbuds',
          price: 59.99,
          originalPrice: 89.99,
          discount: 33,
          image: '/images/products/earbuds.jpg',
          rating: 4.5,
          reviews: 128,
          inStock: true,
        },
        {
          id: 'prod-002',
          name: 'Smart Fitness Tracker Watch',
          price: 79.99,
          originalPrice: 129.99,
          discount: 38,
          image: '/images/products/watch.jpg',
          rating: 4.2,
          reviews: 95,
          inStock: true,
        },
        {
          id: 'prod-003',
          name: 'Ultra HD 4K Action Camera',
          price: 129.99,
          originalPrice: 199.99,
          discount: 35,
          image: '/images/products/camera.jpg',
          rating: 4.7,
          reviews: 210,
          inStock: false,
        },
      ];
      
      setWishlistItems(mockWishlistItems);
      setIsLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  const handleRemoveFromWishlist = (productId) => {
    setWishlistItems(wishlistItems.filter(item => item.id !== productId));
    toast.success('Item removed from wishlist');
  };

  const handleAddToCart = (product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    });
    toast.success('Added to cart');
  };

  const handleClearWishlist = () => {
    setWishlistItems([]);
    toast.success('Wishlist cleared');
  };

  // Empty state
  if (!isLoading && wishlistItems.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl shadow-sm p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-6">
            <div className="w-24 h-24 rounded-full bg-blue-50 flex items-center justify-center">
              <Heart className="w-12 h-12 text-blue-500" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Your Wishlist is Empty</h1>
            <p className="text-gray-500 max-w-md mx-auto">
              Items added to your wishlist will be saved here. Start exploring to add products you love!
            </p>
            <Link 
              href="/products" 
              className="mt-6 inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Explore Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Page Header */}
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
          <p className="mt-1 text-gray-500">Save your favorite items to purchase later</p>
        </div>
        
        {wishlistItems.length > 0 && (
          <button
            onClick={handleClearWishlist}
            className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Clear Wishlist
          </button>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading your wishlist...</p>
        </div>
      )}

      {/* Enhanced Promotional Banner */}
      {!isLoading && wishlistItems.length > 0 && (
        <div className="mb-10 relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 p-8 shadow-xl">
          {/* Animated dashed border */}
          <div className="absolute -inset-1 rounded-xl border-2 border-dashed border-yellow-400 animate-spin-slow z-10 opacity-70"></div>
          
          {/* Special offer badge */}
          <div className="absolute -top-6 -right-6 z-20">
            <div className="bg-yellow-400 text-purple-900 font-extrabold px-4 py-2 rounded-full transform rotate-12 shadow-xl animate-bounce-slow">
              <span className="text-sm md:text-base tracking-wide uppercase">50% OFF TODAY</span>
            </div>
          </div>
          
          {/* Sale timer - Client-side rendered */}
          {countdownTime && (
            <div className="absolute top-3 left-3 bg-white/20 backdrop-blur-sm px-3 py-1 rounded-lg text-white text-xs font-mono z-20">
              LIMITED TIME OFFER: {countdownTime}
            </div>
          )}
          
          <div className="relative z-10 py-2">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
              <div className="flex-1">
                <h3 className="text-2xl md:text-3xl font-extrabold text-white mb-2">
                  <span className="relative">
                    Summer Sale 
                    <span className="absolute -top-1 -right-4 text-xs bg-red-500 text-white px-1 rounded">HOT</span>
                  </span>
                </h3>
                <p className="text-blue-100 mb-3 text-lg max-w-md">
                  Items in your wishlist are in <span className="font-bold underline decoration-yellow-400 decoration-wavy decoration-2 underline-offset-2">high demand</span>. 
                  Complete your purchase now before they sell out!
                </p>
                <div className="flex flex-wrap gap-3 mb-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Free Shipping
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Extra 10% Off Bundle
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Gift Wrapping
                  </span>
                </div>
                <button 
                  onClick={() => wishlistItems.forEach(item => handleAddToCart(item))}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-bold rounded-lg shadow-lg text-purple-900 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 transform hover:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 group"
                >
                  <ShoppingCart className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                  Add All to Cart
                </button>
              </div>
              
              <div className="hidden md:block relative">
                <div className="absolute -inset-4 bg-yellow-500 opacity-20 blur-xl rounded-full animate-pulse-slow"></div>
                <div className="relative">
                  <div className="text-center bg-white/20 backdrop-blur-sm p-4 rounded-lg">
                    <div className="text-5xl font-black text-white mb-1">SAVE</div>
                    <div className="text-3xl font-extrabold text-yellow-400">50%</div>
                    <div className="text-sm text-white mt-1">Use code: SUMMER50</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Enhanced decorative elements */}
          <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white opacity-10 rounded-full animate-pulse-slow"></div>
          <div className="absolute top-1/2 right-12 w-16 h-16 bg-yellow-400 opacity-10 rounded-full animate-ping"></div>
          <div className="absolute top-12 left-1/2 w-20 h-20 bg-purple-500 opacity-10 rounded-full animate-pulse-slow"></div>
          
          {/* Layered background effects */}
          <div className="absolute inset-0 bg-blue-600 opacity-20 mix-blend-overlay"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-transparent to-blue-900/50"></div>
        </div>
      )}

      {/* Wishlist Items */}
      {!isLoading && wishlistItems.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <ul className="divide-y divide-gray-200">
            {wishlistItems.map((product) => (
              <li key={product.id} className="p-4 sm:p-6 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col sm:flex-row">
                  {/* Product Image */}
                  <div className="flex-shrink-0 relative w-full sm:w-32 h-32 mb-4 sm:mb-0">
                    <div className="rounded-lg overflow-hidden bg-gray-200 h-full w-full relative">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                      {!product.inStock && (
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                          <span className="text-white font-medium text-sm px-2 py-1 bg-gray-900 bg-opacity-70 rounded">
                            Out of Stock
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Product Details */}
                  <div className="flex-1 sm:ml-6 flex flex-col">
                    <div className="flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          <Link href={`/products/${product.id}`} className="hover:text-blue-600">
                            {product.name}
                          </Link>
                        </h3>
                        {/* Price */}
                        <div className="mt-1 flex items-center">
                          <span className="text-lg font-bold text-gray-900">${product.price.toFixed(2)}</span>
                          {product.originalPrice && (
                            <>
                              <span className="ml-2 text-sm text-gray-500 line-through">
                                ${product.originalPrice.toFixed(2)}
                              </span>
                              <span className="ml-2 text-sm font-medium text-green-600">
                                {product.discount}% off
                              </span>
                            </>
                          )}
                        </div>
                        {/* Rating */}
                        <div className="mt-1 flex items-center">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <svg
                                key={i}
                                className={`h-4 w-4 ${
                                  i < Math.floor(product.rating) 
                                    ? 'text-yellow-400' 
                                    : i < product.rating 
                                      ? 'text-yellow-300' 
                                      : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <span className="ml-1 text-sm text-gray-500">
                            ({product.reviews} reviews)
                          </span>
                        </div>
                      </div>
                      
                      {/* Remove button */}
                      <button
                        onClick={() => handleRemoveFromWishlist(product.id)}
                        className="text-gray-400 hover:text-gray-500"
                        aria-label="Remove from wishlist"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    
                    {/* Stock Status and Actions */}
                    <div className="mt-4 sm:mt-auto flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
                      {!product.inStock ? (
                        <div className="flex items-center text-orange-600">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          <span className="text-sm">Currently unavailable</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-green-600">
                          <span className="inline-block h-2 w-2 rounded-full bg-green-600 mr-2"></span>
                          <span className="text-sm">In stock</span>
                        </div>
                      )}
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleAddToCart(product)}
                          disabled={!product.inStock}
                          className={`inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm ${
                            product.inStock
                              ? 'text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          <ShoppingCart className="mr-1 h-4 w-4" />
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Back to shopping link */}
      <div className="mt-8 text-center">
        <Link 
          href="/products" 
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          Continue Shopping
          <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </Link>
      </div>
    </div>
  );
}
