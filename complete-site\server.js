require('dotenv').config()
const express = require('express')
const path = require('path')
const compression = require('compression')
const helmet = require('helmet')
const fs = require('fs')

const dev = process.env.NODE_ENV !== 'production'
const hostname = process.env.HOST || 'localhost'
const port = process.env.PORT || 3000
const app = express()

// Error handling for uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err)
})

// Error handling for unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('Unhandled Rejection:', err)
})

// Security middleware with relaxed settings for static files
app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
}))

// Enable compression
app.use(compression())

// Log all requests
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`)
    next()
})

// Serve static files from root directory
app.use(express.static(__dirname, {
    maxAge: '1h',
    index: false
}))

// Handle all routes
app.get('/*', (req, res) => {
    const cleanUrl = req.url.split('?')[0].split('#')[0]
    console.log(`Processing request for: ${cleanUrl}`)
    
    // Try to serve from root directory
    const filePath = path.join(__dirname, cleanUrl)
    
    // If requesting a directory or root, try to serve index.html
    if (cleanUrl === '/' || cleanUrl.endsWith('/')) {
        const indexPath = path.join(__dirname, cleanUrl, 'index.html')
        console.log(`Attempting to serve index from: ${indexPath}`)
        
        // Check if index.html exists in the requested directory
        if (fs.existsSync(indexPath)) {
            res.sendFile(indexPath, (err) => {
                if (err) {
                    console.error(`Error serving ${indexPath}:`, err)
                    // Fallback to root index.html
                    res.sendFile(path.join(__dirname, 'index.html'), (err) => {
                        if (err) {
                            console.error('Error serving root index.html:', err)
                            res.status(404).sendFile(path.join(__dirname, '404.html'))
                        }
                    })
                }
            })
            return
        }
        
        // If no index.html in directory, try root index.html
        res.sendFile(path.join(__dirname, 'index.html'), (err) => {
            if (err) {
                console.error('Error serving root index.html:', err)
                res.status(404).sendFile(path.join(__dirname, '404.html'))
            }
        })
        return
    }
    
    // Try to serve the exact file
    console.log(`Attempting to serve file: ${filePath}`)
    if (fs.existsSync(filePath)) {
        res.sendFile(filePath, (err) => {
            if (err) {
                console.error(`Error serving ${filePath}:`, err)
                res.status(404).sendFile(path.join(__dirname, '404.html'))
            }
        })
        return
    }
    
    // File doesn't exist, serve as SPA route
    console.log('File not found, serving as SPA route')
    res.sendFile(path.join(__dirname, 'index.html'), (err) => {
        if (err) {
            console.error('Error serving root index.html:', err)
            res.status(404).sendFile(path.join(__dirname, '404.html'))
        }
    })
})

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Application error:', err.stack)
    res.status(500).sendFile(path.join(__dirname, '404.html'))
})

// Start server with error handling
const server = app.listen(port, '127.0.0.1', () => {
    console.log(`Server running at http://127.0.0.1:${port}`)
    console.log(`Serving files from: ${__dirname}`)
    console.log('Server configuration:', {
        port,
        nodeVersion: process.version,
        platform: process.platform,
        cwd: process.cwd(),
        memoryUsage: process.memoryUsage()
    })
}).on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`Port ${port} is already in use`)
    } else {
        console.error('Server error:', err)
    }
})
