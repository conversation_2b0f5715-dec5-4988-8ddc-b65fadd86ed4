import Link from 'next/link';

export const metadata = {
  title: 'Accessibility | Deal4u',
  description: 'Learn about Deal4u\'s commitment to accessibility and how we make our website accessible to all users.',
};

export default function AccessibilityPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          {/* Header with professional styling */}
          <div className="mb-10 pb-6 border-b border-gray-200">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">Accessibility Statement</h1>
            <p className="text-gray-500">Last updated: June 3, 2025</p>
          </div>

          {/* Introduction */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Our Commitment to Accessibility</h2>
            <p className="text-gray-600 mb-4">
              At Deal4u, we are committed to ensuring digital accessibility for people with disabilities. We are continuously 
              improving the user experience for everyone, and applying the relevant accessibility standards.
            </p>
            <p className="text-gray-600 mb-4">
              We strive to ensure that our website and applications follow the Web Content Accessibility Guidelines (WCAG 2.1), 
              levels A and AA. These guidelines provide recommendations to make content more accessible to a wider range of 
              people with disabilities, including blindness and low vision, deafness and hearing loss, learning disabilities, 
              cognitive limitations, limited movement, speech disabilities, photosensitivity and combinations of these.
            </p>
          </section>

          {/* Measures Taken */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Measures We've Taken for Accessibility</h2>
            <p className="text-gray-600 mb-4">
              We take the following measures to ensure accessibility of our website:
            </p>
            <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-2">
              <li>Include accessibility as part of our mission statement</li>
              <li>Provide clear navigation and semantic structure</li>
              <li>Include alternative text for all images</li>
              <li>Ensure sufficient color contrast for text and important graphics</li>
              <li>Design our forms to be accessible with properly labeled form fields</li>
              <li>Make all functionality available from a keyboard</li>
              <li>Provide visible focus indicators for keyboard users</li>
              <li>Allow users to resize text without breaking functionality</li>
              <li>Support modern screen readers and other assistive technologies</li>
              <li>Regularly test with automated tools and real users</li>
            </ul>
          </section>

          {/* Accessibility Features */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Accessibility Features on Our Website</h2>
            <p className="text-gray-600 mb-4">
              Our website includes the following accessibility features:
            </p>
            
            <div className="mb-6">
              <h3 className="text-xl font-medium text-gray-700 mb-3">Navigation</h3>
              <p className="text-gray-600 mb-4">
                We've designed our website with a consistent navigation structure, making it easier for users with screen readers 
                and keyboard-only users to navigate our site. We include:
              </p>
              <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-1">
                <li>Skip to main content links</li>
                <li>Consistent navigation across all pages</li>
                <li>Breadcrumb navigation on appropriate pages</li>
                <li>Descriptive page titles</li>
              </ul>
            </div>
            
            <div className="mb-6">
              <h3 className="text-xl font-medium text-gray-700 mb-3">Text and Typography</h3>
              <p className="text-gray-600 mb-4">
                We've paid special attention to text readability:
              </p>
              <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-1">
                <li>Sufficient color contrast between text and background</li>
                <li>Resizable text that doesn't break the layout</li>
                <li>Clear headings and content structure</li>
                <li>Avoiding justified text which can be hard for some users to read</li>
              </ul>
            </div>
            
            <div className="mb-6">
              <h3 className="text-xl font-medium text-gray-700 mb-3">Images and Media</h3>
              <p className="text-gray-600 mb-4">
                For users with visual impairments:
              </p>
              <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-1">
                <li>All images have appropriate alternative text</li>
                <li>Complex images have longer descriptions available</li>
                <li>Videos include captions and transcripts when possible</li>
                <li>No content relies solely on color to convey information</li>
              </ul>
            </div>
            
            <div className="mb-6">
              <h3 className="text-xl font-medium text-gray-700 mb-3">Forms and Interactive Elements</h3>
              <p className="text-gray-600 mb-4">
                Our forms and interactive elements are designed for accessibility:
              </p>
              <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-1">
                <li>All form fields have associated labels</li>
                <li>Error messages are clearly identified and described</li>
                <li>Forms can be navigated and completed using keyboard only</li>
                <li>Interactive elements have appropriate focus states</li>
              </ul>
            </div>
          </section>

          {/* Compatibility with Assistive Technologies */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Compatibility with Assistive Technologies</h2>
            <p className="text-gray-600 mb-4">
              Our website is designed to be compatible with a variety of assistive technologies, including:
            </p>
            <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-2">
              <li>Screen readers (such as JAWS, NVDA, VoiceOver, and TalkBack)</li>
              <li>Screen magnification software</li>
              <li>Speech recognition software</li>
              <li>Keyboard-only navigation</li>
              <li>Switch controls and other adaptive devices</li>
            </ul>
            <p className="text-gray-600">
              We regularly test with these technologies to ensure a good user experience.
            </p>
          </section>
          
          {/* Known Limitations */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Known Limitations</h2>
            <p className="text-gray-600 mb-4">
              Despite our efforts to ensure accessibility of Deal4u, there may be some limitations. Below is a list of 
              known limitations and current workarounds:
            </p>
            <ul className="list-disc pl-5 mb-4 text-gray-600 space-y-2">
              <li>Some older content may not yet meet all our accessibility standards. We are working to update these pages.</li>
              <li>Some third-party content or functionality may not be fully accessible. We are working with our partners to address these issues.</li>
              <li>Some complex product images may not have fully detailed descriptions. Please contact us if you need assistance with specific product details.</li>
            </ul>
          </section>

          {/* Feedback */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Feedback and Contact Information</h2>
            <p className="text-gray-600 mb-4">
              We welcome your feedback on the accessibility of Deal4u. Please let us know if you encounter any accessibility 
              barriers on our website:
            </p>
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
              <p className="text-gray-700 font-medium">Deal4u Accessibility Team</p>
              <p className="text-gray-600">Email: <EMAIL></p>
              <p className="text-gray-600">Phone: (*************</p>
            </div>
            <p className="text-gray-600">
              We try to respond to feedback within 3 business days.
            </p>
          </section>
          
          {/* Assessment and Compliance Status */}
          <section className="mb-10">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Assessment and Compliance Status</h2>
            <p className="text-gray-600 mb-4">
              The Web Content Accessibility Guidelines (WCAG) defines requirements for designers and developers to improve 
              accessibility for people with disabilities. It defines three levels of conformance: Level A, Level AA, and Level AAA.
            </p>
            <p className="text-gray-600 mb-4">
              Deal4u is partially conformant with WCAG 2.1 level AA. Partially conformant means that some parts of the content 
              do not fully conform to the accessibility standard.
            </p>
            <p className="text-gray-600">
              This statement was prepared on June 3, 2025. It was last reviewed on June 3, 2025.
            </p>
          </section>

          {/* Return Button */}
          <div className="mt-12 text-center">
            <Link 
              href="/"
              className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-colors"
            >
              Return to Homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
