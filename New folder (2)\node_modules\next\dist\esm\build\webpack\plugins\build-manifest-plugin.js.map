{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "names": ["devalue", "webpack", "sources", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "SYSTEM_ENTRYPOINTS", "getRouteFromEntrypoint", "ampFirstEntryNamesMap", "getSortedRoutes", "spans", "srcEmptySsgManifest", "buildNodejsLowPriorityPath", "filename", "buildId", "createEdgeRuntimeManifest", "originAssetMap", "manifestFilenames", "assetMap", "lowPriorityFiles", "manifestDefCode", "JSON", "stringify", "lowPriorityFilesCode", "map", "join", "normalizeRewrite", "item", "has", "source", "destination", "normalizeRewritesForBuildManifest", "rewrites", "afterFiles", "beforeFiles", "fallback", "generateClientManifest", "compiler", "compilation", "compilationSpan", "get", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "appDependencies", "Set", "pages", "sortedPageKeys", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "getEntrypointFiles", "entrypoint", "getFiles", "file", "test", "replace", "processRoute", "r", "rewrite", "startsWith", "BuildManifestPlugin", "constructor", "options", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "exportRuntime", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "rootMainFiles", "ampFirstPages", "ampFirstEntryNames", "entryName", "pagePath", "push", "mainFiles", "compilationAssets", "getAssets", "p", "name", "endsWith", "info", "v", "values", "filesForPage", "buildManifestPath", "ssgManifestPath", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AACA,OAAOA,aAAa,6BAA4B;AAChD,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,yBAAyB,EACzBC,wBAAwB,EACxBC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,+BAA+B,EAC/BC,kBAAkB,QACb,gCAA+B;AAEtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,KAAK,QAAQ,qBAAoB;AAQ1C,sEAAsE;AACtE,kEAAkE;AAClE,cAAc;AACd,OAAO,MAAMC,sBAAsB,CAAC,4EAA4E,CAAC,CAAA;AAEjH,+CAA+C;AAC/C,SAASC,2BAA2BC,QAAgB,EAAEC,OAAe;IACnE,OAAO,CAAC,EAAEd,yBAAyB,CAAC,EAAEc,QAAQ,CAAC,EAAED,SAAS,CAAC;AAC7D;AAEA,SAASE,0BAA0BC,cAA6B;IAC9D,MAAMC,oBAAoB;QAAC;QAAqB;KAAkB;IAElE,MAAMC,WAA0B;QAC9B,GAAGF,cAAc;QACjBG,kBAAkB,EAAE;IACtB;IAEA,MAAMC,kBAAkB,CAAC,wBAAwB,EAAEC,KAAKC,SAAS,CAC/DJ,UACA,MACA,GACA,GAAG,CAAC;IACN,+FAA+F;IAC/F,gIAAgI;IAChI,MAAMK,uBACJ,CAAC,4CAA4C,CAAC,GAC9CN,kBACGO,GAAG,CAAC,CAACX;QACJ,OAAO,CAAC,6CAA6C,EAAEA,SAAS,IAAI,CAAC;IACvE,GACCY,IAAI,CAAC,OACR,CAAC,IAAI,CAAC;IAER,OAAOL,kBAAkBG;AAC3B;AAEA,SAASG,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEA,OAAO,SAASC,kCACdC,QAAkC;QAGpBA,sBACCA,uBACHA;IAHZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,qBAAnBD,qBAAqBR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QAChEO,WAAW,GAAEF,wBAAAA,SAASE,WAAW,qBAApBF,sBAAsBR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QAClEQ,QAAQ,GAAEH,qBAAAA,SAASG,QAAQ,qBAAjBH,mBAAmBR,GAAG,CAAC,CAACG,OAASD,iBAAiBC;IAC9D;AACF;AAEA,mFAAmF;AACnF,yCAAyC;AACzC,SAASS,uBACPC,QAAa,EACbC,WAAgB,EAChBpB,QAAuB,EACvBc,QAAkC;IAElC,MAAMO,kBAAkB7B,MAAM8B,GAAG,CAACF,gBAAgB5B,MAAM8B,GAAG,CAACH;IAC5D,MAAMI,wBAAwBF,mCAAAA,gBAAiBG,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYd,kCAAkCC;QAChD;QACA,MAAMc,kBAAkB,IAAIC,IAAI7B,SAAS8B,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiBxC,gBAAgByC,OAAOC,IAAI,CAACjC,SAAS8B,KAAK;QAEjEC,eAAeG,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAepC,SAAS8B,KAAK,CAACK,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACX,gBAAgBlB,GAAG,CAAC6B;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBd,cAAc,CAACS,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEX,eAAee,WAAW,GAAGV;QAE7B,OAAOtD,QAAQiD;IACjB;AACF;AAEA,OAAO,SAASgB,mBAAmBC,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACCvC,GAAG,CAAC,CAACuC,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,MAAMC,eAAe,CAACC;IACpB,MAAMC,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,CAACC,QAAQtC,WAAW,CAACuC,UAAU,CAAC,MAAM;QACxC,OAAO,AAACD,QAAgBtC,WAAW;IACrC;IACA,OAAOsC;AACT;AAEA,iFAAiF;AACjF,+GAA+G;AAC/G,eAAe,MAAME;IAOnBC,YAAYC,OAMX,CAAE;QACD,IAAI,CAAC1D,OAAO,GAAG0D,QAAQ1D,OAAO;QAC9B,IAAI,CAAC2D,aAAa,GAAG,CAAC,CAACD,QAAQC,aAAa;QAC5C,IAAI,CAACzC,QAAQ,GAAG;YACdE,aAAa,EAAE;YACfD,YAAY,EAAE;YACdE,UAAU,EAAE;QACd;QACA,IAAI,CAACuC,aAAa,GAAGF,QAAQE,aAAa;QAC1C,IAAI,CAAC1C,QAAQ,CAACE,WAAW,GAAGsC,QAAQxC,QAAQ,CAACE,WAAW,CAACV,GAAG,CAAC0C;QAC7D,IAAI,CAAClC,QAAQ,CAACC,UAAU,GAAGuC,QAAQxC,QAAQ,CAACC,UAAU,CAACT,GAAG,CAAC0C;QAC3D,IAAI,CAAClC,QAAQ,CAACG,QAAQ,GAAGqC,QAAQxC,QAAQ,CAACG,QAAQ,CAACX,GAAG,CAAC0C;QACvD,IAAI,CAACS,aAAa,GAAG,CAAC,CAACH,QAAQG,aAAa;IAC9C;IAEAC,aAAavC,QAAa,EAAEC,WAAgB,EAAEuC,MAAW,EAAE;QACzD,MAAMtC,kBAAkB7B,MAAM8B,GAAG,CAACF,gBAAgB5B,MAAM8B,GAAG,CAACH;QAC5D,MAAMyC,mBAAmBvC,mCAAAA,gBAAiBG,UAAU,CAClD;QAEF,OAAOoC,oCAAAA,iBAAkBnC,OAAO,CAAC;YAC/B,MAAMoC,cAAgCzC,YAAYyC,WAAW;YAC7D,MAAM7D,WAAuC;gBAC3C8D,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACf/D,kBAAkB,EAAE;gBACpBgE,eAAe,EAAE;gBACjBnC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBoC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqB7E,sBAAsBgC,GAAG,CAACF;YACrD,IAAI+C,oBAAoB;gBACtB,KAAK,MAAMC,aAAaD,mBAAoB;oBAC1C,MAAME,WAAWhF,uBAAuB+E;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEArE,SAASkE,aAAa,CAACI,IAAI,CAACD;gBAC9B;YACF;YAEA,MAAME,YAAY,IAAI1C,IACpBa,mBAAmBmB,YAAYvC,GAAG,CAACvC;YAGrC,IAAI,IAAI,CAACyE,aAAa,EAAE;gBACtBxD,SAASiE,aAAa,GAAG;uBACpB,IAAIpC,IACLa,mBACEmB,YAAYvC,GAAG,CAACtC;iBAGrB;YACH;YAEA,MAAMwF,oBAIApD,YAAYqD,SAAS;YAE3BzE,SAAS8D,aAAa,GAAGU,kBACtBlC,MAAM,CAAC,CAACoC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAEC,IAAI,CAACC,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACEF,EAAEG,IAAI,IAAI5F,gDAAgDyF,EAAEG,IAAI;YAEpE,GACCvE,GAAG,CAAC,CAACwE,IAAMA,EAAEH,IAAI;YAEpB3E,SAAS+D,QAAQ,GAAGrB,mBAClBmB,YAAYvC,GAAG,CAACpC,4CAChBoD,MAAM,CAAC,CAACO,OAAS,CAAC0B,UAAU7D,GAAG,CAACmC;YAElC7C,SAASgE,WAAW,GAAGtB,mBACrBmB,YAAYvC,GAAG,CAACnC;YAGlB,KAAK,MAAMwD,cAAcvB,YAAYyC,WAAW,CAACkB,MAAM,GAAI;gBACzD,IAAI3F,mBAAmBsB,GAAG,CAACiC,WAAWgC,IAAI,GAAG;gBAC7C,MAAMN,WAAWhF,uBAAuBsD,WAAWgC,IAAI;gBAEvD,IAAI,CAACN,UAAU;oBACb;gBACF;gBAEA,MAAMW,eAAetC,mBAAmBC;gBAExC3C,SAAS8B,KAAK,CAACuC,SAAS,GAAG;uBAAI,IAAIxC,IAAI;2BAAI0C;2BAAcS;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B,MAAM0B,oBAAoBvF,2BACxB,qBACA,IAAI,CAACE,OAAO;gBAEd,MAAMsF,kBAAkBxF,2BACtB,mBACA,IAAI,CAACE,OAAO;gBAEdI,SAASC,gBAAgB,CAACqE,IAAI,CAACW,mBAAmBC;gBAClDvB,MAAM,CAACuB,gBAAgB,GAAG,IAAIvG,QAAQwG,SAAS,CAAC1F;YAClD;YAEAO,SAAS8B,KAAK,GAAGE,OAAOC,IAAI,CAACjC,SAAS8B,KAAK,EACxCsD,IAAI,GACJC,MAAM,CACL,2BAA2B;YAC3B,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGvF,SAAS8B,KAAK,CAACyD,EAAE,EAAGD,CAAAA,GACvC,CAAC;YAGL,IAAIE,oBAAoB5G;YAExB,IAAI,IAAI,CAAC2E,aAAa,EAAE;gBACtBiC,oBAAoB,CAAC,SAAS,EAAE5G,eAAe,CAAC;YAClD;YAEA+E,MAAM,CAAC6B,kBAAkB,GAAG,IAAI7G,QAAQwG,SAAS,CAC/ChF,KAAKC,SAAS,CAACJ,UAAU,MAAM;YAGjC2D,MAAM,CAAC,CAAC,OAAO,EAAE9E,0BAA0B,GAAG,CAAC,CAAC,GAAG,IAAIF,QAAQwG,SAAS,CACtE,CAAC,EAAEtF,0BAA0BG,UAAU,CAAC;YAG1C,IAAI,CAAC,IAAI,CAACuD,aAAa,EAAE;gBACvB,MAAMkC,qBAAqB,CAAC,EAAE3G,yBAAyB,CAAC,EAAE,IAAI,CAACc,OAAO,CAAC,kBAAkB,CAAC;gBAE1F+D,MAAM,CAAC8B,mBAAmB,GAAG,IAAI9G,QAAQwG,SAAS,CAChD,CAAC,wBAAwB,EAAEjE,uBACzBC,UACAC,aACApB,UACA,IAAI,CAACc,QAAQ,EACb,uDAAuD,CAAC;YAE9D;YAEA,OAAO6C;QACT;IACF;IAEA+B,MAAMvE,QAA0B,EAAE;QAChCA,SAASwE,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACzE;YAC9CA,YAAYuE,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACElB,MAAM;gBACNoB,OAAOrH,QAAQsH,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACtC;gBACC,IAAI,CAACD,YAAY,CAACvC,UAAUC,aAAauC;YAC3C;QAEJ;QACA;IACF;AACF"}