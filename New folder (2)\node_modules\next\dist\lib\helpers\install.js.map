{"version": 3, "sources": ["../../../src/lib/helpers/install.ts"], "names": ["install", "root", "dependencies", "packageManager", "isOnline", "devDependencies", "npmFlags", "yarnFlags", "Promise", "resolve", "reject", "args", "command", "<PERSON><PERSON><PERSON><PERSON>", "length", "push", "console", "log", "yellow", "child", "spawn", "stdio", "env", "process", "ADBLOCK", "NODE_ENV", "DISABLE_OPENCOLLECTIVE", "on", "code", "join"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;4BAzBO;mEACL;;;;;;AAwBX,SAASA,QACdC,IAAY,EACZC,YAA6B,EAC7B,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAe;IAE1D;;GAEC,GACD,MAAMC,WAAqB,EAAE;IAC7B;;GAEC,GACD,MAAMC,YAAsB,EAAE;IAC9B;;GAEC,GACD,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,IAAIC;QACJ,IAAIC,UAAUT;QACd,MAAMU,UAAUV,mBAAmB;QAEnC,IAAID,gBAAgBA,aAAaY,MAAM,EAAE;YACvC;;OAEC,GACD,IAAID,SAAS;gBACX;;SAEC,GACDF,OAAO;oBAAC;oBAAO;iBAAU;gBACzB,IAAI,CAACP,UAAUO,KAAKI,IAAI,CAAC;gBACzBJ,KAAKI,IAAI,CAAC,SAASd;gBACnB,IAAII,iBAAiBM,KAAKI,IAAI,CAAC;gBAC/BJ,KAAKI,IAAI,IAAIb;YACf,OAAO;gBACL;;SAEC,GACDS,OAAO;oBAAC;oBAAW;iBAAe;gBAClCA,KAAKI,IAAI,CAACV,kBAAkB,eAAe;gBAC3CM,KAAKI,IAAI,IAAIb;YACf;QACF,OAAO;YACL;;;OAGC,GACDS,OAAO;gBAAC;aAAU;YAClB,IAAI,CAACP,UAAU;gBACbY,QAAQC,GAAG,CAACC,IAAAA,kBAAM,EAAC;gBACnB,IAAIL,SAAS;oBACXG,QAAQC,GAAG,CAACC,IAAAA,kBAAM,EAAC;oBACnBF,QAAQC,GAAG;oBACXN,KAAKI,IAAI,CAAC;gBACZ,OAAO;oBACLC,QAAQC,GAAG;gBACb;YACF;QACF;QACA;;KAEC,GACD,IAAIJ,SAAS;YACXF,KAAKI,IAAI,IAAIR;QACf,OAAO;YACLI,KAAKI,IAAI,IAAIT;QACf;QACA;;KAEC,GACD,MAAMa,QAAQC,IAAAA,mBAAK,EAACR,SAASD,MAAM;YACjCU,OAAO;YACPC,KAAK;gBACH,GAAGC,QAAQD,GAAG;gBACdE,SAAS;gBACT,mDAAmD;gBACnD,+BAA+B;gBAC/BC,UAAU;gBACVC,wBAAwB;YAC1B;QACF;QACAP,MAAMQ,EAAE,CAAC,SAAS,CAACC;YACjB,IAAIA,SAAS,GAAG;gBACdlB,OAAO;oBAAEE,SAAS,CAAC,EAAEA,QAAQ,CAAC,EAAED,KAAKkB,IAAI,CAAC,KAAK,CAAC;gBAAC;gBACjD;YACF;YACApB;QACF;IACF;AACF"}