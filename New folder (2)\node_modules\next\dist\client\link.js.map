{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "isLocalURL", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "doPrefetch", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "RouterContext", "appRouter", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "resolveHref", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "__NEXT_LINK_NO_TOUCH_START", "isAbsoluteUrl", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "getDomainLocale", "locales", "domainLocales", "addBasePath", "addLocale", "defaultLocale", "cloneElement"], "mappings": "AAAA;;;;;+BAwwBA;;;eAAA;;;;;gEAjwBkB;6BAEU;4BACD;2BACD;uBACI;2BACJ;4CACI;+CACG;iCAKD;iCACA;6BACJ;oCACC;AAkG7B,MAAMA,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAACE,IAAAA,sBAAU,EAACN,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQK,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAON,QAAQM,MAAM,KAAK,cACtBN,QAAQM,MAAM,GAEhB,YAAYT,SACVA,OAAOS,MAAM,GACbC;QAEN,MAAMC,gBAAgBV,OAAO,MAAMC,KAAK,MAAMO;QAE9C,kEAAkE;QAClE,IAAIZ,WAAWe,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bd,WAAWgB,GAAG,CAACF;IACjB;IAEA,MAAMG,aAAa;QACjB,IAAIT,aAAa;YACf,sDAAsD;YACtD,wFAAwF;YACxF,OAAO,AAACL,OAA6BD,QAAQ,CAACE,MAAMG;QACtD,OAAO;YACL,OAAO,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;QACnD;IACF;IAEA,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDW,aAAaC,KAAK,CAAC,CAACC;QAClB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBjC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVgC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB3B,MAAuB,EACvBJ,WAAqB;IAErB,MAAM,EAAEgC,QAAQ,EAAE,GAAGJ,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACClB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC5B,eAAe,CAACE,IAAAA,sBAAU,EAACN,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEAgC,EAAEO,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBpC,QAAQ;YAC9BA,MAAM,CAACkC,UAAU,YAAY,OAAO,CAACjC,MAAMC,IAAI;gBAC7CiC;gBACA1B;gBACA2B,QAAQM;YACV;QACF,OAAO;YACL1C,MAAM,CAACkC,UAAU,YAAY,OAAO,CAAChC,MAAMD,MAAM;gBAC/CmC,QAAQM;YACV;QACF;IACF;IAEA,IAAIrC,aAAa;QACfsC,cAAK,CAACC,eAAe,CAACH;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASI,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,IAAAA,oBAAS,EAACD;AACnB;AAEA;;;;;;;CAOC,GACD,MAAME,qBAAOL,cAAK,CAACM,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJpD,MAAMqD,QAAQ,EACdpD,IAAIqD,MAAM,EACVF,UAAUG,YAAY,EACtBzD,UAAU0D,eAAe,IAAI,EAC7BC,QAAQ,EACRxB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN3B,MAAM,EACNkD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,qBAACa;sBAAGb;;IACjB;IAEA,MAAMc,cAAcxB,cAAK,CAACyB,UAAU,CAACC,yCAAa;IAClD,MAAMC,YAAY3B,cAAK,CAACyB,UAAU,CAACG,+CAAgB;IACnD,MAAMvE,SAASmE,sBAAAA,cAAeG;IAE9B,0DAA0D;IAC1D,MAAMjE,cAAc,CAAC8D;IAErB,MAAMK,kBAAkBf,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMgB,kBACJhB,iBAAiB,OAAOiB,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;IAE/D,IAAI3D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAAS0D,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAO5E,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAM6E,qBAAsD;YAC1DlF,MAAM;QACR;QACA,MAAMmF,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE7B,KAAK,CAAC6B,IAAI,IAAI,QACb,OAAO7B,KAAK,CAAC6B,IAAI,KAAK,YAAY,OAAO7B,KAAK,CAAC6B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ/B,KAAK,CAAC6B,IAAI,KAAK,OAAO,SAAS,OAAO7B,KAAK,CAAC6B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DvF,IAAI;YACJgC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTuB,UAAU;YACV3D,UAAU;YACVU,QAAQ;YACRkD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAM0B,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOxC,KAAK,CAAC6B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI7B,KAAK,CAAC6B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYjD,cAAK,CAACkD,MAAM,CAAC;QAC/B,IAAI1C,MAAMpD,QAAQ,IAAI,CAAC6F,UAAUE,OAAO,IAAI,CAACzF,aAAa;YACxDuF,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAI/E,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAId,eAAe,CAACkD,QAAQ;YAC1B,IAAItD;YACJ,IAAI,OAAOqD,aAAa,UAAU;gBAChCrD,OAAOqD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS2C,QAAQ,KAAK,UAC7B;gBACAhG,OAAOqD,SAAS2C,QAAQ;YAC1B;YAEA,IAAIhG,MAAM;gBACR,MAAMiG,oBAAoBjG,KACvBkG,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiB9E,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGyC,cAAK,CAAC6D,OAAO,CAAC;QACjC,IAAI,CAACrC,aAAa;YAChB,MAAMsC,eAAe5D,kBAAkBS;YACvC,OAAO;gBACLrD,MAAMwG;gBACNvG,IAAIqD,SAASV,kBAAkBU,UAAUkD;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGC,IAAAA,wBAAW,EAC5CxC,aACAb,UACA;QAGF,OAAO;YACLrD,MAAMwG;YACNvG,IAAIqD,SACAoD,IAAAA,wBAAW,EAACxC,aAAaZ,UACzBmD,cAAcD;QACpB;IACF,GAAG;QAACtC;QAAab;QAAUC;KAAO;IAElC,MAAMqD,eAAejE,cAAK,CAACkD,MAAM,CAAS5F;IAC1C,MAAM4G,aAAalE,cAAK,CAACkD,MAAM,CAAS3F;IAExC,oFAAoF;IACpF,IAAI4G;IACJ,IAAI9C,gBAAgB;QAClB,IAAI/C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIwC,SAAS;gBACXoC,QAAQC,IAAI,CACV,AAAC,oDAAoD1C,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpBkC,QAAQC,IAAI,CACV,AAAC,yDAAyD1C,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQnE,cAAK,CAACoE,QAAQ,CAACC,IAAI,CAAC3D;YAC9B,EAAE,OAAOrC,KAAK;gBACZ,IAAI,CAACqC,UAAU;oBACb,MAAM,IAAI0B,MACR,AAAC,uDAAuDzB,WAAS;gBAErE;gBACA,MAAM,IAAIyB,MACR,AAAC,6DAA6DzB,WAAS,8FACpE,CAAA,OAAOhD,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACLwG,QAAQnE,cAAK,CAACoE,QAAQ,CAACC,IAAI,CAAC3D;QAC9B;IACF,OAAO;QACL,IAAIpC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACkC,4BAAD,AAACA,SAAkB4D,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIlC,MACR;YAEJ;QACF;IACF;IAEA,MAAMmC,WAAgBlD,iBAClB8C,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C/D;IAEJ,MAAM,CAACgE,oBAAoBC,WAAWC,aAAa,GAAGC,IAAAA,gCAAe,EAAC;QACpEC,YAAY;IACd;IAEA,MAAMC,SAAS9E,cAAK,CAAC+E,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAId,WAAWf,OAAO,KAAK5F,MAAM0G,aAAad,OAAO,KAAK7F,MAAM;YAC9DqH;YACAT,WAAWf,OAAO,GAAG5F;YACrB0G,aAAad,OAAO,GAAG7F;QACzB;QAEAmH,mBAAmBO;QACnB,IAAIT,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASS;iBACxC,IAAI,OAAOT,aAAa,UAAU;gBACrCA,SAASpB,OAAO,GAAG6B;YACrB;QACF;IACF,GACA;QAACzH;QAAIgH;QAAUjH;QAAMqH;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3DzE,cAAK,CAACiF,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAI3G,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACnB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACqH,aAAa,CAAC7C,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBzE,SACEC,QACAC,MACAC,IACA;YAAEO;QAAO,GACT;YACEoH,MAAMpD;QACR,GACApE;IAEJ,GAAG;QACDH;QACAD;QACAoH;QACA5G;QACA+D;QACAL,+BAAAA,YAAa1D,MAAM;QACnBT;QACAK;QACAoE;KACD;IAED,MAAMqD,aAMF;QACFX,KAAKM;QACL9D,SAAQ1B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAI8C,MACP;gBAEL;YACF;YAEA,IAAI,CAACf,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ1B;YACV;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACAmD,MAAM3D,KAAK,CAACQ,OAAO,CAAC1B;YACtB;YAEA,IAAI,CAACjC,QAAQ;gBACX;YACF;YAEA,IAAIiC,EAAE8F,gBAAgB,EAAE;gBACtB;YACF;YAEA/F,YACEC,GACAjC,QACAC,MACAC,IACAgC,SACAC,SACAC,QACA3B,QACAJ;QAEJ;QACAuD,cAAa3B,CAAC;YACZ,IAAI,CAAC+B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB5B;YACnB;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACS,YAAY,KAAK,YACpC;gBACAkD,MAAM3D,KAAK,CAACS,YAAY,CAAC3B;YAC3B;YAEA,IAAI,CAACjC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAACwE,mBAAmBvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1Dd,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACAuH,UAAU;gBACV,gGAAgG;gBAChGxH,uBAAuB;YACzB,GACA;gBACEqH,MAAMpD;YACR,GACApE;QAEJ;QACAyD,cAAc7C,QAAQC,GAAG,CAAC+G,0BAA0B,GAChDvH,YACA,SAASoD,aAAa7B,CAAC;YACrB,IAAI,CAAC+B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB9B;YACnB;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACW,YAAY,KAAK,YACpC;gBACAgD,MAAM3D,KAAK,CAACW,YAAY,CAAC7B;YAC3B;YAEA,IAAI,CAACjC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACwE,mBAAmBnE,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACAuH,UAAU;gBACV,gGAAgG;gBAChGxH,uBAAuB;YACzB,GACA;gBACEqH,MAAMpD;YACR,GACApE;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAI6H,IAAAA,oBAAa,EAAChI,KAAK;QACrB4H,WAAW7H,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC8D,kBACDN,YACCoD,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAM3D,KAAK,AAAD,GAC7C;QACA,MAAMgF,YACJ,OAAO1H,WAAW,cAAcA,SAAS0D,+BAAAA,YAAa1D,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAM2H,eACJjE,CAAAA,+BAAAA,YAAakE,cAAc,KAC3BC,IAAAA,gCAAe,EACbpI,IACAiI,WACAhE,+BAAAA,YAAaoE,OAAO,EACpBpE,+BAAAA,YAAaqE,aAAa;QAG9BV,WAAW7H,IAAI,GACbmI,gBACAK,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAACxI,IAAIiI,WAAWhE,+BAAAA,YAAawE,aAAa;IACnE;IAEA,OAAO3E,+BACLrB,cAAK,CAACiG,YAAY,CAAC9B,OAAOgB,4BAE1B,qBAAC5D;QAAG,GAAGD,SAAS;QAAG,GAAG6D,UAAU;kBAC7BzE;;AAGP;MAGF,WAAeL"}