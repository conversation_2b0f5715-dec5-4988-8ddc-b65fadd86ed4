Write-Host "🚀 Creating cPanel deployment package..." -ForegroundColor Green

# Clean previous deployment
if (Test-Path "cpanel-ready") {
    Remove-Item -Recurse -Force "cpanel-ready"
    Write-Host "✅ Cleaned previous deployment" -ForegroundColor Yellow
}

# Create deployment directory
New-Item -ItemType Directory -Name "cpanel-ready" | Out-Null
Write-Host "📁 Created deployment directory" -ForegroundColor Green

# Copy Next.js build
Write-Host "📋 Copying Next.js build files..." -ForegroundColor Cyan
Copy-Item -Recurse -Force ".next" "cpanel-ready\.next"

# Copy application directories
Write-Host "📋 Copying application files..." -ForegroundColor Cyan
$directories = @("app", "components", "context", "lib", "public")
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Copy-Item -Recurse -Force $dir "cpanel-ready\$dir"
        Write-Host "✅ Copied $dir/" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Warning: $dir/ directory not found" -ForegroundColor Yellow
    }
}

# Copy configuration files
Write-Host "📋 Copying configuration files..." -ForegroundColor Cyan
$files = @("server.js", "package.json", "next.config.js", "tailwind.config.js", "postcss.config.js", "jsconfig.json")
foreach ($file in $files) {
    if (Test-Path $file) {
        Copy-Item -Force $file "cpanel-ready\$file"
        Write-Host "✅ Copied $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Warning: $file not found" -ForegroundColor Yellow
    }
}

# Create deployment instructions
Write-Host "📝 Creating deployment instructions..." -ForegroundColor Cyan
$instructions = @"
CPANEL DEPLOYMENT INSTRUCTIONS - DYNAMIC NEXT.JS APP
===================================================

🔄 REAL-TIME WOOCOMMERCE SYNC ENABLED
Your app will fetch fresh product data from WooCommerce on every page load!

1. CREATE NODE.JS APP IN CPANEL:
   - Go to cPanel → Node.js Apps
   - Click "Create Application"
   - Node.js Version: 18.x (or latest supported)
   - Application Root: /public_html/your-app-name
   - Application URL: your-domain.com/your-app-name
   - Startup File: server.js

2. UPLOAD FILES:
   - Upload ALL files from this folder
   - Make sure to upload to the Application Root directory
   - Ensure .next folder is uploaded (contains Next.js build)

3. INSTALL DEPENDENCIES:
   - In cPanel Node.js Apps, click on your app
   - Go to "Package.json" tab
   - Click "Run NPM Install"
   - This may take a few minutes for Next.js dependencies

4. SET ENVIRONMENT VARIABLES:
   - NODE_ENV: production
   - (Don't set PORT - cPanel handles this automatically)

5. START APPLICATION:
   - Click "Start App" in cPanel Node.js Apps
   - Wait for "Application is running" status
   - Check the logs for any errors

6. TEST YOUR DYNAMIC SYNC:
   - Visit your application URL
   - Add a new product to your WooCommerce store
   - Refresh your website - new product should appear!
   - Test product pages and categories

DYNAMIC FEATURES:
✅ Real-time product sync from WooCommerce
✅ Live inventory updates
✅ Dynamic pricing
✅ New products appear automatically
✅ No need to rebuild when adding products

TROUBLESHOOTING:
- If app fails to start, check Node.js version (18.x recommended)
- Ensure all directories (.next, app, components, etc.) are uploaded
- Check cPanel logs for specific error messages
- Verify WooCommerce API credentials in your code

For detailed troubleshooting, see CPANEL_DEPLOYMENT_GUIDE.md
"@

$instructions | Out-File -FilePath "cpanel-ready\DEPLOYMENT_INSTRUCTIONS.txt" -Encoding UTF8

Write-Host ""
Write-Host "🎉 Dynamic Next.js deployment ready!" -ForegroundColor Green
Write-Host "📁 Files ready in: cpanel-ready folder" -ForegroundColor Cyan
Write-Host "🔄 Your app will sync with WooCommerce in real-time" -ForegroundColor Yellow
Write-Host "📖 Check DEPLOYMENT_INSTRUCTIONS.txt for next steps" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
