{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["fetchServerResponse", "createHrefFromUrl", "invalidateCacheBelowFlightSegmentPath", "applyRouterStatePatchToTree", "shouldHardNavigate", "isNavigatingToNewRootLayout", "PrefetchCacheEntryStatus", "handleMutable", "applyFlightData", "prefetchQueue", "createEmptyCacheNode", "DEFAULT_SEGMENT_KEY", "listenForDynamicRequest", "updateCacheNodeOnNavigation", "getOrCreatePrefetchCacheEntry", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "clearCacheNodeDataForSegmentPath", "handleExternalUrl", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "triggerLazyFetchForLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "appliedPatch", "rsc", "prefetchRsc", "loading", "Map", "segmentPathsToFill", "map", "segmentPaths", "navigateReducer", "process", "env", "__NEXT_PPR", "navigateReducer_PPR", "navigateReducer_noPPR", "action", "isExternalUrl", "navigateType", "shouldScroll", "hash", "href", "prefetchCache", "preserveCustomHistoryState", "toString", "prefetchValues", "nextUrl", "tree", "buildId", "treeAtTimeOfPrefetch", "data", "bump", "then", "flightData", "canonicalUrlOverride", "isFirstRead", "lastUsedTime", "Date", "now", "document", "getElementById", "currentTree", "cache", "flightDataPath", "slice", "flightSegmentPathWithLeadingEmpty", "newTree", "applied", "status", "stale", "hardNavigate", "subSegment", "scrollableSegmentPath", "patchedTree", "hashFragment", "_postponed", "prefetchedTree", "seedData", "head", "task", "node", "patchedRouterState", "route"], "mappings": "AAKA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,qCAAqC,QAAQ,+CAA8C;AACpG,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SACEC,wBAAwB,QAKnB,0BAAyB;AAChC,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,mBAAmB,QAAQ,iCAAgC;AACpE,SACEC,uBAAuB,EACvBC,2BAA2B,QACtB,qBAAoB;AAC3B,SACEC,6BAA6B,EAC7BC,kBAAkB,QACb,0BAAyB;AAChC,SAASC,gCAAgC,QAAQ,4CAA2C;AAE5F,OAAO,SAASC,kBACdC,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOlB,cAAcW,OAAOC;AAC9B;AAEA,SAASO,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,gCACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B;IAE5B,IAAIC,eAAe;IAEnBJ,SAASK,GAAG,GAAGJ,aAAaI,GAAG;IAC/BL,SAASM,WAAW,GAAGL,aAAaK,WAAW;IAC/CN,SAASO,OAAO,GAAGN,aAAaM,OAAO;IACvCP,SAASV,cAAc,GAAG,IAAIkB,IAAIP,aAAaX,cAAc;IAE7D,MAAMmB,qBAAqBvB,0BAA0BiB,WAAWO,GAAG,CACjE,CAACrB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMsB,gBAAgBF,mBAAoB;QAC7CjC,iCAAiCwB,UAAUC,cAAcU;QAEzDP,eAAe;IACjB;IAEA,OAAOA;AACT;AAEA,8EAA8E;AAC9E,8EAA8E;AAC9E,8DAA8D;AAC9D,OAAO,MAAMQ,kBAAkBC,QAAQC,GAAG,CAACC,UAAU,GACjDC,sBACAC,sBAAqB;AAEzB,8EAA8E;AAC9E,4EAA4E;AAC5E,SAASA,sBACPvC,KAA2B,EAC3BwC,MAAsB;IAEtB,MAAM,EAAEtC,GAAG,EAAEuC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMvC,UAAmB,CAAC;IAC1B,MAAM,EAAE2C,IAAI,EAAE,GAAG1C;IACjB,MAAM2C,OAAO9D,kBAAkBmB;IAC/B,MAAMC,cAAcuC,iBAAiB;IACrC,wFAAwF;IACxF7C,mBAAmBG,MAAM8C,aAAa;IAEtC7C,QAAQ8C,0BAA0B,GAAG;IAErC,IAAIN,eAAe;QACjB,OAAO1C,kBAAkBC,OAAOC,SAASC,IAAI8C,QAAQ,IAAI7C;IAC3D;IAEA,MAAM8C,iBAAiBrD,8BAA8B;QACnDM;QACAgD,SAASlD,MAAMkD,OAAO;QACtBC,MAAMnD,MAAMmD,IAAI;QAChBC,SAASpD,MAAMoD,OAAO;QACtBN,eAAe9C,MAAM8C,aAAa;IACpC;IACA,MAAM,EAAEO,oBAAoB,EAAEC,IAAI,EAAE,GAAGL;IAEvC1D,cAAcgE,IAAI,CAACD;IAEnB,OAAOA,KAAKE,IAAI,CACd;YAAC,CAACC,YAAYC,qBAAqB;QACjC,IAAIC,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACV,eAAeW,YAAY,EAAE;YAChC,gGAAgG;YAChGX,eAAeW,YAAY,GAAGC,KAAKC,GAAG;YACtCH,cAAc;QAChB;QAEA,4DAA4D;QAC5D,IAAI,OAAOF,eAAe,UAAU;YAClC,OAAO1D,kBAAkBC,OAAOC,SAASwD,YAAYtD;QACvD;QAEA,mEAAmE;QACnE,wCAAwC;QACxC,IAAI4D,SAASC,cAAc,CAAC,yBAAyB;YACnD,OAAOjE,kBAAkBC,OAAOC,SAAS4C,MAAM1C;QACjD;QAEA,IAAI8D,cAAcjE,MAAMmD,IAAI;QAC5B,IAAI5B,eAAevB,MAAMkE,KAAK;QAC9B,IAAI5D,qBAA0C,EAAE;QAChD,KAAK,MAAM6D,kBAAkBV,WAAY;YACvC,MAAMjC,oBAAoB2C,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAM3C,YAAY0C,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAO7C;aAAkB;YAEpE,wEAAwE;YACxE,IAAI8C,UAAUrF,4BACZ,sBAAsB;YACtBoF,mCACAJ,aACAxC,WACAoB;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIyB,YAAY,MAAM;gBACpBA,UAAUrF,4BACR,sBAAsB;gBACtBoF,mCACAhB,sBACA5B,WACAoB;YAEJ;YAEA,IAAIyB,YAAY,MAAM;gBACpB,IAAInF,4BAA4B8E,aAAaK,UAAU;oBACrD,OAAOvE,kBAAkBC,OAAOC,SAAS4C,MAAM1C;gBACjD;gBAEA,MAAM+D,QAAmB1E;gBACzB,IAAI+E,UAAU;gBAEd,IACEtB,eAAeuB,MAAM,KAAKpF,yBAAyBqF,KAAK,IACxD,CAACd,aACD;oBACA,yJAAyJ;oBACzJ,uHAAuH;oBACvH,gFAAgF;oBAChF,0FAA0F;oBAC1FY,UAAUlD,gCACR6C,OACA3C,cACAC,mBACAC;oBAEF,yEAAyE;oBACzE,mFAAmF;oBACnFwB,eAAeW,YAAY,GAAGC,KAAKC,GAAG;gBACxC,OAAO;oBACLS,UAAUjF,gBACRiC,cACA2C,OACAC,gBACAlB;gBAEJ;gBAEA,MAAMyB,eAAexF,mBACnB,sBAAsB;gBACtBmF,mCACAJ;gBAGF,IAAIS,cAAc;oBAChB,2CAA2C;oBAC3CR,MAAMvC,GAAG,GAAGJ,aAAaI,GAAG;oBAC5BuC,MAAMtC,WAAW,GAAGL,aAAaK,WAAW;oBAE5C5C,sCACEkF,OACA3C,cACAC;oBAEF,8EAA8E;oBAC9EvB,QAAQiE,KAAK,GAAGA;gBAClB,OAAO,IAAIK,SAAS;oBAClBtE,QAAQiE,KAAK,GAAGA;oBAChB,4EAA4E;oBAC5E,8EAA8E;oBAC9E3C,eAAe2C;gBACjB;gBAEAD,cAAcK;gBAEd,KAAK,MAAMK,cAAcnE,0BAA0BiB,WAAY;oBAC7D,MAAMmD,wBAAwB;2BAAIpD;2BAAsBmD;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsB7D,MAAM,GAAG,EAAE,KACvDtB,qBACA;wBACAa,mBAAmBc,IAAI,CAACwD;oBAC1B;gBACF;YACF;QACF;QAEA3E,QAAQ4E,WAAW,GAAGZ;QACtBhE,QAAQI,YAAY,GAAGqD,uBACnB3E,kBAAkB2E,wBAClBb;QACJ5C,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQ6E,YAAY,GAAGlC;QACvB3C,QAAQ0C,YAAY,GAAGA;QAEvB,OAAOtD,cAAcW,OAAOC;IAC9B,GACA,IAAMD;AAEV;AAEA,8EAA8E;AAC9E,8EAA8E;AAC9E,0BAA0B;AAC1B,SAASsC,oBACPtC,KAA2B,EAC3BwC,MAAsB;IAEtB,MAAM,EAAEtC,GAAG,EAAEuC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMvC,UAAmB,CAAC;IAC1B,MAAM,EAAE2C,IAAI,EAAE,GAAG1C;IACjB,MAAM2C,OAAO9D,kBAAkBmB;IAC/B,MAAMC,cAAcuC,iBAAiB;IACrC,wFAAwF;IACxF7C,mBAAmBG,MAAM8C,aAAa;IAEtC7C,QAAQ8C,0BAA0B,GAAG;IAErC,IAAIN,eAAe;QACjB,OAAO1C,kBAAkBC,OAAOC,SAASC,IAAI8C,QAAQ,IAAI7C;IAC3D;IAEA,MAAM8C,iBAAiBrD,8BAA8B;QACnDM;QACAgD,SAASlD,MAAMkD,OAAO;QACtBC,MAAMnD,MAAMmD,IAAI;QAChBC,SAASpD,MAAMoD,OAAO;QACtBN,eAAe9C,MAAM8C,aAAa;IACpC;IACA,MAAM,EAAEO,oBAAoB,EAAEC,IAAI,EAAE,GAAGL;IAEvC1D,cAAcgE,IAAI,CAACD;IAEnB,OAAOA,KAAKE,IAAI,CACd;YAAC,CAACC,YAAYC,sBAAsBqB,WAAW;QAC7C,IAAIpB,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACV,eAAeW,YAAY,EAAE;YAChC,gGAAgG;YAChGX,eAAeW,YAAY,GAAGC,KAAKC,GAAG;YACtCH,cAAc;QAChB;QAEA,4DAA4D;QAC5D,IAAI,OAAOF,eAAe,UAAU;YAClC,OAAO1D,kBAAkBC,OAAOC,SAASwD,YAAYtD;QACvD;QAEA,mEAAmE;QACnE,wCAAwC;QACxC,IAAI4D,SAASC,cAAc,CAAC,yBAAyB;YACnD,OAAOjE,kBAAkBC,OAAOC,SAAS4C,MAAM1C;QACjD;QAEA,IAAI8D,cAAcjE,MAAMmD,IAAI;QAC5B,IAAI5B,eAAevB,MAAMkE,KAAK;QAC9B,IAAI5D,qBAA0C,EAAE;QAChD,qEAAqE;QACrE,qEAAqE;QACrE,6DAA6D;QAC7D,gBAAgB;QAChB,KAAK,MAAM6D,kBAAkBV,WAAY;YACvC,MAAMjC,oBAAoB2C,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAM3C,YAAY0C,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAO7C;aAAkB;YAEpE,wEAAwE;YACxE,IAAI8C,UAAUrF,4BACZ,sBAAsB;YACtBoF,mCACAJ,aACAxC,WACAoB;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIyB,YAAY,MAAM;gBACpBA,UAAUrF,4BACR,sBAAsB;gBACtBoF,mCACAhB,sBACA5B,WACAoB;YAEJ;YAEA,IAAIyB,YAAY,MAAM;gBACpB,IAAInF,4BAA4B8E,aAAaK,UAAU;oBACrD,OAAOvE,kBAAkBC,OAAOC,SAAS4C,MAAM1C;gBACjD;gBAEA,IACE,iEAAiE;gBACjE,+DAA+D;gBAC/D,+DAA+D;gBAC/D,0BAA0B;gBAC1B,oEAAoE;gBACpE,iEAAiE;gBACjE,uBAAuB;gBACvBgE,eAAepD,MAAM,KAAK,GAC1B;oBACA,MAAMiE,iBAAoCb,cAAc,CAAC,EAAE;oBAC3D,MAAMc,WAAWd,cAAc,CAAC,EAAE;oBAClC,MAAMe,OAAOf,cAAc,CAAC,EAAE;oBAE9B,MAAMgB,OAAOxF,4BACX4B,cACA0C,aACAe,gBACAC,UACAC;oBAEF,IAAIC,SAAS,QAAQA,KAAKC,IAAI,KAAK,MAAM;wBACvC,iEAAiE;wBACjE,4DAA4D;wBAE5D,+DAA+D;wBAC/D,sDAAsD;wBACtD,qDAAqD;wBACrD,8BAA8B;wBAC9B,MAAMC,qBAAwCF,KAAKG,KAAK;wBACxDhB,UAAUe;wBAEV,MAAM/D,WAAW6D,KAAKC,IAAI;wBAE1B,6DAA6D;wBAC7D,mCAAmC;wBACnC,EAAE;wBACF,iEAAiE;wBACjE,+DAA+D;wBAC/D,yDAAyD;wBACzD,2DAA2D;wBAC3D,6DAA6D;wBAC7D,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,iEAAiE;wBACjE,gDAAgD;wBAChD1F,wBACEyF,MACArG,oBACEoB,KACA+D,aACAjE,MAAMkD,OAAO,EACblD,MAAMoD,OAAO;wBAIjBnD,QAAQiE,KAAK,GAAG5C;oBAClB,OAAO;wBACL,2CAA2C;wBAC3C,kEAAkE;wBAClE,8DAA8D;wBAC9D,mBAAmB;wBACnBgD,UAAUU;oBACZ;gBACF,OAAO;oBACL,6DAA6D;oBAC7D,0CAA0C;oBAC1C,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mEAAmE;oBACnE,yDAAyD;oBACzD,qBAAqB;oBACrB,MAAMd,QAAmB1E;oBACzB,IAAI+E,UAAU;oBAEd,IACEtB,eAAeuB,MAAM,KAAKpF,yBAAyBqF,KAAK,IACxD,CAACd,aACD;wBACA,yJAAyJ;wBACzJ,uHAAuH;wBACvH,gFAAgF;wBAChF,0FAA0F;wBAC1FY,UAAUlD,gCACR6C,OACA3C,cACAC,mBACAC;wBAEF,yEAAyE;wBACzE,mFAAmF;wBACnFwB,eAAeW,YAAY,GAAGC,KAAKC,GAAG;oBACxC,OAAO;wBACLS,UAAUjF,gBACRiC,cACA2C,OACAC,gBACAlB;oBAEJ;oBAEA,MAAMyB,eAAexF,mBACnB,sBAAsB;oBACtBmF,mCACAJ;oBAGF,IAAIS,cAAc;wBAChB,2CAA2C;wBAC3CR,MAAMvC,GAAG,GAAGJ,aAAaI,GAAG;wBAC5BuC,MAAMtC,WAAW,GAAGL,aAAaK,WAAW;wBAE5C5C,sCACEkF,OACA3C,cACAC;wBAEF,8EAA8E;wBAC9EvB,QAAQiE,KAAK,GAAGA;oBAClB,OAAO,IAAIK,SAAS;wBAClBtE,QAAQiE,KAAK,GAAGA;wBAChB,4EAA4E;wBAC5E,8EAA8E;wBAC9E3C,eAAe2C;oBACjB;gBACF;gBAEAD,cAAcK;gBAEd,KAAK,MAAMK,cAAcnE,0BAA0BiB,WAAY;oBAC7D,MAAMmD,wBAAwB;2BAAIpD;2BAAsBmD;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsB7D,MAAM,GAAG,EAAE,KACvDtB,qBACA;wBACAa,mBAAmBc,IAAI,CAACwD;oBAC1B;gBACF;YACF;QACF;QAEA3E,QAAQ4E,WAAW,GAAGZ;QACtBhE,QAAQI,YAAY,GAAGqD,uBACnB3E,kBAAkB2E,wBAClBb;QACJ5C,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQ6E,YAAY,GAAGlC;QACvB3C,QAAQ0C,YAAY,GAAGA;QAEvB,OAAOtD,cAAcW,OAAOC;IAC9B,GACA,IAAMD;AAEV"}