/*
 React
 scheduler.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';function f(a,c){var b=a.length;a.push(c);a:for(;0<b;){var d=b-1>>>1,e=a[d];if(0<g(e,c))a[d]=c,a[b]=e,b=d;else break a}}function h(a){return 0===a.length?null:a[0]}function k(a){if(0===a.length)return null;var c=a[0],b=a.pop();if(b!==c){a[0]=b;a:for(var d=0,e=a.length,v=e>>>1;d<v;){var w=2*(d+1)-1,C=a[w],m=w+1,x=a[m];if(0>g(C,b))m<e&&0>g(x,C)?(a[d]=x,a[m]=b,d=m):(a[d]=C,a[w]=b,d=w);else if(m<e&&0>g(x,b))a[d]=x,a[m]=b,d=m;else break a}}return c}
function g(a,c){var b=a.sortIndex-c.sortIndex;return 0!==b?b:a.id-c.id}exports.unstable_now=void 0;if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;exports.unstable_now=function(){return l.now()}}else{var n=Date,p=n.now();exports.unstable_now=function(){return n.now()-p}}
var q=[],r=[],t=1,u=null,y=3,z=!1,A=!1,B=!1,D="function"===typeof setTimeout?setTimeout:null,E="function"===typeof clearTimeout?clearTimeout:null,F="undefined"!==typeof setImmediate?setImmediate:null;"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending?navigator.scheduling.isInputPending.bind(navigator.scheduling):null;
function G(a){for(var c=h(r);null!==c;){if(null===c.callback)k(r);else if(c.startTime<=a)k(r),c.sortIndex=c.expirationTime,f(q,c);else break;c=h(r)}}function H(a){B=!1;G(a);if(!A)if(null!==h(q))A=!0,I();else{var c=h(r);null!==c&&J(H,c.startTime-a)}}var K=!1,L=-1,M=5,N=-1;function O(){return exports.unstable_now()-N<M?!1:!0}
function P(){if(K){var a=exports.unstable_now();N=a;var c=!0;try{a:{A=!1;B&&(B=!1,E(L),L=-1);z=!0;var b=y;try{b:{G(a);for(u=h(q);null!==u&&!(u.expirationTime>a&&O());){var d=u.callback;if("function"===typeof d){u.callback=null;y=u.priorityLevel;var e=d(u.expirationTime<=a);a=exports.unstable_now();if("function"===typeof e){u.callback=e;G(a);c=!0;break b}u===h(q)&&k(q);G(a)}else k(q);u=h(q)}if(null!==u)c=!0;else{var v=h(r);null!==v&&J(H,v.startTime-a);c=!1}}break a}finally{u=null,y=b,z=!1}c=void 0}}finally{c?
Q():K=!1}}}var Q;if("function"===typeof F)Q=function(){F(P)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,S=R.port2;R.port1.onmessage=P;Q=function(){S.postMessage(null)}}else Q=function(){D(P,0)};function I(){K||(K=!0,Q())}function J(a,c){L=D(function(){a(exports.unstable_now())},c)}exports.unstable_IdlePriority=5;exports.unstable_ImmediatePriority=1;exports.unstable_LowPriority=4;exports.unstable_NormalPriority=3;exports.unstable_Profiling=null;
exports.unstable_UserBlockingPriority=2;exports.unstable_cancelCallback=function(a){a.callback=null};exports.unstable_continueExecution=function(){A||z||(A=!0,I())};exports.unstable_forceFrameRate=function(a){0>a||125<a?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<a?Math.floor(1E3/a):5};exports.unstable_getCurrentPriorityLevel=function(){return y};exports.unstable_getFirstCallbackNode=function(){return h(q)};
exports.unstable_next=function(a){switch(y){case 1:case 2:case 3:var c=3;break;default:c=y}var b=y;y=c;try{return a()}finally{y=b}};exports.unstable_pauseExecution=function(){};exports.unstable_requestPaint=function(){};exports.unstable_runWithPriority=function(a,c){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var b=y;y=a;try{return c()}finally{y=b}};
exports.unstable_scheduleCallback=function(a,c,b){var d=exports.unstable_now();"object"===typeof b&&null!==b?(b=b.delay,b="number"===typeof b&&0<b?d+b:d):b=d;switch(a){case 1:var e=-1;break;case 2:e=250;break;case 5:e=1073741823;break;case 4:e=1E4;break;default:e=5E3}e=b+e;a={id:t++,callback:c,priorityLevel:a,startTime:b,expirationTime:e,sortIndex:-1};b>d?(a.sortIndex=b,f(r,a),null===h(q)&&a===h(r)&&(B?(E(L),L=-1):B=!0,J(H,b-d))):(a.sortIndex=e,f(q,a),A||z||(A=!0,I()));return a};
exports.unstable_shouldYield=O;exports.unstable_wrapCallback=function(a){var c=y;return function(){var b=y;y=c;try{return a.apply(this,arguments)}finally{y=b}}};

//# sourceMappingURL=scheduler.production.min.js.map
