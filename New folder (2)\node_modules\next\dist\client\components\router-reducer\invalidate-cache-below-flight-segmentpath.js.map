{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts"], "names": ["invalidateCacheBelowFlightSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "delete", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "slice"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;sCALqB;AAK9B,SAASA,sCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAChD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IAEpC,MAAMK,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAI,CAACI,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACN;IAClD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,iDAAiD;IACjD,IAAIT,aAAa;QACfS,gBAAgBG,MAAM,CAACR;QACvB;IACF;IAEA,MAAMS,yBAAyBP,wBAAwBE,GAAG,CAACJ;IAC3D,IAAIU,iBAAiBL,gBAAgBD,GAAG,CAACJ;IAEzC,IAAI,CAACU,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCZ,gBAAgB,IAAIG,IAAII,eAAeP,cAAc;YACrDa,kBAAkBN,eAAeM,gBAAgB;QACnD;QACAX,gBAAgBE,GAAG,CAACP,UAAUU;IAChC;IAEAlB,sCACEkB,gBACAD,wBACAd,kBAAkBsB,KAAK,CAAC;AAE5B"}