/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
      './pages/**/*.{js,ts,jsx,tsx,mdx}',
      './components/**/*.{js,ts,jsx,tsx,mdx}',
      './app/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
      extend: {
        colors: {
          primary: '#007bff',
          secondary: '#6c757d',
          success: '#28a745',
          danger: '#dc3545',
          warning: '#ffc107',
          info: '#17a2b8',
        },
        animation: {
          'spin-slow': 'spin 20s linear infinite',
          'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
          'bounce-slow': 'bounce-slow 3s ease-in-out infinite',
        },
        keyframes: {
          'bounce-slow': {
            '0%, 100%': { transform: 'translateY(0) rotate(12deg)' },
            '50%': { transform: 'translateY(-10px) rotate(12deg)' },
          }
        }
      },
    },
    plugins: [
      require('@tailwindcss/forms'),
    ],
  }