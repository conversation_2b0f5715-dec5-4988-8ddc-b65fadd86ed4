import { Suspense } from 'react';
import { wooCommerceApi } from '@/lib/woocommerce';
import ProductGrid from '@/components/product/ProductGrid';
import ProductFilter from '@/components/product/ProductFilter';
import ShopHeader from '@/components/shop/ShopHeader';
import LoadingSkeleton from '@/components/ui/LoadingSkeleton';
import Link from 'next/link';
import { ShoppingCart, ChevronRight } from 'lucide-react';
import { notFound } from 'next/navigation';

export const metadata = {
  title: 'Shop - Deal4u',
  description: 'Browse our wide selection of premium products at amazing prices. Find electronics, fashion, home goods, and more.',
};

// Force dynamic rendering - this prevents static generation
export const dynamic = 'force-dynamic';

// Dynamic shop page - fetch data on each request for real-time sync

// Utility function to sort products client-side
function sortProducts(products, sortType) {
  const productsCopy = [...products];
  
  switch (sortType) {
    case 'price-low':
      return productsCopy.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
    case 'price-high':
      return productsCopy.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
    case 'rating':
      return productsCopy.sort((a, b) => parseFloat(b.average_rating) - parseFloat(a.average_rating));
    case 'popularity':
      return productsCopy.sort((a, b) => parseInt(b.rating_count) - parseInt(a.rating_count));
    case 'date':
      // Assuming we have a date field, sort by most recent
      return productsCopy;
    default:
      return productsCopy;
  }
}

async function getProducts() {
  try {
    console.log('🚀 ORIGINAL SIMPLE SYNC: Auto-fetching ALL 132+ products...');

    // Get ALL products by fetching in batches (WooCommerce API limit is 100 per request)
    let allProducts = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      console.log(`📄 Fetching page ${page} of products...`);

      const batchProducts = await wooCommerceApi.getProducts({
        per_page: 100,
        page: page,
        status: 'publish' // Only get PUBLISHED products (like original system)
      });

      if (batchProducts.length > 0) {
        allProducts = [...allProducts, ...batchProducts];
        console.log(`✅ Page ${page}: Added ${batchProducts.length} products (Total: ${allProducts.length})`);

        // If we got less than 100 products, this is the last page
        if (batchProducts.length < 100) {
          hasMore = false;
          console.log(`🏁 Last page reached (got ${batchProducts.length} products). Finished at page ${page}`);
        } else {
          page++;
        }
      } else {
        hasMore = false;
        console.log(`🏁 No more products found. Finished at page ${page - 1}`);
      }

      // Safety check to prevent infinite loops (reduced to 3 pages max for 132 products)
      if (page > 3) {
        console.log('⚠️ Safety limit reached (3 pages). Stopping fetch.');
        hasMore = false;
      }
    }

    console.log(`✅ ORIGINAL SYNC COMPLETE: Loaded ALL ${allProducts.length} products from your WordPress!`);

    if (allProducts.length > 0) {
      console.log('📦 Sample products:', allProducts.slice(0, 2).map(p => ({
        id: p.id,
        name: p.name?.substring(0, 30),
        status: p.status,
        hasImages: p.images?.length > 0,
        price: p.price
      })));
    }

    return allProducts;
  } catch (error) {
    console.error('❌ Error in original simple sync:', error.message);
    return [];
  }
}

async function getCategories() {
  try {
    console.log('🏷️ ORIGINAL SIMPLE SYNC: Auto-fetching categories to match products...');

    const categories = await wooCommerceApi.getCategories();

    if (Array.isArray(categories) && categories.length > 0) {
      console.log(`✅ CATEGORIES SYNCED: Found ${categories.length} real categories from WordPress`);
      console.log('📂 Categories:', categories.map(cat => `${cat.name} (${cat.count} products)`).join(', '));
    } else {
      console.log('⚠️ No categories found - using default categories');
    }

    return Array.isArray(categories) ? categories : [];
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    return [];
  }
}

export default async function ShopPage() {
  try {
    const [products, categories] = await Promise.all([
      getProducts(),
      getCategories()
    ]);

    if (!products.length) {
      return (
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="w-full py-12 flex flex-col items-center justify-center bg-white rounded-lg shadow-sm">
              <ShoppingCart className="w-16 h-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-500 text-center max-w-md mb-6">
                Please check your WooCommerce store configuration or add products to your store.
              </p>
              <div className="mt-6">
                <Link
                  href="/contact"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Contact Support
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Filters */}
            <div className="w-full md:w-64 space-y-6">
              <ProductFilter categories={categories} />
            </div>

            {/* Product Grid */}
            <div className="flex-1">
              <ProductGrid products={products} />
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in shop page:', error);
    notFound();
  }
}

// Loading Skeletons
function FilterSkeleton() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md space-y-6">
      <div className="h-6 bg-gray-200 rounded animate-pulse" />
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-4 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
      <div className="h-6 bg-gray-200 rounded animate-pulse" />
      <div className="h-20 bg-gray-200 rounded animate-pulse" />
    </div>
  );
}

function ProductGridSkeleton() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
        <div className="h-8 bg-gray-200 rounded w-40 animate-pulse" />
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(9)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 animate-pulse" />
            <div className="p-4 space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
              <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse" />
              <div className="h-8 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// No Products Found Component
function NoProductsFound({ searchParams }) {
  const hasFilters = searchParams.search || searchParams.category || searchParams.sort;

  return (
    <div className="w-full py-12 flex flex-col items-center justify-center bg-gray-50 rounded-lg">
      <ShoppingCart className="w-16 h-16 text-gray-400 mb-4" />
      <h3 className="text-xl font-semibold text-gray-900 mb-2">No Products Found</h3>
      <p className="text-gray-500 text-center max-w-md mb-6">
        We couldn't find any products matching your criteria. Please try a different search or browse our categories.
      </p>
      <div className="mt-6">
        <Link 
          href="/categories" 
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Browse Categories
          <ChevronRight className="ml-2 h-4 w-4" />
        </Link>
      </div>
      {/* Demo mode notice removed */}
    </div>
  );
}