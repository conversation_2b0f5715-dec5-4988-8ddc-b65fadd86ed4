import { wooCommerceApi } from '@/lib/woocommerce';
import ProductGrid from '@/components/product/ProductGrid';
import ProductFilter from '@/components/product/ProductFilter';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';

export const metadata = {
  title: 'Shop - Deal4u',
  description: 'Browse our wide selection of premium products at amazing prices. Find electronics, fashion, home goods, and more.',
};

// Dynamic shop page - fetch data on each request for real-time sync

// Utility function to sort products client-side
function sortProducts(products, sortType) {
  const productsCopy = [...products];
  
  switch (sortType) {
    case 'price-low':
      return productsCopy.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
    case 'price-high':
      return productsCopy.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
    case 'rating':
      return productsCopy.sort((a, b) => parseFloat(b.average_rating) - parseFloat(a.average_rating));
    case 'popularity':
      return productsCopy.sort((a, b) => parseInt(b.rating_count) - parseInt(a.rating_count));
    case 'date':
      // Assuming we have a date field, sort by most recent
      return productsCopy;
    default:
      return productsCopy;
  }
}

async function getProducts() {
  try {
    const products = await wooCommerceApi.getProducts({
      per_page: 100,
      status: 'publish'
    });
    return Array.isArray(products) ? products : [];
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

async function getCategories() {
  try {
    const categories = await wooCommerceApi.getCategories();
    return Array.isArray(categories) ? categories : [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export default async function ShopPage() {
  try {
    const [products, categories] = await Promise.all([
      getProducts(),
      getCategories()
    ]);

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Filters */}
            <div className="w-full md:w-64 space-y-6">
              <ProductFilter categories={categories} />
            </div>

            {/* Product Grid */}
            <div className="flex-1">
              {products.length > 0 ? (
                <ProductGrid products={products} />
              ) : (
                <div className="text-center py-12">
                  <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900">
                    No products available
                  </h3>
                  <p className="mt-2 text-gray-600">
                    Please check your WooCommerce store setup or try again later.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in shop page:', error);
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <ShoppingCart className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Unable to load products
            </h3>
            <p className="mt-2 text-gray-600">
              There was an error connecting to the product database. Please try again later.
            </p>
            <div className="mt-6">
              <Link
                href="/"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Return Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

// Loading Skeletons
function FilterSkeleton() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md space-y-6">
      <div className="h-6 bg-gray-200 rounded animate-pulse" />
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-4 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
      <div className="h-6 bg-gray-200 rounded animate-pulse" />
      <div className="h-20 bg-gray-200 rounded animate-pulse" />
    </div>
  );
}

function ProductGridSkeleton() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
        <div className="h-8 bg-gray-200 rounded w-40 animate-pulse" />
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(9)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 animate-pulse" />
            <div className="p-4 space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
              <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse" />
              <div className="h-8 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// No Products Found Component
function NoProductsFound({ searchParams }) {
  const hasFilters = searchParams.search || searchParams.category || searchParams.sort;

  return (
    <div className="w-full py-12 flex flex-col items-center justify-center bg-gray-50 rounded-lg">
      <ShoppingCart className="w-16 h-16 text-gray-400 mb-4" />
      <h3 className="text-xl font-semibold text-gray-900 mb-2">No Products Found</h3>
      <p className="text-gray-500 text-center max-w-md mb-6">
        We couldn't find any products matching your criteria. Please try a different search or browse our categories.
      </p>
      <div className="mt-6">
        <Link 
          href="/categories" 
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Browse Categories
          <ChevronRight className="ml-2 h-4 w-4" />
        </Link>
      </div>
      {/* Demo mode notice removed */}
    </div>
  );
}