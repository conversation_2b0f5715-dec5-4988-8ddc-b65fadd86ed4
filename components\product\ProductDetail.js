'use client';

import { useState } from 'react';
import Image from 'next/image';
import {
  Minus, Plus, ShoppingCart, AlertCircle, Package, Truck, Shield, Star,
  Heart, Share2, Eye, Zap, Award, CheckCircle, Clock, ArrowRight,
  ImageIcon, Sparkles, ThumbsUp, Users, TrendingUp, Gift, Verified
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useCart } from '@/context/CartContext';

const ProductDetail = ({ product }) => {
  const { addToCart, isInCart, getItemQuantity, updateQuantity } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [imageError, setImageError] = useState(false);

  // Format price with currency
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const currentPrice = selectedVariant ? selectedVariant.price : product.price;
  const regularPrice = selectedVariant ? selectedVariant.regular_price : product.regular_price;
  const salePrice = selectedVariant ? selectedVariant.sale_price : product.sale_price;
  const isOnSale = regularPrice > currentPrice;

  const handleQuantityChange = (amount) => {
    const newQuantity = Math.max(1, quantity + amount);
    if (product.stockQuantity && newQuantity > product.stockQuantity) {
      toast.error('Not enough stock available');
      return;
    }
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    try {
      setIsLoading(true);

      // Check if the product is in stock
      if (!product.inStock) {
        toast.error('This product is out of stock');
        return;
      }

      // Check stock availability
      if (product.stockQuantity && quantity > product.stockQuantity) {
        toast.error('Not enough stock available');
        return;
      }

      // Prepare the item to add to cart
      const itemToAdd = {
        id: product.id,
        name: product.name,
        price: currentPrice,
        image: product.images?.[activeImageIndex]?.src || product.image || '/placeholder.jpg',
        quantity: quantity,
        stockQuantity: product.stockQuantity || 0,
        inStock: product.inStock
      };

      // If a variant is selected, include that information
      if (selectedVariant) {
        itemToAdd.selectedVariant = selectedVariant;
        itemToAdd.name = `${product.name} - ${selectedVariant.name || 'Selected Option'}`;
      }

      await addToCart(itemToAdd, quantity);
      toast.success(`Added ${itemToAdd.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Get product image with proper fallback
  const getProductImage = (image) => {
    if (imageError) return '/placeholder.jpg';
    
    // If image is already a string URL, use it directly
    if (typeof image === 'string') return image;
    
    // Check for WooCommerce image object
    if (image && image.src) return image.src;
    
    return '/placeholder.jpg';
  };

  // Get all product images
  const getProductImages = () => {
    // Check for WooCommerce image structure
    if (product.images && product.images.length > 0) {
      return product.images.map(img => getProductImage(img));
    }
    
    // Check for direct image URL
    if (product.image) {
      return [getProductImage(product.image)];
    }
    
    return [];
  };

  const productImages = getProductImages();
  const isInStock = product.inStock || product.stockStatus === 'instock';

  // Calculate discount percentage
  const discountPercentage = isOnSale ? Math.round(((regularPrice - currentPrice) / regularPrice) * 100) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
          <span>Home</span>
          <ArrowRight className="w-4 h-4" />
          <span>Products</span>
          <ArrowRight className="w-4 h-4" />
          <span className="text-gray-900 font-medium truncate">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images Section */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative group">
              <div className="aspect-square rounded-3xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-2xl">
                <Image
                  src={getProductImage(product.images?.[activeImageIndex] || product.image)}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                  onError={() => setImageError(true)}
                  priority
                />

                {/* Discount Badge */}
                {discountPercentage > 0 && (
                  <div className="absolute top-6 left-6 z-10">
                    <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full shadow-lg">
                      <span className="font-bold text-lg">-{discountPercentage}%</span>
                    </div>
                  </div>
                )}

                {/* Stock Status Badge */}
                <div className="absolute top-6 right-6 z-10">
                  <div className={`px-4 py-2 rounded-full shadow-lg ${
                    isInStock
                      ? 'bg-green-500 text-white'
                      : 'bg-red-500 text-white'
                  }`}>
                    <span className="font-medium text-sm">
                      {isInStock ? '✓ In Stock' : '✗ Out of Stock'}
                    </span>
                  </div>
                </div>

                {/* Action Buttons Overlay */}
                <div className="absolute bottom-6 right-6 flex space-x-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg hover:bg-white transition-colors">
                    <Heart className="w-5 h-5 text-gray-700" />
                  </button>
                  <button className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg hover:bg-white transition-colors">
                    <Share2 className="w-5 h-5 text-gray-700" />
                  </button>
                  <button className="bg-white/90 backdrop-blur-sm p-3 rounded-full shadow-lg hover:bg-white transition-colors">
                    <Eye className="w-5 h-5 text-gray-700" />
                  </button>
                </div>
              </div>
            </div>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className="flex space-x-3 overflow-x-auto pb-2">
                {product.images.slice(0, 4).map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-xl overflow-hidden border-2 transition-all ${
                      activeImageIndex === index
                        ? 'border-blue-500 shadow-lg'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Image
                      src={getProductImage(image)}
                      alt={`${product.name} ${index + 1}`}
                      width={80}
                      height={80}
                      className="object-cover w-full h-full"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info Section */}
          <div className="space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-yellow-600 bg-yellow-50 px-3 py-1 rounded-full">
                  Premium Quality
                </span>
              </div>

              <h1 className="text-4xl font-bold text-gray-900 leading-tight">
                {product.name}
              </h1>

              {/* Rating */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(product.rating || 4.5)
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-600">
                  {product.rating || 4.5} ({product.reviews || 0} reviews)
                </span>
                <div className="flex items-center space-x-1 text-green-600">
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm font-medium">95% Recommended</span>
                </div>
              </div>
            </div>

            {/* Price Section */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-2xl border border-blue-100">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="flex items-center space-x-4">
                    <span className="text-4xl font-bold text-gray-900">
                      {formatPrice(currentPrice)}
                    </span>
                    {isOnSale && (
                      <>
                        <span className="text-2xl text-gray-500 line-through">
                          {formatPrice(regularPrice)}
                        </span>
                        <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                          Save {formatPrice(regularPrice - currentPrice)}
                        </span>
                      </>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <TrendingUp className="w-4 h-4" />
                    <span>Price trending down</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    Best Price
                  </div>
                </div>
              </div>
            </div>

            {/* Product Description */}
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <ImageIcon className="w-5 h-5 mr-2 text-blue-500" />
                Product Details
              </h3>
              <div className="prose prose-gray max-w-none">
                <div dangerouslySetInnerHTML={{
                  __html: product.short_description || product.description || 'High-quality product with excellent features and durability.'
                }} />
              </div>
            </div>

            {/* Quantity and Add to Cart */}
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 space-y-6">
              {/* Quantity Selector */}
              <div className="flex items-center justify-between">
                <span className="text-lg font-semibold text-gray-900">Quantity:</span>
                <div className="flex items-center bg-gray-50 rounded-xl border border-gray-200">
                  <button
                    onClick={() => handleQuantityChange(-1)}
                    className="p-3 hover:bg-gray-100 rounded-l-xl transition-colors"
                    disabled={quantity <= 1 || isLoading}
                  >
                    <Minus className="w-5 h-5 text-gray-600" />
                  </button>
                  <span className="px-6 py-3 text-lg font-semibold text-gray-900 min-w-[4rem] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => handleQuantityChange(1)}
                    className="p-3 hover:bg-gray-100 rounded-r-xl transition-colors"
                    disabled={isLoading || (product.stockQuantity && quantity >= product.stockQuantity)}
                  >
                    <Plus className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <button
                  onClick={handleAddToCart}
                  disabled={!isInStock || isLoading}
                  className={`relative overflow-hidden py-4 px-6 rounded-xl flex items-center justify-center gap-3 font-semibold text-lg transition-all transform hover:scale-105 ${
                    isInStock
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-400 cursor-not-allowed text-white'
                  }`}
                >
                  <ShoppingCart className="w-6 h-6" />
                  {isLoading ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Adding...
                    </span>
                  ) : isInStock ? 'Add to Cart' : 'Out of Stock'}

                  {isInStock && (
                    <div className="absolute inset-0 bg-white opacity-0 hover:opacity-20 transition-opacity rounded-xl"></div>
                  )}
                </button>

                <button className="py-4 px-6 rounded-xl border-2 border-gray-300 hover:border-gray-400 flex items-center justify-center gap-3 font-semibold text-gray-700 hover:text-gray-900 transition-all">
                  <Heart className="w-6 h-6" />
                  Add to Wishlist
                </button>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-500 p-2 rounded-lg">
                    <Package className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Free Shipping</h4>
                    <p className="text-sm text-gray-600">On orders over $50</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-4 rounded-xl border border-blue-100">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-500 p-2 rounded-lg">
                    <Truck className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Fast Delivery</h4>
                    <p className="text-sm text-gray-600">2-3 business days</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-100">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-500 p-2 rounded-lg">
                    <Shield className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Secure Payment</h4>
                    <p className="text-sm text-gray-600">SSL protected</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Features */}
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-2xl border border-yellow-200">
              <div className="flex items-center space-x-3 mb-4">
                <Gift className="w-6 h-6 text-orange-500" />
                <h3 className="text-lg font-semibold text-gray-900">Special Offers</h3>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>30-day money-back guarantee</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>1-year warranty included</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>24/7 customer support</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Authentic product guarantee</span>
                </div>
              </div>
            </div>

            {/* Social Proof */}
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Users className="w-5 h-5 mr-2 text-blue-500" />
                  Customer Activity
                </h3>
                <span className="text-sm text-gray-500">Live updates</span>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-gray-600">
                    <strong>23 people</strong> are viewing this product right now
                  </span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">
                    <strong>156 people</strong> bought this in the last 24 hours
                  </span>
                </div>
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600">
                    <strong>4.8/5</strong> average rating from verified buyers
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
