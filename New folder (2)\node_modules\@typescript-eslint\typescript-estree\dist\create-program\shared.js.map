{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["../../src/create-program/shared.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAwB;AAExB,+CAAiC;AAcjC;;GAEG;AACH,MAAM,qBAAqB,GAAuB;IAChD,MAAM,EAAE,IAAI,EAAE,qDAAqD;IAEnE;;OAEG;IACH,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;CACzB,CAAC;AAyGA,sDAAqB;AAvGvB;;GAEG;AACH,MAAM,wBAAwB,GAAuB;IACnD,GAAG,qBAAqB;IACxB,oBAAoB,EAAE,IAAI;IAC1B,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,SAAS,qCAAqC,CAC5C,aAA4B;IAE5B,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;QAC/C,OAAO;YACL,GAAG,wBAAwB;YAC3B,mBAAmB,EAAE,IAAI;SAC1B,CAAC;IACJ,CAAC;IAED,OAAO,wBAAwB,CAAC;AAClC,CAAC;AAqFC,sFAAqC;AAhFvC,8EAA8E;AAC9E,MAAM,yBAAyB;AAC7B,uEAAuE;AACvE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC;AACjE,MAAM,iBAAiB,GAAG,yBAAyB;IACjD,CAAC,CAAC,CAAC,QAAgB,EAAU,EAAE,CAAC,QAAQ;IACxC,CAAC,CAAC,CAAC,QAAgB,EAAU,EAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AAEzD,SAAS,oBAAoB,CAAC,QAAgB;IAC5C,IAAI,UAAU,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC1C,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAClC,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,iBAAiB,CAAC,UAAU,CAAkB,CAAC;AACxD,CAAC;AAqEC,oDAAoB;AAnEtB,SAAS,kBAAkB,CAAC,CAAS,EAAE,eAAuB;IAC5D,OAAO,cAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,eAAe,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACrD,CAAC;AA8DC,gDAAkB;AA5DpB,SAAS,gBAAgB,CAAC,CAAgB;IACxC,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAkB,CAAC;AAC1C,CAAC;AAsDC,4CAAgB;AApDlB,MAAM,qBAAqB,GAAG;IAC5B,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,IAAI;IACjB,EAAE,CAAC,SAAS,CAAC,IAAI;CACT,CAAC;AACX,SAAS,YAAY,CAAC,QAA4B;IAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CACzC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CACjC,IAAI,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAuB,EACvB,QAAgB;IAEhB,MAAM,GAAG,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEnD,oFAAoF;IACpF,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAChD,IAAI,WAAW,KAAK,WAAW,EAAE,CAAC;QAChC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;AACjD,CAAC;AA2BC,8CAAiB;AAzBnB;;;;GAIG;AACH,SAAS,UAAU,CAAC,OAAe;IACjC,qCAAqC;IACrC,uEAAuE;IACvE,IAAI,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC;QACvB,OAAO,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAUC,gCAAU"}