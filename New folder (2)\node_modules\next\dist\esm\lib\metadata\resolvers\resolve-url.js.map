{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "names": ["path", "isStringOrURL", "icon", "URL", "createLocalMetadataBase", "process", "env", "PORT", "getPreviewDeploymentUrl", "origin", "VERCEL_BRANCH_URL", "VERCEL_URL", "undefined", "getProductionDeploymentUrl", "VERCEL_PROJECT_PRODUCTION_URL", "getSocialImageFallbackMetadataBase", "metadataBase", "isMetadataBaseMissing", "defaultMetadataBase", "previewDeploymentUrl", "productionDeploymentUrl", "fallbackMetadataBase", "NODE_ENV", "VERCEL_ENV", "resolveUrl", "url", "parsedUrl", "basePath", "pathname", "joinedPath", "posix", "join", "resolveRelativeUrl", "startsWith", "resolve", "FILE_REGEX", "isFilePattern", "test", "resolveAbsoluteUrlWithPathname", "trailingSlash", "resolvedUrl", "result", "href", "endsWith", "isRelative", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isExternal", "isFileUrl"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AAGtD,SAASC,cAAcC,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,OAAO,IAAID,IAAI,CAAC,iBAAiB,EAAEE,QAAQC,GAAG,CAACC,IAAI,IAAI,KAAK,CAAC;AAC/D;AAEA,SAASC;IACP,MAAMC,SAASJ,QAAQC,GAAG,CAACI,iBAAiB,IAAIL,QAAQC,GAAG,CAACK,UAAU;IACtE,OAAOF,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,OAAO,CAAC,IAAIG;AACjD;AAEA,SAASC;IACP,MAAMJ,SAASJ,QAAQC,GAAG,CAACQ,6BAA6B;IACxD,OAAOL,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,OAAO,CAAC,IAAIG;AACjD;AAEA,uFAAuF;AACvF,qDAAqD;AACrD,OAAO,SAASG,mCAAmCC,YAAwB;IAIzE,MAAMC,wBAAwB,CAACD;IAC/B,MAAME,sBAAsBd;IAC5B,MAAMe,uBAAuBX;IAC7B,MAAMY,0BAA0BP;IAEhC,IAAIQ;IACJ,IAAIhB,QAAQC,GAAG,CAACgB,QAAQ,KAAK,eAAe;QAC1CD,uBAAuBH;IACzB,OAAO;QACLG,uBACEhB,QAAQC,GAAG,CAACgB,QAAQ,KAAK,gBACzBH,wBACAd,QAAQC,GAAG,CAACiB,UAAU,KAAK,YACvBJ,uBACAH,gBAAgBI,2BAA2BF;IACnD;IAEA,OAAO;QACLG;QACAJ;IACF;AACF;AAQA,SAASO,WACPC,GAAoC,EACpCT,YAAwB;IAExB,IAAIS,eAAetB,KAAK,OAAOsB;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAIvB,IAAIsB;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACV,cAAc;QACjBA,eAAeZ;IACjB;IAEA,oCAAoC;IACpC,MAAMuB,WAAWX,aAAaY,QAAQ,IAAI;IAC1C,MAAMC,aAAa7B,KAAK8B,KAAK,CAACC,IAAI,CAACJ,UAAUF;IAE7C,OAAO,IAAItB,IAAI0B,YAAYb;AAC7B;AAEA,uDAAuD;AACvD,SAASgB,mBAAmBP,GAAiB,EAAEG,QAAgB;IAC7D,IAAI,OAAOH,QAAQ,YAAYA,IAAIQ,UAAU,CAAC,OAAO;QACnD,OAAOjC,KAAK8B,KAAK,CAACI,OAAO,CAACN,UAAUH;IACtC;IACA,OAAOA;AACT;AAEA,+EAA+E;AAC/E,MAAMU,aACJ;AACF,SAASC,cAAcR,QAAgB;IACrC,OAAOO,WAAWE,IAAI,CAACT;AACzB;AAEA,kFAAkF;AAClF,SAASU,+BACPb,GAAiB,EACjBT,YAAwB,EACxB,EAAEuB,aAAa,EAAEX,QAAQ,EAAmB;IAE5C,wDAAwD;IACxDH,MAAMO,mBAAmBP,KAAKG;IAE9B,6DAA6D;IAC7D,yDAAyD;IACzD,IAAIY,cAAc;IAClB,MAAMC,SAASzB,eAAeQ,WAAWC,KAAKT,gBAAgBS;IAC9D,IAAI,OAAOgB,WAAW,UAAU;QAC9BD,cAAcC;IAChB,OAAO;QACLD,cAAcC,OAAOb,QAAQ,KAAK,MAAMa,OAAOhC,MAAM,GAAGgC,OAAOC,IAAI;IACrE;IAEA,oEAAoE;IACpE,gDAAgD;IAChD,uBAAuB;IACvB,IAAIH,iBAAiB,CAACC,YAAYG,QAAQ,CAAC,MAAM;QAC/C,IAAIC,aAAaJ,YAAYP,UAAU,CAAC;QACxC,IAAIY,WAAWL,YAAYM,QAAQ,CAAC;QACpC,IAAIC,aAAa;QACjB,IAAIC,YAAY;QAEhB,IAAI,CAACJ,YAAY;YACf,IAAI;gBACF,MAAMlB,YAAY,IAAIvB,IAAIqC;gBAC1BO,aACE/B,gBAAgB,QAAQU,UAAUjB,MAAM,KAAKO,aAAaP,MAAM;gBAClEuC,YAAYZ,cAAcV,UAAUE,QAAQ;YAC9C,EAAE,OAAM;gBACN,gDAAgD;gBAChDmB,aAAa;YACf;YACA,IACE,kGAAkG;YAClG,CAACC,aACD,CAACD,cACD,CAACF,UAED,OAAO,CAAC,EAAEL,YAAY,CAAC,CAAC;QAC5B;IACF;IAEA,OAAOA;AACT;AAEA,SACEvC,aAAa,EACbuB,UAAU,EACVQ,kBAAkB,EAClBM,8BAA8B,KAC/B"}