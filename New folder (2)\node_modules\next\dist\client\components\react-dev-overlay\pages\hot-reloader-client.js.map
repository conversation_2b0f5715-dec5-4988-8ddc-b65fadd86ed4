{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hot-reloader-client.ts"], "names": ["connect", "performFullReload", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "MODE", "mode", "register", "addMessageListener", "payload", "processMessage", "err", "console", "warn", "stack", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "RuntimeError<PERSON>andler", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "sendMessage", "handleUpdateError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdates", "onBeforeFastRefresh", "onFastRefresh", "onBuildOk", "handleWarnings", "warnings", "printWarnings", "formatted", "formatWebpackMessages", "errors", "i", "length", "stripAnsi", "handleErrors", "onBuildError", "error", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "startLatency", "undefined", "updatedModules", "onBeforeRefresh", "onRefresh", "reportHmrLatency", "endLatency", "latency", "log", "JSON", "stringify", "event", "id", "startTime", "endTime", "location", "pathname", "isPageHidden", "document", "visibilityState", "__NEXT_HMR_LATENCY_CB", "handleAvailableHash", "hash", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "reload", "SERVER_ERROR", "errorJSON", "message", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "extractModulesFromTurbopackMessage", "data", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "onBeforeHotUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;;;;;;;;;;;;;;;;IA2C3G,OAmCC;eAnCuBA;;IA4ZRC,iBAAiB;eAAjBA;;;;wBA9bT;oEACe;2BAC0B;gFACd;kCACU;oDAKO;wBACE;qCACjB;AAkBpCC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC5E,IAAIC,OAAgC;AACrB,SAASX,QAAQY,IAA6B;IAC3DD,OAAOC;IACPC,IAAAA,gBAAQ;IAERC,IAAAA,6BAAkB,EAAC,CAACC;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAU;gBAE+BA;YADhDC,QAAQC,IAAI,CACV,4BAA4BJ,UAAU,OAAQE,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKG,KAAK,YAAVH,aAAc,EAAC;QAEjE;IACF;IAEA,OAAO;QACLI,qBAAoBC,OAAY;YAC9Bb,wBAAwBa;QAC1B;QACAC;YACEC,wCAAmB,CAACC,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEjB,0BAA0BkB,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9BC,IAAAA,sBAAW,EAACD;QACd;QACAE,mBAAkBf,GAAY;YAC5BhB,kBAAkBgB;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIgB,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOlB,YAAY,eAAe,OAAOA,QAAQmB,KAAK,KAAK,YAAY;QACzE,IAAIF,kBAAkB;YACpBjB,QAAQmB,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPF;IAEA,IAAIzB,SAAS,WAAW;QACtB,MAAM4B,cACJ,CAACN,sBACA/B,OAAOsC,aAAa,CAACC,IAAI,KAAK,aAAaC;QAC9CT,qBAAqB;QACrBE,mBAAmB;QAEnB,0CAA0C;QAC1C,IAAII,aAAa;YACfI,gBAAgBC,qBAAqBC;QACvC;IACF,OAAO;QACLC,IAAAA,iBAAS;IACX;AACF;AAEA,2CAA2C;AAC3C,SAASC,eAAeC,QAAa;IACnCZ;IAEA,MAAMG,cAAc,CAACN;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASc;QACP,iCAAiC;QACjC,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCH,UAAUA;YACVI,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOlC,YAAY,eAAe,OAAOA,QAAQC,IAAI,KAAK,YAAY;gBACpD+B;YAApB,IAAK,IAAIG,IAAI,GAAGA,MAAIH,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBI,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXnC,QAAQC,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAD,QAAQC,IAAI,CAACoC,IAAAA,kBAAS,EAACL,UAAUF,QAAQ,CAACK,EAAE;YAC9C;QACF;IACF;IAEAJ;IAEA,0CAA0C;IAC1C,IAAIV,aAAa;QACfI,gBAAgBC,qBAAqBC;IACvC;AACF;AAEA,kEAAkE;AAClE,SAASW,aAAaJ,MAAW;IAC/BhB;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIe,YAAYC,IAAAA,8BAAqB,EAAC;QACpCC,QAAQA;QACRJ,UAAU,EAAE;IACd;IAEA,6BAA6B;IAC7BS,IAAAA,oBAAY,EAACP,UAAUE,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAOlC,YAAY,eAAe,OAAOA,QAAQwC,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIL,IAAI,GAAGA,IAAIH,UAAUE,MAAM,CAACE,MAAM,EAAED,IAAK;YAChDnC,QAAQwC,KAAK,CAACH,IAAAA,kBAAS,EAACL,UAAUE,MAAM,CAACC,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIM,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACb,UAAUE,MAAM,CAAC,EAAE;YACtCU,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,eAAmCC;AAEvC,SAASrB,oBAAoBsB,cAAwB;IACnD,IAAIA,eAAeZ,MAAM,GAAG,GAAG;QAC7B,2DAA2D;QAC3D,sBAAsB;QACtBa,IAAAA,uBAAe;IACjB;AACF;AAEA,SAAStB,cAAcqB,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAC/DpB,IAAAA,iBAAS;IACT,IAAIoB,eAAeZ,MAAM,KAAK,GAAG;QAC/B;IACF;IAEAc,IAAAA,iBAAS;IAETC;AACF;AAEA,SAASA,iBAAiBH,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAClE,IAAIF,cAAc;QAChB,MAAMM,aAAa/D,KAAKC,GAAG;QAC3B,MAAM+D,UAAUD,aAAaN;QAC7B9C,QAAQsD,GAAG,CAAC,AAAC,4BAAyBD,UAAQ;QAC9CxC,IAAAA,sBAAW,EACT0C,KAAKC,SAAS,CAAC;YACbC,OAAO;YACPC,IAAI1E,OAAOC,iBAAiB;YAC5B0E,WAAWb;YACXc,SAASR;YACT7B,MAAMvC,OAAO6E,QAAQ,CAACC,QAAQ;YAC9Bd;YACA,oEAAoE;YACpE,sDAAsD;YACtDe,cAAcC,SAASC,eAAe,KAAK;QAC7C;QAEF,IAAIrB,KAAKsB,qBAAqB,EAAE;YAC9BtB,KAAKsB,qBAAqB,CAACb;QAC7B;IACF;AACF;AAEA,kDAAkD;AAClD,SAASc,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpD,4BAA4BoD;AAC9B;AAEA,2DAA2D,GAC3D,SAAStE,eAAeuE,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,sEAAsE;IACtE,OAAQA,IAAIC,MAAM;QAChB,KAAKC,6CAA2B,CAACC,QAAQ;YAAE;gBACzC1B,eAAezD,KAAKC,GAAG;gBACvBU,QAAQsD,GAAG,CAAC;gBACZ;YACF;QACA,KAAKiB,6CAA2B,CAACE,KAAK;QACtC,KAAKF,6CAA2B,CAACG,IAAI;YAAE;gBACrC,IAAIL,IAAID,IAAI,EAAED,oBAAoBE,IAAID,IAAI;gBAE1C,MAAM,EAAElC,MAAM,EAAEJ,QAAQ,EAAE,GAAGuC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKM,IAAAA,qBAAa,EAACN,IAAIO,WAAW;gBAEvD,MAAMC,YAAYC,QAAQ5C,UAAUA,OAAOE,MAAM;gBACjD,IAAIyC,WAAW;oBACbhE,IAAAA,sBAAW,EACT0C,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPsB,YAAY7C,OAAOE,MAAM;wBACzB4C,UAAUhG,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOqD,aAAaJ;gBACtB;gBAEA,MAAM+C,cAAcH,QAAQhD,YAAYA,SAASM,MAAM;gBACvD,IAAI6C,aAAa;oBACfpE,IAAAA,sBAAW,EACT0C,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPyB,cAAcpD,SAASM,MAAM;wBAC7B4C,UAAUhG,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO4C,eAAeC;gBACxB;gBAEAjB,IAAAA,sBAAW,EACT0C,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPuB,UAAUhG,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOmC;YACT;QACA,KAAKmD,6CAA2B,CAACY,wBAAwB;YAAE;gBACzDnG,OAAO6E,QAAQ,CAACuB,MAAM;gBACtB;YACF;QACA,KAAKb,6CAA2B,CAACc,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGjB;gBACtB,IAAIiB,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAErF,KAAK,EAAE,GAAGqD,KAAKiC,KAAK,CAACF;oBACtC,MAAM9C,QAAQ,IAAIiD,MAAMF;oBACxB/C,MAAMtC,KAAK,GAAGA;oBACdoC,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK+B,6CAA2B,CAACmB,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYnG,0BAA2B;oBAChDmG,SAAS;wBACPC,MAAMrB,6CAA2B,CAACmB,mBAAmB;oBACvD;gBACF;gBACA;YACF;QACA,KAAKnB,6CAA2B,CAACsB,iBAAiB;YAAE;gBAClD,MAAM7C,iBAAiB8C,IAAAA,sEAAkC,EAACzB,IAAI0B,IAAI;gBAClErE,oBAAoBsB;gBACpB,KAAK,MAAM2C,YAAYnG,0BAA2B;oBAChDmG,SAAS;wBACPC,MAAMrB,6CAA2B,CAACsB,iBAAiB;wBACnDE,MAAM1B,IAAI0B,IAAI;oBAChB;gBACF;gBACA,IAAIzF,wCAAmB,CAACC,eAAe,EAAE;oBACvCP,QAAQC,IAAI,CAAC+F,4CAAoC;oBACjDjH,kBAAkB;gBACpB;gBACAmE,IAAAA,iBAAS;gBACTC,iBAAiBH;gBACjB;YACF;QACA;YAAS;gBACP,IAAIzD,uBAAuB;oBACzBA,sBAAsB8E;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAAS7C;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOR,8BAA8BiF;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASnG,QAAQiG,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrB,yIAAyI;gBACzIF,OAAOC,GAAG,CAACI,mBAAmB,CAACpG;gBAC/BmG;YACF;QACF;QACA,yIAAyI;QACzIJ,OAAOC,GAAG,CAACK,gBAAgB,CAACrG;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASqB,gBACPiF,iBAAsE,EACtEC,kBAAyD;IAEzD,yIAAyI;IACzI,IAAI,CAACR,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DpG,QAAQwC,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAAChB,uBAAuB,CAAC0E,mBAAmB;QAC9CtE,IAAAA,iBAAS;QACT;IACF;IAEA,SAASgF,mBAAmB7G,GAAQ,EAAEiD,cAA+B;QACnE,IAAIjD,OAAOO,wCAAmB,CAACC,eAAe,IAAI,CAACyC,gBAAgB;YACjE,IAAIjD,KAAK;gBACPC,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIK,wCAAmB,CAACC,eAAe,EAAE;gBAC9CP,QAAQC,IAAI,CACV;YAEJ;YACAlB,kBAAkBgB;YAClB;QACF;QAEA,IAAI,OAAO4G,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmB3D;QACrB;QAEA,IAAIxB,qBAAqB;YACvB,+DAA+D;YAC/D,6DAA6D;YAC7DC,gBACEuB,eAAeZ,MAAM,GAAG,IAAIW,YAAY2D,mBACxC1D,eAAeZ,MAAM,GAAG,IAAIR,iBAAS,GAAG+E;QAE5C,OAAO;YACL/E,IAAAA,iBAAS;YACT,IAAIa,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChC2D,kBAAkB;oBAChB,IAAI1D,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,yIAAyI;IACzIsD,OAAOC,GAAG,CACPS,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC9D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAO0D,sBAAsB,YAAY;YAC3CA,kBAAkB1D;QACpB;QACA,yIAAyI;QACzI,OAAOmD,OAAOC,GAAG,CAACW,KAAK;IACzB,GACCD,IAAI,CACH,CAAC9D;QACC4D,mBAAmB,MAAM5D;IAC3B,GACA,CAACjD;QACC6G,mBAAmB7G,KAAK;IAC1B;AAEN;AAEO,SAAShB,kBAAkBgB,GAAQ;IACxC,MAAMiH,aACJjH,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAAC+G,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDpH,IAAIwF,OAAO,IACXxF,MAAM,EAAC;IAEXc,IAAAA,sBAAW,EACT0C,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPuD;QACAzG,iBAAiB,CAAC,CAACD,wCAAmB,CAACC,eAAe;QACtD6G,iBAAiBrH,MAAMA,IAAIqH,eAAe,GAAGrE;IAC/C;IAGF/D,OAAO6E,QAAQ,CAACuB,MAAM;AACxB"}