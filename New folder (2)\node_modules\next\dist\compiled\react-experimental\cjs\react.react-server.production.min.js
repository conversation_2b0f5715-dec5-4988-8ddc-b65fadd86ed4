/*
 React
 react.react-server.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var l=Object.assign,m={current:null};function n(){return new Map}
if("function"===typeof fetch){var p=fetch,q=function(a,b){var c=m.current;if(!c||b&&b.signal&&b.signal!==c.getCacheSignal())return p(a,b);if("string"!==typeof a||b){var d="string"===typeof a||a instanceof URL?new Request(a,b):a;if("GET"!==d.method&&"HEAD"!==d.method||d.keepalive)return p(a,b);var e=JSON.stringify([d.method,Array.from(d.headers.entries()),d.mode,d.redirect,d.credentials,d.referrer,d.referrerPolicy,d.integrity]);d=d.url}else e='["GET",[],null,"follow",null,null,null,null]',d=a;var g=
c.getCacheForType(n);c=g.get(d);if(void 0===c)a=p(a,b),g.set(d,[e,a]);else{d=0;for(g=c.length;d<g;d+=2){var f=c[d+1];if(c[d]===e)return a=f,a.then(function(h){return h.clone()})}a=p(a,b);c.push(e,a)}return a.then(function(h){return h.clone()})};l(q,p);try{fetch=q}catch(a){try{globalThis.fetch=q}catch(b){console.warn("React was unable to patch the fetch() function in this environment. Suspensey APIs might not work correctly as a result.")}}}
var r={current:null},t={ReactCurrentDispatcher:r,ReactCurrentOwner:{current:null}},u=new WeakMap,v=new Map,w=new Set,x=new Set,y={ReactCurrentCache:m,TaintRegistryObjects:u,TaintRegistryValues:v,TaintRegistryByteLengths:w,TaintRegistryPendingRequests:x};
function z(a){var b="https://react.dev/errors/"+a;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var c=2;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c])}return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
var A=Array.isArray,B=Symbol.for("react.element"),C=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),F=Symbol.for("react.profiler"),G=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),I=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),K=Symbol.for("react.debug_trace_mode"),aa=Symbol.for("react.postpone"),L=Symbol.iterator;
function ba(a){if(null===a||"object"!==typeof a)return null;a=L&&a[L]||a["@@iterator"];return"function"===typeof a?a:null}var M=Object.prototype.hasOwnProperty,N=t.ReactCurrentOwner;function O(a,b,c,d,e,g,f){c=f.ref;return{$$typeof:B,type:a,key:b,ref:void 0!==c?c:null,props:f,_owner:g}}function ca(a,b){return O(a.type,b,null,void 0,void 0,a._owner,a.props)}function P(a){return"object"===typeof a&&null!==a&&a.$$typeof===B}
function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(c){return b[c]})}var Q=/\/+/g;function R(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}function S(){}
function da(a){switch(a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;default:switch("string"===typeof a.status?a.then(S,S):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case "fulfilled":return a.value;case "rejected":throw a.reason;}}throw a;}
function T(a,b,c,d,e){var g=typeof a;if("undefined"===g||"boolean"===g)a=null;var f=!1;if(null===a)f=!0;else switch(g){case "string":case "number":f=!0;break;case "object":switch(a.$$typeof){case B:case C:f=!0;break;case J:return f=a._init,T(f(a._payload),b,c,d,e)}}if(f)return e=e(a),f=""===d?"."+R(a,0):d,A(e)?(c="",null!=f&&(c=f.replace(Q,"$&/")+"/"),T(e,b,c,"",function(ea){return ea})):null!=e&&(P(e)&&(e=ca(e,c+(!e.key||a&&a.key===e.key?"":(""+e.key).replace(Q,"$&/")+"/")+f)),b.push(e)),1;f=0;var h=
""===d?".":d+":";if(A(a))for(var k=0;k<a.length;k++)d=a[k],g=h+R(d,k),f+=T(d,b,c,g,e);else if(k=ba(a),"function"===typeof k)for(a=k.call(a),k=0;!(d=a.next()).done;)d=d.value,g=h+R(d,k++),f+=T(d,b,c,g,e);else if("object"===g){if("function"===typeof a.then)return T(da(a),b,c,d,e);b=String(a);throw Error(z(31,"[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b));}return f}
function U(a,b,c){if(null==a)return a;var d=[],e=0;T(a,d,"","",function(g){return b.call(c,g,e++)});return d}function fa(a){if(-1===a._status){var b=a._result;b=b();b.then(function(c){if(0===a._status||-1===a._status)a._status=1,a._result=c},function(c){if(0===a._status||-1===a._status)a._status=2,a._result=c});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}function ha(){return new WeakMap}function V(){return{s:0,v:void 0,o:null,p:null}}
var W={transition:null};function ia(){}var X="function"===typeof reportError?reportError:function(a){console.error(a)},ja=Object.getPrototypeOf,ka=y.TaintRegistryObjects,Y=y.TaintRegistryValues,la=y.TaintRegistryByteLengths,ma=y.TaintRegistryPendingRequests,na=ja(Uint32Array.prototype).constructor;function oa(a){var b=Y.get(a);void 0!==b&&(ma.forEach(function(c){c.push(a);b.count++}),1===b.count?Y.delete(a):b.count--)}var Z="function"===typeof FinalizationRegistry?new FinalizationRegistry(oa):null;
exports.Children={map:U,forEach:function(a,b,c){U(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;U(a,function(){b++});return b},toArray:function(a){return U(a,function(b){return b})||[]},only:function(a){if(!P(a))throw Error(z(143));return a}};exports.Fragment=D;exports.Profiler=F;exports.StrictMode=E;exports.Suspense=H;exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=t;exports.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=y;
exports.cache=function(a){return function(){var b=m.current;if(!b)return a.apply(null,arguments);var c=b.getCacheForType(ha);b=c.get(a);void 0===b&&(b=V(),c.set(a,b));c=0;for(var d=arguments.length;c<d;c++){var e=arguments[c];if("function"===typeof e||"object"===typeof e&&null!==e){var g=b.o;null===g&&(b.o=g=new WeakMap);b=g.get(e);void 0===b&&(b=V(),g.set(e,b))}else g=b.p,null===g&&(b.p=g=new Map),b=g.get(e),void 0===b&&(b=V(),g.set(e,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var f=a.apply(null,
arguments);c=b;c.s=1;return c.v=f}catch(h){throw f=b,f.s=2,f.v=h,h;}}};
exports.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error(z(267,a));var d=l({},a.props),e=a.key,g=a._owner;if(null!=b){void 0!==b.ref&&(g=N.current);void 0!==b.key&&(e=""+b.key);if(a.type&&a.type.defaultProps)var f=a.type.defaultProps;for(h in b)!M.call(b,h)||"key"===h||"__self"===h||"__source"===h||"ref"===h&&void 0===b.ref||(d[h]=void 0===b[h]&&void 0!==f?f[h]:b[h])}var h=arguments.length-2;if(1===h)d.children=c;else if(1<h){f=Array(h);for(var k=0;k<h;k++)f[k]=arguments[k+2];d.children=
f}return O(a.type,e,null,void 0,void 0,g,d)};exports.createElement=function(a,b,c){var d,e={},g=null;if(null!=b)for(d in void 0!==b.key&&(g=""+b.key),b)M.call(b,d)&&"key"!==d&&"__self"!==d&&"__source"!==d&&(e[d]=b[d]);var f=arguments.length-2;if(1===f)e.children=c;else if(1<f){for(var h=Array(f),k=0;k<f;k++)h[k]=arguments[k+2];e.children=h}if(a&&a.defaultProps)for(d in f=a.defaultProps,f)void 0===e[d]&&(e[d]=f[d]);return O(a,g,null,void 0,void 0,N.current,e)};exports.createRef=function(){return{current:null}};
exports.experimental_taintObjectReference=function(a,b){a=""+(a||"A tainted value was attempted to be serialized to a Client Component or Action closure. This would leak it to the client.");if("string"===typeof b||"bigint"===typeof b)throw Error(z(496));if(null===b||"object"!==typeof b&&"function"!==typeof b)throw Error(z(497));ka.set(b,a)};
exports.experimental_taintUniqueValue=function(a,b,c){a=""+(a||"A tainted value was attempted to be serialized to a Client Component or Action closure. This would leak it to the client.");if(null===b||"object"!==typeof b&&"function"!==typeof b)throw Error(z(493));if("string"!==typeof c&&"bigint"!==typeof c)if(c instanceof na||c instanceof DataView)la.add(c.byteLength),c=String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength));else{a=null===c?"null":typeof c;if("object"===
a||"function"===a)throw Error(z(494));throw Error(z(495,a));}var d=Y.get(c);void 0===d?Y.set(c,{message:a,count:1}):d.count++;null!==Z&&Z.register(b,c)};exports.forwardRef=function(a){return{$$typeof:G,render:a}};exports.isValidElement=P;exports.lazy=function(a){return{$$typeof:J,_payload:{_status:-1,_result:a},_init:fa}};exports.memo=function(a,b){return{$$typeof:I,type:a,compare:void 0===b?null:b}};
exports.startTransition=function(a){var b=W.transition,c=new Set;W.transition={_callbacks:c};var d=W.transition;try{var e=a();"object"===typeof e&&null!==e&&"function"===typeof e.then&&(c.forEach(function(g){return g(d,e)}),e.then(ia,X))}catch(g){X(g)}finally{W.transition=b}};exports.unstable_DebugTracingMode=K;exports.unstable_SuspenseList=H;exports.unstable_getCacheForType=function(a){var b=m.current;return b?b.getCacheForType(a):a()};
exports.unstable_getCacheSignal=function(){var a=m.current;if(!a){a=new AbortController;var b=Error(z(455));a.abort(b);return a.signal}return a.getCacheSignal()};exports.unstable_postpone=function(a){a=Error(a);a.$$typeof=aa;throw a;};exports.use=function(a){return r.current.use(a)};exports.useCallback=function(a,b){return r.current.useCallback(a,b)};exports.useDebugValue=function(){};exports.useId=function(){return r.current.useId()};exports.useMemo=function(a,b){return r.current.useMemo(a,b)};
exports.version="18.3.0-experimental-178c267a4e-20241218";

//# sourceMappingURL=react.react-server.production.min.js.map
