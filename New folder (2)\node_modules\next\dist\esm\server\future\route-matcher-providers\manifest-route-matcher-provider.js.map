{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/manifest-route-matcher-provider.ts"], "names": ["CachedRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "manifestName", "manifest<PERSON><PERSON>der", "load", "compare", "left", "right"], "mappings": "AAKA,SAASA,0BAA0B,QAAQ,0CAAyC;AAEpF,OAAO,MAAeC,qCAEZD;IACRE,YAAYC,YAAoB,EAAEC,cAA8B,CAAE;QAChE,KAAK,CAAC;YACJC,MAAM,UAAYD,eAAeC,IAAI,CAACF;YACtCG,SAAS,CAACC,MAAMC,QAAUD,SAASC;QACrC;IACF;AACF"}