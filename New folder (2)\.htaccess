# Next.js Shop with WordPress Admin Access
# Main domain (deal4u.co) shows Next.js shop, WordPress admin still accessible

RewriteEngine On

# IMPORTANT: WordPress Core Routes - Always handled by WordPress/PHP
RewriteRule ^wp-admin/?(.*)$ - [L]
RewriteRule ^wp-content/?(.*)$ - [L]
RewriteRule ^wp-includes/?(.*)$ - [L]
RewriteRule ^wp-json/?(.*)$ - [L]

# WordPress core files
RewriteRule ^xmlrpc\.php$ - [L]
RewriteRule ^wp-login\.php$ - [L]
RewriteRule ^wp-cron\.php$ - [L]
RewriteRule ^wp-config\.php$ - [L]
RewriteRule ^index\.php$ - [L]

# WordPress uploads and media
RewriteRule ^uploads/?(.*)$ - [L]

# Block access to Next.js source files for security
<Files ~ "^(package\.json|package-lock\.json|next\.config\.js|\.env)">
    Order allow,deny
    Deny from all
</Files>

# Block access to node_modules and .next build files
RedirectMatch 404 /node_modules/
RedirectMatch 404 /\.next/

# Next.js Static Assets (MUST be accessible)
RewriteRule ^_next/static/(.*)$ /_next/static/$1 [L]
RewriteRule ^_next/(.*)$ /_next/$1 [L]
RewriteRule ^static/?(.*)$ /static/$1 [L]
RewriteRule ^favicon\.ico$ /favicon.ico [L]
RewriteRule ^robots\.txt$ /robots.txt [L]

# Allow CSS and JS files
RewriteRule \.css$ - [L]
RewriteRule \.js$ - [L]
RewriteRule \.map$ - [L]

# Next.js Shop Application Routes (redirect to Next.js if it exists)
# Only redirect these specific routes to Next.js, everything else goes to WordPress
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^shop/?(.*)$ /shop/$1 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^products/?(.*)$ /products/$1 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^categories/?(.*)$ /categories/$1 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^cart/?(.*)$ /cart/$1 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^checkout/?(.*)$ /checkout/$1 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^account/?(.*)$ /account/$1 [L,QSA]

# Admin routes for Next.js (NOT WordPress admin)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/?(.*)$ /admin/$1 [L,QSA]

# API routes for Next.js
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/?(.*)$ /api/$1 [L,QSA]

# DEFAULT: Everything else goes to Next.js (Dynamic)
# This ensures deal4u.co/ shows your Next.js shop with full API functionality
# Note: Requires Node.js server running on cPanel
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.js [L]

# Performance and Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css
    AddOutputFilterByType DEFLATE application/xml application/xhtml+xml application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript application/x-javascript
</IfModule>

# Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
