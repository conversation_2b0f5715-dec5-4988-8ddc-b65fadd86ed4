#!/usr/bin/env node

/**
 * WooCommerce API Test Script
 * Tests the connection to your WooCommerce store and verifies product sync
 */

const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

// Configuration from your .env.local
const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WORDPRESS_URL 
    ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/wc/v3`
    : 'https://deal4u.co/wp-json/wc/v3',
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET
};

console.log('🧪 WooCommerce API Test Script');
console.log('================================\n');

// Test 1: Configuration Check
console.log('📋 Configuration Check:');
console.log(`   WordPress URL: ${process.env.NEXT_PUBLIC_WORDPRESS_URL}`);
console.log(`   API Base URL: ${WC_CONFIG.baseURL}`);
console.log(`   Consumer Key: ${WC_CONFIG.consumerKey ? '✅ Set' : '❌ Missing'}`);
console.log(`   Consumer Secret: ${WC_CONFIG.consumerSecret ? '✅ Set' : '❌ Missing'}`);
console.log('');

if (!WC_CONFIG.consumerKey || !WC_CONFIG.consumerSecret) {
  console.log('❌ WooCommerce credentials are missing!');
  console.log('Please check your .env.local file.');
  process.exit(1);
}

// Create axios instance
const api = axios.create({
  baseURL: WC_CONFIG.baseURL,
  timeout: 30000,
  auth: {
    username: WC_CONFIG.consumerKey,
    password: WC_CONFIG.consumerSecret
  }
});

async function testWooCommerceAPI() {
  try {
    // Test 2: API Connection
    console.log('🔌 Testing API Connection...');
    const systemStatusResponse = await api.get('/system_status');
    console.log('✅ API Connection: Success');
    console.log(`   WooCommerce Version: ${systemStatusResponse.data.environment?.version || 'Unknown'}`);
    console.log(`   WordPress Version: ${systemStatusResponse.data.environment?.wp_version || 'Unknown'}`);
    console.log('');

    // Test 3: Products
    console.log('📦 Testing Products API...');
    const productsResponse = await api.get('/products', {
      params: { per_page: 5, status: 'publish' }
    });
    
    console.log(`✅ Products API: Success`);
    console.log(`   Total Products Found: ${productsResponse.data.length}`);
    
    if (productsResponse.data.length > 0) {
      console.log('   Sample Products:');
      productsResponse.data.slice(0, 3).forEach((product, index) => {
        console.log(`      ${index + 1}. ${product.name} - $${product.price}`);
      });
    } else {
      console.log('   ⚠️  No products found in your WooCommerce store');
    }
    console.log('');

    // Test 4: Categories
    console.log('📂 Testing Categories API...');
    const categoriesResponse = await api.get('/products/categories', {
      params: { per_page: 10, hide_empty: true }
    });
    
    console.log(`✅ Categories API: Success`);
    console.log(`   Total Categories Found: ${categoriesResponse.data.length}`);
    
    if (categoriesResponse.data.length > 0) {
      console.log('   Sample Categories:');
      categoriesResponse.data.slice(0, 5).forEach((category, index) => {
        console.log(`      ${index + 1}. ${category.name} (${category.count} products)`);
      });
    } else {
      console.log('   ⚠️  No categories found in your WooCommerce store');
    }
    console.log('');

    // Test 5: Orders (if accessible)
    console.log('🛒 Testing Orders API...');
    try {
      const ordersResponse = await api.get('/orders', {
        params: { per_page: 1 }
      });
      console.log(`✅ Orders API: Success`);
      console.log(`   Recent Orders: ${ordersResponse.data.length}`);
    } catch (orderError) {
      console.log('⚠️  Orders API: Limited access (this is normal for some setups)');
    }
    console.log('');

    // Test 6: Store Settings
    console.log('⚙️  Testing Store Settings...');
    const settingsResponse = await api.get('/settings/general');
    const storeInfo = settingsResponse.data.find(setting => setting.id === 'woocommerce_store_address');
    const currency = settingsResponse.data.find(setting => setting.id === 'woocommerce_currency');
    
    console.log(`✅ Store Settings: Success`);
    console.log(`   Store Currency: ${currency?.value || 'Unknown'}`);
    console.log(`   Store Address: ${storeInfo?.value || 'Not set'}`);
    console.log('');

    // Summary
    console.log('🎉 WooCommerce API Test Complete!');
    console.log('================================');
    console.log('✅ All tests passed successfully');
    console.log('✅ Your WooCommerce integration is working properly');
    console.log('✅ Products will sync dynamically with your website');
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Start your development server: npm run dev');
    console.log('   2. Visit http://localhost:3000/shop to see your products');
    console.log('   3. Test the contact information updates');
    console.log('   4. Deploy to your live server when ready');

  } catch (error) {
    console.log('❌ WooCommerce API Test Failed!');
    console.log('================================');
    
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || error.message}`);
      
      if (error.response.status === 401) {
        console.log('');
        console.log('🔧 Authentication Error - Possible Solutions:');
        console.log('   1. Check your Consumer Key and Secret in .env.local');
        console.log('   2. Verify the keys are active in WooCommerce > Settings > Advanced > REST API');
        console.log('   3. Ensure the keys have Read/Write permissions');
      } else if (error.response.status === 404) {
        console.log('');
        console.log('🔧 Not Found Error - Possible Solutions:');
        console.log('   1. Check your WordPress URL in .env.local');
        console.log('   2. Verify WooCommerce is installed and activated');
        console.log('   3. Check if REST API is enabled');
      }
    } else if (error.request) {
      console.log('   Network Error: Could not connect to the server');
      console.log('');
      console.log('🔧 Network Error - Possible Solutions:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify the WordPress URL is correct');
      console.log('   3. Check if the server is online');
    } else {
      console.log(`   Error: ${error.message}`);
    }
    
    console.log('');
    console.log('📞 Need Help?');
    console.log('   Contact: <EMAIL>');
    console.log('   Phone: +44 7447 186806');
  }
}

// Run the test
testWooCommerceAPI();
