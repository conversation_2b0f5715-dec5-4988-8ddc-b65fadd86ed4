{"version": 3, "sources": ["../../../../src/build/webpack/plugins/jsconfig-paths-plugin.ts"], "names": ["JsConfigPathsPlugin", "findBestPatternMatch", "hasZeroOrOneAsteriskCharacter", "isString", "matchPatternOrExact", "matchedText", "pathIsRelative", "patternText", "tryParsePattern", "log", "debug", "asterisk", "str", "seenAsterisk", "i", "length", "charCodeAt", "testPath", "test", "pattern", "indexOfStar", "indexOf", "undefined", "prefix", "slice", "suffix", "isPatternMatch", "candidate", "startsWith", "endsWith", "values", "getPattern", "matchedValue", "longestMatchPrefixLength", "v", "patternStrings", "patterns", "patternString", "push", "_", "text", "substring", "forEachBail", "array", "iterator", "callback", "next", "loop", "err", "result", "NODE_MODULES_REGEX", "constructor", "paths", "resolvedBaseUrl", "jsConfigPlugin", "apply", "resolver", "target", "ensureH<PERSON>", "getHook", "tapAsync", "request", "resolveContext", "pathsKeys", "Object", "keys", "moduleName", "path", "match", "posix", "isAbsolute", "process", "platform", "win32", "matchedPattern", "matchedStar", "matchedPatternText", "triedPaths", "subst", "pathCallback", "curPath", "replace", "join", "baseUrl", "obj", "assign", "doResolve", "resolverErr", "resolverResult"], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;IAqKYA,mBAAmB;eAAnBA;;IA5GGC,oBAAoB;eAApBA;;IA1CAC,6BAA6B;eAA7BA;;IA4FAC,QAAQ;eAARA;;IAtBAC,mBAAmB;eAAnBA;;IA8BAC,WAAW;eAAXA;;IAlFAC,cAAc;eAAdA;;IAyFAC,WAAW;eAAXA;;IArFAC,eAAe;eAAfA;;;6DApCC;uBAEK;;;;;;AAGtB,MAAMC,MAAMC,IAAAA,YAAK,EAAC;AAOlB,MAAMC,WAAW;AAEV,SAAST,8BAA8BU,GAAW;IACvD,IAAIC,eAAe;IACnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,IAAIF,IAAII,UAAU,CAACF,OAAOH,UAAU;YAClC,IAAI,CAACE,cAAc;gBACjBA,eAAe;YACjB,OAAO;gBACL,6BAA6B;gBAC7B,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAKO,SAASP,eAAeW,QAAgB;IAC7C,OAAO,kBAAkBC,IAAI,CAACD;AAChC;AAEO,SAAST,gBAAgBW,OAAe;IAC7C,qEAAqE;IACrE,MAAMC,cAAcD,QAAQE,OAAO,CAAC;IACpC,OAAOD,gBAAgB,CAAC,IACpBE,YACA;QACEC,QAAQJ,QAAQK,KAAK,CAAC,GAAGJ;QACzBK,QAAQN,QAAQK,KAAK,CAACJ,cAAc;IACtC;AACN;AAEA,SAASM,eAAe,EAAEH,MAAM,EAAEE,MAAM,EAAW,EAAEE,SAAiB;IACpE,OACEA,UAAUZ,MAAM,IAAIQ,OAAOR,MAAM,GAAGU,OAAOV,MAAM,IACjDY,UAAUC,UAAU,CAACL,WACrBI,UAAUE,QAAQ,CAACJ;AAEvB;AAGO,SAASxB,qBACd6B,MAAoB,EACpBC,UAAiC,EACjCJ,SAAiB;IAEjB,IAAIK;IACJ,8CAA8C;IAC9C,IAAIC,2BAA2B,CAAC;IAEhC,KAAK,MAAMC,KAAKJ,OAAQ;QACtB,MAAMX,UAAUY,WAAWG;QAC3B,IACER,eAAeP,SAASQ,cACxBR,QAAQI,MAAM,CAACR,MAAM,GAAGkB,0BACxB;YACAA,2BAA2Bd,QAAQI,MAAM,CAACR,MAAM;YAChDiB,eAAeE;QACjB;IACF;IAEA,OAAOF;AACT;AAOO,SAAS5B,oBACd+B,cAAiC,EACjCR,SAAiB;IAEjB,MAAMS,WAAsB,EAAE;IAC9B,KAAK,MAAMC,iBAAiBF,eAAgB;QAC1C,IAAI,CAACjC,8BAA8BmC,gBAAgB;QACnD,MAAMlB,UAAUX,gBAAgB6B;QAChC,IAAIlB,SAAS;YACXiB,SAASE,IAAI,CAACnB;QAChB,OAAO,IAAIkB,kBAAkBV,WAAW;YACtC,wDAAwD;YACxD,OAAOU;QACT;IACF;IAEA,OAAOpC,qBAAqBmC,UAAU,CAACG,IAAMA,GAAGZ;AAClD;AAKO,SAASxB,SAASqC,IAAa;IACpC,OAAO,OAAOA,SAAS;AACzB;AAMO,SAASnC,YAAYc,OAAgB,EAAEQ,SAAiB;IAC7D,OAAOA,UAAUc,SAAS,CACxBtB,QAAQI,MAAM,CAACR,MAAM,EACrBY,UAAUZ,MAAM,GAAGI,QAAQM,MAAM,CAACV,MAAM;AAE5C;AAEO,SAASR,YAAY,EAAEgB,MAAM,EAAEE,MAAM,EAAW;IACrD,OAAO,CAAC,EAAEF,OAAO,CAAC,EAAEE,OAAO,CAAC;AAC9B;AAEA;;;CAGC,GACD,SAASiB,YACPC,KAAe,EACfC,QAGS,EACTC,QAA2C;IAE3C,IAAIF,MAAM5B,MAAM,KAAK,GAAG,OAAO8B;IAE/B,IAAI/B,IAAI;IACR,MAAMgC,OAAO;QACX,IAAIC,OAA4BzB;QAChCsB,SAASD,KAAK,CAAC7B,IAAI,EAAE,CAACkC,KAAKC;YACzB,IAAID,OAAOC,WAAW3B,aAAaR,KAAK6B,MAAM5B,MAAM,EAAE;gBACpD,OAAO8B,SAASG,KAAKC;YACvB;YACA,IAAIF,SAAS,OAAO,MAAOD;YAC3BC,OAAO;QACT;QACA,IAAI,CAACA,MAAMA,OAAO;QAClB,OAAOA;IACT;IACA,MAAOD;AACT;AAEA,MAAMI,qBAAqB;AASpB,MAAMlD;IAKXmD,YAAYC,KAAY,EAAEC,eAAgC,CAAE;QAC1D,IAAI,CAACD,KAAK,GAAGA;QACb,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACC,cAAc,GAAG;QACtB7C,IAAI,4CAA4C2C;QAChD3C,IAAI,wBAAwB4C;IAC9B;IACAE,MAAMC,QAAa,EAAE;QACnB,MAAMC,SAASD,SAASE,UAAU,CAAC;QACnCF,SACGG,OAAO,CAAC,qBACRC,QAAQ,CACP,uBACA,CACEC,SACAC,gBACAjB;YAEA,MAAMQ,kBAAkB,IAAI,CAACA,eAAe;YAC5C,IAAIA,oBAAoB/B,WAAW;gBACjC,OAAOuB;YACT;YACA,MAAMO,QAAQ,IAAI,CAACA,KAAK;YACxB,MAAMW,YAAYC,OAAOC,IAAI,CAACb;YAE9B,mCAAmC;YACnC,IAAIW,UAAUhD,MAAM,KAAK,GAAG;gBAC1BN,IAAI;gBACJ,OAAOoC;YACT;YAEA,MAAMqB,aAAaL,QAAQA,OAAO;YAElC,gEAAgE;YAChE,IAAIA,QAAQM,IAAI,CAACC,KAAK,CAAClB,qBAAqB;gBAC1CzC,IAAI,oDAAoDyD;gBACxD,OAAOrB;YACT;YAEA,IACEsB,aAAI,CAACE,KAAK,CAACC,UAAU,CAACJ,eACrBK,QAAQC,QAAQ,KAAK,WAAWL,aAAI,CAACM,KAAK,CAACH,UAAU,CAACJ,aACvD;gBACAzD,IAAI,iDAAiDyD;gBACrD,OAAOrB;YACT;YAEA,IAAIvC,eAAe4D,aAAa;gBAC9BzD,IAAI,gDAAgDyD;gBACpD,OAAOrB;YACT;YAEA,oDAAoD;YAEpD,oGAAoG;YACpG,MAAM6B,iBAAiBtE,oBAAoB2D,WAAWG;YACtD,IAAI,CAACQ,gBAAgB;gBACnBjE,IAAI,iDAAiDyD;gBACrD,OAAOrB;YACT;YAEA,MAAM8B,cAAcxE,SAASuE,kBACzBpD,YACAjB,YAAYqE,gBAAgBR;YAChC,MAAMU,qBAAqBzE,SAASuE,kBAChCA,iBACAnE,YAAYmE;YAEhB,IAAIG,aAAa,EAAE;YAEnBnC,YACEU,KAAK,CAACwB,mBAAmB,EACzB,CAACE,OAAOC;gBACN,MAAMC,UAAUL,cACZG,MAAMG,OAAO,CAAC,KAAKN,eACnBG;gBACJ,8BAA8B;gBAC9B,IAAIE,QAAQnD,QAAQ,CAAC,UAAU;oBAC7B,0BAA0B;oBAC1B,OAAOkD;gBACT;gBACA,MAAMpD,YAAYwC,aAAI,CAACe,IAAI,CAAC7B,gBAAgB8B,OAAO,EAAEH;gBACrD,MAAMI,MAAMpB,OAAOqB,MAAM,CAAC,CAAC,GAAGxB,SAAS;oBACrCA,SAASlC;gBACX;gBACA6B,SAAS8B,SAAS,CAChB7B,QACA2B,KACA,CAAC,4CAA4C,EAAER,mBAAmB,IAAI,EAAEjD,UAAU,CAAC,EACnFmC,gBACA,CAACyB,aAAkBC;oBACjB,IAAID,eAAeC,mBAAmBlE,WAAW;wBAC/CuD,WAAWvC,IAAI,CAACX;wBAChB,0BAA0B;wBAC1B,OAAOoD;oBACT;oBACA,OAAOA,aAAaQ,aAAaC;gBACnC;YAEJ,GACA3C;QAEJ;IAEN;AACF"}