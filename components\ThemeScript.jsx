'use client';

import { useEffect } from 'react';

// Client-side theme script to prevent FOUC
export default function ThemeScript() {
  useEffect(() => {
    try {
      const mode = localStorage.getItem('theme');
      if (mode && mode === 'dark') {
        document.documentElement.classList.add('dark');
      }
    } catch (e) {
      console.error('Error accessing localStorage:', e);
    }
  }, []);
  
  // Return null as we don't need to render anything
  return null;
}
