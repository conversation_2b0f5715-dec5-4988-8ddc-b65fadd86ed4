{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "names": ["computeChangedPath", "isNotUndefined", "value", "handleMutable", "state", "mutable", "shouldScroll", "nextUrl", "patchedTree", "changedPath", "tree", "canonicalUrl", "buildId", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "split", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAO3D,SAASC,eAAkBC,KAAQ;IACjC,OAAO,OAAOA,UAAU;AAC1B;AAEA,OAAO,SAASC,cACdC,KAA2B,EAC3BC,OAAgB;QAoDRA;QAjDaA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;IAE7C,IAAIE,UAAUH,MAAMG,OAAO;IAE3B,IAAIN,eAAeI,QAAQG,WAAW,GAAG;QACvC,sEAAsE;QACtE,MAAMC,cAAcT,mBAAmBI,MAAMM,IAAI,EAAEL,QAAQG,WAAW;QACtE,IAAIC,aAAa;YACf,qDAAqD;YACrDF,UAAUE;QACZ,OAAO,IAAI,CAACF,SAAS;YACnB,6HAA6H;YAC7HA,UAAUH,MAAMO,YAAY;QAC9B;IACA,0EAA0E;IAC5E;QA6CQN;IA3CR,OAAO;QACLO,SAASR,MAAMQ,OAAO;QACtB,YAAY;QACZD,cAAcV,eAAeI,QAAQM,YAAY,IAC7CN,QAAQM,YAAY,KAAKP,MAAMO,YAAY,GACzCP,MAAMO,YAAY,GAClBN,QAAQM,YAAY,GACtBP,MAAMO,YAAY;QACtBE,SAAS;YACPC,aAAab,eAAeI,QAAQS,WAAW,IAC3CT,QAAQS,WAAW,GACnBV,MAAMS,OAAO,CAACC,WAAW;YAC7BC,eAAed,eAAeI,QAAQU,aAAa,IAC/CV,QAAQU,aAAa,GACrBX,MAAMS,OAAO,CAACE,aAAa;YAC/BC,4BAA4Bf,eAC1BI,QAAQW,0BAA0B,IAEhCX,QAAQW,0BAA0B,GAClCZ,MAAMS,OAAO,CAACG,0BAA0B;QAC9C;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOZ,eACHL,eAAeI,2BAAAA,QAASc,kBAAkB,IACxC,OACAf,MAAMa,iBAAiB,CAACC,KAAK,GAE/B;YACJE,gBACE,CAAC,CAACf,QAAQgB,YAAY,IACtBjB,MAAMO,YAAY,CAACW,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACjCjB,wBAAAA,QAAQM,YAAY,qBAApBN,sBAAsBiB,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YAC1CD,cAAcf,eAEV,oCAAoC;YACpCD,QAAQgB,YAAY,IAAIhB,QAAQgB,YAAY,KAAK,KAE/CE,mBAAmBlB,QAAQgB,YAAY,CAACG,KAAK,CAAC,MAC9CpB,MAAMa,iBAAiB,CAACI,YAAY,GAEtC;YACJI,cAAcnB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASc,kBAAkB,YAA3Bd,8BAA+BD,MAAMa,iBAAiB,CAACQ,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOrB,QAAQqB,KAAK,GAAGrB,QAAQqB,KAAK,GAAGtB,MAAMsB,KAAK;QAClDC,eAAetB,QAAQsB,aAAa,GAChCtB,QAAQsB,aAAa,GACrBvB,MAAMuB,aAAa;QACvB,8BAA8B;QAC9BjB,MAAMT,eAAeI,QAAQG,WAAW,IACpCH,QAAQG,WAAW,GACnBJ,MAAMM,IAAI;QACdH;IACF;AACF"}