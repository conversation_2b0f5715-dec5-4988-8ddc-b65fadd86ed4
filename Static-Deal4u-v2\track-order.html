<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Order - Deal4u</title>
    
    <!-- Favicon and app icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="manifest" href="manifest.json">
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body class="antialiased min-h-screen flex flex-col bg-gray-50">
    <!-- Header -->
    <header id="header" class="sticky top-0 z-50 transition-all duration-300 bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <a href="index.html" class="flex items-center space-x-2 flex-shrink-0">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-colors">
                        Deal4u
                    </div>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-1" style="display: flex !important;">
                    <a href="index.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Home</a>
                    <a href="shop.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="px-3 py-2 rounded-md text-sm font-medium text-blue-600 bg-blue-50 transition-colors">Track Order</a>
                    <a href="about.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                </nav>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Wishlist - Desktop -->
                    <a href="wishlist.html" class="hidden md:flex items-center space-x-1 text-gray-700 hover:text-red-500 transition-colors" style="display: flex !important;">
                        <i data-lucide="heart" class="w-5 h-5"></i>
                        <span class="text-sm">Wishlist</span>
                    </a>

                    <!-- Shopping Cart -->
                    <a href="cart.html" class="relative flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors">
                        <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                        <span class="hidden md:block text-sm" style="display: block !important;">Cart</span>
                        <span id="cart-count" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </a>

                    <!-- User Menu -->
                    <div class="relative">
                        <button id="user-menu-btn" onclick="toggleUserMenu()" class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors">
                            <i data-lucide="user" class="w-5 h-5"></i>
                            <span class="hidden md:block text-sm" id="user-menu-text" style="display: block !important;">Login</span>
                        </button>
                        <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 hidden">
                            <a href="login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Login</a>
                            <a href="register.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Register</a>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button class="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors" onclick="toggleMobileMenu()" id="mobile-menu-btn">
                        <i data-lucide="menu" class="w-6 h-6" id="mobile-menu-icon"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden bg-white border-t border-gray-200 py-4 hidden">
                <div class="space-y-1">
                    <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Home</a>
                    <a href="shop.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Shop</a>
                    <a href="categories.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Categories</a>
                    <a href="track-order.html" class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 bg-blue-50 transition-colors">Track Order</a>
                    <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">About</a>
                    <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">Contact</a>
                    <a href="faq.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors">FAQ</a>
                    <a href="wishlist.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-500 hover:bg-gray-50 transition-colors">Wishlist</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
        <!-- Page Header -->
        <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl font-bold mb-4">Track Your Order</h1>
                <p class="text-xl opacity-90">Enter your order details to track your package</p>
            </div>
        </section>

        <!-- Track Order Form -->
        <section class="py-16">
            <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="text-center mb-8">
                        <i data-lucide="package" class="w-16 h-16 text-blue-600 mx-auto mb-4"></i>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Track Your Package</h2>
                        <p class="text-gray-600">Enter your order number and email to get real-time updates</p>
                    </div>

                    <form id="track-order-form" class="space-y-6">
                        <div>
                            <label for="order-number" class="block text-sm font-medium text-gray-700 mb-2">Order Number</label>
                            <input type="text" id="order-number" name="order-number" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="e.g., ORD-123456789">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>

                        <button type="submit" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            Track Order
                        </button>
                    </form>

                    <!-- Order Status Display (Hidden by default) -->
                    <div id="order-status" class="mt-8 hidden">
                        <div class="border-t pt-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
                            <div id="order-details" class="space-y-4">
                                <!-- Order details will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="mt-12 text-center">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
                    <p class="text-gray-600 mb-6">Can't find your order or having issues? We're here to help!</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="contact.html" class="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                            <i data-lucide="mail" class="w-5 h-5 mr-2"></i>
                            Contact Support
                        </a>
                        <a href="faq.html" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i data-lucide="help-circle" class="w-5 h-5 mr-2"></i>
                            View FAQ
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl">
                        Deal4u
                    </div>
                </div>
                <p class="text-gray-400 mb-4">Your trusted partner for amazing deals and quality products</p>
                <div class="flex justify-center space-x-6">
                    <a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a>
                    <a href="shop.html" class="text-gray-400 hover:text-white transition-colors">Shop</a>
                    <a href="about.html" class="text-gray-400 hover:text-white transition-colors">About</a>
                    <a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-400">
                    <p>&copy; 2024 Deal4u. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Navigation Functions
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('mobile-menu-icon');
            
            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.setAttribute('data-lucide', 'x');
            } else {
                mobileMenu.classList.add('hidden');
                menuIcon.setAttribute('data-lucide', 'menu');
            }
            lucide.createIcons();
        }

        function toggleUserMenu() {
            const userDropdown = document.getElementById('user-dropdown');
            userDropdown.classList.toggle('hidden');
        }

        // Track Order Form Handler
        document.getElementById('track-order-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const orderNumber = document.getElementById('order-number').value;
            const email = document.getElementById('email').value;
            
            // Simulate order tracking (replace with real API call)
            setTimeout(() => {
                showOrderStatus(orderNumber, email);
            }, 1000);
        });

        function showOrderStatus(orderNumber, email) {
            const orderStatus = document.getElementById('order-status');
            const orderDetails = document.getElementById('order-details');
            
            // Sample order status (replace with real data)
            orderDetails.innerHTML = `
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-semibold">Order #${orderNumber}</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">In Transit</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Estimated delivery: 2-3 business days</p>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm">Order Confirmed</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm">Processing</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm">Shipped</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gray-300 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-500">Delivered</span>
                        </div>
                    </div>
                </div>
            `;
            
            orderStatus.classList.remove('hidden');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('user-dropdown');
            const userMenuBtn = document.getElementById('user-menu-btn');
            
            if (userDropdown && userMenuBtn && !userMenuBtn.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.add('hidden');
            }
        });

        // Initialize Lucide Icons
        lucide.createIcons();
    </script>
</body>
</html>
