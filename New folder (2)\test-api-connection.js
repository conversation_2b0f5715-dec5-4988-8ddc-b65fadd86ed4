const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

async function testWooCommerceAPI() {
  console.log('🔍 Testing WooCommerce API Connection...\n');
  
  const baseURL = process.env.NEXT_PUBLIC_WORDPRESS_URL;
  const consumerKey = process.env.WOOCOMMERCE_CONSUMER_KEY;
  const consumerSecret = process.env.WOOCOMMERCE_CONSUMER_SECRET;
  
  console.log('Configuration:');
  console.log('- WordPress URL:', baseURL);
  console.log('- Consumer Key:', consumerKey ? `${consumerKey.substring(0, 10)}...` : 'NOT SET');
  console.log('- Consumer Secret:', consumerSecret ? `${consumerSecret.substring(0, 10)}...` : 'NOT SET');
  console.log('');
  
  if (!consumerKey || !consumerSecret || consumerKey === 'your_consumer_key_here') {
    console.log('❌ API keys not configured. Please update .env.local file with your actual WooCommerce API keys.');
    return;
  }
  
  const tests = [
    {
      name: 'WordPress REST API',
      url: `${baseURL}/wp-json`
    },
    {
      name: 'WooCommerce REST API',
      url: `${baseURL}/wp-json/wc/v3`
    },
    {
      name: 'WooCommerce Products (with auth)',
      url: `${baseURL}/wp-json/wc/v3/products`,
      auth: true
    },
    {
      name: 'WooCommerce Categories (with auth)',
      url: `${baseURL}/wp-json/wc/v3/products/categories`,
      auth: true
    }
  ];
  
  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const config = {
        timeout: 15000,
        validateStatus: function (status) {
          return status < 500;
        }
      };
      
      if (test.auth) {
        config.auth = {
          username: consumerKey,
          password: consumerSecret
        };
        config.params = { per_page: 1 };
      }
      
      const response = await axios.get(test.url, config);
      
      if (response.status === 200) {
        console.log('✅ SUCCESS!');
        if (Array.isArray(response.data)) {
          console.log(`   Found ${response.data.length} items`);
          if (response.data.length > 0 && response.data[0].name) {
            console.log(`   First item: ${response.data[0].name}`);
          }
        } else if (typeof response.data === 'object') {
          console.log(`   Response keys: ${Object.keys(response.data).slice(0, 5).join(', ')}`);
        }
      } else {
        console.log(`⚠️  Status: ${response.status}`);
        if (response.status === 401) {
          console.log('   Authentication failed - check your API keys');
        }
      }
      
    } catch (error) {
      console.log('❌ FAILED');
      console.log(`   Error: ${error.response?.status || error.code} - ${error.message}`);
      
      if (error.response?.status === 401) {
        console.log('   → Check your WooCommerce API keys');
      } else if (error.response?.status === 404) {
        console.log('   → REST API might be disabled');
      } else if (error.code === 'ECONNABORTED') {
        console.log('   → Connection timeout - server might be slow');
      }
    }
    
    console.log('');
  }
  
  console.log('🎯 Next Steps:');
  console.log('1. If REST API tests fail: Enable WordPress REST API in your hosting/WordPress settings');
  console.log('2. If auth tests fail: Generate new WooCommerce API keys and update .env.local');
  console.log('3. If all tests pass: Your website will work with real data!');
}

testWooCommerceAPI();
