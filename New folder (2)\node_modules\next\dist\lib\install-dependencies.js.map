{"version": 3, "sources": ["../../src/lib/install-dependencies.ts"], "names": ["installDependencies", "baseDir", "deps", "dev", "packageManager", "getPkgManager", "isOnline", "getOnline", "length", "console", "log", "dep", "cyan", "pkg", "install", "path", "resolve", "map", "devDependencies"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;4BAZD;6DACJ;+BAGa;yBACN;2BACE;;;;;;AAMnB,eAAeA,oBACpBC,OAAe,EACfC,IAAS,EACTC,MAAe,KAAK;IAEpB,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACJ;IACrC,MAAMK,WAAW,MAAMC,IAAAA,oBAAS;IAEhC,IAAIL,KAAKM,MAAM,EAAE;QACfC,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,CAAC,WAAW,EACVP,MAAM,oBAAoB,eAC3B,EAAE,EAAEC,eAAe,EAAE,CAAC;QAEzB,KAAK,MAAMO,OAAOT,KAAM;YACtBO,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEE,IAAAA,gBAAI,EAACD,IAAIE,GAAG,EAAE,CAAC;QAClC;QACAJ,QAAQC,GAAG;QAEX,MAAMI,IAAAA,gBAAO,EACXC,aAAI,CAACC,OAAO,CAACf,UACbC,KAAKe,GAAG,CAAC,CAACN,MAA2BA,IAAIE,GAAG,GAC5C;YAAEK,iBAAiBf;YAAKG;YAAUF;QAAe;QAEnDK,QAAQC,GAAG;IACb;AACF"}