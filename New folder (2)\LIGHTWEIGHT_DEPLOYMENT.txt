DEAL4U CPANEL DEPLOYMENT - LIGHTWEIGHT VERSION
===============================================

🚀 MEMORY-OPTIMIZED FOR SHARED HOSTING
✅ Uses only Express.js (lightweight)
✅ Serves static files from 'out' folder
✅ Works within 4GB memory limits

STEP 1: UPLOAD TO CPANEL
========================
1. Go to cPanel File Manager
2. Navigate to /public_html/deal4u
3. DELETE all old files first
4. Upload ALL files from this folder

IMPORTANT: WORDPRESS COEXISTENCE
================================
If you have WordPress installed and want both to work:

Option A: Separate Directories (Recommended)
- WordPress in: /public_html/wp/ or /public_html/wordpress/
- Deal4u in: /public_html/deal4u/
- Access WordPress: yourdomain.com/wp/
- Access Deal4u: yourdomain.com/deal4u/

Option B: WordPress in Root, Deal4u in Subdirectory
- WordPress in: /public_html/
- Deal4u in: /public_html/deal4u/
- Access WordPress: yourdomain.com/wp-admin/
- Access Deal4u: yourdomain.com/deal4u/

STEP 2: CREATE NODE.JS APP
==========================
1. Go to cPanel Node.js Apps
2. DELETE any existing Deal4u apps
3. Create new application:
   - Node.js Version: 18.x
   - Application Root: /public_html/deal4u
   - Application URL: yourdomain.com/deal4u
   - Startup File: server.js

STEP 3: INSTALL DEPENDENCIES
============================
1. Click on your app
2. Go to Package.json tab
3. Click "Run NPM Install" (only 2 lightweight packages)
4. Wait for completion

STEP 4: START APP
=================
1. Click "Start App"
2. Should show "Running" status
3. Visit your website!

WHAT'S DIFFERENT:
================
- Uses Express.js instead of Next.js (much lighter)
- Serves pre-built static files
- Only 2 dependencies (express + compression)
- Should work within your hosting memory limits

TROUBLESHOOTING:
================
If getting "content type" or "return code None" errors:
1. Check server logs in cPanel Node.js Apps
2. Run diagnostic: node diagnostic.js
3. Verify /health endpoint works: yourdomain.com/deal4u/health
4. Check that index.html exists in 'out' folder

If WordPress paths (like /wp) show Deal4u instead:
1. Check that .htaccess file is uploaded
2. Verify WordPress is in correct directory
3. Check cPanel Node.js app configuration
4. Make sure Application URL is set to /deal4u not root
5. Try accessing: yourdomain.com/wp/wp-admin.php directly

If still getting memory errors:
1. Contact your hosting provider
2. Ask to increase memory limits
3. Or upgrade to a VPS/dedicated server

WORDPRESS ACCESS SOLUTIONS:
===========================
Problem: /wp shows Deal4u website instead of WordPress

Solution 1: Check WordPress Location
- WordPress should be in: /public_html/wp/
- Try: yourdomain.com/wp/wp-admin.php
- Or: yourdomain.com/wp/index.php

Solution 2: Check Node.js App Configuration
- Application URL should be: yourdomain.com/deal4u
- NOT: yourdomain.com (root)
- This prevents Deal4u from capturing all requests

Solution 3: Direct WordPress Access
- Try: yourdomain.com/wp-admin/
- Or: yourdomain.com/wordpress/
- Check where WordPress files are actually located

ENHANCED FEATURES:
==================
✅ Fixed content-type headers (UTF-8 charset)
✅ Added health check endpoint (/health)
✅ Enhanced error handling and logging
✅ File existence validation
✅ Diagnostic script included

TESTING YOUR DEPLOYMENT:
========================
1. Visit: yourdomain.com/deal4u/health
   Should return JSON with server status
2. Visit: yourdomain.com/deal4u
   Should load your website
3. Check cPanel logs for any errors
4. Run: node diagnostic.js (via SSH if available)

/home/<USER>/deal4u.co/Deal4u
