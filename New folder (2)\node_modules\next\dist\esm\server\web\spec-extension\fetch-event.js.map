{"version": 3, "sources": ["../../../../src/server/web/spec-extension/fetch-event.ts"], "names": ["PageSignatureError", "responseSymbol", "Symbol", "passThroughSymbol", "waitUntilSymbol", "FetchEvent", "constructor", "_request", "respondWith", "response", "Promise", "resolve", "passThroughOnException", "waitUntil", "promise", "push", "NextFetchEvent", "params", "request", "sourcePage", "page"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,WAAU;AAG7C,MAAMC,iBAAiBC,OAAO;AAC9B,MAAMC,oBAAoBD,OAAO;AACjC,OAAO,MAAME,kBAAkBF,OAAO,aAAY;AAElD,MAAMG;IAKJ,qEAAqE;IACrEC,YAAYC,QAAiB,CAAE;YALtB,CAACH,gBAAgB,GAAmB,EAAE;YAE/C,CAACD,kBAAkB,GAAG;IAGU;IAEhCK,YAAYC,QAAsC,EAAQ;QACxD,IAAI,CAAC,IAAI,CAACR,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGS,QAAQC,OAAO,CAACF;QACzC;IACF;IAEAG,yBAA+B;QAC7B,IAAI,CAACT,kBAAkB,GAAG;IAC5B;IAEAU,UAAUC,OAAqB,EAAQ;QACrC,IAAI,CAACV,gBAAgB,CAACW,IAAI,CAACD;IAC7B;AACF;AAEA,OAAO,MAAME,uBAAuBX;IAGlCC,YAAYW,MAA8C,CAAE;QAC1D,KAAK,CAACA,OAAOC,OAAO;QACpB,IAAI,CAACC,UAAU,GAAGF,OAAOG,IAAI;IAC/B;IAEA;;;;GAIC,GACD,IAAIF,UAAU;QACZ,MAAM,IAAIlB,mBAAmB;YAC3BoB,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;IAEA;;;;GAIC,GACDX,cAAc;QACZ,MAAM,IAAIR,mBAAmB;YAC3BoB,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;AACF"}