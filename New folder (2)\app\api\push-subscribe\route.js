import { NextResponse } from 'next/server';

/**
 * Subscribe to push notifications
 * POST /api/push-subscribe
 */
export async function POST(request) {
  try {
    const subscription = await request.json();
    
    console.log('New push subscription:', subscription);
    
    // Validate subscription object
    if (!subscription || !subscription.endpoint) {
      return NextResponse.json(
        { error: 'Invalid subscription object' },
        { status: 400 }
      );
    }

    // In a real application, you would:
    // 1. Store the subscription in your database
    // 2. Associate it with the user's account
    // 3. Set up categories/preferences for notifications
    
    // For now, we'll just log it and return success
    // Example database storage:
    /*
    await db.pushSubscriptions.create({
      data: {
        endpoint: subscription.endpoint,
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
        userId: getUserIdFromSession(request),
        createdAt: new Date(),
        isActive: true
      }
    });
    */

    // Store in memory for demo (in production, use a database)
    if (typeof global.pushSubscriptions === 'undefined') {
      global.pushSubscriptions = [];
    }
    
    // Check if subscription already exists
    const existingIndex = global.pushSubscriptions.findIndex(
      sub => sub.endpoint === subscription.endpoint
    );
    
    if (existingIndex >= 0) {
      // Update existing subscription
      global.pushSubscriptions[existingIndex] = {
        ...subscription,
        updatedAt: new Date().toISOString()
      };
      console.log('Updated existing push subscription');
    } else {
      // Add new subscription
      global.pushSubscriptions.push({
        ...subscription,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        isActive: true
      });
      console.log('Added new push subscription');
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to push notifications',
      subscriptionId: subscription.endpoint.split('/').pop()
    });

  } catch (error) {
    console.error('Error handling push subscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process subscription',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Get subscription status
 * GET /api/push-subscribe
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');
    
    if (!endpoint) {
      return NextResponse.json(
        { error: 'Endpoint parameter required' },
        { status: 400 }
      );
    }

    // Check if subscription exists
    const subscriptions = global.pushSubscriptions || [];
    const subscription = subscriptions.find(sub => sub.endpoint === endpoint);
    
    return NextResponse.json({
      exists: !!subscription,
      isActive: subscription?.isActive || false,
      createdAt: subscription?.createdAt || null
    });

  } catch (error) {
    console.error('Error checking subscription status:', error);
    
    return NextResponse.json(
      { error: 'Failed to check subscription status' },
      { status: 500 }
    );
  }
}
