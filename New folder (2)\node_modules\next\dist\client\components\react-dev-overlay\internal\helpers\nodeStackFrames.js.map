{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.ts"], "names": ["getFilesystemFrame", "getServerError", "frame", "f", "file", "startsWith", "test", "error", "type", "n", "Error", "message", "e", "name", "stack", "toString", "parse", "map", "str", "methodName", "loc", "lineNumber", "column", "join", "decorateServerError"], "mappings": ";;;;;;;;;;;;;;;IAOgBA,kBAAkB;eAAlBA;;IAmBAC,cAAc;eAAdA;;;kCA1BM;6BAKf;AAEA,SAASD,mBAAmBE,KAAiB;IAClD,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,SAAS;QACTD,EAAEC,IAAI,CAACC,UAAU,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,AAAC,YAASD,EAAEC,IAAI;QAC3B;IACF;IAEA,OAAOD;AACT;AAEO,SAASF,eAAeM,KAAY,EAAEC,IAAqB;IAChE,IAAIC;IACJ,IAAI;QACF,MAAM,IAAIC,MAAMH,MAAMI,OAAO;IAC/B,EAAE,OAAOC,GAAG;QACVH,IAAIG;IACN;IAEAH,EAAEI,IAAI,GAAGN,MAAMM,IAAI;IACnB,IAAI;QACFJ,EAAEK,KAAK,GAAG,AAAGL,EAAEM,QAAQ,KAAG,OAAIC,IAAAA,uBAAK,EAACT,MAAMO,KAAK,EAC5CG,GAAG,CAACjB,oBACJiB,GAAG,CAAC,CAACd;YACJ,IAAIe,MAAM,AAAC,YAASf,EAAEgB,UAAU;YAChC,IAAIhB,EAAEC,IAAI,EAAE;gBACV,IAAIgB,MAAMjB,EAAEC,IAAI;gBAChB,IAAID,EAAEkB,UAAU,EAAE;oBAChBD,OAAO,AAAC,MAAGjB,EAAEkB,UAAU;oBACvB,IAAIlB,EAAEmB,MAAM,EAAE;wBACZF,OAAO,AAAC,MAAGjB,EAAEmB,MAAM;oBACrB;gBACF;gBACAJ,OAAO,AAAC,OAAIE,MAAI;YAClB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC;IACV,EAAE,UAAM;QACNd,EAAEK,KAAK,GAAGP,MAAMO,KAAK;IACvB;IAEAU,IAAAA,gCAAmB,EAACf,GAAGD;IACvB,OAAOC;AACT"}