import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function POST(request) {
  try {
    const { action } = await request.json();
    
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    if (action === 'clean') {
      // Step 1: Get all products
      console.log('🧹 Starting product cleanup...');
      
      let allProducts = [];
      let page = 1;
      let hasMore = true;
      
      while (hasMore) {
        const response = await api.get('products', {
          per_page: 100,
          page: page,
          status: 'any' // Get all products regardless of status
        });
        
        const products = response.data;
        allProducts = [...allProducts, ...products];
        
        hasMore = products.length === 100;
        page++;
        
        console.log(`📦 Fetched ${products.length} products from page ${page - 1}`);
      }
      
      console.log(`🎯 Found ${allProducts.length} total products to clean`);
      
      // Step 2: Delete all products in batches
      const batchSize = 10;
      let deletedCount = 0;
      
      for (let i = 0; i < allProducts.length; i += batchSize) {
        const batch = allProducts.slice(i, i + batchSize);
        
        const deletePromises = batch.map(async (product) => {
          try {
            await api.delete(`products/${product.id}`, { force: true });
            deletedCount++;
            console.log(`🗑️ Deleted product ${product.id}: ${product.name}`);
            return { success: true, id: product.id, name: product.name };
          } catch (error) {
            console.error(`❌ Failed to delete product ${product.id}:`, error.message);
            return { success: false, id: product.id, error: error.message };
          }
        });
        
        await Promise.all(deletePromises);
        
        // Small delay between batches to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      return NextResponse.json({
        success: true,
        action: 'clean',
        message: `Successfully cleaned ${deletedCount} products`,
        totalFound: allProducts.length,
        deleted: deletedCount
      });
      
    } else if (action === 'resync') {
      // Step 1: Get fresh products from WooCommerce
      console.log('🔄 Starting fresh product sync...');
      
      let allProducts = [];
      let page = 1;
      let hasMore = true;
      
      while (hasMore) {
        const response = await api.get('products', {
          per_page: 100,
          page: page,
          status: 'any' // Get ALL products regardless of status
        });
        
        const products = response.data;
        allProducts = [...allProducts, ...products];
        
        hasMore = products.length === 100;
        page++;
      }
      
      console.log(`📦 Found ${allProducts.length} total products to process (all statuses)`);
      
      // Step 2: Process each product with smart image extraction
      const processedProducts = [];
      
      for (const product of allProducts) {
        try {
          const processedProduct = await processProductImages(product, api);
          processedProducts.push(processedProduct);
          console.log(`✅ Processed product ${product.id}: ${product.name}`);
        } catch (error) {
          console.error(`❌ Failed to process product ${product.id}:`, error.message);
          processedProducts.push({
            id: product.id,
            name: product.name,
            error: error.message,
            processed: false
          });
        }
      }
      
      return NextResponse.json({
        success: true,
        action: 'resync',
        message: `Processed ${processedProducts.length} products`,
        products: processedProducts.slice(0, 10), // Return first 10 for preview
        totalProcessed: processedProducts.length
      });

    } else if (action === 'sync-all') {
      // New action: Sync ALL products regardless of status
      console.log('🔄 Starting sync of ALL products (any status)...');

      let allProducts = [];
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const response = await api.get('products', {
          per_page: 100,
          page: page,
          status: 'any' // Get all products, we'll filter out trash later
        });

        const products = response.data;
        allProducts = [...allProducts, ...products];

        hasMore = products.length === 100;
        page++;
      }

      // Filter out trash products
      const nonTrashProducts = allProducts.filter(product => product.status !== 'trash');
      console.log(`📦 Found ${allProducts.length} total products, ${nonTrashProducts.length} non-trash products to sync`);

      // Use non-trash products for processing
      allProducts = nonTrashProducts;

      // Group products by status for reporting
      const statusGroups = {};
      allProducts.forEach(product => {
        const status = product.status || 'unknown';
        statusGroups[status] = (statusGroups[status] || 0) + 1;
      });

      console.log('📊 Product status breakdown:', statusGroups);

      // Process each product with smart image extraction
      const processedProducts = [];

      for (const product of allProducts) {
        try {
          const processedProduct = await processProductImages(product, api);
          processedProducts.push(processedProduct);
          console.log(`✅ Processed product ${product.id}: ${product.name} (${product.status})`);
        } catch (error) {
          console.error(`❌ Failed to process product ${product.id}:`, error.message);
          processedProducts.push({
            id: product.id,
            name: product.name,
            status: product.status,
            error: error.message,
            processed: false
          });
        }
      }

      return NextResponse.json({
        success: true,
        action: 'sync-all',
        message: `Synced ${processedProducts.length} products (all statuses)`,
        products: processedProducts.slice(0, 10), // Return first 10 for preview
        totalProcessed: processedProducts.length,
        statusBreakdown: statusGroups
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Clean/Resync API error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

// Smart image processing function
async function processProductImages(product, api) {
  const productImages = [];
  
  // First, try to use gallery images if they exist and are not size charts
  if (product.images && product.images.length > 0) {
    for (const img of product.images) {
      const imgSrc = img.src || '';
      const imgName = img.name || '';
      const imgAlt = img.alt || '';
      
      // Check if gallery image is a size chart
      const isSizeChart = [imgSrc, imgName, imgAlt].some(text => {
        const lower = text.toLowerCase();
        return ['size', 'chart', 'table', 'measurement', 'guide', 'cm', 'inch'].some(term => 
          lower.includes(term)
        );
      });
      
      if (!isSizeChart) {
        productImages.push(imgSrc);
      }
    }
  }
  
  // If no good gallery images, extract from description with SMART filtering
  if (productImages.length === 0 && product.description) {
    const smartImages = extractSmartImages(product.description);
    productImages.push(...smartImages);
  }
  
  // Update product with new images if we found better ones
  if (productImages.length > 0) {
    const imageObjects = productImages.slice(0, 5).map((src, index) => ({
      src: src,
      name: `${product.name} - Image ${index + 1}`,
      alt: product.name,
      position: index
    }));
    
    try {
      await api.put(`products/${product.id}`, {
        images: imageObjects
      });
      
      return {
        id: product.id,
        name: product.name,
        processed: true,
        imagesFound: productImages.length,
        imagesUsed: imageObjects.length,
        images: productImages
      };
    } catch (error) {
      throw new Error(`Failed to update product images: ${error.message}`);
    }
  }
  
  return {
    id: product.id,
    name: product.name,
    processed: true,
    imagesFound: 0,
    imagesUsed: 0,
    message: 'No suitable images found'
  };
}

// Smart image extraction from description
function extractSmartImages(description) {
  const imageRegex = /<img[^>]+src="([^">]+)"[^>]*>/gi;
  const matches = [...description.matchAll(imageRegex)];
  
  if (matches.length === 0) return [];
  
  // Strategy: Look for images that appear in the FIRST HALF of the description
  // These are more likely to be product photos rather than size charts
  const firstHalf = description.substring(0, Math.floor(description.length / 2));
  const firstHalfMatches = [...firstHalf.matchAll(imageRegex)];
  
  if (firstHalfMatches.length > 0) {
    return firstHalfMatches.slice(0, 3).map(match => {
      let src = match[1];
      if (src.startsWith('//')) {
        src = 'https:' + src;
      }
      return src;
    });
  }
  
  // Fallback: Use first 2 images from entire description but skip obvious size charts
  const goodImages = [];
  for (const match of matches) {
    let src = match[1];
    if (src.startsWith('//')) {
      src = 'https:' + src;
    }
    
    const lowerSrc = src.toLowerCase();
    const isSizeChart = ['size', 'chart', 'table', 'cm', 'inch', 'waist', 'hip'].some(term => 
      lowerSrc.includes(term)
    );
    
    if (!isSizeChart) {
      goodImages.push(src);
      if (goodImages.length >= 2) break;
    }
  }
  
  return goodImages;
}
