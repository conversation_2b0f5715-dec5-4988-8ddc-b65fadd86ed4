@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  line-height: 1.6;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox Scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Custom Utilities */
@layer utilities {
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(8px);
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }
}

/* Custom Components */
@layer components {
  /* Button Variants */
  .btn-primary {
    @apply px-4 py-2 bg-primary text-white rounded-md hover:opacity-90 transition-opacity;
  }

  .btn-secondary {
    @apply px-4 py-2 bg-secondary text-white rounded-md hover:opacity-90 transition-opacity;
  }

  .btn-outline {
    @apply border border-blue-600 text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-ghost {
    @apply text-gray-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Input Styles */
  .input-primary {
    @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors placeholder-gray-400;
  }

  .input-error {
    @apply w-full px-4 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors placeholder-gray-400;
  }

  /* Card Styles */
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .card-hover {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300;
  }

  /* Loading Spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }

  /* Badge Styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  /* Alert Styles */
  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-700;
  }

  .alert-success {
    @apply bg-green-50 border-green-200 text-green-700;
  }

  .alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-700;
  }

  .alert-error {
    @apply bg-red-50 border-red-200 text-red-700;
  }

  /* Gradient Text */
  .gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600;
  }

  /* Glass Effect */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-md border border-white border-opacity-20;
  }

  /* Image Overlay */
  .image-overlay {
    @apply absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300;
  }

  /* Skeleton Loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Focus Visible */
  .focus-visible {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
  }

  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Toast Overrides */
.react-hot-toast div[role="status"] {
  @apply !bg-white !text-gray-900 !shadow-lg !border !border-gray-200;
}

.react-hot-toast div[role="status"][data-type="success"] {
  @apply !bg-green-50 !text-green-800 !border-green-200;
}

.react-hot-toast div[role="status"][data-type="error"] {
  @apply !bg-red-50 !text-red-800 !border-red-200;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    color: black !important;
    background: white !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-900;
  }
  
  .btn-primary {
    @apply border-2 border-black;
  }
}

/* Dark Mode Support (if needed later) */
@media (prefers-color-scheme: dark) {
  /* Add dark mode styles here if needed */
}

/* Swiper Gallery Styles */
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
}

.swiper-button-next,
.swiper-button-prev {
  background-color: white;
  border-radius: 50%;
  width: 40px !important;
  height: 40px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 18px !important;
  font-weight: bold;
}

.swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background: rgba(0, 0, 0, 0.5);
}

.swiper-pagination-bullet-active {
  background: #000;
}

/* Thumbnail Gallery */
.thumbs-gallery {
  padding: 10px 0;
}

.thumbs-gallery .swiper-slide {
  opacity: 0.6;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.thumbs-gallery .swiper-slide-thumb-active {
  opacity: 1;
  border: 2px solid #3b82f6;
  border-radius: 0.375rem;
}

/* Hide navigation buttons on small screens */
@media (max-width: 640px) {
  .swiper-button-next,
  .swiper-button-prev {
    display: none !important;
  }
}

/* Enhanced Product Display Styles */
.product-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.product-image-overlay {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

.price-gradient {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.badge-gradient {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.feature-icon-bg {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Smooth animations */
.smooth-bounce {
  animation: smooth-bounce 2s infinite;
}

@keyframes smooth-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Product gallery styles */
.product-gallery-main {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.product-gallery-thumb {
  transition: all 0.2s ease;
}

.product-gallery-thumb:hover {
  transform: scale(1.05);
}

.product-gallery-thumb.active {
  box-shadow: 0 0 0 3px #3b82f6, 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Enhanced buttons */
.btn-primary-enhanced {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
}

.btn-primary-enhanced:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
  transform: translateY(-2px);
}

/* Text effects */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading animations */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}