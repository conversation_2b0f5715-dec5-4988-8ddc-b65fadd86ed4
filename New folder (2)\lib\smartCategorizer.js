// Smart Category System for AliDrop Integration
// Automatically categorizes products and corrects wrong categories

import axios from 'axios';

// Category mapping database with keywords and patterns
const CATEGORY_MAPPINGS = {
  // Electronics & Technology
  'electronics': {
    keywords: ['phone', 'smartphone', 'tablet', 'laptop', 'computer', 'headphones', 'earbuds', 'speaker', 'charger', 'cable', 'adapter', 'power bank', 'wireless', 'bluetooth', 'usb', 'hdmi', 'electronic', 'digital', 'smart watch', 'fitness tracker', 'camera', 'drone', 'gaming', 'console', 'keyboard', 'mouse', 'monitor', 'tv', 'television'],
    patterns: [/\b(iphone|samsung|apple|android|ios)\b/i, /\b\d+gb\b/i, /\bmah\b/i, /\bwifi\b/i],
    aliases: ['technology', 'gadgets', 'tech', 'digital', 'smart devices']
  },

  // Fashion & Clothing
  'fashion': {
    keywords: ['shirt', 'dress', 'pants', 'jeans', 'jacket', 'coat', 'sweater', 'hoodie', 'shoes', 'sneakers', 'boots', 'sandals', 'hat', 'cap', 'belt', 'bag', 'purse', 'wallet', 'jewelry', 'necklace', 'ring', 'earrings', 'bracelet', 'watch', 'sunglasses', 'clothing', 'apparel', 'fashion', 'style', 'outfit', 'wear', 'cotton', 'polyester', 'denim', 'leather'],
    patterns: [/\b(size|xl|lg|md|sm|xs)\b/i, /\b(men|women|kids|unisex)\b/i, /\b\d+(\"|inch|cm)\b/i],
    aliases: ['clothing', 'apparel', 'style', 'wear', 'accessories']
  },

  // Home & Garden
  'home-garden': {
    keywords: ['furniture', 'chair', 'table', 'bed', 'sofa', 'lamp', 'light', 'curtain', 'pillow', 'blanket', 'kitchen', 'cookware', 'utensils', 'plate', 'cup', 'mug', 'glass', 'bottle', 'storage', 'organizer', 'decor', 'decoration', 'plant', 'garden', 'outdoor', 'patio', 'tools', 'cleaning', 'bathroom', 'towel', 'shower', 'toilet', 'mirror', 'frame'],
    patterns: [/\b(dining|living|bed)room\b/i, /\b(indoor|outdoor)\b/i],
    aliases: ['home', 'garden', 'furniture', 'decor', 'household']
  },

  // Beauty & Health
  'beauty-health': {
    keywords: ['makeup', 'cosmetics', 'skincare', 'cream', 'lotion', 'serum', 'mask', 'shampoo', 'conditioner', 'perfume', 'fragrance', 'nail', 'polish', 'lipstick', 'foundation', 'mascara', 'eyeshadow', 'brush', 'beauty', 'health', 'wellness', 'vitamin', 'supplement', 'medicine', 'first aid', 'thermometer', 'massage', 'spa'],
    patterns: [/\b(anti-aging|moisturizing|organic|natural)\b/i, /\bml\b/i, /\boz\b/i],
    aliases: ['beauty', 'health', 'cosmetics', 'skincare', 'wellness']
  },

  // Sports & Outdoors
  'sports-outdoors': {
    keywords: ['sports', 'fitness', 'exercise', 'gym', 'workout', 'running', 'cycling', 'swimming', 'yoga', 'camping', 'hiking', 'outdoor', 'tent', 'backpack', 'sleeping bag', 'fishing', 'hunting', 'ball', 'equipment', 'gear', 'athletic', 'training', 'weights', 'dumbbell', 'treadmill', 'bike', 'bicycle'],
    patterns: [/\b(waterproof|breathable|lightweight)\b/i, /\b(kg|lbs|pounds)\b/i],
    aliases: ['sports', 'outdoors', 'fitness', 'exercise', 'athletic']
  },

  // Toys & Games
  'toys-games': {
    keywords: ['toy', 'game', 'puzzle', 'doll', 'action figure', 'lego', 'blocks', 'educational', 'learning', 'kids', 'children', 'baby', 'infant', 'toddler', 'plush', 'stuffed animal', 'board game', 'card game', 'video game', 'console', 'controller', 'remote control', 'rc', 'drone', 'robot'],
    patterns: [/\b(ages|years|months)\s+\d+/i, /\b(educational|learning)\b/i],
    aliases: ['toys', 'games', 'kids', 'children', 'baby']
  },

  // Automotive
  'automotive': {
    keywords: ['car', 'auto', 'vehicle', 'automotive', 'parts', 'accessories', 'tire', 'wheel', 'engine', 'oil', 'filter', 'brake', 'battery', 'charger', 'mount', 'holder', 'cover', 'seat', 'steering', 'dashboard', 'gps', 'navigation', 'dash cam', 'tools', 'mechanic'],
    patterns: [/\b(universal|compatible)\b/i, /\b(12v|24v)\b/i],
    aliases: ['automotive', 'car', 'vehicle', 'auto']
  },

  // Books & Media
  'books-media': {
    keywords: ['book', 'novel', 'magazine', 'journal', 'notebook', 'diary', 'pen', 'pencil', 'marker', 'stationery', 'office', 'supplies', 'paper', 'cd', 'dvd', 'blu-ray', 'movie', 'music', 'album', 'vinyl', 'record'],
    patterns: [/\b(paperback|hardcover|ebook)\b/i, /\b(isbn|pages)\b/i],
    aliases: ['books', 'media', 'stationery', 'office']
  }
};

// AI-powered product analysis using product title, description, and images
class SmartCategorizer {
  constructor() {
    this.categoryMappings = CATEGORY_MAPPINGS;
  }

  // Main function to categorize a product
  async categorizeProduct(product) {
    try {
      console.log(`🧠 Smart categorizing product: ${product.name}`);
      
      // Extract product information
      const productText = this.extractProductText(product);
      
      // Analyze using multiple methods
      const keywordScore = this.analyzeKeywords(productText);
      const patternScore = this.analyzePatterns(productText);
      const titleScore = this.analyzeTitle(product.name);
      
      // Combine scores
      const combinedScores = this.combineScores([keywordScore, patternScore, titleScore]);
      
      // Get the best category
      const suggestedCategory = this.getBestCategory(combinedScores);
      
      // Validate against current category
      const currentCategory = this.getCurrentCategory(product);
      const needsCorrection = this.needsCategoryCorrection(currentCategory, suggestedCategory);
      
      return {
        currentCategory,
        suggestedCategory,
        needsCorrection,
        confidence: combinedScores[suggestedCategory] || 0,
        analysis: {
          keywordMatches: this.getKeywordMatches(productText, suggestedCategory),
          patternMatches: this.getPatternMatches(productText, suggestedCategory)
        }
      };
      
    } catch (error) {
      console.error('Error in smart categorization:', error);
      return {
        currentCategory: this.getCurrentCategory(product),
        suggestedCategory: 'uncategorized',
        needsCorrection: false,
        confidence: 0,
        error: error.message
      };
    }
  }

  // Extract all text content from product
  extractProductText(product) {
    const texts = [
      product.name || '',
      product.description || '',
      product.short_description || '',
      product.sku || '',
      ...(product.tags || []).map(tag => tag.name || ''),
      ...(product.attributes || []).map(attr => `${attr.name} ${attr.options?.join(' ') || ''}`)
    ];
    
    return texts.join(' ').toLowerCase();
  }

  // Analyze keywords in product text
  analyzeKeywords(productText) {
    const scores = {};
    
    Object.entries(this.categoryMappings).forEach(([category, mapping]) => {
      let score = 0;
      const keywords = mapping.keywords;
      
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = productText.match(regex);
        if (matches) {
          score += matches.length * 2; // Weight keyword matches highly
        }
      });
      
      scores[category] = score;
    });
    
    return scores;
  }

  // Analyze patterns in product text
  analyzePatterns(productText) {
    const scores = {};
    
    Object.entries(this.categoryMappings).forEach(([category, mapping]) => {
      let score = 0;
      const patterns = mapping.patterns || [];
      
      patterns.forEach(pattern => {
        const matches = productText.match(pattern);
        if (matches) {
          score += matches.length * 3; // Weight pattern matches very highly
        }
      });
      
      scores[category] = score;
    });
    
    return scores;
  }

  // Analyze product title specifically
  analyzeTitle(title) {
    const titleText = title.toLowerCase();
    const scores = {};
    
    Object.entries(this.categoryMappings).forEach(([category, mapping]) => {
      let score = 0;
      
      // Check for exact category name in title
      if (titleText.includes(category.replace('-', ' '))) {
        score += 10;
      }
      
      // Check for aliases
      mapping.aliases?.forEach(alias => {
        if (titleText.includes(alias)) {
          score += 8;
        }
      });
      
      // Check for high-value keywords in title
      mapping.keywords.slice(0, 10).forEach(keyword => {
        if (titleText.includes(keyword)) {
          score += 5; // Title keywords are very important
        }
      });
      
      scores[category] = score;
    });
    
    return scores;
  }

  // Combine multiple scoring methods
  combineScores(scoreArrays) {
    const combined = {};
    
    scoreArrays.forEach(scores => {
      Object.entries(scores).forEach(([category, score]) => {
        combined[category] = (combined[category] || 0) + score;
      });
    });
    
    return combined;
  }

  // Get the category with highest score
  getBestCategory(scores) {
    let bestCategory = 'uncategorized';
    let bestScore = 0;
    
    Object.entries(scores).forEach(([category, score]) => {
      if (score > bestScore) {
        bestScore = score;
        bestCategory = category;
      }
    });
    
    // Require minimum confidence
    return bestScore >= 3 ? bestCategory : 'uncategorized';
  }

  // Get current category from product
  getCurrentCategory(product) {
    if (product.categories && product.categories.length > 0) {
      return product.categories[0].slug || product.categories[0].name?.toLowerCase().replace(/\s+/g, '-');
    }
    return 'uncategorized';
  }

  // Check if category needs correction
  needsCategoryCorrection(currentCategory, suggestedCategory) {
    if (!currentCategory || currentCategory === 'uncategorized') {
      return suggestedCategory !== 'uncategorized';
    }
    
    // Check if current category is completely wrong
    const currentNormalized = currentCategory.toLowerCase().replace(/[-_\s]/g, '');
    const suggestedNormalized = suggestedCategory.toLowerCase().replace(/[-_\s]/g, '');
    
    return currentNormalized !== suggestedNormalized && suggestedCategory !== 'uncategorized';
  }

  // Get keyword matches for analysis
  getKeywordMatches(productText, category) {
    const mapping = this.categoryMappings[category];
    if (!mapping) return [];
    
    const matches = [];
    mapping.keywords.forEach(keyword => {
      if (productText.includes(keyword)) {
        matches.push(keyword);
      }
    });
    
    return matches;
  }

  // Get pattern matches for analysis
  getPatternMatches(productText, category) {
    const mapping = this.categoryMappings[category];
    if (!mapping || !mapping.patterns) return [];
    
    const matches = [];
    mapping.patterns.forEach(pattern => {
      const match = productText.match(pattern);
      if (match) {
        matches.push(match[0]);
      }
    });
    
    return matches;
  }

  // Batch categorize multiple products
  async batchCategorize(products) {
    console.log(`🧠 Batch categorizing ${products.length} products...`);
    
    const results = [];
    for (const product of products) {
      const result = await this.categorizeProduct(product);
      results.push({
        productId: product.id,
        productName: product.name,
        ...result
      });
    }
    
    return results;
  }

  // Get category statistics
  getCategoryStats(results) {
    const stats = {
      total: results.length,
      needsCorrection: 0,
      byCategory: {},
      corrections: []
    };
    
    results.forEach(result => {
      if (result.needsCorrection) {
        stats.needsCorrection++;
        stats.corrections.push({
          productId: result.productId,
          productName: result.productName,
          from: result.currentCategory,
          to: result.suggestedCategory,
          confidence: result.confidence
        });
      }
      
      const category = result.suggestedCategory;
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
    });
    
    return stats;
  }
}

const smartCategorizer = new SmartCategorizer();
export default smartCategorizer;
