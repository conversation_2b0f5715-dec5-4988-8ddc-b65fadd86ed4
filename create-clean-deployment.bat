@echo off
echo Creating clean cPanel deployment package...

REM Create deployment directory
set DEPLOY_PATH=Deal4u_FINAL_DEPLOYMENT
if exist "%DEPLOY_PATH%" rmdir /s /q "%DEPLOY_PATH%"
mkdir "%DEPLOY_PATH%"

echo Copying essential files...

REM Copy main files
copy /Y server.js "%DEPLOY_PATH%\"
copy /Y package.json "%DEPLOY_PATH%\"
copy /Y next.config.js "%DEPLOY_PATH%\"
copy /Y tailwind.config.js "%DEPLOY_PATH%\"
copy /Y postcss.config.js "%DEPLOY_PATH%\"
copy /Y jsconfig.json "%DEPLOY_PATH%\"

echo Copying application folders...

REM Copy essential folders only
xcopy /E /I /Y .next "%DEPLOY_PATH%\.next"
xcopy /E /I /Y app "%DEPLOY_PATH%\app"
xcopy /E /I /Y components "%DEPLOY_PATH%\components"
xcopy /E /I /Y context "%DEPLOY_PATH%\context"
xcopy /E /I /Y lib "%DEPLOY_PATH%\lib"
xcopy /E /I /Y public "%DEPLOY_PATH%\public"

REM Copy diagnostic server for troubleshooting
copy /Y diagnostic-server.js "%DEPLOY_PATH%\"

echo Creating deployment instructions...

echo DEAL4U CPANEL DEPLOYMENT - FINAL VERSION > "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ============================================ >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ✅ BUILD_ID ISSUE FIXED! >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ✅ All necessary files included >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ✅ Dynamic WooCommerce sync enabled >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 1: UPLOAD TO CPANEL >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ======================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Go to cPanel File Manager >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Navigate to /public_html/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. DELETE all old files first >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Upload ALL files from this folder >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 2: CREATE NODE.JS APP >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ========================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Go to cPanel Node.js Apps >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. DELETE any existing Deal4u apps >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Create new application: >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Node.js Version: 18.x >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Application Root: /public_html/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Application URL: yourdomain.com/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Startup File: server.js >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 3: INSTALL DEPENDENCIES >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ============================ >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Click on your app >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Go to Package.json tab >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Click "Run NPM Install" >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Wait for completion >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 4: SET ENVIRONMENT >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ====================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Environment Variables tab >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Add: NODE_ENV = production >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 5: START APP >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ================= >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Click "Start App" >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Should show "Running" status >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Visit your website! >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo TROUBLESHOOTING: >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ================ >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo If app fails to start: >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Change startup file to: diagnostic-server.js >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Restart app >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Visit website to see diagnostic info >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Check what files are missing >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"

echo.
echo ✅ SUCCESS! Clean deployment package created!
echo 📁 Location: %DEPLOY_PATH%
echo 📖 Read README_DEPLOYMENT.txt for instructions
echo 🔧 BUILD_ID issue is now fixed!
echo.
pause
