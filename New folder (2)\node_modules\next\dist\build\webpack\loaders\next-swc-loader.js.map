{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["sw<PERSON><PERSON><PERSON><PERSON>", "pitch", "raw", "maybeExclude", "excludePath", "transpilePackages", "babelIncludeRegexes", "some", "r", "test", "shouldBeBundled", "isResourceInPackages", "includes", "FORCE_TRANSPILE_CONDITIONS", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "shouldMaybeExclude", "Error", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "bundleLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "path", "relative", "swcOptions", "getLoaderSWCOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "transform", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "callback", "async", "process", "versions", "pnp", "loaders", "length", "loaderIndex", "isAbsolute", "isWasm", "loaderSpan", "currentTraceSpan", "addDependency", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;;;;;;;;;;IAgMA,OAmBC;eAnBuBA;;IAhCRC,KAAK;eAALA;;IAsDHC,GAAG;eAAHA;;;qBAlNqB;yBACE;8DACH;+BACG;iCACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErC,MAAMC,eAAe,CACnBC,aACAC;IAEA,IAAIC,kCAAmB,CAACC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACL,eAAe;QACxD,OAAO;IACT;IAEA,MAAMM,kBAAkBC,IAAAA,qCAAoB,EAACP,aAAaC;IAC1D,IAAIK,iBAAiB,OAAO;IAE5B,OAAON,YAAYQ,QAAQ,CAAC;AAC9B;AAmBA,0CAA0C;AAC1C,2CAA2C;AAC3C,MAAMC,6BACJ;AAEF,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QA+CMC,0BACZA,2BAESA;IAhDvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAC5D,MAAMC,qBAAqBpB,aACzBgB,UACAE,cAAchB,iBAAiB,IAAI,EAAE;IAGvC,IAAIkB,oBAAoB;QACtB,IAAI,CAACP,QAAQ;YACX,MAAM,IAAIQ,MAAM,CAAC,8CAA8C,CAAC;QAClE;QAEA,IAAI,CAACX,2BAA2BJ,IAAI,CAACO,SAAS;YAC5C,OAAO;gBAACA;gBAAQC;aAAe;QACjC;IACF;IAEA,MAAM,EACJQ,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfX,UAAU,EACVY,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,WAAW,EACXC,GAAG,EACJ,GAAGd;IACJ,MAAMe,aAAajB,SAASkB,UAAU,CAACV;IACvC,MAAMW,2BAA2BC,aAAI,CAACC,QAAQ,CAACd,SAASP;IAExD,MAAMsB,aAAaC,IAAAA,4BAAmB,EAAC;QACrCf;QACAC;QACAT;QACAM;QACAW;QACAO,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bf;QACAgB,iBAAiB,EAAE3B,8BAAAA,WAAY2B,iBAAiB;QAChDC,sBAAsB,EAAE5B,+BAAAA,2BAAAA,WAAY6B,YAAY,qBAAxB7B,yBAA0B4B,sBAAsB;QACxEE,UAAU,EAAE9B,+BAAAA,4BAAAA,WAAY6B,YAAY,qBAAxB7B,0BAA0B8B,UAAU;QAChDC,eAAe,EAAE/B,8BAAAA,WAAYgC,QAAQ;QACrCC,mBAAmB,EAAEjC,+BAAAA,4BAAAA,WAAY6B,YAAY,qBAAxB7B,0BAA0BiC,mBAAmB;QAClErB;QACAC;QACAC;QACAM;QACAL;QACAC;QACAC;IACF;IAEA,MAAMiB,sBAAsB;QAC1B,GAAGX,UAAU;QACbtB;QACAF,gBAAgBA,iBAAiBoC,KAAKC,SAAS,CAACrC,kBAAkBsC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBxC;IAClB;IAEA,IAAI,CAACiC,oBAAoBnC,cAAc,EAAE;QACvC,OAAOmC,oBAAoBnC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAAC2B,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACC,SAAS,IACjCT,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCd,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,EACvC,gBAEF;QACAV,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,CAACnB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMuB,UAAUpD,YAAYqD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BR,IAAAA,cAAS,EAAC7C,QAAeoC,qBAAqBkB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOpB,KAAKqB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGxB,KAAKqB,KAAK,CAACH,OAAOM,GAAG,IAAItB;aAAU;QACvE;AAEJ;AAEA,MAAMuB,iBACJ;AAEK,SAAS7E;IACd,MAAM8E,WAAW,IAAI,CAACC,KAAK;IAC3B,IAAI3D,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAMC,qBAAqBpB,aACzB,IAAI,CAACiB,YAAY,EACjBC,cAAchB,iBAAiB,IAAI,EAAE;IAGrC,CAAA;QACA,IACE,0DAA0D;QAC1D,CAACkB,sBACD,kDAAkD;QAClD,CAAC0D,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAerE,IAAI,CAAC,IAAI,CAACW,YAAY,KACtC,IAAI,CAACgE,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CC,IAAAA,gBAAU,EAAC,IAAI,CAACnE,YAAY,KAC5B,CAAE,MAAMoE,IAAAA,WAAM,KACd;YACA,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;YACpD,IAAI,CAACuB,aAAa,CAAC,IAAI,CAACvE,YAAY;YACpC,OAAOqE,WAAWpB,YAAY,CAAC,IAC7BvD,gBAAgBoD,IAAI,CAAC,IAAI,EAAEuB;QAE/B;IACF,CAAA,IAAKnB,IAAI,CAAC,CAAC9D;QACT,IAAIA,GAAG,OAAOuE,SAAS,SAASvE;QAChCuE;IACF,GAAGA;AACL;AAEe,SAAS/E,UAEtB4F,WAAmB,EACnB3E,cAAmB;IAEnB,MAAMwE,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;IACpD,MAAMW,WAAW,IAAI,CAACC,KAAK;IAC3BS,WACGpB,YAAY,CAAC,IACZvD,gBAAgBoD,IAAI,CAAC,IAAI,EAAEuB,YAAYG,aAAa3E,iBAErDqD,IAAI,CACH,CAAC,CAACuB,mBAAmBC,gBAAqB;QACxCf,SAAS,MAAMc,mBAAmBC,mBAAmB7E;IACvD,GACA,CAAC8E;QACChB,SAASgB;IACX;AAEN;AAGO,MAAM7F,MAAM"}