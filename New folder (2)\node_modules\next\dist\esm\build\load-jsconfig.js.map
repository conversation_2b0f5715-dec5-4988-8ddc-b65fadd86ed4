{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "names": ["path", "fs", "Log", "getTypeScriptConfiguration", "readFileSync", "isError", "hasNecessaryDependencies", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "trim", "parse", "err", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "loadJsConfig", "dir", "config", "jsConfig", "typeScriptPath", "deps", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "info", "ts", "Promise", "resolve", "tsConfig", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl", "isImplicit"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,YAAYC,SAAS,eAAc;AACnC,SAASC,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,aAAa,kBAAiB;AACrC,SAASC,wBAAwB,QAAQ,oCAAmC;AAE5E,IAAIC,kBAAkB;AAEtB,SAASC,cAAcC,QAAgB;IACrC,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWR,aAAaK,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASC,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOH,MAAMI,KAAK,CAACF;IACrB,EAAE,OAAOG,KAAK;QACZ,IAAI,CAACV,QAAQU,MAAM,MAAMA;QACzB,MAAM,EAAEC,gBAAgB,EAAE,GAAGL,QAAQ;QACrC,MAAMM,YAAYD,iBAChBE,OAAON,WACP;YACEO,OAAO;gBACLC,MAAM,AAACL,IAAwCM,UAAU,IAAI;gBAC7DC,QAAQ,AAACP,IAA0CQ,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAAST,IAAIS,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEjB,SAAS,IAAI,EAAEQ,UAAU,CAAC;IAChE;AACF;AAQA,eAAe,eAAeU,aAC5BC,GAAW,EACXC,MAA0B;QA+CtBC;IAzCJ,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAM1B,yBAAyBsB,KAAK;YAC/C;gBACEK,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDJ,iBAAiBC,KAAKI,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAetC,KAAKuC,IAAI,CAACX,KAAKC,OAAOW,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQZ,kBAAkB9B,GAAG2C,UAAU,CAACN;IAE9D,IAAIO;IACJ,IAAIf;IACJ,mCAAmC;IACnC,IAAIY,eAAe;QACjB,IACEb,OAAOW,UAAU,CAACC,YAAY,KAAK,mBACnClC,oBAAoB,OACpB;YACAA,kBAAkB;YAClBL,IAAI4C,IAAI,CAAC,CAAC,qBAAqB,EAAEjB,OAAOW,UAAU,CAACC,YAAY,CAAC,CAAC;QACnE;QAEA,MAAMM,KAAM,MAAMC,QAAQC,OAAO,CAC/BtC,QAAQoB;QAEV,MAAMmB,WAAW,MAAM/C,2BAA2B4C,IAAIT,cAAc;QACpER,WAAW;YAAEqB,iBAAiBD,SAASE,OAAO;QAAC;QAC/CP,kBAAkB7C,KAAKqD,OAAO,CAACf;IACjC;IAEA,MAAMgB,eAAetD,KAAKuC,IAAI,CAACX,KAAK;IACpC,IAAI,CAACc,iBAAiBzC,GAAG2C,UAAU,CAACU,eAAe;QACjDxB,WAAWtB,cAAc8C;QACzBT,kBAAkB7C,KAAKqD,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAIzB,6BAAAA,4BAAAA,SAAUqB,eAAe,qBAAzBrB,0BAA2B0B,OAAO,EAAE;QACtCD,kBAAkB;YAChBC,SAASxD,KAAKiD,OAAO,CAACrB,KAAKE,SAASqB,eAAe,CAACK,OAAO;YAC3DC,YAAY;QACd;IACF,OAAO;QACL,IAAIZ,iBAAiB;YACnBU,kBAAkB;gBAChBC,SAASX;gBACTY,YAAY;YACd;QACF;IACF;IAEA,OAAO;QACLf;QACAZ;QACAyB;IACF;AACF"}