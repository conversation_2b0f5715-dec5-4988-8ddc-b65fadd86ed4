const axios = require('axios');

async function testAPIStatus() {
  console.log('🔍 Testing API Status and Shop Page...\n');
  
  try {
    // Test 1: Check if server is responding
    console.log('1. Testing server response...');
    const response = await axios.get('http://localhost:3000/shop', { 
      timeout: 10000 
    });
    
    console.log(`✅ Server responding (${response.status})`);
    console.log(`   Content length: ${response.data.length} characters`);
    
    // Test 2: Check for demo mode banner
    const content = response.data;
    const hasDemoMode = content.includes('Demo Mode') || content.includes('demo mode');
    const hasAPIError = content.includes('API authentication failed') || content.includes('authentication');
    const hasProducts = content.includes('Premium Wireless Headphones') || content.includes('Smart Fitness Watch');
    
    console.log('\n📊 Content Analysis:');
    console.log(`   Demo mode banner: ${hasDemoMode ? 'Present' : 'Missing'}`);
    console.log(`   API error message: ${hasAPIError ? 'Present' : 'Missing'}`);
    console.log(`   Demo products: ${hasProducts ? 'Present' : 'Missing'}`);
    
    // Test 3: Check for loading states
    const isLoading = content.includes('Loading amazing deals') || 
                     content.includes('animate-spin');
    
    console.log(`   Loading state: ${isLoading ? 'Active' : 'Inactive'}`);
    
    // Test 4: Look for specific elements
    const elements = {
      'Product grid': content.includes('ProductGrid') || content.includes('product-grid'),
      'Add to cart': content.includes('Add to Cart'),
      'Price display': content.match(/\$\d+\.\d+/g)?.length || 0,
      'Product cards': content.includes('product-card') || content.includes('ProductCard'),
      'Filter sidebar': content.includes('ProductFilter') || content.includes('filter')
    };
    
    console.log('\n🔍 UI Elements:');
    Object.entries(elements).forEach(([element, found]) => {
      if (typeof found === 'number') {
        console.log(`   ${element}: ${found} instances`);
      } else {
        console.log(`   ${element}: ${found ? 'Found' : 'Missing'}`);
      }
    });
    
    // Test 5: Check for React hydration
    const hasReactData = content.includes('__NEXT_DATA__');
    const hasClientJS = content.includes('_next/static');
    
    console.log('\n⚛️ React Status:');
    console.log(`   Next.js data: ${hasReactData ? 'Present' : 'Missing'}`);
    console.log(`   Client JS: ${hasClientJS ? 'Present' : 'Missing'}`);
    
    if (isLoading && hasReactData) {
      console.log('\n💡 Diagnosis: Page is server-side rendered but stuck in client-side loading');
      console.log('   This suggests a JavaScript error preventing React hydration');
    } else if (!hasReactData) {
      console.log('\n💡 Diagnosis: Static HTML without React hydration');
    } else if (hasDemoMode) {
      console.log('\n💡 Diagnosis: Demo mode active due to API issues');
    } else {
      console.log('\n💡 Diagnosis: Page appears to be working correctly');
    }
    
  } catch (error) {
    console.log('❌ Error testing API status:');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Make sure the development server is running on port 3000');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   Server is taking too long to respond');
    }
  }
}

testAPIStatus();
