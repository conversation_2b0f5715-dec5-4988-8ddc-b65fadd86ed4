'use client';

import { FaGoogle, FaFacebook } from 'react-icons/fa';
import { socialAuth } from '@/lib/socialAuth';

export default function SocialLoginButtons() {
  // Initiate social login
  const handleSocialLogin = (provider) => {
    socialAuth.initiateLogin(provider);
  };

  return (
    <div className="flex flex-col space-y-3 w-full">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300 dark:border-gray-700"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
            Or continue with
          </span>
        </div>
      </div>
      
      <div className="flex space-x-3">
        <button
          type="button"
          onClick={() => handleSocialLogin('google')}
          className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <div className="flex items-center justify-center">
            <FaGoogle className="h-5 w-5 text-red-600 mr-2" />
            <span>Google</span>
          </div>
        </button>
        
        <button
          type="button"
          onClick={() => handleSocialLogin('facebook')}
          className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <div className="flex items-center justify-center">
            <FaFacebook className="h-5 w-5 text-blue-600 mr-2" />
            <span>Facebook</span>
          </div>
        </button>
      </div>
    </div>
  );
}
