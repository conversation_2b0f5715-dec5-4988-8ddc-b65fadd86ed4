{"version": 3, "sources": ["../../../src/lib/eslint/hasEslintConfiguration.ts"], "names": ["hasEslintConfiguration", "eslintrcFile", "packageJsonConfig", "configObject", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "replace", "eslintConfig", "Object", "keys", "length"], "mappings": ";;;;+BASsBA;;;eAAAA;;;oBATS;AASxB,eAAeA,uBACpBC,YAA2B,EAC3BC,iBAA+C;IAE/C,MAAMC,eAAe;QACnBC,QAAQ;QACRC,eAAe;QACfC,oBAAoB;IACtB;IAEA,IAAIL,cAAc;QAChB,MAAMM,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACR,cAAc;YAAES,UAAU;QAAO,GAAGC,IAAI,CACxE,CAACC,MAAQA,IAAIC,IAAI,GAAGC,OAAO,CAAC,OAAO,KACnC,IAAM;QAGR,IACEP,YAAY,MACZA,YAAY,QACZA,YAAY,SACZA,YAAY,uBACZ;YACAJ,aAAaE,aAAa,GAAG;QAC/B,OAAO;YACLF,aAAaC,MAAM,GAAG;QACxB;IACF,OAAO,IAAIF,qCAAAA,kBAAmBa,YAAY,EAAE;QAC1C,IAAIC,OAAOC,IAAI,CAACf,kBAAkBa,YAAY,EAAEG,MAAM,EAAE;YACtDf,aAAaC,MAAM,GAAG;QACxB,OAAO;YACLD,aAAaG,kBAAkB,GAAG;QACpC;IACF;IACA,OAAOH;AACT"}