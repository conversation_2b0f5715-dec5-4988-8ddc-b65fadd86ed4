"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sync-wordpress-products/route";
exports.ids = ["app/api/sync-wordpress-products/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync-wordpress-products%2Froute&page=%2Fapi%2Fsync-wordpress-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync-wordpress-products%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync-wordpress-products%2Froute&page=%2Fapi%2Fsync-wordpress-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync-wordpress-products%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_sync_wordpress_products_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/sync-wordpress-products/route.js */ \"(rsc)/./app/api/sync-wordpress-products/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sync-wordpress-products/route\",\n        pathname: \"/api/sync-wordpress-products\",\n        filename: \"route\",\n        bundlePath: \"app/api/sync-wordpress-products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Last1\\\\New folder (2)\\\\app\\\\api\\\\sync-wordpress-products\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_sync_wordpress_products_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/sync-wordpress-products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync-wordpress-products%2Froute&page=%2Fapi%2Fsync-wordpress-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync-wordpress-products%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sync-wordpress-products/route.js":
/*!**************************************************!*\
  !*** ./app/api/sync-wordpress-products/route.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _woocommerce_woocommerce_rest_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @woocommerce/woocommerce-rest-api */ \"(rsc)/./node_modules/@woocommerce/woocommerce-rest-api/index.js\");\n\n\nconst WC_CONFIG = {\n    baseURL: \"https://deal4u.co\",\n    consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,\n    consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,\n    version: \"wc/v3\"\n};\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDD04 Starting WordPress products sync...\");\n        const api = new _woocommerce_woocommerce_rest_api__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n            url: WC_CONFIG.baseURL,\n            consumerKey: WC_CONFIG.consumerKey,\n            consumerSecret: WC_CONFIG.consumerSecret,\n            version: WC_CONFIG.version\n        });\n        // Get ALL products from WordPress (any status)\n        let allProducts = [];\n        let page = 1;\n        let hasMore = true;\n        while(hasMore){\n            console.log(`📦 Fetching page ${page} from WordPress...`);\n            const response = await api.get(\"products\", {\n                per_page: 100,\n                page: page,\n                status: \"any\" // Get all products regardless of status\n            });\n            const products = response.data.filter((p)=>p.status !== \"trash\");\n            allProducts = [\n                ...allProducts,\n                ...products\n            ];\n            hasMore = products.length === 100;\n            page++;\n        }\n        console.log(`📊 Found ${allProducts.length} products in WordPress`);\n        // Process each product\n        const processedProducts = [];\n        const errors = [];\n        for (const product of allProducts){\n            try {\n                // Clean up product data\n                const cleanProduct = {\n                    id: product.id,\n                    name: product.name?.replace(/AliExpress/gi, \"Deal4U\") || \"Untitled Product\",\n                    description: product.description?.replace(/AliExpress/gi, \"Deal4U\") || \"\",\n                    short_description: product.short_description?.replace(/AliExpress/gi, \"Deal4U\") || \"\",\n                    price: product.regular_price || product.price || \"0\",\n                    sale_price: product.sale_price || \"\",\n                    images: product.images || [],\n                    categories: product.categories || [],\n                    status: product.status,\n                    stock_status: product.stock_status || \"instock\",\n                    permalink: product.permalink || \"\",\n                    date_created: product.date_created,\n                    date_modified: product.date_modified\n                };\n                // Filter out size chart images\n                if (cleanProduct.images.length > 0) {\n                    cleanProduct.images = cleanProduct.images.filter((img)=>{\n                        const imgSrc = img.src || \"\";\n                        const imgName = img.name || \"\";\n                        const imgAlt = img.alt || \"\";\n                        const isSizeChart = [\n                            imgSrc,\n                            imgName,\n                            imgAlt\n                        ].some((text)=>{\n                            const lower = text.toLowerCase();\n                            return [\n                                \"size\",\n                                \"chart\",\n                                \"table\",\n                                \"measurement\",\n                                \"guide\",\n                                \"cm\",\n                                \"inch\"\n                            ].some((term)=>lower.includes(term));\n                        });\n                        return !isSizeChart;\n                    });\n                }\n                processedProducts.push(cleanProduct);\n                console.log(`✅ Processed: ${cleanProduct.name}`);\n            } catch (error) {\n                console.error(`❌ Error processing product ${product.id}:`, error.message);\n                errors.push({\n                    productId: product.id,\n                    productName: product.name,\n                    error: error.message\n                });\n            }\n        }\n        // Group products by status\n        const statusGroups = {\n            published: processedProducts.filter((p)=>p.status === \"publish\").length,\n            draft: processedProducts.filter((p)=>p.status === \"draft\").length,\n            private: processedProducts.filter((p)=>p.status === \"private\").length,\n            pending: processedProducts.filter((p)=>p.status === \"pending\").length\n        };\n        // PUBLISH ALL DRAFT/PRIVATE PRODUCTS\n        console.log(\"\\uD83D\\uDCE2 Publishing draft/private products...\");\n        let publishedCount = 0;\n        for (const product of allProducts){\n            if (product.status !== \"publish\" && product.status !== \"trash\") {\n                try {\n                    await api.put(`products/${product.id}`, {\n                        status: \"publish\"\n                    });\n                    publishedCount++;\n                    console.log(`✅ Published: ${product.name}`);\n                } catch (error) {\n                    console.error(`❌ Failed to publish product ${product.id}:`, error.message);\n                }\n            }\n        }\n        console.log(`🎉 Sync completed: ${processedProducts.length} products processed`);\n        console.log(`📢 Published: ${publishedCount} products`);\n        console.log(`📊 Status breakdown:`, statusGroups);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Successfully synced ${processedProducts.length} products from WordPress and published ${publishedCount} products`,\n            totalProducts: allProducts.length,\n            processedProducts: processedProducts.length,\n            publishedCount: publishedCount,\n            statusBreakdown: statusGroups,\n            errors: errors,\n            products: processedProducts.slice(0, 10) // Return first 10 for preview\n        });\n    } catch (error) {\n        console.error(\"❌ WordPress sync error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message,\n            details: error.stack\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        message: \"WordPress Products Sync API\",\n        usage: {\n            \"POST /api/sync-wordpress-products\": \"Sync all products from WordPress to website\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sync-wordpress-products/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/@woocommerce","vendor-chunks/url-parse","vendor-chunks/supports-color","vendor-chunks/requires-port","vendor-chunks/querystringify","vendor-chunks/oauth-1.0a","vendor-chunks/has-flag","vendor-chunks/create-hmac"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync-wordpress-products%2Froute&page=%2Fapi%2Fsync-wordpress-products%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync-wordpress-products%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();