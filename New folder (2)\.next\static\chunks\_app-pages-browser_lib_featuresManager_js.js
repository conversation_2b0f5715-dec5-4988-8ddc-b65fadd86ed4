"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_featuresManager_js"],{

/***/ "(app-pages-browser)/./lib/featuresManager.js":
/*!********************************!*\
  !*** ./lib/featuresManager.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// Features Manager - Controls all website features with WooCommerce integration\nclass FeaturesManager {\n    // Initialize features from API\n    async initialize() {\n        try {\n            // Initialize WooCommerce integration first\n            await this.initializeWooCommerce();\n            const response = await fetch(\"/api/admin/toggle-feature\");\n            const data = await response.json();\n            if (data.success) {\n                this.features = data.features;\n                this.initialized = true;\n                console.log(\"✅ Features Manager initialized with WooCommerce integration\");\n                // Get current user if logged in\n                this.getCurrentUser();\n                this.applyFeatures();\n            }\n        } catch (error) {\n            console.error(\"❌ Error initializing Features Manager:\", error);\n            this.loadDefaultFeatures();\n        }\n    }\n    // Initialize WooCommerce integration\n    async initializeWooCommerce() {\n        try {\n            // Dynamically import WooCommerce features to avoid build issues\n            const wooCommerceModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_woocommerceFeatures_js\").then(__webpack_require__.bind(__webpack_require__, /*! ./woocommerceFeatures.js */ \"(app-pages-browser)/./lib/woocommerceFeatures.js\"));\n            this.wooCommerce = wooCommerceModule.default;\n            await this.wooCommerce.initialize();\n        } catch (error) {\n            console.error(\"Error initializing WooCommerce:\", error);\n            // Create a mock WooCommerce object for fallback\n            this.wooCommerce = {\n                getProductRecommendations: async ()=>[],\n                getLoyaltyPoints: async ()=>({\n                        points: 0,\n                        tier: \"Bronze\",\n                        totalSpent: 0,\n                        orderCount: 0\n                    }),\n                getReorderableOrders: async ()=>[],\n                addToCart: async ()=>({\n                        success: false,\n                        error: \"WooCommerce not available\"\n                    }),\n                bulkAddToCart: async ()=>({\n                        success: false,\n                        error: \"WooCommerce not available\"\n                    })\n            };\n        }\n    }\n    // Get current logged-in user\n    async getCurrentUser() {\n        try {\n            // Check if user is logged in (you can adapt this to your auth system)\n            const userToken = localStorage.getItem(\"userToken\") || sessionStorage.getItem(\"userToken\");\n            if (userToken) {\n                // In a real implementation, you'd decode the token or call an API\n                this.currentUser = {\n                    id: 1,\n                    email: \"<EMAIL>\"\n                }; // Demo user\n            }\n        } catch (error) {\n            console.error(\"Error getting current user:\", error);\n        }\n    }\n    // Load default features if API fails\n    loadDefaultFeatures() {\n        this.features = {\n            liveChat: {\n                enabled: true\n            },\n            productRecommendations: {\n                enabled: true\n            },\n            loyaltyProgram: {\n                enabled: false\n            },\n            arTryOn: {\n                enabled: false\n            },\n            voiceSearch: {\n                enabled: false\n            },\n            socialCommerce: {\n                enabled: true\n            },\n            pushNotifications: {\n                enabled: true\n            },\n            oneClickReorder: {\n                enabled: true\n            }\n        };\n        this.initialized = true;\n        this.applyFeatures();\n    }\n    // Check if feature is enabled\n    isEnabled(featureName) {\n        var _this_features_featureName;\n        return ((_this_features_featureName = this.features[featureName]) === null || _this_features_featureName === void 0 ? void 0 : _this_features_featureName.enabled) || false;\n    }\n    // Get feature settings\n    getSettings(featureName) {\n        var _this_features_featureName;\n        return ((_this_features_featureName = this.features[featureName]) === null || _this_features_featureName === void 0 ? void 0 : _this_features_featureName.settings) || {};\n    }\n    // Apply all enabled features\n    applyFeatures() {\n        if (this.isEnabled(\"liveChat\")) {\n            this.enableLiveChat();\n        }\n        if (this.isEnabled(\"productRecommendations\")) {\n            this.enableProductRecommendations();\n        }\n        if (this.isEnabled(\"loyaltyProgram\")) {\n            this.enableLoyaltyProgram();\n        }\n        if (this.isEnabled(\"pushNotifications\")) {\n            this.enablePushNotifications();\n        }\n        if (this.isEnabled(\"socialCommerce\")) {\n            this.enableSocialCommerce();\n        }\n        if (this.isEnabled(\"oneClickReorder\")) {\n            this.enableOneClickReorder();\n        }\n        if (this.isEnabled(\"voiceSearch\")) {\n            this.enableVoiceSearch();\n        }\n        if (this.isEnabled(\"arTryOn\")) {\n            this.enableARTryOn();\n        }\n    }\n    // Live Chat Feature\n    enableLiveChat() {\n        console.log(\"\\uD83D\\uDCAC Enabling Live Chat...\");\n        // Create chat widget\n        const chatWidget = document.createElement(\"div\");\n        chatWidget.id = \"live-chat-widget\";\n        chatWidget.innerHTML = '\\n      <div class=\"fixed bottom-4 right-4 z-50\">\\n        <button id=\"chat-toggle\" class=\"bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors\">\\n          \\uD83D\\uDCAC\\n        </button>\\n        <div id=\"chat-window\" class=\"hidden absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-xl border\">\\n          <div class=\"bg-blue-600 text-white p-4 rounded-t-lg\">\\n            <h3 class=\"font-semibold\">Deal4u Support</h3>\\n            <p class=\"text-sm opacity-90\">We\\'re here to help!</p>\\n          </div>\\n          <div class=\"p-4 h-64 overflow-y-auto\">\\n            <div class=\"bg-gray-100 p-3 rounded-lg mb-3\">\\n              <p class=\"text-sm\">\\uD83D\\uDC4B Hello! How can we help you today?</p>\\n            </div>\\n          </div>\\n          <div class=\"p-4 border-t\">\\n            <div class=\"flex space-x-2\">\\n              <input type=\"text\" placeholder=\"Type your message...\" class=\"flex-1 p-2 border rounded\">\\n              <button class=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\">Send</button>\\n            </div>\\n            <div class=\"mt-2\">\\n              <a href=\"https://wa.me/96171172405\" target=\"_blank\" class=\"text-green-600 text-sm hover:underline\">\\n                \\uD83D\\uDCF1 Chat on WhatsApp\\n              </a>\\n            </div>\\n          </div>\\n        </div>\\n      </div>\\n    ';\n        document.body.appendChild(chatWidget);\n        // Add chat functionality\n        document.getElementById(\"chat-toggle\").addEventListener(\"click\", ()=>{\n            const chatWindow = document.getElementById(\"chat-window\");\n            chatWindow.classList.toggle(\"hidden\");\n        });\n    }\n    // Product Recommendations Feature\n    enableProductRecommendations() {\n        console.log(\"\\uD83C\\uDFAF Enabling Product Recommendations...\");\n        // Add recommendations to product pages\n        const productContainers = document.querySelectorAll(\".product-container, .product-detail\");\n        productContainers.forEach((container)=>{\n            const recommendationsHTML = '\\n        <div class=\"mt-8 p-6 bg-gray-50 rounded-lg\">\\n          <h3 class=\"text-xl font-semibold mb-4\">You might also like</h3>\\n          <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\" id=\"recommendations-container\">\\n            <!-- Recommendations will be loaded here -->\\n          </div>\\n        </div>\\n      ';\n            container.insertAdjacentHTML(\"afterend\", recommendationsHTML);\n        });\n        // Load recommendations (you can integrate with your product API)\n        this.loadProductRecommendations();\n    }\n    // Load product recommendations using WooCommerce data\n    async loadProductRecommendations() {\n        try {\n            // Get current product ID from page\n            const currentProductId = this.getCurrentProductId();\n            if (!currentProductId) {\n                console.log(\"No product ID found, loading popular products\");\n                return this.loadPopularProducts();\n            }\n            console.log(\"\\uD83C\\uDFAF Loading recommendations for product \".concat(currentProductId, \"...\"));\n            // Get recommendations from WooCommerce\n            const recommendations = await this.wooCommerce.getProductRecommendations(currentProductId, 4);\n            if (recommendations && recommendations.length > 0) {\n                const container = document.getElementById(\"recommendations-container\");\n                if (container) {\n                    container.innerHTML = recommendations.map((product)=>'\\n            <div class=\"bg-white p-4 rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer\" onclick=\"window.location.href=\\'/products/'.concat(product.id, '\\'\">\\n              <img src=\"').concat(product.image, '\" alt=\"').concat(product.name, '\" class=\"w-full h-32 object-cover rounded mb-2\" loading=\"lazy\">\\n              <h4 class=\"font-medium text-sm mb-1 line-clamp-2\">').concat(product.name, '</h4>\\n              <div class=\"flex items-center mb-2\">\\n                ').concat(product.rating > 0 ? '\\n                  <div class=\"flex text-yellow-400 text-xs\">\\n                    '.concat(\"★\".repeat(Math.floor(product.rating))).concat(\"☆\".repeat(5 - Math.floor(product.rating)), '\\n                  </div>\\n                  <span class=\"text-xs text-gray-500 ml-1\">(').concat(product.rating_count, \")</span>\\n                \") : \"\", '\\n              </div>\\n              <div class=\"flex items-center justify-between\">\\n                <div>\\n                  ').concat(product.sale_price ? '\\n                    <span class=\"text-red-600 font-semibold\">\\xa3'.concat(product.sale_price, '</span>\\n                    <span class=\"text-gray-400 line-through text-sm ml-1\">\\xa3').concat(product.regular_price, \"</span>\\n                  \") : '\\n                    <span class=\"text-blue-600 font-semibold\">\\xa3'.concat(product.price, \"</span>\\n                  \"), '\\n                </div>\\n              </div>\\n              <button onclick=\"event.stopPropagation(); addToCartFromRecommendation(').concat(product.id, ')\" class=\"w-full mt-2 bg-blue-600 text-white py-1 px-2 rounded text-sm hover:bg-blue-700 transition-colors\">\\n                Add to Cart\\n              </button>\\n            </div>\\n          ')).join(\"\");\n                    console.log(\"✅ Loaded \".concat(recommendations.length, \" product recommendations\"));\n                }\n            } else {\n                console.log(\"No recommendations found, loading popular products\");\n                this.loadPopularProducts();\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading recommendations:\", error);\n            this.loadPopularProducts();\n        }\n    }\n    // Get current product ID from page\n    getCurrentProductId() {\n        // Try multiple methods to get product ID\n        const urlPath = window.location.pathname;\n        // Method 1: URL pattern /products/123\n        const productMatch = urlPath.match(/\\/products\\/(\\d+)/);\n        if (productMatch) return parseInt(productMatch[1]);\n        // Method 2: Data attribute on page\n        const productElement = document.querySelector(\"[data-product-id]\");\n        if (productElement) return parseInt(productElement.dataset.productId);\n        // Method 3: Meta tag\n        const metaProduct = document.querySelector('meta[name=\"product-id\"]');\n        if (metaProduct) return parseInt(metaProduct.content);\n        return null;\n    }\n    // Load popular products as fallback\n    async loadPopularProducts() {\n        try {\n            const response = await fetch(\"/api/sync-wordpress-products?action=get&limit=4&orderby=popularity\");\n            const data = await response.json();\n            if (data.success && data.products) {\n                const container = document.getElementById(\"recommendations-container\");\n                if (container) {\n                    container.innerHTML = data.products.map((product)=>{\n                        var _product_images_, _product_images;\n                        return '\\n            <div class=\"bg-white p-4 rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer\" onclick=\"window.location.href=\\'/products/'.concat(product.id, '\\'\">\\n              <img src=\"').concat(((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.src) || \"/placeholder.jpg\", '\" alt=\"').concat(product.name, '\" class=\"w-full h-32 object-cover rounded mb-2\" loading=\"lazy\">\\n              <h4 class=\"font-medium text-sm mb-1 line-clamp-2\">').concat(product.name, '</h4>\\n              <p class=\"text-blue-600 font-semibold\">\\xa3').concat(product.price, '</p>\\n              <button onclick=\"event.stopPropagation(); addToCartFromRecommendation(').concat(product.id, ')\" class=\"w-full mt-2 bg-blue-600 text-white py-1 px-2 rounded text-sm hover:bg-blue-700\">\\n                Add to Cart\\n              </button>\\n            </div>\\n          ');\n                    }).join(\"\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading popular products:\", error);\n        }\n    }\n    // Loyalty Program Feature with WooCommerce integration\n    async enableLoyaltyProgram() {\n        console.log(\"\\uD83C\\uDF81 Enabling Loyalty Program...\");\n        if (!this.currentUser) {\n            console.log(\"No user logged in, showing demo loyalty widget\");\n            this.showDemoLoyaltyWidget();\n            return;\n        }\n        try {\n            // Get real loyalty data from WooCommerce\n            const loyaltyData = await this.wooCommerce.getLoyaltyPoints(this.currentUser.id);\n            // Add loyalty points display with real data\n            const loyaltyWidget = document.createElement(\"div\");\n            loyaltyWidget.id = \"loyalty-widget\";\n            loyaltyWidget.innerHTML = '\\n        <div class=\"fixed top-20 right-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-lg shadow-lg z-40 max-w-xs\">\\n          <div class=\"text-center\">\\n            <div class=\"flex items-center justify-between mb-2\">\\n              <h4 class=\"font-semibold\">Your Points</h4>\\n              <span class=\"text-xs bg-white bg-opacity-20 px-2 py-1 rounded\">'.concat(loyaltyData.tier, '</span>\\n            </div>\\n            <p class=\"text-3xl font-bold\">').concat(loyaltyData.points.toLocaleString(), '</p>\\n            <p class=\"text-xs opacity-90\">\\xa3').concat((loyaltyData.points / 100).toFixed(2), ' value</p>\\n\\n            <div class=\"mt-3 text-xs\">\\n              <p>Total Spent: \\xa3').concat(loyaltyData.totalSpent.toFixed(2), \"</p>\\n              <p>Orders: \").concat(loyaltyData.orderCount, \"</p>\\n              \").concat(loyaltyData.nextTierThreshold ? '\\n                <p class=\"mt-1\">Next tier: \\xa3'.concat(loyaltyData.nextTierThreshold - loyaltyData.totalSpent, \" to go</p>\\n              \") : \"\", '\\n            </div>\\n\\n            <div class=\"mt-3 space-y-1\">\\n              <button onclick=\"showLoyaltyDetails()\" class=\"w-full bg-white text-purple-600 px-3 py-1 rounded text-sm font-medium hover:bg-opacity-90\">\\n                View Benefits\\n              </button>\\n              <button onclick=\"redeemPoints()\" class=\"w-full bg-white bg-opacity-20 text-white px-3 py-1 rounded text-sm hover:bg-opacity-30\">\\n                Redeem Points\\n              </button>\\n            </div>\\n\\n            <button onclick=\"closeLoyaltyWidget()\" class=\"absolute top-1 right-2 text-white opacity-70 hover:opacity-100\">\\xd7</button>\\n          </div>\\n        </div>\\n      ');\n            document.body.appendChild(loyaltyWidget);\n            // Add global functions for loyalty actions\n            window.showLoyaltyDetails = ()=>this.showLoyaltyDetails(loyaltyData);\n            window.redeemPoints = ()=>this.redeemPoints(loyaltyData);\n            window.closeLoyaltyWidget = ()=>{\n                var _document_getElementById;\n                return (_document_getElementById = document.getElementById(\"loyalty-widget\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.remove();\n            };\n            // Auto-hide after 10 seconds\n            setTimeout(()=>{\n                const widget = document.getElementById(\"loyalty-widget\");\n                if (widget) {\n                    widget.style.opacity = \"0.7\";\n                    widget.style.transform = \"scale(0.9)\";\n                }\n            }, 10000);\n            console.log(\"✅ Loyalty program loaded: \".concat(loyaltyData.points, \" points (\").concat(loyaltyData.tier, \" tier)\"));\n        } catch (error) {\n            console.error(\"❌ Error loading loyalty program:\", error);\n            this.showDemoLoyaltyWidget();\n        }\n    }\n    // Show demo loyalty widget for non-logged-in users\n    showDemoLoyaltyWidget() {\n        const loyaltyWidget = document.createElement(\"div\");\n        loyaltyWidget.id = \"loyalty-widget\";\n        loyaltyWidget.innerHTML = '\\n      <div class=\"fixed top-20 right-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-lg shadow-lg z-40\">\\n        <div class=\"text-center\">\\n          <h4 class=\"font-semibold\">Join Our Loyalty Program</h4>\\n          <p class=\"text-sm opacity-90 mt-1\">Earn points with every purchase!</p>\\n          <div class=\"mt-3\">\\n            <button onclick=\"window.location.href=\\'/login\\'\" class=\"bg-white text-purple-600 px-4 py-2 rounded text-sm font-medium\">\\n              Sign In to Earn Points\\n            </button>\\n          </div>\\n          <button onclick=\"document.getElementById(\\'loyalty-widget\\').remove()\" class=\"absolute top-1 right-2 text-white opacity-70 hover:opacity-100\">\\xd7</button>\\n        </div>\\n      </div>\\n    ';\n        document.body.appendChild(loyaltyWidget);\n    }\n    // Show loyalty details modal\n    showLoyaltyDetails(loyaltyData) {\n        const modal = document.createElement(\"div\");\n        modal.innerHTML = '\\n      <div class=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\\n        <div class=\"bg-white rounded-lg p-6 max-w-md w-full\">\\n          <h3 class=\"text-xl font-bold mb-4\">'.concat(loyaltyData.tier, ' Tier Benefits</h3>\\n          <ul class=\"space-y-2 mb-4\">\\n            ').concat(loyaltyData.benefits.map((benefit)=>'<li class=\"flex items-center\"><span class=\"text-green-500 mr-2\">✓</span>'.concat(benefit, \"</li>\")).join(\"\"), '\\n          </ul>\\n          <div class=\"bg-gray-50 p-3 rounded mb-4\">\\n            <p class=\"text-sm\"><strong>Your Stats:</strong></p>\\n            <p class=\"text-sm\">Points: ').concat(loyaltyData.points.toLocaleString(), '</p>\\n            <p class=\"text-sm\">Total Spent: \\xa3').concat(loyaltyData.totalSpent.toFixed(2), '</p>\\n            <p class=\"text-sm\">Orders: ').concat(loyaltyData.orderCount, '</p>\\n          </div>\\n          <button onclick=\"this.parentElement.parentElement.remove()\" class=\"w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700\">\\n            Close\\n          </button>\\n        </div>\\n      </div>\\n    ');\n        document.body.appendChild(modal);\n    }\n    // Redeem points\n    redeemPoints(loyaltyData) {\n        if (loyaltyData.points < 100) {\n            alert(\"You need at least 100 points to redeem (\\xa31.00 value)\");\n            return;\n        }\n        const redeemAmount = prompt(\"How many points would you like to redeem? (You have \".concat(loyaltyData.points, \" points)\"));\n        if (redeemAmount && parseInt(redeemAmount) <= loyaltyData.points) {\n            alert(\"Redeeming \".concat(redeemAmount, \" points for \\xa3\").concat((parseInt(redeemAmount) / 100).toFixed(2), \" discount!\"));\n        // Here you would integrate with your WooCommerce coupon system\n        }\n    }\n    // Push Notifications Feature\n    enablePushNotifications() {\n        console.log(\"\\uD83D\\uDD14 Enabling Push Notifications...\");\n        // Request notification permission\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission().then((permission)=>{\n                if (permission === \"granted\") {\n                    new Notification(\"Deal4u Notifications Enabled!\", {\n                        body: \"You'll now receive updates about deals and orders.\",\n                        icon: \"/favicon.ico\"\n                    });\n                }\n            });\n        }\n        // Add notification preferences\n        const notificationSettings = document.createElement(\"div\");\n        notificationSettings.innerHTML = '\\n      <div class=\"fixed bottom-20 right-4 bg-white p-4 rounded-lg shadow-lg border z-40\" id=\"notification-settings\">\\n        <h4 class=\"font-semibold mb-2\">Notification Preferences</h4>\\n        <div class=\"space-y-2\">\\n          <label class=\"flex items-center\">\\n            <input type=\"checkbox\" checked class=\"mr-2\">\\n            <span class=\"text-sm\">Order updates</span>\\n          </label>\\n          <label class=\"flex items-center\">\\n            <input type=\"checkbox\" checked class=\"mr-2\">\\n            <span class=\"text-sm\">Special offers</span>\\n          </label>\\n          <label class=\"flex items-center\">\\n            <input type=\"checkbox\" class=\"mr-2\">\\n            <span class=\"text-sm\">Back in stock alerts</span>\\n          </label>\\n        </div>\\n        <button onclick=\"this.parentElement.style.display=\\'none\\'\" class=\"mt-2 text-xs text-gray-500\">\\n          Close\\n        </button>\\n      </div>\\n    ';\n        document.body.appendChild(notificationSettings);\n        // Auto-hide after 5 seconds\n        setTimeout(()=>{\n            const settings = document.getElementById(\"notification-settings\");\n            if (settings) settings.style.display = \"none\";\n        }, 5000);\n    }\n    // Social Commerce Feature\n    enableSocialCommerce() {\n        console.log(\"\\uD83D\\uDCF1 Enabling Social Commerce...\");\n        // Add social sharing buttons to products\n        const products = document.querySelectorAll(\".product-item, .product-detail\");\n        products.forEach((product)=>{\n            const socialButtons = document.createElement(\"div\");\n            socialButtons.className = \"social-share mt-2\";\n            socialButtons.innerHTML = '\\n        <div class=\"flex space-x-2\">\\n          <button class=\"bg-blue-600 text-white px-2 py-1 rounded text-xs\" onclick=\"shareOnFacebook()\">\\n            \\uD83D\\uDCD8 Share\\n          </button>\\n          <button class=\"bg-pink-600 text-white px-2 py-1 rounded text-xs\" onclick=\"shareOnInstagram()\">\\n            \\uD83D\\uDCF7 Instagram\\n          </button>\\n          <button class=\"bg-blue-400 text-white px-2 py-1 rounded text-xs\" onclick=\"shareOnTwitter()\">\\n            \\uD83D\\uDC26 Tweet\\n          </button>\\n        </div>\\n      ';\n            product.appendChild(socialButtons);\n        });\n        // Add sharing functions to window\n        window.shareOnFacebook = ()=>{\n            window.open(\"https://www.facebook.com/sharer/sharer.php?u=\".concat(encodeURIComponent(window.location.href)), \"_blank\");\n        };\n        window.shareOnInstagram = ()=>{\n            alert(\"Copy this link to share on Instagram: \" + window.location.href);\n        };\n        window.shareOnTwitter = ()=>{\n            window.open(\"https://twitter.com/intent/tweet?url=\".concat(encodeURIComponent(window.location.href), \"&text=Check out this amazing product from Deal4u!\"), \"_blank\");\n        };\n    }\n    // One-Click Reorder Feature with WooCommerce integration\n    async enableOneClickReorder() {\n        console.log(\"⚡ Enabling One-Click Reorder...\");\n        if (!this.currentUser) {\n            console.log(\"No user logged in, showing demo reorder buttons\");\n            this.addDemoReorderButtons();\n            return;\n        }\n        try {\n            // Get reorderable orders from WooCommerce\n            const reorderableOrders = await this.wooCommerce.getReorderableOrders(this.currentUser.id, 10);\n            if (reorderableOrders.length === 0) {\n                console.log(\"No reorderable orders found\");\n                return;\n            }\n            // Add reorder buttons to existing order history elements\n            const orderHistory = document.querySelectorAll(\".order-item, .order-history-item, [data-order-id]\");\n            orderHistory.forEach((orderElement)=>{\n                var _orderElement_querySelector;\n                // Try to get order ID from element\n                const orderId = orderElement.dataset.orderId || ((_orderElement_querySelector = orderElement.querySelector(\"[data-order-id]\")) === null || _orderElement_querySelector === void 0 ? void 0 : _orderElement_querySelector.dataset.orderId) || this.extractOrderIdFromElement(orderElement);\n                if (orderId) {\n                    const orderData = reorderableOrders.find((order)=>order.id.toString() === orderId.toString());\n                    if (orderData) {\n                        this.addReorderButton(orderElement, orderData);\n                    }\n                }\n            });\n            // Create a reorder section if no order history elements found\n            if (orderHistory.length === 0) {\n                this.createReorderSection(reorderableOrders);\n            }\n            console.log(\"✅ One-click reorder enabled for \".concat(reorderableOrders.length, \" orders\"));\n        } catch (error) {\n            console.error(\"❌ Error enabling one-click reorder:\", error);\n            this.addDemoReorderButtons();\n        }\n    }\n    // Add reorder button to order element\n    addReorderButton(orderElement, orderData) {\n        // Check if button already exists\n        if (orderElement.querySelector(\".reorder-button\")) return;\n        const reorderButton = document.createElement(\"button\");\n        reorderButton.className = \"reorder-button bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 ml-2 transition-colors\";\n        reorderButton.innerHTML = \"⚡ Reorder\";\n        reorderButton.title = \"Reorder \".concat(orderData.itemCount, \" items from this order\");\n        reorderButton.onclick = (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            this.handleReorder(orderData);\n        };\n        // Find the best place to insert the button\n        const buttonContainer = orderElement.querySelector(\".order-actions, .order-buttons\") || orderElement;\n        buttonContainer.appendChild(reorderButton);\n    }\n    // Handle reorder action\n    async handleReorder(orderData) {\n        try {\n            console.log(\"\\uD83D\\uDED2 Reordering \".concat(orderData.items.length, \" items from order #\").concat(orderData.id, \"...\"));\n            // Show loading state\n            const button = event.target;\n            const originalText = button.innerHTML;\n            button.innerHTML = \"⏳ Adding...\";\n            button.disabled = true;\n            let addedItems = 0;\n            let failedItems = 0;\n            // Add each item to cart\n            for (const item of orderData.items){\n                try {\n                    const result = await this.wooCommerce.addToCart(item.product_id, item.quantity, item.variation_id);\n                    if (result.success) {\n                        addedItems++;\n                    } else {\n                        failedItems++;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to add item \".concat(item.name, \":\"), error);\n                    failedItems++;\n                }\n            }\n            // Show result\n            if (addedItems > 0) {\n                const message = failedItems > 0 ? \"Added \".concat(addedItems, \" items to cart. \").concat(failedItems, \" items were unavailable.\") : \"All \".concat(addedItems, \" items added to cart successfully!\");\n                this.showReorderSuccess(message, addedItems);\n                // Update cart UI\n                this.updateCartCount(addedItems);\n            } else {\n                alert(\"Sorry, none of the items from this order are currently available.\");\n            }\n            // Restore button\n            button.innerHTML = originalText;\n            button.disabled = false;\n        } catch (error) {\n            console.error(\"❌ Error during reorder:\", error);\n            alert(\"Sorry, there was an error processing your reorder. Please try again.\");\n            // Restore button\n            event.target.innerHTML = \"⚡ Reorder\";\n            event.target.disabled = false;\n        }\n    }\n    // Show reorder success message\n    showReorderSuccess(message, itemCount) {\n        const notification = document.createElement(\"div\");\n        notification.className = \"fixed top-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm\";\n        notification.innerHTML = '\\n      <div class=\"flex items-start\">\\n        <div class=\"flex-shrink-0\">\\n          <span class=\"text-2xl\">✅</span>\\n        </div>\\n        <div class=\"ml-3\">\\n          <p class=\"font-medium\">'.concat(message, '</p>\\n          <div class=\"mt-2 space-x-2\">\\n            <button onclick=\"window.location.href=\\'/cart\\'\" class=\"bg-white text-green-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100\">\\n              View Cart\\n            </button>\\n            <button onclick=\"this.parentElement.parentElement.parentElement.remove()\" class=\"text-green-100 hover:text-white text-sm\">\\n              Dismiss\\n            </button>\\n          </div>\\n        </div>\\n      </div>\\n    ');\n        document.body.appendChild(notification);\n        // Auto-remove after 5 seconds\n        setTimeout(()=>{\n            notification.remove();\n        }, 5000);\n    }\n    // Update cart count in header\n    updateCartCount(addedItems) {\n        const cartCountElements = document.querySelectorAll(\".cart-count, [data-cart-count]\");\n        cartCountElements.forEach((element)=>{\n            const currentCount = parseInt(element.textContent || \"0\");\n            element.textContent = currentCount + addedItems;\n        });\n    }\n    // Extract order ID from element\n    extractOrderIdFromElement(element) {\n        var _element_querySelector;\n        // Try various methods to extract order ID\n        const text = element.textContent || \"\";\n        const orderMatch = text.match(/#(\\d+)/);\n        if (orderMatch) return orderMatch[1];\n        const href = ((_element_querySelector = element.querySelector(\"a\")) === null || _element_querySelector === void 0 ? void 0 : _element_querySelector.href) || \"\";\n        const urlMatch = href.match(/order[s]?\\/(\\d+)/);\n        if (urlMatch) return urlMatch[1];\n        return null;\n    }\n    // Add demo reorder buttons for non-logged-in users\n    addDemoReorderButtons() {\n        const orderHistory = document.querySelectorAll(\".order-item, .order-history-item\");\n        orderHistory.forEach((order)=>{\n            const reorderButton = document.createElement(\"button\");\n            reorderButton.className = \"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 ml-2\";\n            reorderButton.textContent = \"⚡ Reorder\";\n            reorderButton.onclick = ()=>{\n                alert(\"Please log in to use the one-click reorder feature!\");\n            };\n            order.appendChild(reorderButton);\n        });\n    }\n    // Create reorder section if no order history found\n    createReorderSection(reorderableOrders) {\n        const reorderSection = document.createElement(\"div\");\n        reorderSection.className = \"fixed bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm z-40\";\n        reorderSection.innerHTML = '\\n      <h4 class=\"font-semibold mb-2\">Quick Reorder</h4>\\n      <p class=\"text-sm text-gray-600 mb-3\">Reorder from your recent purchases</p>\\n      <div class=\"space-y-2 max-h-40 overflow-y-auto\">\\n        '.concat(reorderableOrders.slice(0, 3).map((order)=>'\\n          <div class=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\\n            <div class=\"flex-1\">\\n              <p class=\"text-sm font-medium\">Order #'.concat(order.id, '</p>\\n              <p class=\"text-xs text-gray-500\">').concat(order.itemCount, \" items - \\xa3\").concat(order.total, '</p>\\n            </div>\\n            <button onclick=\"reorderFromSection(').concat(order.id, ')\" class=\"bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700\">\\n              Reorder\\n            </button>\\n          </div>\\n        ')).join(\"\"), '\\n      </div>\\n      <button onclick=\"this.remove()\" class=\"absolute top-1 right-2 text-gray-400 hover:text-gray-600\">\\xd7</button>\\n    ');\n        document.body.appendChild(reorderSection);\n        // Add global reorder function\n        window.reorderFromSection = (orderId)=>{\n            const orderData = reorderableOrders.find((order)=>order.id === orderId);\n            if (orderData) {\n                this.handleReorder(orderData);\n            }\n        };\n        // Auto-hide after 10 seconds\n        setTimeout(()=>{\n            reorderSection.style.opacity = \"0.8\";\n        }, 10000);\n    }\n    // Voice Search Feature\n    enableVoiceSearch() {\n        console.log(\"\\uD83C\\uDFA4 Enabling Voice Search...\");\n        // Add voice search button to search bars\n        const searchInputs = document.querySelectorAll('input[type=\"search\"], .search-input');\n        searchInputs.forEach((input)=>{\n            const voiceButton = document.createElement(\"button\");\n            voiceButton.className = \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600\";\n            voiceButton.innerHTML = \"\\uD83C\\uDFA4\";\n            voiceButton.onclick = ()=>this.startVoiceSearch(input);\n            // Make parent relative if not already\n            if (input.parentElement.style.position !== \"relative\") {\n                input.parentElement.style.position = \"relative\";\n            }\n            input.parentElement.appendChild(voiceButton);\n        });\n    }\n    // Start voice search\n    startVoiceSearch(input) {\n        if (\"webkitSpeechRecognition\" in window) {\n            const recognition = new webkitSpeechRecognition();\n            recognition.continuous = false;\n            recognition.interimResults = false;\n            recognition.lang = \"en-GB\";\n            recognition.onstart = ()=>{\n                input.placeholder = \"Listening...\";\n            };\n            recognition.onresult = (event1)=>{\n                const transcript = event1.results[0][0].transcript;\n                input.value = transcript;\n                input.placeholder = \"Search products...\";\n                // Trigger search\n                const searchEvent = new Event(\"input\", {\n                    bubbles: true\n                });\n                input.dispatchEvent(searchEvent);\n            };\n            recognition.onerror = ()=>{\n                input.placeholder = \"Voice search failed. Try again.\";\n            };\n            recognition.start();\n        } else {\n            alert(\"Voice search not supported in this browser\");\n        }\n    }\n    // AR Try-On Feature\n    enableARTryOn() {\n        console.log(\"\\uD83D\\uDCF7 Enabling AR Try-On...\");\n        // Add AR buttons to clothing/accessory products\n        const products = document.querySelectorAll(\".product-item, .product-detail\");\n        products.forEach((product)=>{\n            var _product_querySelector_textContent, _product_querySelector;\n            // Check if product is clothing/accessory (you can improve this logic)\n            const productName = ((_product_querySelector = product.querySelector(\"h1, h2, h3, .product-title\")) === null || _product_querySelector === void 0 ? void 0 : (_product_querySelector_textContent = _product_querySelector.textContent) === null || _product_querySelector_textContent === void 0 ? void 0 : _product_querySelector_textContent.toLowerCase()) || \"\";\n            if (productName.includes(\"dress\") || productName.includes(\"shirt\") || productName.includes(\"watch\") || productName.includes(\"glasses\")) {\n                const arButton = document.createElement(\"button\");\n                arButton.className = \"bg-purple-600 text-white px-4 py-2 rounded mt-2 hover:bg-purple-700\";\n                arButton.innerHTML = \"\\uD83D\\uDCF7 Try On with AR\";\n                arButton.onclick = ()=>{\n                    alert(\"AR Try-On feature coming soon! This will open your camera for virtual try-on.\");\n                };\n                product.appendChild(arButton);\n            }\n        });\n    }\n    // Update feature status\n    async updateFeature(featureName, enabled) {\n        let settings = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        try {\n            const response = await fetch(\"/api/admin/toggle-feature\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    feature: featureName,\n                    enabled,\n                    settings\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                this.features[featureName] = data.feature;\n                // Re-apply features\n                this.applyFeatures();\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Error updating feature:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.features = {};\n        this.initialized = false;\n        this.wooCommerce = null;\n        this.currentUser = null;\n    }\n}\n// Create global instance\nconst featuresManager = new FeaturesManager();\n// Initialize when DOM is ready\nif (true) {\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>featuresManager.initialize());\n    } else {\n        featuresManager.initialize();\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (featuresManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/featuresManager.js\n"));

/***/ })

}]);