const { wooCommerceApi } = require('./lib/woocommerce');

async function testProductsList() {
  console.log('🧪 Testing Products List API Call...\n');
  
  try {
    console.log('1. Testing products list...');
    const products = await wooCommerceApi.getProducts({ per_page: 5 });
    
    if (products && products.length > 0) {
      console.log('✅ Products list loaded successfully!');
      console.log(`   Found: ${products.length} products`);
      
      products.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} (ID: ${product.id})`);
      });
      
      // Test individual product access
      console.log('\n2. Testing individual product access...');
      const firstProductId = products[0].id;
      console.log(`   Trying to fetch product ID: ${firstProductId}`);
      
      try {
        const singleProduct = await wooCommerceApi.getProduct(firstProductId);
        if (singleProduct) {
          console.log('✅ Individual product access working!');
          console.log(`   Product: ${singleProduct.name}`);
        } else {
          console.log('❌ Individual product returned null');
        }
      } catch (error) {
        console.log('❌ Individual product access failed:');
        console.log(`   Error: ${error.message}`);
      }
      
    } else {
      console.log('❌ No products found or returned empty array');
    }
    
  } catch (error) {
    console.log('❌ Error testing products list API:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

testProductsList();
