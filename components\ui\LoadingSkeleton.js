const LoadingSkeleton = ({ className = '', children, ...props }) => {
  return (
    <div 
      className={`animate-pulse bg-gray-200 rounded ${className}`} 
      {...props}
    >
      {children}
    </div>
  );
};

// Product Card Skeleton
export const ProductCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-md overflow-hidden">
    <LoadingSkeleton className="h-48 w-full" />
    <div className="p-4 space-y-3">
      <LoadingSkeleton className="h-4 w-full" />
      <LoadingSkeleton className="h-4 w-3/4" />
      <div className="flex items-center space-x-1">
        <LoadingSkeleton className="h-4 w-20" />
        <LoadingSkeleton className="h-4 w-16" />
      </div>
      <LoadingSkeleton className="h-6 w-1/2" />
      <LoadingSkeleton className="h-10 w-full" />
    </div>
  </div>
);

// Product Grid Skeleton
export const ProductGridSkeleton = ({ count = 8 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {[...Array(count)].map((_, i) => (
      <ProductCardSkeleton key={i} />
    ))}
  </div>
);

// Product Detail Skeleton
export const ProductDetailSkeleton = () => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
    {/* Image Section */}
    <div>
      <LoadingSkeleton className="aspect-square w-full mb-4" />
      <div className="flex space-x-2">
        {[...Array(4)].map((_, i) => (
          <LoadingSkeleton key={i} className="w-20 h-20" />
        ))}
      </div>
    </div>
    
    {/* Product Info */}
    <div className="space-y-6">
      <LoadingSkeleton className="h-8 w-3/4" />
      <div className="flex items-center space-x-2">
        <LoadingSkeleton className="h-4 w-24" />
        <LoadingSkeleton className="h-4 w-20" />
      </div>
      <LoadingSkeleton className="h-10 w-1/2" />
      <LoadingSkeleton className="h-6 w-20" />
      <LoadingSkeleton className="h-20 w-full" />
      <div className="flex space-x-4">
        <LoadingSkeleton className="h-12 w-32" />
        <LoadingSkeleton className="h-12 flex-1" />
      </div>
    </div>
  </div>
);

// Cart Item Skeleton
export const CartItemSkeleton = () => (
  <div className="p-6 flex space-x-4">
    <LoadingSkeleton className="w-24 h-24" />
    <div className="flex-1 space-y-3">
      <LoadingSkeleton className="h-5 w-3/4" />
      <LoadingSkeleton className="h-4 w-1/2" />
      <div className="flex items-center justify-between">
        <LoadingSkeleton className="h-8 w-24" />
        <LoadingSkeleton className="h-6 w-16" />
      </div>
    </div>
  </div>
);

// Header Skeleton
export const HeaderSkeleton = () => (
  <header className="bg-white shadow-md">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center h-16">
        <LoadingSkeleton className="h-8 w-24" />
        <LoadingSkeleton className="h-8 w-96 hidden md:block" />
        <div className="flex items-center space-x-4">
          <LoadingSkeleton className="h-8 w-8 rounded-full" />
          <LoadingSkeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>
    </div>
  </header>
);

// Filter Skeleton
export const FilterSkeleton = () => (
  <div className="bg-white p-6 rounded-lg shadow-md space-y-6">
    <LoadingSkeleton className="h-6 w-32" />
    <div className="space-y-3">
      {[...Array(5)].map((_, i) => (
        <LoadingSkeleton key={i} className="h-4 w-full" />
      ))}
    </div>
    <LoadingSkeleton className="h-6 w-28" />
    <LoadingSkeleton className="h-20 w-full" />
    <LoadingSkeleton className="h-6 w-24" />
    <div className="space-y-2">
      {[...Array(4)].map((_, i) => (
        <LoadingSkeleton key={i} className="h-4 w-3/4" />
      ))}
    </div>
  </div>
);

// Order Item Skeleton
export const OrderItemSkeleton = () => (
  <div className="border border-gray-200 rounded-lg p-6">
    <div className="flex items-center justify-between mb-4">
      <div className="space-y-2">
        <LoadingSkeleton className="h-5 w-32" />
        <LoadingSkeleton className="h-4 w-24" />
      </div>
      <LoadingSkeleton className="h-6 w-20" />
    </div>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <LoadingSkeleton className="h-4 w-full" />
      <LoadingSkeleton className="h-4 w-full" />
      <LoadingSkeleton className="h-4 w-full" />
    </div>
    <div className="flex justify-end mt-4 space-x-2">
      <LoadingSkeleton className="h-8 w-24" />
      <LoadingSkeleton className="h-8 w-20" />
    </div>
  </div>
);

// Page Loading Skeleton
export const PageLoadingSkeleton = () => (
  <div className="min-h-screen bg-gray-50">
    <HeaderSkeleton />
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <LoadingSkeleton className="h-8 w-64 mb-6" />
      <LoadingSkeleton className="h-4 w-48 mb-8" />
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div className="lg:col-span-1">
          <FilterSkeleton />
        </div>
        <div className="lg:col-span-3">
          <ProductGridSkeleton />
        </div>
      </div>
    </div>
  </div>
);

// Text Skeleton
export const TextSkeleton = ({ lines = 3, className = '' }) => (
  <div className={`space-y-2 ${className}`}>
    {[...Array(lines)].map((_, i) => (
      <LoadingSkeleton 
        key={i} 
        className={`h-4 ${
          i === lines - 1 ? 'w-3/4' : 'w-full'
        }`} 
      />
    ))}
  </div>
);

// Avatar Skeleton
export const AvatarSkeleton = ({ size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };
  
  return (
    <LoadingSkeleton className={`${sizeClasses[size]} rounded-full`} />
  );
};

// Button Skeleton
export const ButtonSkeleton = ({ className = '' }) => (
  <LoadingSkeleton className={`h-10 w-24 rounded-md ${className}`} />
);

// Card Skeleton
export const CardSkeleton = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
    {children}
  </div>
);

// Table Row Skeleton
export const TableRowSkeleton = ({ columns = 4 }) => (
  <tr>
    {[...Array(columns)].map((_, i) => (
      <td key={i} className="px-6 py-4">
        <LoadingSkeleton className="h-4 w-full" />
      </td>
    ))}
  </tr>
);

// List Item Skeleton
export const ListItemSkeleton = () => (
  <div className="flex items-center space-x-4 py-3">
    <AvatarSkeleton size="sm" />
    <div className="flex-1 space-y-2">
      <LoadingSkeleton className="h-4 w-3/4" />
      <LoadingSkeleton className="h-3 w-1/2" />
    </div>
    <LoadingSkeleton className="h-4 w-16" />
  </div>
);

// Form Skeleton
export const FormSkeleton = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <LoadingSkeleton className="h-4 w-20" />
        <LoadingSkeleton className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <LoadingSkeleton className="h-4 w-20" />
        <LoadingSkeleton className="h-10 w-full" />
      </div>
    </div>
    <div className="space-y-2">
      <LoadingSkeleton className="h-4 w-24" />
      <LoadingSkeleton className="h-20 w-full" />
    </div>
    <LoadingSkeleton className="h-10 w-32" />
  </div>
);

export default LoadingSkeleton;