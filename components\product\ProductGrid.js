'use client';

import { useState } from 'react';
import ProductCard from './ProductCard';
import { Grid, List } from 'lucide-react';

const ProductGrid = ({ products = [] }) => {
  const [viewMode, setViewMode] = useState('grid'); // Default to grid for modern look

  if (!products || products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Grid className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600">Try adjusting your filters or check back later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with View Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Products ({products.length})
          </h2>
          <p className="text-gray-600 mt-1">
            Discover amazing deals and quality products
          </p>
        </div>

        {/* Modern View Mode Toggle */}
        <div className="inline-flex rounded-xl bg-gray-100 p-1 shadow-sm">
          <button
            onClick={() => setViewMode('grid')}
            className={`px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 ${
              viewMode === 'grid'
                ? 'bg-white text-blue-600 shadow-sm font-medium'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            aria-label="Grid view"
          >
            <Grid className="w-4 h-4" />
            <span className="text-sm">Grid</span>
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 ${
              viewMode === 'list'
                ? 'bg-white text-blue-600 shadow-sm font-medium'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            aria-label="List view"
          >
            <List className="w-4 h-4" />
            <span className="text-sm">List</span>
          </button>
        </div>
      </div>

      {/* Modern Products Display */}
      <div
        className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }
      >
        {products.map((product, index) => (
          <div
            key={product.id}
            className="animate-fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <ProductCard
              product={product}
              viewMode={viewMode}
            />
          </div>
        ))}
      </div>

      {/* Load More Button (if needed) */}
      {products.length >= 20 && (
        <div className="text-center pt-8">
          <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
            Load More Products
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductGrid;