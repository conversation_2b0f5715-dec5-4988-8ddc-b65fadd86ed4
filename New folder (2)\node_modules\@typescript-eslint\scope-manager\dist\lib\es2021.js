"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2021 = void 0;
const es2020_1 = require("./es2020");
const es2021_intl_1 = require("./es2021.intl");
const es2021_promise_1 = require("./es2021.promise");
const es2021_string_1 = require("./es2021.string");
const es2021_weakref_1 = require("./es2021.weakref");
exports.es2021 = {
    ...es2020_1.es2020,
    ...es2021_promise_1.es2021_promise,
    ...es2021_string_1.es2021_string,
    ...es2021_weakref_1.es2021_weakref,
    ...es2021_intl_1.es2021_intl,
};
//# sourceMappingURL=es2021.js.map