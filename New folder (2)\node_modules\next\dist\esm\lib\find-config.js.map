{"version": 3, "sources": ["../../src/lib/find-config.ts"], "names": ["findUp", "readFile", "JSON5", "pathToFileURL", "findConfigPath", "dir", "key", "cwd", "findConfig", "directory", "_returnFile", "packageJsonPath", "isESM", "packageJsonStr", "packageJson", "JSON", "parse", "Error", "type", "filePath", "esmImport", "path", "process", "platform", "env", "JEST_WORKER_ID", "toString", "endsWith", "default", "require", "fileContents"], "mappings": "AAAA,OAAOA,YAAY,6BAA4B;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,aAAa,QAAQ,MAAK;AAMnC,OAAO,SAASC,eACdC,GAAW,EACXC,GAAW;IAEX,4EAA4E;IAC5E,mBAAmB;IACnB,OAAON,OACL;QACE,CAAC,CAAC,EAAEM,IAAI,OAAO,CAAC;QAChB,CAAC,EAAEA,IAAI,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,IAAI,KAAK,CAAC;QACd,CAAC,EAAEA,IAAI,UAAU,CAAC;QAClB,CAAC,EAAEA,IAAI,WAAW,CAAC;QACnB,CAAC,EAAEA,IAAI,WAAW,CAAC;KACpB,EACD;QACEC,KAAKF;IACP;AAEJ;AAEA,6EAA6E;AAC7E,4EAA4E;AAC5E,+CAA+C;AAC/C,OAAO,eAAeG,WACpBC,SAAiB,EACjBH,GAAW,EACXI,WAAqB;IAErB,oEAAoE;IACpE,MAAMC,kBAAkB,MAAMX,OAAO,gBAAgB;QAAEO,KAAKE;IAAU;IACtE,IAAIG,QAAQ;IAEZ,IAAID,iBAAiB;QACnB,IAAI;YACF,MAAME,iBAAiB,MAAMZ,SAASU,iBAAiB;YACvD,MAAMG,cAAcC,KAAKC,KAAK,CAACH;YAI/B,IAAI,OAAOC,gBAAgB,UAAU;gBACnC,MAAM,IAAIG,QAAQ,+BAA+B;;YACnD;YAEA,IAAIH,YAAYI,IAAI,KAAK,UAAU;gBACjCN,QAAQ;YACV;YAEA,IAAIE,WAAW,CAACR,IAAI,IAAI,QAAQ,OAAOQ,WAAW,CAACR,IAAI,KAAK,UAAU;gBACpE,OAAOQ,WAAW,CAACR,IAAI;YACzB;QACF,EAAE,OAAM;QACN,4BAA4B;QAC9B;IACF;IAEA,MAAMa,WAAW,MAAMf,eAAeK,WAAWH;IAEjD,MAAMc,YAAY,CAACC;QACjB,0EAA0E;QAC1E,qEAAqE;QACrE,IAAIC,QAAQC,QAAQ,KAAK,WAAW,CAACD,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC/D,sEAAsE;YACtE,mBAAmB;YACnB,OAAO,MAAM,CAACtB,cAAckB,MAAMK,QAAQ;QAC5C,OAAO;YACL,OAAO,MAAM,CAACL;QAChB;IACF;IAEA,IAAIF,UAAU;QACZ,IAAIA,SAASQ,QAAQ,CAAC,QAAQ;YAC5B,IAAIf,OAAO;gBACT,OAAO,AAAC,CAAA,MAAMQ,UAAUD,SAAQ,EAAGS,OAAO;YAC5C,OAAO;gBACL,OAAOC,QAAQV;YACjB;QACF,OAAO,IAAIA,SAASQ,QAAQ,CAAC,SAAS;YACpC,OAAO,AAAC,CAAA,MAAMP,UAAUD,SAAQ,EAAGS,OAAO;QAC5C,OAAO,IAAIT,SAASQ,QAAQ,CAAC,SAAS;YACpC,OAAOE,QAAQV;QACjB;QAEA,sEAAsE;QACtE,kEAAkE;QAClE,MAAMW,eAAe,MAAM7B,SAASkB,UAAU;QAC9C,OAAOjB,MAAMc,KAAK,CAACc;IACrB;IAEA,OAAO;AACT"}