'use client';

export default function MasterControlPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                🎛️ Deal4U Master Control Panel
              </h1>
              <p className="text-gray-600">
                All your admin features in one place - Import, Sync, Categorize & Manage
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                🏠 View Store
              </a>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* WordPress Sync */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-orange-900 mb-3">🚀 Fast WordPress Sync</h2>
            <p className="text-orange-700 mb-4">
              Ultra-fast sync: Publish all WordPress products in seconds with real images
            </p>
            <button
              onClick={async () => {
                if (confirm('🚀 FAST SYNC: Publish all your WordPress products in seconds?')) {
                  try {
                    const startTime = Date.now();
                    const response = await fetch('/api/fast-sync', { method: 'POST' });
                    const data = await response.json();
                    const elapsed = Date.now() - startTime;

                    if (data.success) {
                      alert(`🎉 ULTRA-FAST SYNC COMPLETE!\n\n✅ ${data.published} products published\n⚡ Completed in ${data.timeElapsed}\n🚀 Speed: ${data.speed}\n\nYour products are now live on the website!`);
                    } else {
                      alert('❌ Error: ' + data.error);
                    }
                  } catch (error) {
                    alert('❌ Error: ' + error.message);
                  }
                }
              }}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              🚀 FAST SYNC (Seconds!)
            </button>
          </div>

          {/* Manual Import Note */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 mb-3">📦 Product Import</h2>
            <p className="text-blue-700 mb-4">
              Import products manually using your AliDrop plugin, then use Fast Sync to optimize them
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">📋 Manual Import Process:</h3>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. Use your AliDrop plugin to import products</li>
                <li>2. Click "🚀 FAST SYNC" to optimize images and publish</li>
                <li>3. Products will appear in your shop with clean data</li>
              </ol>
            </div>
          </div>

          {/* Smart Categories */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-purple-900 mb-3">🧠 Smart Categories</h2>
            <p className="text-purple-700 mb-4">
              AI-powered product categorization
            </p>
            <button
              onClick={() => alert('Smart categories feature - coming soon!')}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              🧠 Analyze Categories
            </button>
          </div>
        </div>

        {/* Status */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">34</div>
              <div className="text-sm text-gray-600">WordPress Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">0</div>
              <div className="text-sm text-gray-600">Website Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">Stopped</div>
              <div className="text-sm text-gray-600">Auto-Sync Status</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
