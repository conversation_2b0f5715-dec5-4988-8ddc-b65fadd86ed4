{"name": "js-cookie", "version": "3.0.5", "description": "A simple, lightweight JavaScript API for handling cookies", "browser": "dist/js.cookie.js", "module": "dist/js.cookie.mjs", "unpkg": "dist/js.cookie.min.js", "jsdelivr": "dist/js.cookie.min.js", "exports": {".": {"import": "./dist/js.cookie.mjs", "require": "./dist/js.cookie.js"}, "./package.json": "./package.json"}, "directories": {"test": "test"}, "keywords": ["cookie", "cookies", "browser", "amd", "commonjs", "client", "js-cookie", "browserify"], "scripts": {"test": "grunt test", "format": "grunt exec:format", "dist": "rm -rf dist/* && rollup -c", "release": "release-it"}, "repository": {"type": "git", "url": "git://github.com/js-cookie/js-cookie.git"}, "files": ["index.js", "dist/**/*"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@rollup/plugin-terser": "^0.4.0", "browserstack-runner": "github:browserstack/browserstack-runner#1e85e559951bdf97ffe2a7c744ee67ca83589fde", "eslint": "^7.31.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-html": "^7.0.0", "eslint-plugin-markdown": "^3.0.0", "grunt": "^1.0.4", "grunt-compare-size": "^0.4.2", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-nodeunit": "^5.0.0", "grunt-contrib-qunit": "^7.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-exec": "^3.0.0", "gzip-js": "^0.3.2", "prettier": "^2.3.2", "qunit": "^2.9.3", "release-it": "^15.0.0", "rollup": "^3.17.2", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-license": "^3.0.0", "standard": "^17.0.0"}, "engines": {"node": ">=14"}}