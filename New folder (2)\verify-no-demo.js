#!/usr/bin/env node

/**
 * Demo Data Removal Verification Script
 * Verifies that all demo/mock data has been removed from "New folder (2)"
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Demo Data Removal Verification - New folder (2)');
console.log('==================================================\n');

// Files to check for demo data
const filesToCheck = [
  'lib/woocommerce.js',
  'app/shop/page.js',
  'components/product/ProductCard.js',
  'components/product/ProductDetail.js'
];

// Demo data patterns to search for
const demoPatterns = [
  'mockProducts',
  'mockCategories',
  'Premium Wireless Headphones',
  'Smart Fitness Watch',
  'Ergonomic Office Chair',
  'LED Desk Lamp',
  'Wireless Phone Charger',
  'Bluetooth Speaker',
  'using demo products',
  'demo mode',
  'fallback to demo',
  'mock data'
];

let foundDemoData = false;
let checkedFiles = 0;

console.log('📋 Checking files for demo data...\n');

filesToCheck.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  if (fs.existsSync(fullPath)) {
    checkedFiles++;
    console.log(`📄 Checking: ${filePath}`);
    
    const content = fs.readFileSync(fullPath, 'utf8');
    const foundPatterns = [];
    
    demoPatterns.forEach(pattern => {
      if (content.toLowerCase().includes(pattern.toLowerCase())) {
        foundPatterns.push(pattern);
        foundDemoData = true;
      }
    });
    
    if (foundPatterns.length > 0) {
      console.log(`   ❌ Found demo data: ${foundPatterns.join(', ')}`);
    } else {
      console.log(`   ✅ Clean - no demo data found`);
    }
  } else {
    console.log(`   ⚠️ File not found: ${filePath}`);
  }
});

console.log('\n' + '='.repeat(50));

if (foundDemoData) {
  console.log('❌ DEMO DATA STILL PRESENT');
  console.log('Some files still contain demo/mock data.');
  console.log('Please review the files marked above.');
} else {
  console.log('✅ DEMO DATA SUCCESSFULLY REMOVED');
  console.log('All demo/mock data has been removed from the codebase.');
  console.log('\n🎉 Your website will now only show:');
  console.log('   • Real products from your WooCommerce store');
  console.log('   • Actual categories from WooCommerce');
  console.log('   • Live product data (names, prices, descriptions)');
  console.log('   • Real-time stock status');
  console.log('\n💡 If WooCommerce is not configured:');
  console.log('   • Shop page will show "No Products Available"');
  console.log('   • Users will be directed to contact support');
  console.log('   • No fake/demo products will be displayed');
}

console.log(`\n📊 Summary:`);
console.log(`   Files checked: ${checkedFiles}`);
console.log(`   Demo patterns searched: ${demoPatterns.length}`);
console.log(`   Demo data found: ${foundDemoData ? 'Yes' : 'No'}`);

console.log('\n🚀 Next Steps:');
console.log('   1. Ensure your WooCommerce store has products');
console.log('   2. Verify .env.local has correct API credentials');
console.log('   3. Run: npm run dev');
console.log('   4. Visit: http://localhost:3000/shop');
console.log('   5. Confirm only real WooCommerce products appear');

console.log('\n📞 Support:');
console.log('   Email: <EMAIL>');
console.log('   Phone: +44 7447 186806');
