import { NextResponse } from 'next/server';

// Import the jobs storage
let importJobs = new Map();

export async function POST(request) {
  try {
    const { jobId } = await request.json();
    
    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'Job ID is required'
      }, { status: 400 });
    }

    const job = importJobs.get(jobId);
    
    if (!job) {
      return NextResponse.json({
        success: false,
        error: 'Job not found'
      }, { status: 404 });
    }

    // Stop the job
    job.status = 'stopped';
    job.endTime = new Date().toISOString();
    
    console.log(`⏹️ Import job ${jobId} stopped by user`);

    return NextResponse.json({
      success: true,
      message: `Import job ${jobId} stopped successfully`,
      job: job
    });

  } catch (error) {
    console.error('❌ Error stopping import job:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Export the jobs Map
export { importJobs };
