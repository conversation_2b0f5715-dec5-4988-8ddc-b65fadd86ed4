'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Star } from 'lucide-react';

export default function HeroSection() {
  return (
    <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
      {/* NEW SALE RIBBON - Prominent at the top */}
      <div className="absolute top-0 left-0 right-0 bg-yellow-400 text-purple-900 py-2 px-4 text-center font-bold transform -skew-y-2 shadow-lg z-30">
        <div className="container mx-auto flex items-center justify-center gap-4">
          <span className="animate-pulse">🔥</span>
          <span className="text-lg uppercase tracking-wider">SUMMER SALE: UP TO 50% OFF EVERYTHING</span>
          <span className="animate-pulse">🔥</span>
        </div>
      </div>
      
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{ 
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '30px 30px'
        }}></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
        {/* Promotional Banner - Floating */}
        <div className="absolute top-4 right-4 md:top-8 md:right-8 animate-bounce-slow">
          <div className="relative w-24 h-24 md:w-32 md:h-32 bg-yellow-400 rounded-full flex items-center justify-center transform rotate-12 shadow-xl">
            {/* Removed dashed border from here */}
            <div className="text-center px-2">
              <p className="text-xl md:text-2xl font-black text-purple-900 leading-none">50%</p>
              <p className="text-sm md:text-base font-bold text-purple-900 leading-none">OFF</p>
              <p className="text-[10px] md:text-xs mt-1 font-semibold text-purple-800">SUMMER50</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3 text-center md:text-left">
            <div className="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-6 animate-pulse">
              <span className="text-xs font-semibold tracking-wider">🔥 SUMMER SALE NOW LIVE 🔥</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Premium Products,
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                Amazing Deals
              </span>
            </h1>
            <p className="text-xl mb-8 max-w-2xl">
              Discover thousands of high-quality products at unbeatable prices. Fast shipping, competitive deals, and exceptional customer service - that's the Deal4u promise.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-8 md:mb-0">
              <Link
                href="/shop?sale=true"
                className="bg-yellow-400 text-purple-900 px-8 py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <span>Shop Summer Sale</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/shop"
                className="bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-700 transition-colors border border-white border-opacity-40"
              >
                Browse All
              </Link>
            </div>
          </div>
          
          {/* Image/Visual Element */}
          <div className="md:w-1/3 hidden md:block relative">
            <div className="relative z-0 mt-8">
              {/* Removed the dashed border at user's request */}
              <div className="absolute -inset-4 bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 rounded-full opacity-50 blur-xl animate-pulse-slow"></div>
              <div className="relative bg-white bg-opacity-20 backdrop-blur-md rounded-2xl p-4 shadow-2xl border border-white border-opacity-20">
                {/* Properly positioned dashed border on the card */}
                <div className="absolute -inset-3 rounded-full border-2 border-dashed border-yellow-600/90 animate-spin-slow" style={{ zIndex: 30 }}></div>
                <div className="aspect-square w-full max-w-[300px] flex flex-col items-center justify-between text-center p-6">
                  {/* Top section */}
                  <div className="text-5xl font-black mt-2">SALE</div>
                  
                  {/* Middle section - main offer */}
                  <div className="flex flex-col items-center -mt-4">
                    <div className="text-xl font-bold mb-1">Up to</div>
                    <div className="text-7xl font-extrabold text-yellow-400 leading-none drop-shadow-lg">50%</div>
                    <div className="text-2xl font-bold tracking-wider mt-1">OFF</div>
                    <div className="h-px w-3/4 bg-white opacity-30 my-2"></div>
                  </div>
                  
                  {/* Bottom section */}
                  <div className="text-sm font-medium py-1 px-4 bg-white bg-opacity-20 rounded-full shadow-inner">Limited Time Only</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Trust Indicators */}
        <div className="mt-12 flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
          <div className="flex items-center space-x-2">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-current" />
              ))}
            </div>
            <span className="text-blue-100">4.9/5 from 10,000+ reviews</span>
          </div>
          <div className="text-blue-100">
            📦 Free shipping on orders $50+
          </div>
          <div className="text-blue-100">
            🔒 Secure checkout guaranteed
          </div>
        </div>
      </div>
      
      {/* Wave Decoration */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-auto">
          <path d="M0 120L48 105C96 90 192 60 288 55C384 50 480 70 576 75C672 80 768 70 864 65C960 60 1056 60 1152 65C1248 70 1344 80 1392 85L1440 90V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fillOpacity="0.05"/>
          <path d="M0 120L48 110C96 100 192 80 288 75C384 70 480 80 576 85C672 90 768 90 864 85C960 80 1056 70 1152 75C1248 80 1344 100 1392 110L1440 120V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0Z" fill="white" fillOpacity="0.1"/>
        </svg>
      </div>
    </section>
  );
}

// Add these animations to your globals.css or tailwind.config.js
// @keyframes spin-slow {
//   from { transform: rotate(0deg); }
//   to { transform: rotate(360deg); }
// }
// @keyframes bounce-slow {
//   0%, 100% { transform: translateY(0) rotate(12deg); }
//   50% { transform: translateY(-10px) rotate(12deg); }
// }
// @keyframes pulse-slow {
//   0%, 100% { opacity: 0.5; }
//   50% { opacity: 0.8; }
// }