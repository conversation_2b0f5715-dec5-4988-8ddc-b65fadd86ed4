{"version": 3, "sources": ["../../../src/client/components/headers.ts"], "names": ["RequestCookiesAdapter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RequestCookies", "actionAsyncStorage", "DraftMode", "trackDynamicDataAccessed", "staticGenerationAsyncStorage", "getExpectedRequestStore", "headers", "callingExpression", "staticGenerationStore", "getStore", "forceStatic", "seal", "Headers", "cookies", "requestStore", "asyncActionStore", "isAction", "isAppRoute", "mutableCookies", "draftMode"], "mappings": "AAAA,SAEEA,qBAAqB,QAChB,2DAA0D;AACjE,SAASC,cAAc,QAAQ,mDAAkD;AACjF,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,wBAAwB,QAAQ,4CAA2C;AACpF,SAASC,4BAA4B,QAAQ,6CAA4C;AACzF,SAASC,uBAAuB,QAAQ,mCAAkC;AAE1E;;;;;;;;CAQC,GACD,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,wBAAwBJ,6BAA6BK,QAAQ;IAEnE,IAAID,uBAAuB;QACzB,IAAIA,sBAAsBE,WAAW,EAAE;YACrC,wGAAwG;YACxG,OAAOX,eAAeY,IAAI,CAAC,IAAIC,QAAQ,CAAC;QAC1C,OAAO;YACL,wGAAwG;YACxGT,yBAAyBK,uBAAuBD;QAClD;IACF;IAEA,OAAOF,wBAAwBE,mBAAmBD,OAAO;AAC3D;AAEA,OAAO,SAASO;IACd,MAAMN,oBAAoB;IAC1B,MAAMC,wBAAwBJ,6BAA6BK,QAAQ;IAEnE,IAAID,uBAAuB;QACzB,IAAIA,sBAAsBE,WAAW,EAAE;YACrC,wGAAwG;YACxG,OAAOZ,sBAAsBa,IAAI,CAAC,IAAIX,eAAe,IAAIY,QAAQ,CAAC;QACpE,OAAO;YACL,wGAAwG;YACxGT,yBAAyBK,uBAAuBD;QAClD;IACF;IAEA,MAAMO,eAAeT,wBAAwBE;IAE7C,MAAMQ,mBAAmBd,mBAAmBQ,QAAQ;IACpD,IAAIM,CAAAA,oCAAAA,iBAAkBC,QAAQ,MAAID,oCAAAA,iBAAkBE,UAAU,GAAE;QAC9D,2EAA2E;QAC3E,+DAA+D;QAC/D,OAAOH,aAAaI,cAAc;IACpC;IAEA,OAAOJ,aAAaD,OAAO;AAC7B;AAEA,OAAO,SAASM;IACd,MAAMZ,oBAAoB;IAC1B,MAAMO,eAAeT,wBAAwBE;IAE7C,OAAO,IAAIL,UAAUY,aAAaK,SAAS;AAC7C"}