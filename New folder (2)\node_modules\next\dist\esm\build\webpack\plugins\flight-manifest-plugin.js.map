{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "names": ["path", "webpack", "sources", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "CLIENT_REFERENCE_MANIFEST", "SYSTEM_ENTRYPOINTS", "relative", "getProxiedPluginState", "WEBPACK_LAYERS", "normalizePagePath", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getDeploymentIdQueryOrEmptyString", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "pluginState", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "chunks", "for<PERSON>ach", "chunk", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "encodeURI", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "PLUGIN_NAME", "ClientReferenceManifestPlugin", "constructor", "options", "dev", "appDir", "appDirBase", "dirname", "sep", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "publicPath", "Error", "prefix", "rootMainFiles", "Set", "entrypoints", "get", "getFiles", "add", "entrypoint", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "requiredChunks", "recordModule", "modId", "mod", "resource", "type", "_identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "ssrNamedModuleId", "resourceResolveData", "isAsyncModule", "esmResource", "matchResource", "addClientReference", "exportName", "async", "edgeExportName", "addSSRIdMapping", "checkedChunkGroups", "checkedChunks", "recordChunkGroup", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "layer", "appPagesBrowser", "request", "includes", "connections", "moduleGraph", "connection", "dependency", "clientEntryMod", "getResolvedModule", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "child", "childrenIterable", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "length", "RawSource"], "mappings": "AAAA;;;;;CAKC,GAED,OAAOA,UAAU,OAAM;AACvB,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,kBAAkB,QACb,gCAA+B;AACtC,SAASC,QAAQ,QAAQ,OAAM;AAC/B,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,oCAAoC,QAAQ,gCAA+B;AACpF,SAASC,iCAAiC,QAAQ,sBAAqB;AACvE,SACEC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AAiBjB,MAAMC,cAAcP,sBAAsB;IACxCQ,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IACtB,uCAAuC;IACvCC,sBAAsB,CAAC;AACzB;AA4CA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBV;IAE/B,MAAMW,SAAwB,EAAE;IAChCH,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAInB,mBAAmBoB,GAAG,CAACD,MAAME,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIF,MAAMG,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKJ,MAAMG,EAAE;YAC7BH,MAAMK,KAAK,CAACN,OAAO,CAAC,CAACO;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIX,cAAcK,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOR,OAAOU,IAAI,CAACJ,SAASK,UAAUH,OAAOT;YAC/C;QACF;IACF;IACA,OAAOC;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,kCAAkC;AAClC,gDAAgD;AAChD,SAASY,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,KAChC,eAAe;KACdC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B,GACnC,2GAA2G;IAC3G,gHAAgH;IAChH,mHAAmH;KAClHA,OAAO,CAAC,6BAA6B;IAExC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;AACrE;AAEA,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAKXC,YAAYC,OAAgB,CAAE;aAJ9BC,MAAsB;QAKpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGzD,KAAK0D,OAAO,CAAC,IAAI,CAACF,MAAM,IAAIxD,KAAK2D,GAAG;IACxD;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5Bb,aACA,CAACY,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjClE,QAAQmE,YAAY,CAACC,gBAAgB,EACrCJ;YAEFF,YAAYO,mBAAmB,CAACH,GAAG,CACjClE,QAAQmE,YAAY,CAACC,gBAAgB,EACrC,IAAIpE,QAAQmE,YAAY,CAACG,cAAc,CAACC,QAAQ;YAElDT,YAAYD,KAAK,CAACW,aAAa,CAACT,GAAG,CACjC;gBACErC,MAAMwB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CuB,OAAOzE,QAAQ0E,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,WAAW,CAACD,QAAQd,aAAaF,SAASkB,OAAO;QAEtE;IAEJ;IAEAD,YACED,MAAqC,EACrCd,WAAgC,EAChCgB,OAAe,EACf;YAuBAhB;QAtBA,MAAMiB,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJpB,YAAYqB,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,cACF;QAEN,IAAI,OAAOpB,YAAYqB,aAAa,CAACG,UAAU,KAAK,UAAU;YAC5D,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMC,SAAS1B,YAAYqB,aAAa,CAACG,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAIC;SACvC5B,+BAAAA,YAAY6B,WAAW,CACpBC,GAAG,CAAClF,0DADPoD,6BAEI+B,QAAQ,GACTtE,OAAO,CAAC,CAACO;YACR,IAAI,oCAAoCU,IAAI,CAACV,OAAO;gBAClD2D,cAAcK,GAAG,CAAChE,KAAKS,OAAO,CAAC,OAAO;YACxC;QACF;QAEF,KAAK,IAAI,CAACJ,WAAW4D,WAAW,IAAIjC,YAAY6B,WAAW,CAAE;YAC3D,IACExD,cAAczB,wCACdyB,cAAcjC,sBACd;gBACAiC,YAAY;YACd,OAAO,IAAI,CAAC,YAAYK,IAAI,CAACL,YAAY;gBACvC;YACF;YAEA,MAAMO,WAAoC;gBACxCsD,eAAe;oBACbR;oBACAS,aAAaZ;gBACf;gBACAtC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,sCAAsC;YACtC,MAAMiD,iBAAiB,AAAC,CAAA,IAAI,CAAC1C,UAAU,GAAGrB,SAAQ,EAAGI,OAAO,CAC1D,UACAxC,KAAK2D,GAAG;YAEVhB,SAASO,aAAa,CAACiD,eAAe,GAAGH,WACtCF,QAAQ,GACRM,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAErE,QAAQ,CAAC;YAElE,MAAMuE,iBAAiBpF,yBAAyB6E,YAAYN;YAC5D,MAAMc,eAAe,CAACC,OAAiBC;oBAoBnCA,0BAqBEA;gBAxCJ,IAAIC,WACFD,IAAIE,IAAI,KAAK,qBAETF,IAAIG,WAAW,CAACvE,KAAK,CAACoE,IAAIG,WAAW,CAACtE,WAAW,CAAC,OAAO,KACzDmE,IAAIC,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBnE,SAASI,aAAa;gBAC/C,MAAMgE,kBAAkBpE,SAASK,gBAAgB;gBACjD,MAAMgE,sBAAsBrE,SAASM,oBAAoB;gBAEzD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAIgE,mBAAmB1G,SACrBwE,SACA2B,EAAAA,2BAAAA,IAAIQ,mBAAmB,qBAAvBR,yBAAyB1G,IAAI,KAAI2G;gBAGnC,IAAI,CAACM,iBAAiBX,UAAU,CAAC,MAC/BW,mBAAmB,CAAC,EAAE,EAAEA,iBAAiBzE,OAAO,CAAC,OAAO,KAAK,CAAC;gBAEhE,MAAM2E,gBAAgB,CAAC,CAACpG,YAAYG,oBAAoB,CAACwF,IAAIC,QAAQ,CAAC;gBAEtE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMS,cAAc,0BAA0B3E,IAAI,CAACkE,YAC/CA,SAASnE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOxC,KAAK2D,GAAG,KAE3C;gBAEJ,wEAAwE;gBACxE,2EAA2E;gBAC3E,8DAA8D;gBAC9D,yDAAyD;gBACzD,KAAI+C,qBAAAA,IAAIW,aAAa,qBAAjBX,mBAAmBJ,UAAU,CAAClG,6BAA6B;oBAC7D6G,mBAAmBpG,8BACjBoG,kBACAP,IAAIW,aAAa;oBAEnBV,WAAW9F,8BAA8B8F,UAAUD,IAAIW,aAAa;gBACtE;gBAEA,SAASC;oBACP,MAAMC,aAAaZ;oBACnBhE,SAASI,aAAa,CAACwE,WAAW,GAAG;wBACnC3F,IAAI6E;wBACJ9E,MAAM;wBACNJ,QAAQgF;wBACRiB,OAAOL;oBACT;oBACA,IAAIC,aAAa;wBACf,MAAMK,iBAAiBL;wBACvBzE,SAASI,aAAa,CAAC0E,eAAe,GACpC9E,SAASI,aAAa,CAACwE,WAAW;oBACtC;gBACF;gBAEA,SAASG;oBACP,MAAMH,aAAaZ;oBACnB,IACE,OAAO5F,YAAYC,eAAe,CAACiG,iBAAiB,KAAK,aACzD;wBACAF,eAAe,CAACN,MAAM,GAAGM,eAAe,CAACN,MAAM,IAAI,CAAC;wBACpDM,eAAe,CAACN,MAAM,CAAC,IAAI,GAAG;4BAC5B,GAAG9D,SAASI,aAAa,CAACwE,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvChG,QAAQ,EAAE;4BACVK,IAAIb,YAAYC,eAAe,CAACiG,iBAAiB;wBACnD;oBACF;oBAEA,IACE,OAAOlG,YAAYE,mBAAmB,CAACgG,iBAAiB,KACxD,aACA;wBACAD,mBAAmB,CAACP,MAAM,GAAGO,mBAAmB,CAACP,MAAM,IAAI,CAAC;wBAC5DO,mBAAmB,CAACP,MAAM,CAAC,IAAI,GAAG;4BAChC,GAAG9D,SAASI,aAAa,CAACwE,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvChG,QAAQ,EAAE;4BACVK,IAAIb,YAAYE,mBAAmB,CAACgG,iBAAiB;wBACvD;oBACF;gBACF;gBAEAK;gBACAI;gBAEA/E,SAASI,aAAa,GAAG+D;gBACzBnE,SAASK,gBAAgB,GAAG+D;gBAC5BpE,SAASM,oBAAoB,GAAG+D;YAClC;YAEA,MAAMW,qBAAqB,IAAIhC;YAC/B,MAAMiC,gBAAgB,IAAIjC;YAE1B,SAASkC,iBAAiBzG,UAAsB;gBAC9C,yEAAyE;gBACzE,IAAIuG,mBAAmBjG,GAAG,CAACN,aAAa;gBACxCuG,mBAAmB5B,GAAG,CAAC3E;gBACvB,0EAA0E;gBAC1E,oEAAoE;gBACpE,oEAAoE;gBACpE,qDAAqD;gBACrD,6CAA6C;gBAC7CA,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;oBACzB,mEAAmE;oBACnE,IAAImG,cAAclG,GAAG,CAACD,QAAQ;oBAC9BmG,cAAc7B,GAAG,CAACtE;oBAClB,MAAMqG,YACJ/D,YAAYgE,UAAU,CAACC,4BAA4B,CAACvG;oBACtD,KAAK,MAAMiF,OAAOoB,UAAW;wBAC3B,IAAIpB,IAAIuB,KAAK,KAAKxH,eAAeyH,eAAe,EAAE;wBAElD,MAAMC,UAAU,AAACzB,IAA6ByB,OAAO;wBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;4BACA;wBACF;wBAEA,MAAMC,cAAcvH,2BAClB4F,KACA3C,YAAYuE,WAAW;wBAGzB,KAAK,MAAMC,cAAcF,YAAa;4BACpC,MAAMG,aAAaD,WAAWC,UAAU;4BACxC,IAAI,CAACA,YAAY;4BAEjB,MAAMC,iBAAiB1E,YAAYuE,WAAW,CAACI,iBAAiB,CAC9DF;4BAEF,MAAM/B,QAAQ1C,YAAYgE,UAAU,CAACY,WAAW,CAC9CF;4BAGF,IAAIhC,UAAU,MAAM;gCAClBD,aAAaC,OAAOgC;4BACtB,OAAO;oCAGHF;gCAFF,oEAAoE;gCACpE,IACEA,EAAAA,qBAAAA,WAAWK,MAAM,qBAAjBL,mBAAmBlF,WAAW,CAAC1B,IAAI,MAAK,sBACxC;oCACA,MAAMkH,kBAAkBN,WAAWK,MAAM;oCACzC,MAAME,oBACJ/E,YAAYgE,UAAU,CAACY,WAAW,CAACE;oCACrCrC,aAAasC,mBAAmBL;gCAClC;4BACF;wBACF;oBACF;gBACF;gBAEA,8CAA8C;gBAC9C,KAAK,MAAMM,SAAS3H,WAAW4H,gBAAgB,CAAE;oBAC/CnB,iBAAiBkB;gBACnB;YACF;YAEAlB,iBAAiB7B;YAEjB,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBvD,IAAI,CAACL,YAAY;gBACvC8C,mBAAmBjD,IAAI,CAACG,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,MAAMH,YAAYF,qBAAqBC;YACvC,IAAI,CAAC4C,kBAAkBtD,GAAG,CAACW,YAAY;gBACrC2C,kBAAkBb,GAAG,CAAC9B,WAAW,EAAE;YACrC;YACA2C,kBAAkBa,GAAG,CAACxD,WAAYJ,IAAI,CAACU;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMsG,YAAY/D,mBAAoB;YACzC,MAAMgE,iBAA0C;gBAC9CjD,eAAe;oBACbR;oBACAS,aAAaZ;gBACf;gBACAtC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,MAAMiG,WAAW;mBAAIhH,qBAAqB8G,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMxG,YAAYqC,kBAAkBa,GAAG,CAACwD,UAAU,EAAE,CAAE;oBACzD3G,cAAcwG,gBAAgBvG;gBAChC;gBACA0G,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAASzG,OAAO,CAAC,QAAQ;YAC1C,MAAMmH,iBAAiBjJ,kBAAkBgJ,SAASpH,KAAK,CAAC,MAAMsH,MAAM;YACpE/E,MAAM,CACJ,eAAe8E,iBAAiB,MAAMtJ,4BAA4B,MACnE,GAAG,IAAIH,QAAQ2J,SAAS,CACvB,CAAC,oFAAoF,EAAEL,KAAKC,SAAS,CACnGC,SAASpH,KAAK,CAAC,MAAMsH,MAAM,GAC3B,EAAE,EAAEL,KAAK,CAAC;QAEhB;QAEAxI,YAAYG,oBAAoB,GAAG,CAAC;IACtC;AACF"}