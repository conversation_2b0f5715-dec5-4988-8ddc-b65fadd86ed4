{"version": 3, "file": "ast-guards.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/ast-guards.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAI3D,8CAEC;AAED,8DAEC;AAGD,kDAEC;AAED,gDAEC;AAED,8CAEC;AAED,gEAEC;AAED,oCAEC;AAED,sDAEC;AAED,oDAEC;AAED,oCAEC;AAED,8BAEC;AAED,gDAEC;AAED,gDAEC;AAED,kDAEC;AAED,gCAEC;AAED,oDAEC;AAED,kDAEC;AAED,4DAEC;AAED,sDAEC;AAED,8CAEC;AAED,4DAEC;AAED,oDAEC;AAGD,gEAEC;AAED,wEAIC;AAED,sEAIC;AAED,0CAEC;AAED,8DAEC;AAED,0CAEC;AAOD,8DAEC;AAKD,oEAEC;AAQD,kEAQC;AAMD,8DAEC;AAOD,0EAMC;AAMD,4DAEC;AAMD,wDAEC;AAOD,gEAEC;AAMD,4HAIC;AAMD,8HAIC;AAMD,sHAIC;AAOD,wIAIC;AAgBD,wCAeC;AA1QD,SAAgB,iBAAiB,CAAC,IAAmB;IACnD,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;AACzC,CAAC;AAED,SAAgB,yBAAyB,CAAC,IAAmB;IAC3D,OAAO,IAAI,CAAC,IAAI,KAAK,yBAAyB,CAAC;AACjD,CAAC;AAED,yBAAyB;AACzB,SAAgB,mBAAmB,CAAC,IAAmB;IACrD,OAAO,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC;AAC3C,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAmB;IACpD,OAAO,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAC1C,CAAC;AAED,SAAgB,iBAAiB,CAAC,IAAmB;IACnD,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;AACzC,CAAC;AAED,SAAgB,0BAA0B,CAAC,IAAmB;IAC5D,OAAO,IAAI,CAAC,IAAI,KAAK,0BAA0B,CAAC;AAClD,CAAC;AAED,SAAgB,YAAY,CAAC,IAAmB;IAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC1C,CAAC;AAED,SAAgB,qBAAqB,CAAC,IAAmB;IACvD,OAAO,IAAI,CAAC,IAAI,KAAK,qBAAqB,CAAC;AAC7C,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAmB;IACtD,OAAO,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC;AAC5C,CAAC;AAED,SAAgB,YAAY,CAAC,IAAmB;IAC9C,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC;AACpC,CAAC;AAED,SAAgB,SAAS,CAAC,IAAmB;IAC3C,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC;AACjC,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAmB;IACpD,OAAO,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAC1C,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAmB;IACpD,OAAO,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAC1C,CAAC;AAED,SAAgB,mBAAmB,CAAC,IAAmB;IACrD,OAAO,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC;AAC3C,CAAC;AAED,SAAgB,UAAU,CAAC,IAAmB;IAC5C,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AAClC,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAmB;IACtD,OAAO,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC;AAC5C,CAAC;AAED,SAAgB,mBAAmB,CAAC,IAAmB;IACrD,OAAO,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC;AAC3C,CAAC;AAED,SAAgB,wBAAwB,CAAC,IAAmB;IAC1D,OAAO,IAAI,CAAC,IAAI,KAAK,wBAAwB,CAAC;AAChD,CAAC;AAED,SAAgB,qBAAqB,CAAC,IAAmB;IACvD,OAAO,IAAI,CAAC,IAAI,KAAK,qBAAqB,CAAC;AAC7C,CAAC;AAED,SAAgB,iBAAiB,CAAC,IAAmB;IACnD,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;AACzC,CAAC;AAED,SAAgB,wBAAwB,CAAC,IAAmB;IAC1D,OAAO,IAAI,CAAC,IAAI,KAAK,wBAAwB,CAAC;AAChD,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAmB;IACtD,OAAO,IAAI,CAAC,IAAI,KAAK,oBAAoB,CAAC;AAC5C,CAAC;AAED,4EAA4E;AAC5E,SAAgB,0BAA0B,CAAC,IAAmB;IAC5D,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AACtD,CAAC;AAED,SAAgB,8BAA8B,CAC5C,IAAmB;IAEnB,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;AACtE,CAAC;AAED,SAAgB,6BAA6B,CAC3C,IAAmB;IAEnB,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AACzD,CAAC;AAED,SAAgB,eAAe,CAAC,IAAmB;IACjD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AAC3D,CAAC;AAED,SAAgB,yBAAyB,CAAC,IAAmB;IAC3D,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;AAC9E,CAAC;AAED,SAAgB,eAAe,CAAC,IAAmB;IACjD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AAC3D,CAAC;AAOD,SAAgB,yBAAyB,CAAC,IAAmB;IAC3D,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AACrD,CAAC;AAKD,SAAgB,4BAA4B,CAAC,IAAmB;IAC9D,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AACxD,CAAC;AAQD,SAAgB,2BAA2B,CAAC,IAAmB;IAC7D,MAAM,2BAA2B,GAAyC;QACxE,yBAAyB;QACzB,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;KACnB,CAAC;IACF,OAAO,2BAA2B,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,CAAC;AAMD,SAAgB,yBAAyB,CAAC,IAAmB;IAC3D,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9D,CAAC;AAOD,SAAgB,+BAA+B,CAAC,IAAmB;IACjE,OAAO,CACL,oBAAoB,CAAC,IAAI,CAAC;QAC1B,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,KAAK,IAAI,CACpB,CAAC;AACJ,CAAC;AAMD,SAAgB,wBAAwB,CAAC,IAAmB;IAC1D,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/F,CAAC;AAMD,SAAgB,sBAAsB,CAAC,IAAmB;IACxD,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACvF,CAAC;AAOD,SAAgB,0BAA0B,CAAC,IAAmB;IAC5D,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACnF,CAAC;AAMD,SAAgB,wDAAwD,CACtE,IAAmB;IAEnB,OAAO,yBAAyB,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpF,CAAC;AAMD,SAAgB,yDAAyD,CACvE,IAAmB;IAEnB,OAAO,0BAA0B,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpF,CAAC;AAMD,SAAgB,qDAAqD,CACnE,IAAmB;IAEnB,OAAO,sBAAsB,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjF,CAAC;AAOD,SAAgB,8DAA8D,CAC5E,IAAmB;IAEnB,OAAO,+BAA+B,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1F,CAAC;AAgBD,SAAgB,cAAc,CAAC,IAAmB;IAChD,OAAO,CACL,0BAA0B,CAAC,IAAI,CAAC;QAChC,6BAA6B,CAAC,IAAI,CAAC;QACnC,yBAAyB,CAAC,IAAI,CAAC;QAC/B,4BAA4B,CAAC,IAAI,CAAC;QAClC,yDAAyD,CAAC,IAAI,CAAC;QAC/D,qDAAqD,CAAC,IAAI,CAAC;QAC3D,8DAA8D,CAAC,IAAI,CAAC;QACpE,wDAAwD,CAAC,IAAI,CAAC;QAC9D,wBAAwB,CAAC,IAAI,CAAC;QAC9B,mBAAmB,CAAC,IAAI,CAAC;QACzB,wBAAwB,CAAC,IAAI,CAAC;QAC9B,wBAAwB,CAAC,IAAI,CAAC,CAC/B,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type { TSESTree } from '@typescript-eslint/types';\n\nexport function isArrayExpression(node: TSESTree.Node): node is TSESTree.ArrayExpression {\n  return node.type === 'ArrayExpression';\n}\n\nexport function isArrowFunctionExpression(node: TSESTree.Node): node is TSESTree.ArrowFunctionExpression {\n  return node.type === 'ArrowFunctionExpression';\n}\n\n/** default parameters */\nexport function isAssignmentPattern(node: TSESTree.Node): node is TSESTree.AssignmentPattern {\n  return node.type === 'AssignmentPattern';\n}\n\nexport function isClassDeclaration(node: TSESTree.Node): node is TSESTree.ClassDeclaration {\n  return node.type === 'ClassDeclaration';\n}\n\nexport function isClassExpression(node: TSESTree.Node): node is TSESTree.ClassExpression {\n  return node.type === 'ClassExpression';\n}\n\nexport function isExportDefaultDeclaration(node: TSESTree.Node): node is TSESTree.ExportDefaultDeclaration {\n  return node.type === 'ExportDefaultDeclaration';\n}\n\nexport function isExpression(node: TSESTree.Node): node is TSESTree.Expression {\n  return node.type.includes('Expression');\n}\n\nexport function isFunctionDeclaration(node: TSESTree.Node): node is TSESTree.FunctionDeclaration {\n  return node.type === 'FunctionDeclaration';\n}\n\nexport function isFunctionExpression(node: TSESTree.Node): node is TSESTree.FunctionExpression {\n  return node.type === 'FunctionExpression';\n}\n\nexport function isIdentifier(node: TSESTree.Node): node is TSESTree.Identifier {\n  return node.type === 'Identifier';\n}\n\nexport function isLiteral(node: TSESTree.Node): node is TSESTree.Literal {\n  return node.type === 'Literal';\n}\n\nexport function isMethodDefinition(node: TSESTree.Node): node is TSESTree.MethodDefinition {\n  return node.type === 'MethodDefinition';\n}\n\nexport function isObjectExpression(node: TSESTree.Node): node is TSESTree.ObjectExpression {\n  return node.type === 'ObjectExpression';\n}\n\nexport function isPrivateIdentifier(node: TSESTree.Node): node is TSESTree.PrivateIdentifier {\n  return node.type === 'PrivateIdentifier';\n}\n\nexport function isProperty(node: TSESTree.Node): node is TSESTree.Property {\n  return node.type === 'Property';\n}\n\nexport function isPropertyDefinition(node: TSESTree.Node): node is TSESTree.PropertyDefinition {\n  return node.type === 'PropertyDefinition';\n}\n\nexport function isTSEnumDeclaration(node: TSESTree.Node): node is TSESTree.TSEnumDeclaration {\n  return node.type === 'TSEnumDeclaration';\n}\n\nexport function isTSInterfaceDeclaration(node: TSESTree.Node): node is TSESTree.TSInterfaceDeclaration {\n  return node.type === 'TSInterfaceDeclaration';\n}\n\nexport function isTSModuleDeclaration(node: TSESTree.Node): node is TSESTree.TSModuleDeclaration {\n  return node.type === 'TSModuleDeclaration';\n}\n\nexport function isTSQualifiedName(node: TSESTree.Node): node is TSESTree.TSQualifiedName {\n  return node.type === 'TSQualifiedName';\n}\n\nexport function isTSTypeAliasDeclaration(node: TSESTree.Node): node is TSESTree.TSTypeAliasDeclaration {\n  return node.type === 'TSTypeAliasDeclaration';\n}\n\nexport function isVariableDeclarator(node: TSESTree.Node): node is TSESTree.VariableDeclarator {\n  return node.type === 'VariableDeclarator';\n}\n\n// Compound Type Guards for @typescript-eslint/types ast-spec compound types\nexport function isClassDeclarationWithName(node: TSESTree.Node): node is TSESTree.ClassDeclarationWithName {\n  return isClassDeclaration(node) && node.id !== null;\n}\n\nexport function isClassPropertyNameNonComputed(\n  node: TSESTree.Node\n): node is TSESTree.ClassPropertyNameNonComputed {\n  return isPrivateIdentifier(node) || isPropertyNameNonComputed(node);\n}\n\nexport function isFunctionDeclarationWithName(\n  node: TSESTree.Node\n): node is TSESTree.FunctionDeclarationWithName {\n  return isFunctionDeclaration(node) && node.id !== null;\n}\n\nexport function isNumberLiteral(node: TSESTree.Node): node is TSESTree.NumberLiteral {\n  return isLiteral(node) && typeof node.value === 'number';\n}\n\nexport function isPropertyNameNonComputed(node: TSESTree.Node): node is TSESTree.PropertyNameNonComputed {\n  return isIdentifier(node) || isNumberLiteral(node) || isStringLiteral(node);\n}\n\nexport function isStringLiteral(node: TSESTree.Node): node is TSESTree.StringLiteral {\n  return isLiteral(node) && typeof node.value === 'string';\n}\n\n// Custom compound types\nexport interface IClassExpressionWithName extends TSESTree.ClassExpression {\n  id: TSESTree.Identifier;\n}\n\nexport function isClassExpressionWithName(node: TSESTree.Node): node is IClassExpressionWithName {\n  return isClassExpression(node) && node.id !== null;\n}\nexport interface IFunctionExpressionWithName extends TSESTree.FunctionExpression {\n  id: TSESTree.Identifier;\n}\n\nexport function isFunctionExpressionWithName(node: TSESTree.Node): node is IFunctionExpressionWithName {\n  return isFunctionExpression(node) && node.id !== null;\n}\n\nexport type NormalAnonymousExpression =\n  | TSESTree.ArrowFunctionExpression\n  | TSESTree.ClassExpression\n  | TSESTree.FunctionExpression\n  | TSESTree.ObjectExpression;\n\nexport function isNormalAnonymousExpression(node: TSESTree.Node): node is NormalAnonymousExpression {\n  const ANONYMOUS_EXPRESSION_GUARDS: ((node: TSESTree.Node) => boolean)[] = [\n    isArrowFunctionExpression,\n    isClassExpression,\n    isFunctionExpression,\n    isObjectExpression\n  ];\n  return ANONYMOUS_EXPRESSION_GUARDS.some((guard) => guard(node));\n}\n\nexport interface INormalAssignmentPattern extends TSESTree.AssignmentPattern {\n  left: TSESTree.Identifier;\n}\n\nexport function isNormalAssignmentPattern(node: TSESTree.Node): node is INormalAssignmentPattern {\n  return isAssignmentPattern(node) && isIdentifier(node.left);\n}\n\nexport interface INormalClassPropertyDefinition extends TSESTree.PropertyDefinitionNonComputedName {\n  key: TSESTree.PrivateIdentifier | TSESTree.Identifier;\n  value: TSESTree.Expression;\n}\n\nexport function isNormalClassPropertyDefinition(node: TSESTree.Node): node is INormalClassPropertyDefinition {\n  return (\n    isPropertyDefinition(node) &&\n    (isIdentifier(node.key) || isPrivateIdentifier(node.key)) &&\n    node.value !== null\n  );\n}\n\nexport interface INormalMethodDefinition extends TSESTree.MethodDefinitionNonComputedName {\n  key: TSESTree.PrivateIdentifier | TSESTree.Identifier;\n}\n\nexport function isNormalMethodDefinition(node: TSESTree.Node): node is INormalMethodDefinition {\n  return isMethodDefinition(node) && (isIdentifier(node.key) || isPrivateIdentifier(node.key));\n}\n\nexport interface INormalObjectProperty extends TSESTree.PropertyNonComputedName {\n  key: TSESTree.Identifier;\n}\n\nexport function isNormalObjectProperty(node: TSESTree.Node): node is INormalObjectProperty {\n  return isProperty(node) && (isIdentifier(node.key) || isPrivateIdentifier(node.key));\n}\n\nexport type INormalVariableDeclarator = TSESTree.LetOrConstOrVarDeclaration & {\n  id: TSESTree.Identifier;\n  init: TSESTree.Expression;\n};\n\nexport function isNormalVariableDeclarator(node: TSESTree.Node): node is INormalVariableDeclarator {\n  return isVariableDeclarator(node) && isIdentifier(node.id) && node.init !== null;\n}\n\nexport interface INormalAssignmentPatternWithAnonymousExpressionAssigned extends INormalAssignmentPattern {\n  right: NormalAnonymousExpression;\n}\n\nexport function isNormalAssignmentPatternWithAnonymousExpressionAssigned(\n  node: TSESTree.Node\n): node is INormalAssignmentPatternWithAnonymousExpressionAssigned {\n  return isNormalAssignmentPattern(node) && isNormalAnonymousExpression(node.right);\n}\n\nexport type INormalVariableDeclaratorWithAnonymousExpressionAssigned = INormalVariableDeclarator & {\n  init: NormalAnonymousExpression;\n};\n\nexport function isNormalVariableDeclaratorWithAnonymousExpressionAssigned(\n  node: TSESTree.Node\n): node is INormalVariableDeclaratorWithAnonymousExpressionAssigned {\n  return isNormalVariableDeclarator(node) && isNormalAnonymousExpression(node.init);\n}\n\nexport interface INormalObjectPropertyWithAnonymousExpressionAssigned extends INormalObjectProperty {\n  value: NormalAnonymousExpression;\n}\n\nexport function isNormalObjectPropertyWithAnonymousExpressionAssigned(\n  node: TSESTree.Node\n): node is INormalObjectPropertyWithAnonymousExpressionAssigned {\n  return isNormalObjectProperty(node) && isNormalAnonymousExpression(node.value);\n}\n\nexport interface INormalClassPropertyDefinitionWithAnonymousExpressionAssigned\n  extends INormalClassPropertyDefinition {\n  value: NormalAnonymousExpression;\n}\n\nexport function isNormalClassPropertyDefinitionWithAnonymousExpressionAssigned(\n  node: TSESTree.Node\n): node is INormalClassPropertyDefinitionWithAnonymousExpressionAssigned {\n  return isNormalClassPropertyDefinition(node) && isNormalAnonymousExpression(node.value);\n}\n\nexport type NodeWithName =\n  | TSESTree.ClassDeclarationWithName\n  | TSESTree.FunctionDeclarationWithName\n  | IClassExpressionWithName\n  | IFunctionExpressionWithName\n  | INormalVariableDeclaratorWithAnonymousExpressionAssigned\n  | INormalObjectPropertyWithAnonymousExpressionAssigned\n  | INormalClassPropertyDefinitionWithAnonymousExpressionAssigned\n  | INormalAssignmentPatternWithAnonymousExpressionAssigned\n  | INormalMethodDefinition\n  | TSESTree.TSEnumDeclaration\n  | TSESTree.TSInterfaceDeclaration\n  | TSESTree.TSTypeAliasDeclaration;\n\nexport function isNodeWithName(node: TSESTree.Node): node is NodeWithName {\n  return (\n    isClassDeclarationWithName(node) ||\n    isFunctionDeclarationWithName(node) ||\n    isClassExpressionWithName(node) ||\n    isFunctionExpressionWithName(node) ||\n    isNormalVariableDeclaratorWithAnonymousExpressionAssigned(node) ||\n    isNormalObjectPropertyWithAnonymousExpressionAssigned(node) ||\n    isNormalClassPropertyDefinitionWithAnonymousExpressionAssigned(node) ||\n    isNormalAssignmentPatternWithAnonymousExpressionAssigned(node) ||\n    isNormalMethodDefinition(node) ||\n    isTSEnumDeclaration(node) ||\n    isTSInterfaceDeclaration(node) ||\n    isTSTypeAliasDeclaration(node)\n  );\n}\n"]}