{"version": 3, "sources": ["../../../../../src/server/future/route-modules/helpers/response-handlers.ts"], "names": ["handleBadRequestResponse", "handleInternalServerErrorResponse", "handleMethodNotAllowedResponse", "handleNotFoundResponse", "handleRedirectResponse", "url", "mutableCookies", "status", "headers", "Headers", "location", "appendMutableCookies", "Response"], "mappings": ";;;;;;;;;;;;;;;;;;IAegBA,wBAAwB;eAAxBA;;IAYAC,iCAAiC;eAAjCA;;IAJAC,8BAA8B;eAA9BA;;IAJAC,sBAAsB;eAAtBA;;IAhBAC,sBAAsB;eAAtBA;;;gCAHqB;AAG9B,SAASA,uBACdC,GAAW,EACXC,cAA+B,EAC/BC,MAAc;IAEd,MAAMC,UAAU,IAAIC,QAAQ;QAAEC,UAAUL;IAAI;IAE5CM,IAAAA,oCAAoB,EAACH,SAASF;IAE9B,OAAO,IAAIM,SAAS,MAAM;QAAEL;QAAQC;IAAQ;AAC9C;AAEO,SAASR;IACd,OAAO,IAAIY,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASJ;IACd,OAAO,IAAIS,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASL;IACd,OAAO,IAAIU,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C;AAEO,SAASN;IACd,OAAO,IAAIW,SAAS,MAAM;QAAEL,QAAQ;IAAI;AAC1C"}