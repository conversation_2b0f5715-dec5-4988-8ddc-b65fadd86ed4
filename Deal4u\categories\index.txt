1:HL["/_next/static/css/0ebc520152eede79.css","style",{"crossOrigin":""}]
0:["qBGG3i9NaXjuV_NA48--5",[[["",{"children":["categories",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L2",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0ebc520152eede79.css","precedence":"next","crossOrigin":""}]],"$L3"]]]]
4:I[3843,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
5:I[9768,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"AuthProvider"]
6:I[8365,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"CartProvider"]
7:I[2194,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
8:I[6954,[],""]
9:I[5822,["326","static/chunks/326-80cc90e4cc9eac2a.js","601","static/chunks/app/error-253db9213a1fa457.js"],""]
a:I[7264,[],""]
b:I[1253,["326","static/chunks/326-80cc90e4cc9eac2a.js","160","static/chunks/app/not-found-6dd0f2e974eb5fd4.js"],""]
e:I[6547,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
f:I[5925,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"Toaster"]
10:I[1045,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
11:I[6873,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
12:I[4527,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
2:[null,["$","html",null,{"lang":"en","className":"scroll-smooth","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preconnect","href":"https://images.unsplash.com"}],["$","link",null,{"rel":"icon","href":"/favicon.ico","sizes":"any"}],["$","link",null,{"rel":"icon","href":"/icon.svg","type":"image/svg+xml"}],["$","link",null,{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}],["$","$L4",null,{}]]}],["$","body",null,{"className":"antialiased expansion-alids-init","children":["$undefined",["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","div",null,{"className":"min-h-screen flex flex-col bg-gray-50","children":[["$","$L7",null,{}],["$","main",null,{"className":"flex-1 relative","children":["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":["$","div",null,{"className":"min-h-screen bg-gray-50 flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-bold text-2xl mb-8 inline-block","children":"Deal4u"}],["$","div",null,{"className":"relative mb-6","children":[["$","div",null,{"className":"animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"w-8 h-8 bg-blue-100 rounded-full animate-pulse"}]}]]}],["$","p",null,{"className":"text-gray-600 text-lg font-medium animate-pulse","children":"Loading amazing deals..."}],["$","div",null,{"className":"mt-6 w-64 mx-auto bg-gray-200 rounded-full h-2 overflow-hidden","children":["$","div",null,{"className":"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"}]}],["$","div",null,{"className":"flex justify-center space-x-2 mt-6","children":[["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce"}],["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce","style":{"animationDelay":"0.1s"}}],["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce","style":{"animationDelay":"0.2s"}}]]}]]}]}],"loadingStyles":[],"loadingScripts":[],"hasLoading":true,"error":"$9","errorStyles":[],"errorScripts":[],"template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Lb",null,{}],"notFoundStyles":[],"childProp":{"current":["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children","categories","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","childProp":{"current":["$Lc","$Ld",null],"segment":"__PAGE__"},"styles":null}],"segment":"categories"},"styles":null}]}],["$","$Le",null,{}]]}],["$","$Lf",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"#fff","color":"#374151","border":"1px solid #e5e7eb","borderRadius":"8px","fontSize":"14px","fontWeight":"500"},"success":{"iconTheme":{"primary":"#10b981","secondary":"#fff"}},"error":{"iconTheme":{"primary":"#ef4444","secondary":"#fff"}}}}],["$","div",null,{"id":"loading-indicator","className":"hidden fixed top-0 left-0 w-full h-1 bg-blue-600 z-50","children":["$","div",null,{"className":"h-full bg-blue-400 animate-pulse"}]}]]}]}],["$","a",null,{"href":"#main-content","className":"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50","children":"Skip to main content"}],["$","$L10",null,{}],["$","$L11",null,{}],["$","$L12",null,{}]]}]]}],null]
3:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"}],["$","meta","1",{"name":"theme-color","media":"(prefers-color-scheme: light)","content":"#3b82f6"}],["$","meta","2",{"name":"theme-color","media":"(prefers-color-scheme: dark)","content":"#1e40af"}],["$","meta","3",{"charSet":"utf-8"}],["$","title","4",{"children":"Product Categories - Deal4u | Deal4u"}],["$","meta","5",{"name":"description","content":"Browse all product categories available at Deal4u"}],["$","meta","6",{"name":"author","content":"Deal4u Team"}],["$","meta","7",{"name":"keywords","content":"ecommerce,deals,shopping,products,online store"}],["$","meta","8",{"name":"creator","content":"Deal4u"}],["$","meta","9",{"name":"publisher","content":"Deal4u"}],["$","meta","10",{"name":"robots","content":"index, follow"}],["$","meta","11",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","12",{"rel":"canonical","href":"http://localhost:3000/"}],["$","meta","13",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","14",{"property":"og:title","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","15",{"property":"og:description","content":"Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service."}],["$","meta","16",{"property":"og:url","content":"http://localhost:3000/"}],["$","meta","17",{"property":"og:site_name","content":"Deal4u"}],["$","meta","18",{"property":"og:locale","content":"en_US"}],["$","meta","19",{"property":"og:image","content":"http://localhost:3000/og-image.jpg"}],["$","meta","20",{"property":"og:image:width","content":"1200"}],["$","meta","21",{"property":"og:image:height","content":"630"}],["$","meta","22",{"property":"og:image:alt","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","23",{"property":"og:type","content":"website"}],["$","meta","24",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","25",{"name":"twitter:site","content":"@deal4u"}],["$","meta","26",{"name":"twitter:creator","content":"@deal4u"}],["$","meta","27",{"name":"twitter:title","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","28",{"name":"twitter:description","content":"Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service."}],["$","meta","29",{"name":"twitter:image","content":"http://localhost:3000/og-image.jpg"}]]
c:null
13:I[8326,["326","static/chunks/326-80cc90e4cc9eac2a.js","413","static/chunks/413-f4d1f1c534704df9.js","457","static/chunks/app/categories/page-68f77dc4bda5efc6.js"],""]
d:["$","div",null,{"className":"min-h-screen bg-gray-50","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":[["$","div",null,{"className":"mb-10 text-center","children":[["$","h1",null,{"className":"text-3xl md:text-4xl font-bold text-gray-900 mb-4","children":"Shop by Category"}],["$","p",null,{"className":"text-lg text-gray-600 max-w-3xl mx-auto","children":"Browse our wide selection of products by category to find exactly what you're looking for."}]]}],["$","div",null,{"className":"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6","children":[["$","$L13","1142",{"href":"/shop?category=booking","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-yellow-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-yellow-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Booking"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[6," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","584",{"href":"/shop?category=cats-funny-catnip-plush-toy","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-red-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-red-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Cat’s Funny Catnip Plush Toy"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[1," ","product"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","585",{"href":"/shop?category=consumer-electronics","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-red-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-red-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Consumer Electronics"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[2," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1339",{"href":"/shop?category=electronic","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Electronic"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[8," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","19",{"href":"/shop?category=electronics","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Electronics"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[4," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","26",{"href":"/shop?category=fashion","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-indigo-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-indigo-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Fashion"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[20," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1340",{"href":"/shop?category=food","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-indigo-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-indigo-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Food"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[8," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1343",{"href":"/shop?category=handbags","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Handbags"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[8," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1344",{"href":"/shop?category=health","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Health"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[7," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","20",{"href":"/shop?category=home-garden","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Home &amp; Garden"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[13," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","617",{"href":"/shop?category=home-improvement-tools","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Home Improvement &amp; Tools"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[1," ","product"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1166",{"href":"/shop?category=hoodies","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Hoodies"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[3," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1348",{"href":"/shop?category=meats","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Meats"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[4," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1130",{"href":"/shop?category=men","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Men"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[9," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1362",{"href":"/shop?category=mugs","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Mugs"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[5," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1162",{"href":"/shop?category=music","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-pink-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-pink-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Music"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[6," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","607",{"href":"/shop?category=office-school-supplies","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-orange-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-orange-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Office &amp; School Supplies"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[1," ","product"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1350",{"href":"/shop?category=organic","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-orange-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-orange-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Organic"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[5," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1165",{"href":"/shop?category=posters","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-blue-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-blue-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Posters"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[5," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1355",{"href":"/shop?category=spices","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-red-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-red-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Spices"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[6," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","602",{"href":"/shop?category=sports","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-red-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-red-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Sports"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[4," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1139",{"href":"/shop?category=sweaters","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-red-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-red-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Sweaters"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[7," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1364",{"href":"/shop?category=tshirts","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-purple-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-purple-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Tshirts"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[6," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1357",{"href":"/shop?category=vegetable","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-indigo-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-indigo-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Vegetable"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[5," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}],["$","$L13","1140",{"href":"/shop?category=women","className":"group","children":["$","div",null,{"className":"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full","children":[["$","div",null,{"className":"bg-orange-200 h-48 flex items-center justify-center relative","children":[["$","svg",null,{"xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-orange-700","children":[["$","path","hou9p0",{"d":"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"}],["$","path","d0wm0j",{"d":"M3 6h18"}],["$","path","1ltviw",{"d":"M16 10a4 4 0 0 1-8 0"}],"$undefined"]}],["$","div",null,{"className":"absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity","children":["$","span",null,{"className":"text-white font-medium text-lg","children":"View Products"}]}]]}],["$","div",null,{"className":"p-4","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors","children":"Women"}],["$","div",null,{"className":"mt-2 flex items-center justify-between","children":[["$","span",null,{"className":"text-sm text-gray-600","children":[15," ","products"]}],["$","span",null,{"className":"text-blue-600 text-sm group-hover:underline","children":"Browse →"}]]}]]}]]}]}]]}]]}]}]
