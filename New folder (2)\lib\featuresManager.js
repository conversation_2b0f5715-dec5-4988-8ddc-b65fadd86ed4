// Features Manager - Controls all website features with WooCommerce integration

class FeaturesManager {
  constructor() {
    this.features = {};
    this.initialized = false;
    this.wooCommerce = null;
    this.currentUser = null;
  }

  // Initialize features from API
  async initialize() {
    try {
      // Initialize WooCommerce integration first
      await this.initializeWooCommerce();

      const response = await fetch('/api/admin/toggle-feature');
      const data = await response.json();

      if (data.success) {
        this.features = data.features;
        this.initialized = true;
        console.log('✅ Features Manager initialized with WooCommerce integration');

        // Get current user if logged in
        this.getCurrentUser();

        this.applyFeatures();
      }
    } catch (error) {
      console.error('❌ Error initializing Features Manager:', error);
      this.loadDefaultFeatures();
    }
  }

  // Initialize WooCommerce integration
  async initializeWooCommerce() {
    try {
      // Dynamically import WooCommerce features to avoid build issues
      const wooCommerceModule = await import('./woocommerceFeatures.js');
      this.wooCommerce = wooCommerceModule.default;
      await this.wooCommerce.initialize();
    } catch (error) {
      console.error('Error initializing WooCommerce:', error);
      // Create a mock WooCommerce object for fallback
      this.wooCommerce = {
        getProductRecommendations: async () => [],
        getLoyaltyPoints: async () => ({ points: 0, tier: 'Bronze', totalSpent: 0, orderCount: 0 }),
        getReorderableOrders: async () => [],
        addToCart: async () => ({ success: false, error: 'WooCommerce not available' }),
        bulkAddToCart: async () => ({ success: false, error: 'WooCommerce not available' })
      };
    }
  }

  // Get current logged-in user
  async getCurrentUser() {
    try {
      // Check if user is logged in (you can adapt this to your auth system)
      const userToken = localStorage.getItem('userToken') || sessionStorage.getItem('userToken');
      if (userToken) {
        // In a real implementation, you'd decode the token or call an API
        this.currentUser = { id: 1, email: '<EMAIL>' }; // Demo user
      }
    } catch (error) {
      console.error('Error getting current user:', error);
    }
  }

  // Load default features if API fails
  loadDefaultFeatures() {
    this.features = {
      liveChat: { enabled: true },
      productRecommendations: { enabled: true },
      loyaltyProgram: { enabled: false },
      arTryOn: { enabled: false },
      voiceSearch: { enabled: false },
      socialCommerce: { enabled: true },
      pushNotifications: { enabled: true },
      oneClickReorder: { enabled: true }
    };
    this.initialized = true;
    this.applyFeatures();
  }

  // Check if feature is enabled
  isEnabled(featureName) {
    return this.features[featureName]?.enabled || false;
  }

  // Get feature settings
  getSettings(featureName) {
    return this.features[featureName]?.settings || {};
  }

  // Apply all enabled features
  applyFeatures() {
    if (this.isEnabled('liveChat')) {
      this.enableLiveChat();
    }

    if (this.isEnabled('productRecommendations')) {
      this.enableProductRecommendations();
    }

    if (this.isEnabled('loyaltyProgram')) {
      this.enableLoyaltyProgram();
    }

    if (this.isEnabled('pushNotifications')) {
      this.enablePushNotifications();
    }

    if (this.isEnabled('socialCommerce')) {
      this.enableSocialCommerce();
    }

    if (this.isEnabled('oneClickReorder')) {
      this.enableOneClickReorder();
    }

    if (this.isEnabled('voiceSearch')) {
      this.enableVoiceSearch();
    }

    if (this.isEnabled('arTryOn')) {
      this.enableARTryOn();
    }
  }

  // Live Chat Feature
  enableLiveChat() {
    console.log('💬 Enabling Live Chat...');
    
    // Create chat widget
    const chatWidget = document.createElement('div');
    chatWidget.id = 'live-chat-widget';
    chatWidget.innerHTML = `
      <div class="fixed bottom-4 right-4 z-50">
        <button id="chat-toggle" class="bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors">
          💬
        </button>
        <div id="chat-window" class="hidden absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-xl border">
          <div class="bg-blue-600 text-white p-4 rounded-t-lg">
            <h3 class="font-semibold">Deal4u Support</h3>
            <p class="text-sm opacity-90">We're here to help!</p>
          </div>
          <div class="p-4 h-64 overflow-y-auto">
            <div class="bg-gray-100 p-3 rounded-lg mb-3">
              <p class="text-sm">👋 Hello! How can we help you today?</p>
            </div>
          </div>
          <div class="p-4 border-t">
            <div class="flex space-x-2">
              <input type="text" placeholder="Type your message..." class="flex-1 p-2 border rounded">
              <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Send</button>
            </div>
            <div class="mt-2">
              <a href="https://wa.me/96171172405" target="_blank" class="text-green-600 text-sm hover:underline">
                📱 Chat on WhatsApp
              </a>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(chatWidget);
    
    // Add chat functionality
    document.getElementById('chat-toggle').addEventListener('click', () => {
      const chatWindow = document.getElementById('chat-window');
      chatWindow.classList.toggle('hidden');
    });
  }

  // Product Recommendations Feature
  enableProductRecommendations() {
    console.log('🎯 Enabling Product Recommendations...');
    
    // Add recommendations to product pages
    const productContainers = document.querySelectorAll('.product-container, .product-detail');
    
    productContainers.forEach(container => {
      const recommendationsHTML = `
        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 class="text-xl font-semibold mb-4">You might also like</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="recommendations-container">
            <!-- Recommendations will be loaded here -->
          </div>
        </div>
      `;
      
      container.insertAdjacentHTML('afterend', recommendationsHTML);
    });
    
    // Load recommendations (you can integrate with your product API)
    this.loadProductRecommendations();
  }

  // Load product recommendations using WooCommerce data
  async loadProductRecommendations() {
    try {
      // Get current product ID from page
      const currentProductId = this.getCurrentProductId();

      if (!currentProductId) {
        console.log('No product ID found, loading popular products');
        return this.loadPopularProducts();
      }

      console.log(`🎯 Loading recommendations for product ${currentProductId}...`);

      // Get recommendations from WooCommerce
      const recommendations = await this.wooCommerce.getProductRecommendations(currentProductId, 4);

      if (recommendations && recommendations.length > 0) {
        const container = document.getElementById('recommendations-container');
        if (container) {
          container.innerHTML = recommendations.map(product => `
            <div class="bg-white p-4 rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location.href='/products/${product.id}'">
              <img src="${product.image}" alt="${product.name}" class="w-full h-32 object-cover rounded mb-2" loading="lazy">
              <h4 class="font-medium text-sm mb-1 line-clamp-2">${product.name}</h4>
              <div class="flex items-center mb-2">
                ${product.rating > 0 ? `
                  <div class="flex text-yellow-400 text-xs">
                    ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                  </div>
                  <span class="text-xs text-gray-500 ml-1">(${product.rating_count})</span>
                ` : ''}
              </div>
              <div class="flex items-center justify-between">
                <div>
                  ${product.sale_price ? `
                    <span class="text-red-600 font-semibold">£${product.sale_price}</span>
                    <span class="text-gray-400 line-through text-sm ml-1">£${product.regular_price}</span>
                  ` : `
                    <span class="text-blue-600 font-semibold">£${product.price}</span>
                  `}
                </div>
              </div>
              <button onclick="event.stopPropagation(); addToCartFromRecommendation(${product.id})" class="w-full mt-2 bg-blue-600 text-white py-1 px-2 rounded text-sm hover:bg-blue-700 transition-colors">
                Add to Cart
              </button>
            </div>
          `).join('');

          console.log(`✅ Loaded ${recommendations.length} product recommendations`);
        }
      } else {
        console.log('No recommendations found, loading popular products');
        this.loadPopularProducts();
      }
    } catch (error) {
      console.error('❌ Error loading recommendations:', error);
      this.loadPopularProducts();
    }
  }

  // Get current product ID from page
  getCurrentProductId() {
    // Try multiple methods to get product ID
    const urlPath = window.location.pathname;

    // Method 1: URL pattern /products/123
    const productMatch = urlPath.match(/\/products\/(\d+)/);
    if (productMatch) return parseInt(productMatch[1]);

    // Method 2: Data attribute on page
    const productElement = document.querySelector('[data-product-id]');
    if (productElement) return parseInt(productElement.dataset.productId);

    // Method 3: Meta tag
    const metaProduct = document.querySelector('meta[name="product-id"]');
    if (metaProduct) return parseInt(metaProduct.content);

    return null;
  }

  // Load popular products as fallback
  async loadPopularProducts() {
    try {
      const response = await fetch('/api/sync-wordpress-products?action=get&limit=4&orderby=popularity');
      const data = await response.json();

      if (data.success && data.products) {
        const container = document.getElementById('recommendations-container');
        if (container) {
          container.innerHTML = data.products.map(product => `
            <div class="bg-white p-4 rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer" onclick="window.location.href='/products/${product.id}'">
              <img src="${product.images?.[0]?.src || '/placeholder.jpg'}" alt="${product.name}" class="w-full h-32 object-cover rounded mb-2" loading="lazy">
              <h4 class="font-medium text-sm mb-1 line-clamp-2">${product.name}</h4>
              <p class="text-blue-600 font-semibold">£${product.price}</p>
              <button onclick="event.stopPropagation(); addToCartFromRecommendation(${product.id})" class="w-full mt-2 bg-blue-600 text-white py-1 px-2 rounded text-sm hover:bg-blue-700">
                Add to Cart
              </button>
            </div>
          `).join('');
        }
      }
    } catch (error) {
      console.error('Error loading popular products:', error);
    }
  }

  // Loyalty Program Feature with WooCommerce integration
  async enableLoyaltyProgram() {
    console.log('🎁 Enabling Loyalty Program...');

    if (!this.currentUser) {
      console.log('No user logged in, showing demo loyalty widget');
      this.showDemoLoyaltyWidget();
      return;
    }

    try {
      // Get real loyalty data from WooCommerce
      const loyaltyData = await this.wooCommerce.getLoyaltyPoints(this.currentUser.id);

      // Add loyalty points display with real data
      const loyaltyWidget = document.createElement('div');
      loyaltyWidget.id = 'loyalty-widget';
      loyaltyWidget.innerHTML = `
        <div class="fixed top-20 right-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-lg shadow-lg z-40 max-w-xs">
          <div class="text-center">
            <div class="flex items-center justify-between mb-2">
              <h4 class="font-semibold">Your Points</h4>
              <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">${loyaltyData.tier}</span>
            </div>
            <p class="text-3xl font-bold">${loyaltyData.points.toLocaleString()}</p>
            <p class="text-xs opacity-90">£${(loyaltyData.points / 100).toFixed(2)} value</p>

            <div class="mt-3 text-xs">
              <p>Total Spent: £${loyaltyData.totalSpent.toFixed(2)}</p>
              <p>Orders: ${loyaltyData.orderCount}</p>
              ${loyaltyData.nextTierThreshold ? `
                <p class="mt-1">Next tier: £${loyaltyData.nextTierThreshold - loyaltyData.totalSpent} to go</p>
              ` : ''}
            </div>

            <div class="mt-3 space-y-1">
              <button onclick="showLoyaltyDetails()" class="w-full bg-white text-purple-600 px-3 py-1 rounded text-sm font-medium hover:bg-opacity-90">
                View Benefits
              </button>
              <button onclick="redeemPoints()" class="w-full bg-white bg-opacity-20 text-white px-3 py-1 rounded text-sm hover:bg-opacity-30">
                Redeem Points
              </button>
            </div>

            <button onclick="closeLoyaltyWidget()" class="absolute top-1 right-2 text-white opacity-70 hover:opacity-100">×</button>
          </div>
        </div>
      `;

      document.body.appendChild(loyaltyWidget);

      // Add global functions for loyalty actions
      window.showLoyaltyDetails = () => this.showLoyaltyDetails(loyaltyData);
      window.redeemPoints = () => this.redeemPoints(loyaltyData);
      window.closeLoyaltyWidget = () => document.getElementById('loyalty-widget')?.remove();

      // Auto-hide after 10 seconds
      setTimeout(() => {
        const widget = document.getElementById('loyalty-widget');
        if (widget) {
          widget.style.opacity = '0.7';
          widget.style.transform = 'scale(0.9)';
        }
      }, 10000);

      console.log(`✅ Loyalty program loaded: ${loyaltyData.points} points (${loyaltyData.tier} tier)`);

    } catch (error) {
      console.error('❌ Error loading loyalty program:', error);
      this.showDemoLoyaltyWidget();
    }
  }

  // Show demo loyalty widget for non-logged-in users
  showDemoLoyaltyWidget() {
    const loyaltyWidget = document.createElement('div');
    loyaltyWidget.id = 'loyalty-widget';
    loyaltyWidget.innerHTML = `
      <div class="fixed top-20 right-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-lg shadow-lg z-40">
        <div class="text-center">
          <h4 class="font-semibold">Join Our Loyalty Program</h4>
          <p class="text-sm opacity-90 mt-1">Earn points with every purchase!</p>
          <div class="mt-3">
            <button onclick="window.location.href='/login'" class="bg-white text-purple-600 px-4 py-2 rounded text-sm font-medium">
              Sign In to Earn Points
            </button>
          </div>
          <button onclick="document.getElementById('loyalty-widget').remove()" class="absolute top-1 right-2 text-white opacity-70 hover:opacity-100">×</button>
        </div>
      </div>
    `;

    document.body.appendChild(loyaltyWidget);
  }

  // Show loyalty details modal
  showLoyaltyDetails(loyaltyData) {
    const modal = document.createElement('div');
    modal.innerHTML = `
      <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
          <h3 class="text-xl font-bold mb-4">${loyaltyData.tier} Tier Benefits</h3>
          <ul class="space-y-2 mb-4">
            ${loyaltyData.benefits.map(benefit => `<li class="flex items-center"><span class="text-green-500 mr-2">✓</span>${benefit}</li>`).join('')}
          </ul>
          <div class="bg-gray-50 p-3 rounded mb-4">
            <p class="text-sm"><strong>Your Stats:</strong></p>
            <p class="text-sm">Points: ${loyaltyData.points.toLocaleString()}</p>
            <p class="text-sm">Total Spent: £${loyaltyData.totalSpent.toFixed(2)}</p>
            <p class="text-sm">Orders: ${loyaltyData.orderCount}</p>
          </div>
          <button onclick="this.parentElement.parentElement.remove()" class="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">
            Close
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // Redeem points
  redeemPoints(loyaltyData) {
    if (loyaltyData.points < 100) {
      alert('You need at least 100 points to redeem (£1.00 value)');
      return;
    }

    const redeemAmount = prompt(`How many points would you like to redeem? (You have ${loyaltyData.points} points)`);
    if (redeemAmount && parseInt(redeemAmount) <= loyaltyData.points) {
      alert(`Redeeming ${redeemAmount} points for £${(parseInt(redeemAmount) / 100).toFixed(2)} discount!`);
      // Here you would integrate with your WooCommerce coupon system
    }
  }

  // Push Notifications Feature
  enablePushNotifications() {
    console.log('🔔 Enabling Push Notifications...');
    
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification('Deal4u Notifications Enabled!', {
            body: 'You\'ll now receive updates about deals and orders.',
            icon: '/favicon.ico'
          });
        }
      });
    }
    
    // Add notification preferences
    const notificationSettings = document.createElement('div');
    notificationSettings.innerHTML = `
      <div class="fixed bottom-20 right-4 bg-white p-4 rounded-lg shadow-lg border z-40" id="notification-settings">
        <h4 class="font-semibold mb-2">Notification Preferences</h4>
        <div class="space-y-2">
          <label class="flex items-center">
            <input type="checkbox" checked class="mr-2">
            <span class="text-sm">Order updates</span>
          </label>
          <label class="flex items-center">
            <input type="checkbox" checked class="mr-2">
            <span class="text-sm">Special offers</span>
          </label>
          <label class="flex items-center">
            <input type="checkbox" class="mr-2">
            <span class="text-sm">Back in stock alerts</span>
          </label>
        </div>
        <button onclick="this.parentElement.style.display='none'" class="mt-2 text-xs text-gray-500">
          Close
        </button>
      </div>
    `;
    
    document.body.appendChild(notificationSettings);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      const settings = document.getElementById('notification-settings');
      if (settings) settings.style.display = 'none';
    }, 5000);
  }

  // Social Commerce Feature
  enableSocialCommerce() {
    console.log('📱 Enabling Social Commerce...');
    
    // Add social sharing buttons to products
    const products = document.querySelectorAll('.product-item, .product-detail');
    
    products.forEach(product => {
      const socialButtons = document.createElement('div');
      socialButtons.className = 'social-share mt-2';
      socialButtons.innerHTML = `
        <div class="flex space-x-2">
          <button class="bg-blue-600 text-white px-2 py-1 rounded text-xs" onclick="shareOnFacebook()">
            📘 Share
          </button>
          <button class="bg-pink-600 text-white px-2 py-1 rounded text-xs" onclick="shareOnInstagram()">
            📷 Instagram
          </button>
          <button class="bg-blue-400 text-white px-2 py-1 rounded text-xs" onclick="shareOnTwitter()">
            🐦 Tweet
          </button>
        </div>
      `;
      
      product.appendChild(socialButtons);
    });
    
    // Add sharing functions to window
    window.shareOnFacebook = () => {
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank');
    };
    
    window.shareOnInstagram = () => {
      alert('Copy this link to share on Instagram: ' + window.location.href);
    };
    
    window.shareOnTwitter = () => {
      window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out this amazing product from Deal4u!`, '_blank');
    };
  }

  // One-Click Reorder Feature with WooCommerce integration
  async enableOneClickReorder() {
    console.log('⚡ Enabling One-Click Reorder...');

    if (!this.currentUser) {
      console.log('No user logged in, showing demo reorder buttons');
      this.addDemoReorderButtons();
      return;
    }

    try {
      // Get reorderable orders from WooCommerce
      const reorderableOrders = await this.wooCommerce.getReorderableOrders(this.currentUser.id, 10);

      if (reorderableOrders.length === 0) {
        console.log('No reorderable orders found');
        return;
      }

      // Add reorder buttons to existing order history elements
      const orderHistory = document.querySelectorAll('.order-item, .order-history-item, [data-order-id]');

      orderHistory.forEach(orderElement => {
        // Try to get order ID from element
        const orderId = orderElement.dataset.orderId ||
                       orderElement.querySelector('[data-order-id]')?.dataset.orderId ||
                       this.extractOrderIdFromElement(orderElement);

        if (orderId) {
          const orderData = reorderableOrders.find(order => order.id.toString() === orderId.toString());
          if (orderData) {
            this.addReorderButton(orderElement, orderData);
          }
        }
      });

      // Create a reorder section if no order history elements found
      if (orderHistory.length === 0) {
        this.createReorderSection(reorderableOrders);
      }

      console.log(`✅ One-click reorder enabled for ${reorderableOrders.length} orders`);

    } catch (error) {
      console.error('❌ Error enabling one-click reorder:', error);
      this.addDemoReorderButtons();
    }
  }

  // Add reorder button to order element
  addReorderButton(orderElement, orderData) {
    // Check if button already exists
    if (orderElement.querySelector('.reorder-button')) return;

    const reorderButton = document.createElement('button');
    reorderButton.className = 'reorder-button bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 ml-2 transition-colors';
    reorderButton.innerHTML = '⚡ Reorder';
    reorderButton.title = `Reorder ${orderData.itemCount} items from this order`;

    reorderButton.onclick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleReorder(orderData);
    };

    // Find the best place to insert the button
    const buttonContainer = orderElement.querySelector('.order-actions, .order-buttons') || orderElement;
    buttonContainer.appendChild(reorderButton);
  }

  // Handle reorder action
  async handleReorder(orderData) {
    try {
      console.log(`🛒 Reordering ${orderData.items.length} items from order #${orderData.id}...`);

      // Show loading state
      const button = event.target;
      const originalText = button.innerHTML;
      button.innerHTML = '⏳ Adding...';
      button.disabled = true;

      let addedItems = 0;
      let failedItems = 0;

      // Add each item to cart
      for (const item of orderData.items) {
        try {
          const result = await this.wooCommerce.addToCart(
            item.product_id,
            item.quantity,
            item.variation_id
          );

          if (result.success) {
            addedItems++;
          } else {
            failedItems++;
          }
        } catch (error) {
          console.error(`Failed to add item ${item.name}:`, error);
          failedItems++;
        }
      }

      // Show result
      if (addedItems > 0) {
        const message = failedItems > 0
          ? `Added ${addedItems} items to cart. ${failedItems} items were unavailable.`
          : `All ${addedItems} items added to cart successfully!`;

        this.showReorderSuccess(message, addedItems);

        // Update cart UI
        this.updateCartCount(addedItems);
      } else {
        alert('Sorry, none of the items from this order are currently available.');
      }

      // Restore button
      button.innerHTML = originalText;
      button.disabled = false;

    } catch (error) {
      console.error('❌ Error during reorder:', error);
      alert('Sorry, there was an error processing your reorder. Please try again.');

      // Restore button
      event.target.innerHTML = '⚡ Reorder';
      event.target.disabled = false;
    }
  }

  // Show reorder success message
  showReorderSuccess(message, itemCount) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
    notification.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <span class="text-2xl">✅</span>
        </div>
        <div class="ml-3">
          <p class="font-medium">${message}</p>
          <div class="mt-2 space-x-2">
            <button onclick="window.location.href='/cart'" class="bg-white text-green-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100">
              View Cart
            </button>
            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-green-100 hover:text-white text-sm">
              Dismiss
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  // Update cart count in header
  updateCartCount(addedItems) {
    const cartCountElements = document.querySelectorAll('.cart-count, [data-cart-count]');
    cartCountElements.forEach(element => {
      const currentCount = parseInt(element.textContent || '0');
      element.textContent = currentCount + addedItems;
    });
  }

  // Extract order ID from element
  extractOrderIdFromElement(element) {
    // Try various methods to extract order ID
    const text = element.textContent || '';
    const orderMatch = text.match(/#(\d+)/);
    if (orderMatch) return orderMatch[1];

    const href = element.querySelector('a')?.href || '';
    const urlMatch = href.match(/order[s]?\/(\d+)/);
    if (urlMatch) return urlMatch[1];

    return null;
  }

  // Add demo reorder buttons for non-logged-in users
  addDemoReorderButtons() {
    const orderHistory = document.querySelectorAll('.order-item, .order-history-item');

    orderHistory.forEach(order => {
      const reorderButton = document.createElement('button');
      reorderButton.className = 'bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 ml-2';
      reorderButton.textContent = '⚡ Reorder';
      reorderButton.onclick = () => {
        alert('Please log in to use the one-click reorder feature!');
      };

      order.appendChild(reorderButton);
    });
  }

  // Create reorder section if no order history found
  createReorderSection(reorderableOrders) {
    const reorderSection = document.createElement('div');
    reorderSection.className = 'fixed bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm z-40';
    reorderSection.innerHTML = `
      <h4 class="font-semibold mb-2">Quick Reorder</h4>
      <p class="text-sm text-gray-600 mb-3">Reorder from your recent purchases</p>
      <div class="space-y-2 max-h-40 overflow-y-auto">
        ${reorderableOrders.slice(0, 3).map(order => `
          <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div class="flex-1">
              <p class="text-sm font-medium">Order #${order.id}</p>
              <p class="text-xs text-gray-500">${order.itemCount} items - £${order.total}</p>
            </div>
            <button onclick="reorderFromSection(${order.id})" class="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700">
              Reorder
            </button>
          </div>
        `).join('')}
      </div>
      <button onclick="this.remove()" class="absolute top-1 right-2 text-gray-400 hover:text-gray-600">×</button>
    `;

    document.body.appendChild(reorderSection);

    // Add global reorder function
    window.reorderFromSection = (orderId) => {
      const orderData = reorderableOrders.find(order => order.id === orderId);
      if (orderData) {
        this.handleReorder(orderData);
      }
    };

    // Auto-hide after 10 seconds
    setTimeout(() => {
      reorderSection.style.opacity = '0.8';
    }, 10000);
  }

  // Voice Search Feature
  enableVoiceSearch() {
    console.log('🎤 Enabling Voice Search...');
    
    // Add voice search button to search bars
    const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
    
    searchInputs.forEach(input => {
      const voiceButton = document.createElement('button');
      voiceButton.className = 'absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-600';
      voiceButton.innerHTML = '🎤';
      voiceButton.onclick = () => this.startVoiceSearch(input);
      
      // Make parent relative if not already
      if (input.parentElement.style.position !== 'relative') {
        input.parentElement.style.position = 'relative';
      }
      
      input.parentElement.appendChild(voiceButton);
    });
  }

  // Start voice search
  startVoiceSearch(input) {
    if ('webkitSpeechRecognition' in window) {
      const recognition = new webkitSpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-GB';
      
      recognition.onstart = () => {
        input.placeholder = 'Listening...';
      };
      
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        input.value = transcript;
        input.placeholder = 'Search products...';
        
        // Trigger search
        const searchEvent = new Event('input', { bubbles: true });
        input.dispatchEvent(searchEvent);
      };
      
      recognition.onerror = () => {
        input.placeholder = 'Voice search failed. Try again.';
      };
      
      recognition.start();
    } else {
      alert('Voice search not supported in this browser');
    }
  }

  // AR Try-On Feature
  enableARTryOn() {
    console.log('📷 Enabling AR Try-On...');
    
    // Add AR buttons to clothing/accessory products
    const products = document.querySelectorAll('.product-item, .product-detail');
    
    products.forEach(product => {
      // Check if product is clothing/accessory (you can improve this logic)
      const productName = product.querySelector('h1, h2, h3, .product-title')?.textContent?.toLowerCase() || '';
      
      if (productName.includes('dress') || productName.includes('shirt') || productName.includes('watch') || productName.includes('glasses')) {
        const arButton = document.createElement('button');
        arButton.className = 'bg-purple-600 text-white px-4 py-2 rounded mt-2 hover:bg-purple-700';
        arButton.innerHTML = '📷 Try On with AR';
        arButton.onclick = () => {
          alert('AR Try-On feature coming soon! This will open your camera for virtual try-on.');
        };
        
        product.appendChild(arButton);
      }
    });
  }

  // Update feature status
  async updateFeature(featureName, enabled, settings = {}) {
    try {
      const response = await fetch('/api/admin/toggle-feature', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature: featureName, enabled, settings })
      });
      
      const data = await response.json();
      
      if (data.success) {
        this.features[featureName] = data.feature;
        
        // Re-apply features
        this.applyFeatures();
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error updating feature:', error);
      return false;
    }
  }
}

// Create global instance
const featuresManager = new FeaturesManager();

// Initialize when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => featuresManager.initialize());
  } else {
    featuresManager.initialize();
  }
}

export default featuresManager;
