// WooCommerce API Handler
class WooCommerceAPI {
    constructor() {
        this.baseURL = window.ENV.NEXT_PUBLIC_WOOCOMMERCE_URL;
        this.consumerKey = window.ENV.WOOCOMMERCE_CONSUMER_KEY;
        this.consumerSecret = window.ENV.WOOCOMMERCE_CONSUMER_SECRET;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    // Create authentication headers
    getAuthHeaders() {
        const credentials = btoa(`${this.consumerKey}:${this.consumerSecret}`);
        return {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/json',
        };
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}/wp-json/wc/v3/${endpoint}`;
        
        try {
            const response = await fetch(url, {
                headers: this.getAuthHeaders(),
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return { success: true, data };
        } catch (error) {
            console.error('WooCommerce API Error:', error);
            return { success: false, error: error.message };
        }
    }

    // Get products with caching
    async getProducts(params = {}) {
        const cacheKey = `products_${JSON.stringify(params)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        const defaultParams = {
            per_page: 20,
            status: 'publish',
            stock_status: 'instock',
            ...params
        };

        const queryString = new URLSearchParams(defaultParams).toString();
        const result = await this.request(`products?${queryString}`);
        
        // Cache successful results
        if (result.success) {
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
        }

        return result;
    }

    // Get featured products
    async getFeaturedProducts(limit = 8) {
        return this.getProducts({
            featured: true,
            per_page: limit,
            orderby: 'popularity',
            order: 'desc'
        });
    }

    // Get best selling products
    async getBestSellingProducts(limit = 8) {
        return this.getProducts({
            orderby: 'popularity',
            order: 'desc',
            per_page: limit
        });
    }

    // Get products by category
    async getProductsByCategory(categoryId, limit = 20) {
        return this.getProducts({
            category: categoryId,
            per_page: limit
        });
    }

    // Search products
    async searchProducts(query, limit = 20) {
        return this.getProducts({
            search: query,
            per_page: limit
        });
    }

    // Get single product
    async getProduct(productId) {
        const cacheKey = `product_${productId}`;
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        const result = await this.request(`products/${productId}`);
        
        if (result.success) {
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
        }

        return result;
    }

    // Get product categories
    async getCategories() {
        const cacheKey = 'categories';
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        const result = await this.request('products/categories?per_page=100');
        
        if (result.success) {
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
        }

        return result;
    }

    // Create order
    async createOrder(orderData) {
        return this.request('orders', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    }

    // Get customer orders
    async getCustomerOrders(customerId) {
        return this.request(`orders?customer=${customerId}`);
    }

    // Process product data for display
    processProductData(product) {
        // Replace AliExpress branding with Deal4u
        const brandingReplacements = {
            'AliExpress': 'Deal4u',
            'aliexpress': 'deal4u',
            'ALIEXPRESS': 'DEAL4U'
        };

        let name = product.name;
        let description = product.description || '';
        let shortDescription = product.short_description || '';

        // Apply branding replacements
        Object.entries(brandingReplacements).forEach(([old, replacement]) => {
            name = name.replace(new RegExp(old, 'g'), replacement);
            description = description.replace(new RegExp(old, 'g'), replacement);
            shortDescription = shortDescription.replace(new RegExp(old, 'g'), replacement);
        });

        // Get the best product image (avoid size charts)
        let imageUrl = '/placeholder-product.jpg';
        if (product.images && product.images.length > 0) {
            // Find the best image (not a size chart)
            const bestImage = product.images.find(img => 
                !img.alt?.toLowerCase().includes('size') &&
                !img.alt?.toLowerCase().includes('chart') &&
                !img.name?.toLowerCase().includes('size')
            ) || product.images[0];
            
            imageUrl = bestImage.src;
        }

        // Calculate discount percentage
        let discountPercentage = 0;
        if (product.regular_price && product.sale_price) {
            const regular = parseFloat(product.regular_price);
            const sale = parseFloat(product.sale_price);
            if (regular > sale) {
                discountPercentage = Math.round(((regular - sale) / regular) * 100);
            }
        }

        return {
            ...product,
            name,
            description,
            short_description: shortDescription,
            image: imageUrl,
            discount_percentage: discountPercentage,
            formatted_price: this.formatPrice(product.price),
            formatted_regular_price: this.formatPrice(product.regular_price),
            formatted_sale_price: this.formatPrice(product.sale_price)
        };
    }

    // Format price for display
    formatPrice(price) {
        if (!price) return '';
        const numPrice = parseFloat(price);
        return new Intl.NumberFormat('en-GB', {
            style: 'currency',
            currency: 'GBP'
        }).format(numPrice);
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
    }
}

// Create global instance
window.wooAPI = new WooCommerceAPI();
