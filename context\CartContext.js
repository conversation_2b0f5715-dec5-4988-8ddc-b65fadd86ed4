'use client';

import { createContext, useContext, useReducer, useEffect } from 'react';
import { toast } from 'react-hot-toast';

const CartContext = createContext();

// Cart reducer to manage state
const cartReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_ITEM': {
      const existingItem = state.items.find(item => item.id === action.payload.id);
      
      if (existingItem) {
        // Update existing item
        const updatedItems = state.items.map(item =>
          item.id === action.payload.id
            ? {
                ...item,
                quantity: item.quantity + (action.payload.quantity || 1)
              }
            : item
        );
        return { ...state, items: updatedItems };
      }
      
      // Add new item
      return {
        ...state,
        items: [...state.items, { ...action.payload, quantity: action.payload.quantity || 1 }]
      };
    }

    case 'REMOVE_ITEM': {
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload)
      };
    }

    case 'UPDATE_QUANTITY': {
      const { id, quantity } = action.payload;
      const parsedQuantity = parseInt(quantity);

      // Remove item if quantity is 0 or invalid
      if (!parsedQuantity || parsedQuantity <= 0) {
        return {
          ...state,
          items: state.items.filter(item => item.id !== id)
        };
      }

      // Update item quantity
      return {
        ...state,
        items: state.items.map(item =>
          item.id === id
            ? { ...item, quantity: parsedQuantity }
            : item
        )
      };
    }

    case 'CLEAR_CART': {
      return { ...state, items: [] };
    }

    case 'LOAD_CART': {
      return { ...state, items: action.payload || [] };
    }

    case 'SET_LOADING': {
      return { ...state, loading: action.payload };
    }

    default:
      return state;
  }
};

// Initial state
const initialState = {
  items: [],
  loading: false
};

export function CartProvider({ children }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedCart = localStorage.getItem('deal4u_cart');
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          if (Array.isArray(parsedCart)) {
            dispatch({ type: 'LOAD_CART', payload: parsedCart });
          }
        }
      } catch (error) {
        console.error('Failed to load cart:', error);
        localStorage.removeItem('deal4u_cart');
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('deal4u_cart', JSON.stringify(state.items));
      } catch (error) {
        console.error('Failed to save cart:', error);
      }
    }
  }, [state.items]);

  // Add item to cart
  const addToCart = (product, quantity = 1) => {
    try {
      // Validate product
      if (!product || !product.id) {
        toast.error('Invalid product');
        return;
      }

      // Parse quantity as integer
      const parsedQuantity = parseInt(quantity) || 1;

      // Check stock availability
      if (product.stockQuantity && product.stockQuantity > 0) {
        const currentQuantity = getItemQuantity(product.id);
        if (currentQuantity + parsedQuantity > product.stockQuantity) {
          toast.error('Not enough stock available');
          return;
        }
      }

      const existingItem = state.items.find(item => item.id === product.id);
      
      if (existingItem) {
        // Update existing item
        dispatch({
          type: 'UPDATE_QUANTITY',
          payload: {
            id: product.id,
            quantity: existingItem.quantity + parsedQuantity
          }
        });
      } else {
        // Add new item
        dispatch({
          type: 'ADD_ITEM',
          payload: {
            id: product.id,
            name: product.name,
            price: parseFloat(product.price) || 0,
            image: product.image,
            quantity: parsedQuantity,
            selectedVariant: product.selectedVariant,
            stockQuantity: product.stockQuantity,
            inStock: product.inStock
          }
        });
      }

      toast.success(`Added ${product.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    }
  };

  // Remove item from cart
  const removeFromCart = (productId) => {
    try {
      const item = state.items.find(item => item.id === productId);
      if (item) {
        dispatch({ type: 'REMOVE_ITEM', payload: productId });
        toast.success(`Removed ${item.name} from cart`);
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
      toast.error('Failed to remove from cart');
    }
  };

  // Update item quantity
  const updateQuantity = (productId, quantity) => {
    try {
      const item = state.items.find(item => item.id === productId);
      if (!item) {
        console.error('Item not found in cart');
        return;
      }

      // Parse and validate quantity
      const newQuantity = Math.max(0, parseInt(quantity) || 0);
      
      // If quantity is 0, remove the item
      if (newQuantity === 0) {
        removeFromCart(productId);
        return;
      }

      // Check stock availability
      if (item.stockQuantity && newQuantity > item.stockQuantity) {
        toast.error('Not enough stock available');
        return;
      }

      dispatch({
        type: 'UPDATE_QUANTITY',
        payload: { id: productId, quantity: newQuantity }
      });
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast.error('Failed to update quantity');
    }
  };

  // Clear entire cart
  const clearCart = () => {
    try {
      dispatch({ type: 'CLEAR_CART' });
      toast.success('Cart cleared');
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast.error('Failed to clear cart');
    }
  };

  // Get total price
  const getTotalPrice = () => {
    return state.items.reduce((total, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 0;
      return total + (price * quantity);
    }, 0);
  };

  // Get total items count
  const getTotalItems = () => {
    return state.items.reduce((total, item) => {
      return total + (parseInt(item.quantity) || 0);
    }, 0);
  };

  // Get specific item quantity
  const getItemQuantity = (productId) => {
    const item = state.items.find(item => item.id === productId);
    return item ? item.quantity : 0;
  };

  // Check if item is in cart
  const isInCart = (productId) => {
    return state.items.some(item => item.id === productId);
  };

  // Get cart item by ID
  const getCartItem = (productId) => {
    return state.items.find(item => item.id === productId);
  };

  // Calculate shipping cost
  const getShippingCost = () => {
    const total = getTotalPrice();
    const freeShippingThreshold = 50;
    return total >= freeShippingThreshold ? 0 : 5.99;
  };

  // Calculate tax (example: 8.5%)
  const getTax = () => {
    const taxRate = 0.085;
    return getTotalPrice() * taxRate;
  };

  // Get final total including shipping and tax
  const getFinalTotal = () => {
    return getTotalPrice() + getShippingCost() + getTax();
  };

  // Validate cart before checkout
  const validateCart = () => {
    const errors = [];

    if (state.items.length === 0) {
      errors.push('Cart is empty');
    }

    state.items.forEach(item => {
      if (!item.inStock) {
        errors.push(`${item.name} is out of stock`);
      }
      if (item.stockQuantity && item.quantity > item.stockQuantity) {
        errors.push(`Not enough stock for ${item.name}`);
      }
    });

    return errors;
  };

  const value = {
    // State
    items: state.items,
    loading: state.loading,
    
    // Actions
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    
    // Getters
    getTotalPrice,
    getTotalItems,
    getItemQuantity,
    isInCart,
    getCartItem,
    getShippingCost,
    getTax,
    getFinalTotal,
    
    // Utilities
    validateCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}

// Hook to use cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export default CartContext;