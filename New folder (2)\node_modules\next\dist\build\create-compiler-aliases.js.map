{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "names": ["createAppRouterApiAliases", "createNextApiEsmAliases", "createRSCAliases", "createServerOnlyClientOnlyAliases", "createWebpackAliases", "getOptimizedModuleAliases", "distDir", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "PAGES_DIR_ALIAS", "reduce", "prev", "ext", "push", "path", "join", "undefined", "hasExternalOtelApiPackage", "images", "loaderFile", "next", "NEXT_PROJECT_ROOT", "defaultOverrides", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "DOT_NEXT_ALIAS", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "dirname", "setimmediate", "isServer", "mapping", "head", "image", "constants", "router", "dynamic", "script", "link", "navigation", "headers", "og", "server", "document", "app", "aliasMap", "key", "value", "Object", "entries", "nextApiFilePath", "isServerOnlyLayer", "bundledReactChannel", "layer", "alias", "react$", "WEBPACK_LAYERS", "serverSideRendering", "assign", "reactServerComponents", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;;;;;IA6NgBA,yBAAyB;eAAzBA;;IA3BAC,uBAAuB;eAAvBA;;IA6CAC,gBAAgB;eAAhBA;;IAnEAC,iCAAiC;eAAjCA;;IAvJAC,oBAAoB;eAApBA;;IA8SAC,yBAAyB;eAAzBA;;;6DAnUC;2BAWV;6BAE0B;+BAC4B;;;;;;AAOtD,SAASD,qBAAqB,EACnCE,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAaZ;IACC,MAAMC,iBAAiBN,OAAOM,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIZ,KAAK;QACP,MAAMa,eAAe,eAAgBf,CAAAA,eAAe,SAAS,EAAC;QAC9Da,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,KAAK,CAAC,CAAC,GAAG;eACxCZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,KAAK,EAAEe,IAAI,CAAC;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,CAAC,EAAEG,0BAAe,CAAC,OAAO,CAAC,CAAC,GAAG;eAC1CZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,OAAO,EAAEe,IAAI,CAAC;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,CAAC,EAAEE,0BAAe,CAAC,UAAU,CAAC,CAAC,GAAG;eAClDZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,UAAU,EAAEe,IAAI,CAAC;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEH,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,eAAe;QAEf,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIf,eACA;YACE,iBAAiB;YACjB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,GAAGP,yBAAyB;QAC9B,IACA8B,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAACC,IAAAA,wCAAyB,OAAM;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIrB,OAAOsB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCvB,OAAOsB,MAAM,CAACC,UAAU;YAC7D,GAAI1B,gBAAgB;gBAClB,yCAAyCG,OAAOsB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAH,SAAS;QAEbI,MAAMC,gCAAiB;QAEvB,qBAAqBC,6BAAgB,CAAC,mBAAmB;QACzD,eAAeA,6BAAgB,CAAC,aAAa;QAE7C,GAAGhB,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIV,WAAW;YAAE,CAACY,0BAAe,CAAC,EAAEZ;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAACyB,wBAAa,CAAC,EAAEzB;QAAO,IAAI,CAAC,CAAC;QAC7C,CAAC0B,yBAAc,CAAC,EAAEzB;QAClB,CAAC0B,yBAAc,CAAC,EAAElC;QAClB,GAAIC,YAAYC,eAAeH,8BAA8B,CAAC,CAAC;QAC/D,GAAIU,2BAA2B0B,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAIhC,eACAiC,6BACE/B,OAAOgC,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACC,oCAAyB,CAAC,EACzB;QAEF,CAACC,0CAA+B,CAAC,EAC/B;QAEF,CAACC,iCAAsB,CAAC,EACtB;QAEF,CAACC,sCAA2B,CAAC,EAAE;QAE/B,GAAIzC,YAAYC,eACZ;YACE,CAACU,sBAAsB,EAAEF,cACrBE,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkBW,aAAI,CAACC,IAAI,CACzBD,aAAI,CAACoB,OAAO,CAAC9B,QAAQC,OAAO,CAAC,+BAC7B;QAGF8B,cAAc;IAChB;AACF;AAEO,SAAS/C,kCACdgD,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEO,SAASlD;IACd,MAAMmD,UAAU;QACdC,MAAM;QACNC,OAAO;QACPC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,MAAM;QACNC,YAAY;QACZC,SAAS;QACTC,IAAI;QACJC,QAAQ;QACR,YAAY;QACZC,UAAU;QACVC,KAAK;IACP;IACA,MAAMC,WAAmC,CAAC;IAC1C,sDAAsD;IACtD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAClB,SAAU;QAClD,MAAMmB,kBAAkB1C,aAAI,CAACC,IAAI,CAACM,gCAAiB,EAAE+B;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IAEA,OAAOF;AACT;AAEO,SAASlE,0BAA0BwE,iBAA0B;IAClE,MAAMpB,UAAkC;QACtCC,MAAM;QACNI,SAAS;IACX;IAEA,IAAIe,mBAAmB;QACrBpB,OAAO,CAAC,aAAa,GAAG;IAC1B;IAEA,MAAMc,WAAmC,CAAC;IAC1C,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAClB,SAAU;QAClD,MAAMmB,kBAAkB1C,aAAI,CAACC,IAAI,CAACM,gCAAiB,EAAE+B;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IACA,OAAOF;AACT;AAEO,SAAShE,iBACduE,mBAA2B,EAC3B,EACEC,KAAK,EACLlE,YAAY,EACZO,wBAAwB,EAKzB;IAED,IAAI4D,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEH,oBAAoB,CAAC;QACxD,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,CAAC;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,gDAAgD,CAAC;QACvE,0BAA0B,CAAC,qDAAqD,CAAC;QACjF,6BAA6B,CAAC,wDAAwD,CAAC;QACvF,yFAAyF;QACzF,0BAA0B,CAAC,mDAAmD,EAAEA,oBAAoB,GAAG,CAAC;QACxG,6BAA6B,CAAC,sDAAsD,EAAEA,oBAAoB,GAAG,CAAC;QAC9G,iCAAiC;QACjC,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;IAC1H;IAEA,IAAI,CAACjE,cAAc;QACjB,IAAIkE,UAAUG,yBAAc,CAACC,mBAAmB,EAAE;YAChDH,QAAQN,OAAOU,MAAM,CAACJ,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF,OAAO,IAAIA,UAAUG,yBAAc,CAACG,qBAAqB,EAAE;YACzDL,QAAQN,OAAOU,MAAM,CAACJ,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;gBAChJ,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF;IACF;IAEA,IAAIlE,cAAc;QAChB,IAAIkE,UAAUG,yBAAc,CAACG,qBAAqB,EAAE;YAClDL,KAAK,CACH,SACD,GAAG,CAAC,wBAAwB,EAAEF,oBAAoB,mBAAmB,CAAC;YACvEE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,uBAAuB,CAAC;QACjF,OAAO;YACL,sDAAsD;YACtDE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,sBAAsB,CAAC;QAChF;IACF;IAEA,IAAI1D,0BAA0B;QAC5B4D,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,UAAU,CAAC;IACpE;IAEAE,KAAK,CACH,gEACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAIO,SAAStE;IACd,OAAO;QACL4E,SAAS9D,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF8D,KAAK/D,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASsB,6BAA6ByC,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsBpE,QAAQ,CAAC,EAAEmE,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsBrE,QAAQC,OAAO,CAAC,CAAC,EAAEkE,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAGzD,aAAI,CAACC,IAAI,CAC5BD,aAAI,CAACoB,OAAO,CAACuC,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAAS3C;IACP,OAAO;QACL,cAAc;IAChB;AACF"}