'use client';

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { X, Star, Heart, ShoppingCart, Minus, Plus, Check, ChevronRight } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { toast } from 'react-hot-toast';

const QuickViewModal = ({ product, onClose }) => {
  const modalRef = useRef(null);
  const { addToCart, isInCart, getItemQuantity, updateQuantity } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [selectedAttributes, setSelectedAttributes] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  const handleQuantityChange = (amount) => {
    const newQuantity = Math.max(1, quantity + amount);
    if (product.stockQuantity && newQuantity > product.stockQuantity) {
      toast.error('Not enough stock available');
      return;
    }
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    try {
      setIsLoading(true);

      // Check stock status
      const isInStock = product.inStock || product.stockStatus === 'instock';
      if (!isInStock) {
        toast.error('Product is out of stock');
        return;
      }

      // Check stock availability
      if (product.stockQuantity && product.stockQuantity > 0) {
        const currentQuantity = getItemQuantity(product.id);
        if (currentQuantity + quantity > product.stockQuantity) {
          toast.error('Not enough stock available');
          return;
        }
      }

      // Prepare the item to add to cart
      const itemToAdd = {
        id: product.id,
        name: product.name,
        price: parseFloat(product.price) || 0,
        image: product.images?.[0]?.src || product.image || '/placeholder.jpg',
        quantity: quantity,
        stockQuantity: product.stockQuantity || 0,
        inStock: product.inStock
      };

      // If a variant is selected, include that information
      if (selectedVariant) {
        itemToAdd.selectedVariant = selectedVariant;
        itemToAdd.name = `${product.name} - ${selectedVariant.name || 'Selected Option'}`;
        itemToAdd.price = parseFloat(selectedVariant.price) || itemToAdd.price;
      }

      await addToCart(itemToAdd, quantity);
      toast.success(`Added ${itemToAdd.name} to cart`);
      onClose();
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Format price with currency
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Get product image with proper fallback
  const getProductImage = (image) => {
    if (imageError) return '/placeholder.jpg';
    
    // If image is already a string URL, use it directly
    if (typeof image === 'string') return image;
    
    // Check for WooCommerce image object
    if (image && image.src) return image.src;
    
    return '/placeholder.jpg';
  };

  const currentPrice = selectedVariant ? selectedVariant.price : product.price;
  const regularPrice = selectedVariant ? selectedVariant.regular_price : product.regular_price;
  const salePrice = selectedVariant ? selectedVariant.sale_price : product.sale_price;
  const isOnSale = regularPrice > currentPrice;
  // Only show variations if they have real attributes or meaningful names
  const hasVariants = product.variations &&
    product.variations.length > 0 &&
    product.variations.some(variant => {
      // Check if variant has real attributes
      if (variant.attributes && variant.attributes.length > 0) {
        return variant.attributes.some(attr =>
          attr.name && attr.option &&
          attr.name.trim() !== '' &&
          attr.option.trim() !== '' &&
          attr.option !== 'Option'
        );
      }
      // Check if variant has a meaningful name
      return variant.name &&
        variant.name.trim() !== '' &&
        variant.name !== 'Option' &&
        !variant.name.startsWith('Option');
    });
  const isInStock = product.inStock || product.stockStatus === 'instock';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm">
      <div
        ref={modalRef}
        className="relative bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-y-auto"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 p-8">
          {/* Enhanced Product Image - Larger */}
          <div className="lg:col-span-2">
            <div className="relative aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg">
              <Image
                src={getProductImage(product.images?.[0] || product.image)}
                alt={product.name}
                fill
                className="object-cover hover:scale-105 transition-transform duration-500"
                onError={() => setImageError(true)}
              />

              {/* Discount Badge */}
              {isOnSale && (
                <div className="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                  -{Math.round(((regularPrice - currentPrice) / regularPrice) * 100)}% OFF
                </div>
              )}

              {/* Stock Status Badge */}
              <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium shadow-lg ${
                isInStock
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {isInStock ? 'In Stock' : 'Out of Stock'}
              </div>
            </div>
          </div>

          {/* Enhanced Product Info */}
          <div className="flex flex-col space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2 leading-tight">{product.name}</h2>
              <p className="text-gray-600 text-sm">SKU: {product.sku || 'N/A'}</p>
            </div>

            {/* Enhanced Rating */}
            <div className="flex items-center space-x-3">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating || 0) ? 'fill-current' : ''
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600 font-medium">
                {product.rating || 0}/5 ({product.reviews || 0} reviews)
              </span>
            </div>

            {/* Enhanced Price Display */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold text-blue-600">
                  {formatPrice(currentPrice)}
                </span>
                {isOnSale && (
                  <>
                    <span className="text-xl text-gray-500 line-through">
                      {formatPrice(regularPrice)}
                    </span>
                    <span className="bg-red-500 text-white px-2 py-1 rounded-full text-sm font-bold">
                      Save {formatPrice(regularPrice - currentPrice)}
                    </span>
                  </>
                )}
              </div>
              {isOnSale && (
                <p className="text-sm text-green-600 font-medium mt-1">
                  You save {Math.round(((regularPrice - currentPrice) / regularPrice) * 100)}%!
                </p>
              )}
            </div>

            {/* Enhanced Description */}
            <div className="bg-gray-50 p-4 rounded-xl">
              <h3 className="font-semibold text-gray-900 mb-2">Product Description</h3>
              <div className="prose prose-sm text-gray-700 max-h-32 overflow-y-auto">
                <div dangerouslySetInnerHTML={{
                  __html: product.description?.length > 200
                    ? product.description.substring(0, 200) + '...'
                    : product.description
                }} />
              </div>
              <Link
                href={`/shop/product/${product.id}/`}
                onClick={onClose}
                className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium mt-2"
              >
                View Full Details <ChevronRight className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* Note: Variations hidden in quick view for simplicity */}
            {/* For full product options, click "View Full Details" */}

            {/* Quantity Selector */}
            <div className="flex items-center mb-6">
              <span className="mr-4 text-gray-700">Quantity:</span>
              <div className="flex items-center border rounded-lg">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  className="p-2 hover:bg-gray-100 rounded-l-lg"
                  disabled={quantity <= 1 || isLoading}
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="px-4 py-2 text-center min-w-[3rem]">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  className="p-2 hover:bg-gray-100 rounded-r-lg"
                  disabled={isLoading || (product.stockQuantity && quantity >= product.stockQuantity)}
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={handleAddToCart}
                disabled={!isInStock || isLoading}
                className={`w-full py-4 px-6 rounded-xl flex items-center justify-center gap-2 text-white font-bold text-lg transition-all transform hover:scale-105 shadow-lg ${
                  isInStock
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'
                    : 'bg-gray-400 cursor-not-allowed'
                }`}
              >
                <ShoppingCart className="w-6 h-6" />
                {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
              </button>

              <Link
                href={`/shop/product/${product.id}/`}
                onClick={onClose}
                className="w-full py-3 px-6 rounded-xl flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold transition-all border border-gray-300"
              >
                View Full Product Details
                <ChevronRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickViewModal;
