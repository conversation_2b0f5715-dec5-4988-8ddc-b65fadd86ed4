'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { 
  Star, 
  Heart, 
  ShoppingCart, 
  Eye, 
  Plus,
  Check
} from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { StarRatingDisplay } from '@/components/reviews/StarRating';
import { toast } from 'react-hot-toast';
import dynamic from 'next/dynamic';

// Import QuickViewModal with dynamic loading to prevent hydration issues
const QuickViewModal = dynamic(() => import('./QuickViewModal'), { ssr: false });

const ProductCard = ({ product, viewMode = 'grid' }) => {
  const { addToCart, isInCart, getItemQuantity } = useCart();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!product.inStock) {
      toast.error('Product is out of stock');
      return;
    }

    setIsLoading(true);
    
    try {
      const itemToAdd = {
        id: product.id,
        name: product.name,
        price: parseFloat(product.price) || 0,
        image: product.images?.[0] || product.image || '/placeholder.jpg',
        quantity: 1,
        stockQuantity: product.stockQuantity || 0,
        inStock: product.inStock
      };

      addToCart(itemToAdd, 1);
      toast.success(`Added ${product.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleWishlist = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsWishlisted(!isWishlisted);
    toast.success(
      isWishlisted ? 'Removed from wishlist' : 'Added to wishlist'
    );
  };

  const handleQuickView = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsQuickViewOpen(true);
  };
  
  const handleCloseQuickView = () => {
    setIsQuickViewOpen(false);
  };

  // Calculate discount percentage
  const discountPercentage = product.originalPrice && product.price && product.originalPrice > product.price
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  // Get product image with fallback - handle both URL arrays and object arrays
  const productImage = imageError
    ? '/placeholder.jpg'
    : product.image ||
      (product.images && product.images.length > 0 &&
       (typeof product.images[0] === 'string' ? product.images[0] : product.images[0].src)) ||
      '/placeholder.jpg';

  if (viewMode === 'list') {
    return (
      <Link href={`/shop/product/${product.id}/`}>
        <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
          <div className="flex p-6">
            {/* Product Image */}
            <div className="relative flex-shrink-0 w-32 h-32 mr-6">
              <Image
                src={productImage}
                alt={product.name}
                fill
                sizes="(max-width: 128px) 100vw, 128px"
                className="object-cover rounded-lg"
                onError={() => setImageError(true)}
              />
              {discountPercentage > 0 && (
                <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
                  -{discountPercentage}%
                </span>
              )}
            </div>

            {/* Product Info */}
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
              <div className="flex items-center mb-2">
                <span className="text-lg font-bold text-gray-900">
                  ${parseFloat(product.price).toFixed(2)}
                </span>
                {discountPercentage > 0 && (
                  <span className="ml-2 text-sm text-gray-500 line-through">
                    ${parseFloat(product.originalPrice).toFixed(2)}
                  </span>
                )}
              </div>
              <p className="text-gray-600 text-sm line-clamp-2">{product.short_description}</p>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  // Grid view (default)
  return (
    <>
    <div className="group relative bg-white rounded-xl shadow-sm overflow-hidden transition-shadow hover:shadow-md h-full">
      {/* Product Image - Clickable area */}
      <Link href={`/shop/product/${product.id}/`} className="block">
        <div className="relative aspect-square">
          <Image
            src={productImage}
            alt={product.name}
            fill
            className="object-cover transition-transform group-hover:scale-105"
            onError={() => setImageError(true)}
          />

          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
              -{discountPercentage}%
            </div>
          )}
        </div>
      </Link>

      {/* Action Buttons - Outside Link */}
      <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
        <button
          onClick={handleToggleWishlist}
          className={`p-2 rounded-full shadow-sm transition-colors ${
            isWishlisted
              ? 'bg-red-500 text-white'
              : 'bg-white text-gray-600 hover:bg-red-500 hover:text-white'
          }`}
        >
          <Heart className="w-4 h-4" fill={isWishlisted ? 'currentColor' : 'none'} />
        </button>
        <button
          onClick={handleQuickView}
          className="p-2 rounded-full bg-white text-gray-600 shadow-sm hover:bg-blue-500 hover:text-white transition-colors"
        >
          <Eye className="w-4 h-4" />
        </button>
      </div>

      {/* Product Info - Clickable area */}
      <Link href={`/shop/product/${product.id}/`} className="block">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
            {product.name}
          </h3>

          {/* Rating */}
          <div className="mt-2">
            <StarRatingDisplay
              rating={product.rating || 0}
              totalReviews={product.reviews || 0}
              size="sm"
              showCount={true}
            />
          </div>

          {/* Price */}
          <div className="mt-2 flex items-center gap-2">
            <span className="text-lg font-bold text-gray-900">
              ${parseFloat(product.price || 0).toFixed(2)}
            </span>
            {discountPercentage > 0 && (
              <span className="text-sm text-gray-500 line-through">
                ${parseFloat(product.originalPrice || 0).toFixed(2)}
              </span>
            )}
          </div>
        </div>
      </Link>

      {/* Add to Cart Button - Outside Link */}
      <div className="p-4 pt-0">
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock || isLoading}
          className={`w-full py-2 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors ${
            isInCart(product.id)
              ? 'bg-green-100 text-green-700 hover:bg-green-200'
              : product.inStock
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <span>Adding...</span>
          ) : isInCart(product.id) ? (
            <>
              <Check className="w-5 h-5" />
              In Cart ({getItemQuantity(product.id)})
            </>
          ) : product.inStock ? (
            <>
              <ShoppingCart className="w-5 h-5" />
              Add to Cart
            </>
          ) : (
            'Out of Stock'
          )}
        </button>
      </div>
    </div>

    {/* Quick View Modal */}
    {isQuickViewOpen && (
      <QuickViewModal
        product={product}
        onClose={handleCloseQuickView}
      />
    )}
    </>
  );
};

export default ProductCard;