{"name": "@woocommerce/woocommerce-rest-api", "version": "1.0.1", "description": "WooCommerce REST API - JavaScript Library", "author": "Automattic", "license": "MIT", "keywords": ["wordpress", "woocommerce", "rest", "promise", "node"], "homepage": "https://github.com/woocommerce/woocommerce-rest-api-js-lib", "repository": {"type": "git", "url": "git+https://github.com/woocommerce/woocommerce-rest-api-js.git"}, "bugs": {"url": "https://github.com/woocommerce/woocommerce-rest-api-js/issues"}, "main": "index", "files": ["index.js", "index.mjs"], "dependencies": {"axios": "^0.19.0", "create-hmac": "^1.1.7", "oauth-1.0a": "^2.2.6", "url-parse": "^1.4.7"}, "devDependencies": {"@babel/cli": "7.5.5", "@babel/core": "7.5.5", "@babel/plugin-transform-modules-commonjs": "7.5.0", "@babel/preset-env": "7.5.5", "babel-jest": "24.8.0", "del-cli": "2.0.0", "eslint": "6.1.0", "eslint-config-prettier": "6.0.0", "eslint-config-standard": "13.0.1", "eslint-plugin-import": "2.18.2", "eslint-plugin-jest": "22.14.1", "eslint-plugin-node": "9.1.0", "eslint-plugin-prettier": "3.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.0", "husky": "3.0.2", "jest": "24.8.0", "lint-staged": "9.2.1", "nock": "10.0.6", "prettier": "1.18.2"}, "scripts": {"build": "del index.js && babel index.mjs --out-dir .", "test": "jest", "format": "prettier --write \"*.mjs\" \"test.js\"", "lint": "eslint *.mjs", "prepare": "npm run build", "prepublishOnly": "npm test && npm run lint", "preversion": "npm run lint", "version": "npm run format && git add -A", "postversion": "git push && git push --tags"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,mjs}": ["eslint --fix", "prettier --write", "git add"]}, "engines": {"node": ">=8.0.0"}}