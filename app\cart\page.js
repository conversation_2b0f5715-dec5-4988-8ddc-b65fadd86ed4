'use client';

import { useCart } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft, Lock, Truck, RefreshCw, User, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function CartPage() {
  const router = useRouter();
  const {
    items,
    updateQuantity,
    removeFromCart,
    getTotalPrice,
    getTotalItems,
    getShippingCost,
    getTax,
    getFinalTotal,
    validateCart
  } = useCart();

  const { user, isAuthenticated } = useAuth();

  const [promoCode, setPromoCode] = useState('');
  const [promoDiscount, setPromoDiscount] = useState(0);
  const [isApplyingPromo, setIsApplyingPromo] = useState(false);

  // Check if user profile is complete for checkout
  const validateUserProfile = () => {
    if (!isAuthenticated || !user) {
      return {
        isComplete: false,
        missingFields: [],
        requiresLogin: true
      };
    }

    const requiredFields = [
      { key: 'firstName', label: 'First Name', value: user.firstName },
      { key: 'lastName', label: 'Last Name', value: user.lastName },
      { key: 'email', label: 'Email', value: user.email },
      { key: 'phone', label: 'Phone Number', value: user.phone },
    ];

    const addressFields = [
      { key: 'address.line1', label: 'Address', value: user.address?.line1 },
      { key: 'address.city', label: 'City', value: user.address?.city },
      { key: 'address.state', label: 'State', value: user.address?.state },
      { key: 'address.zipCode', label: 'ZIP Code', value: user.address?.zipCode },
      { key: 'address.country', label: 'Country', value: user.address?.country },
    ];

    const allFields = [...requiredFields, ...addressFields];
    const missingFields = allFields.filter(field => !field.value || field.value.trim() === '');

    return {
      isComplete: missingFields.length === 0,
      missingFields: missingFields.map(field => field.label),
      requiresLogin: false
    };
  };

  // Handle checkout button click
  const handleCheckoutClick = (e) => {
    e.preventDefault();

    // First check cart validation
    if (cartErrors.length > 0) {
      return;
    }

    const profileValidation = validateUserProfile();

    if (profileValidation.requiresLogin) {
      // Redirect to login with return URL
      router.push('/login?redirect=/cart/checkout');
      return;
    }

    if (!profileValidation.isComplete) {
      // Redirect to account page to complete profile
      router.push('/account?complete=true&redirect=/cart/checkout');
      return;
    }

    // Profile is complete, proceed to checkout
    router.push('/cart/checkout');
  };

  // Handle promo code application (mock functionality)
  const handleApplyPromo = async () => {
    setIsApplyingPromo(true);
    
    // Mock promo code validation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (promoCode.toLowerCase() === 'save10') {
      setPromoDiscount(getTotalPrice() * 0.1); // 10% discount
    } else if (promoCode.toLowerCase() === 'welcome20') {
      setPromoDiscount(Math.min(getTotalPrice() * 0.2, 50)); // 20% discount, max $50
    } else {
      setPromoDiscount(0);
    }
    
    setIsApplyingPromo(false);
  };

  // Calculate final totals
  const subtotal = getTotalPrice();
  const shipping = getShippingCost();
  const tax = getTax();
  const discount = promoDiscount;
  const finalTotal = subtotal + shipping + tax - discount;

  // Validate cart for checkout
  const cartErrors = validateCart();

  if (items.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <ShoppingBag className="mx-auto h-24 w-24 text-gray-400" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">Your cart is empty</h2>
          <p className="mt-4 text-lg text-gray-600">
            Start shopping to add items to your cart.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/shop"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Continue Shopping
            </Link>
            <Link
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
          <p className="text-gray-600 mt-2">
            {getTotalItems()} {getTotalItems() === 1 ? 'item' : 'items'} in your cart
          </p>
        </div>
        <Link
          href="/shop"
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Continue Shopping
        </Link>
      </div>

      <div className="lg:grid lg:grid-cols-12 lg:gap-x-12 lg:items-start">
        {/* Cart Items */}
        <div className="lg:col-span-7">
          <div className="bg-white rounded-lg shadow-md">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Cart Items</h2>
            </div>
            
            <div className="divide-y divide-gray-200">
              {items.map((item) => (
                <div key={item.id} className="p-6 flex">
                  {/* Product Image */}
                  <div className="flex-shrink-0">
                    <Image
                      src={item.image || item.images?.[0] || '/placeholder.jpg'}
                      alt={item.name}
                      width={120}
                      height={120}
                      className="w-20 h-20 sm:w-24 sm:h-24 rounded-md object-cover"
                    />
                  </div>

                  {/* Product Details */}
                  <div className="ml-4 flex-1 flex flex-col">
                    <div>
                      <div className="flex justify-between text-base font-medium text-gray-900">
                        <h3>
                          <Link 
                            href={`/shop/product/${item.id ? (parseInt(item.id) || 1) : 1}`}
                            className="hover:text-blue-600 transition-colors"
                          >
                            {item.name}
                          </Link>
                        </h3>
                        <p className="ml-4 font-semibold">
                          ${(item.price * item.quantity).toFixed(2)}
                        </p>
                      </div>
                      
                      <p className="mt-1 text-sm text-gray-600">
                        ${item.price} each
                      </p>
                      
                      {item.category && (
                        <p className="mt-1 text-sm text-gray-500">
                          Category: {item.category}
                        </p>
                      )}

                      {/* Stock Status */}
                      {item.stockQuantity && item.stockQuantity < 10 && (
                        <p className="mt-1 text-sm text-orange-600">
                          Only {item.stockQuantity} left in stock
                        </p>
                      )}
                    </div>
                    
                    {/* Quantity Controls and Remove Button */}
                    <div className="flex-1 flex items-end justify-between mt-4">
                      <div className="flex items-center border border-gray-300 rounded-md">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-2 hover:bg-gray-100 transition-colors disabled:opacity-50"
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                        <span className="px-4 py-2 text-gray-900 font-medium min-w-[3rem] text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-2 hover:bg-gray-100 transition-colors disabled:opacity-50"
                          disabled={item.stockQuantity && item.quantity >= item.stockQuantity}
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>

                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="font-medium text-red-600 hover:text-red-500 flex items-center transition-colors"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Promo Code */}
          <div className="mt-6 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Promo Code</h3>
            <div className="flex space-x-4">
              <input
                type="text"
                placeholder="Enter promo code"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                className="flex-1 border border-gray-300 rounded-md px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleApplyPromo}
                disabled={!promoCode || isApplyingPromo}
                className="px-6 py-2 bg-gray-900 text-white font-medium rounded-md hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isApplyingPromo ? 'Applying...' : 'Apply'}
              </button>
            </div>
            {discount > 0 && (
              <p className="mt-2 text-sm text-green-600">
                Promo code applied! You saved ${discount.toFixed(2)}
              </p>
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div className="mt-16 lg:mt-0 lg:col-span-5">
          <div className="bg-white rounded-lg shadow-md sticky top-24">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <dt className="text-sm text-gray-600">
                    Subtotal ({getTotalItems()} items)
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    ${subtotal.toFixed(2)}
                  </dd>
                </div>
                
                {discount > 0 && (
                  <div className="flex items-center justify-between">
                    <dt className="text-sm text-gray-600">Discount</dt>
                    <dd className="text-sm font-medium text-green-600">
                      -${discount.toFixed(2)}
                    </dd>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <dt className="text-sm text-gray-600">Shipping</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {shipping === 0 ? (
                      <span className="text-green-600">Free</span>
                    ) : (
                      `$${shipping.toFixed(2)}`
                    )}
                  </dd>
                </div>
                
                <div className="flex items-center justify-between">
                  <dt className="text-sm text-gray-600">Tax</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    ${tax.toFixed(2)}
                  </dd>
                </div>
                
                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                  <dt className="text-lg font-medium text-gray-900">Total</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    ${finalTotal.toFixed(2)}
                  </dd>
                </div>
              </div>

              {/* Shipping Info */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center text-blue-700">
                  <Truck className="w-5 h-5 mr-2" />
                  <span className="text-sm font-medium">
                    {shipping === 0 
                      ? 'Free shipping on this order!' 
                      : `Add $${(50 - subtotal).toFixed(2)} more for free shipping`
                    }
                  </span>
                </div>
              </div>

              {/* Authentication & Profile Status */}
              {(() => {
                const profileValidation = validateUserProfile();

                return (
                  <div className="mt-6">
                    {/* Cart Errors */}
                    {cartErrors.length > 0 && (
                      <div className="mb-4">
                        {cartErrors.map((error, index) => (
                          <p key={index} className="text-sm text-red-600 mb-1 flex items-center">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {error}
                          </p>
                        ))}
                      </div>
                    )}

                    {/* Authentication Status */}
                    {profileValidation.requiresLogin && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div className="flex items-center text-blue-700">
                          <User className="w-4 h-4 mr-2" />
                          <span className="text-sm font-medium">Sign in required</span>
                        </div>
                        <p className="text-sm text-blue-600 mt-1">
                          Please sign in to your account to proceed with checkout.
                        </p>
                      </div>
                    )}

                    {/* Profile Completion Status */}
                    {!profileValidation.requiresLogin && !profileValidation.isComplete && (
                      <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                        <div className="flex items-center text-orange-700">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          <span className="text-sm font-medium">Complete your profile</span>
                        </div>
                        <p className="text-sm text-orange-600 mt-1">
                          Please complete the following information: {profileValidation.missingFields.join(', ')}
                        </p>
                      </div>
                    )}

                    {/* Checkout Button */}
                    <button
                      onClick={handleCheckoutClick}
                      disabled={cartErrors.length > 0}
                      className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white transition-colors ${
                        cartErrors.length > 0
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                      }`}
                    >
                      <Lock className="w-4 h-4 mr-2" />
                      {profileValidation.requiresLogin
                        ? 'Sign In to Checkout'
                        : !profileValidation.isComplete
                          ? 'Complete Profile & Checkout'
                          : 'Proceed to Checkout'
                      }
                    </button>
                  </div>
                );
              })()}

              {/* Security Badge */}
              <div className="mt-4 flex items-center justify-center text-sm text-gray-500">
                <Lock className="w-4 h-4 mr-1" />
                <span>Secure checkout with SSL encryption</span>
              </div>

              {/* Return Policy */}
              <div className="mt-6 text-center text-sm text-gray-500">
                <div className="flex items-center justify-center mb-2">
                  <RefreshCw className="w-4 h-4 mr-1" />
                  <span>30-day return policy</span>
                </div>
                <Link href="/faq" className="text-blue-600 hover:text-blue-500">
                  View return policy
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}