(()=>{var e={6474:(e,r,t)=>{"use strict";var a=t(9258),s=t(2603),i=t(7731),o=t(2961),n=t(6424),l=t(2015),f=t(9233),c=t(1966),u=t(8716);e.exports=Ajv;Ajv.prototype.validate=validate;Ajv.prototype.compile=compile;Ajv.prototype.addSchema=addSchema;Ajv.prototype.addMetaSchema=addMetaSchema;Ajv.prototype.validateSchema=validateSchema;Ajv.prototype.getSchema=getSchema;Ajv.prototype.removeSchema=removeSchema;Ajv.prototype.addFormat=addFormat;Ajv.prototype.errorsText=errorsText;Ajv.prototype._addSchema=_addSchema;Ajv.prototype._compile=_compile;Ajv.prototype.compileAsync=t(2694);var h=t(6765);Ajv.prototype.addKeyword=h.add;Ajv.prototype.getKeyword=h.get;Ajv.prototype.removeKeyword=h.remove;Ajv.prototype.validateKeyword=h.validate;var d=t(6343);Ajv.ValidationError=d.Validation;Ajv.MissingRefError=d.MissingRef;Ajv.$dataMetaSchema=c;var p="http://json-schema.org/draft-07/schema";var v=["removeAdditional","useDefaults","coerceTypes","strictDefaults"];var m=["/properties"];function Ajv(e){if(!(this instanceof Ajv))return new Ajv(e);e=this._opts=u.copy(e)||{};setLogger(this);this._schemas={};this._refs={};this._fragments={};this._formats=l(e.format);this._cache=e.cache||new i;this._loadingSchemas={};this._compilations=[];this.RULES=f();this._getId=chooseGetId(e);e.loopRequired=e.loopRequired||Infinity;if(e.errorDataPath=="property")e._errorDataPathProperty=true;if(e.serialize===undefined)e.serialize=n;this._metaOpts=getMetaSchemaOptions(this);if(e.formats)addInitialFormats(this);if(e.keywords)addInitialKeywords(this);addDefaultMetaSchema(this);if(typeof e.meta=="object")this.addMetaSchema(e.meta);if(e.nullable)this.addKeyword("nullable",{metaSchema:{type:"boolean"}});addInitialSchemas(this)}function validate(e,r){var t;if(typeof e=="string"){t=this.getSchema(e);if(!t)throw new Error('no schema with key or ref "'+e+'"')}else{var a=this._addSchema(e);t=a.validate||this._compile(a)}var s=t(r);if(t.$async!==true)this.errors=t.errors;return s}function compile(e,r){var t=this._addSchema(e,undefined,r);return t.validate||this._compile(t)}function addSchema(e,r,t,a){if(Array.isArray(e)){for(var i=0;i<e.length;i++)this.addSchema(e[i],undefined,t,a);return this}var o=this._getId(e);if(o!==undefined&&typeof o!="string")throw new Error("schema id must be string");r=s.normalizeId(r||o);checkUnique(this,r);this._schemas[r]=this._addSchema(e,t,a,true);return this}function addMetaSchema(e,r,t){this.addSchema(e,r,t,true);return this}function validateSchema(e,r){var t=e.$schema;if(t!==undefined&&typeof t!="string")throw new Error("$schema must be a string");t=t||this._opts.defaultMeta||defaultMeta(this);if(!t){this.logger.warn("meta-schema not available");this.errors=null;return true}var a=this.validate(t,e);if(!a&&r){var s="schema is invalid: "+this.errorsText();if(this._opts.validateSchema=="log")this.logger.error(s);else throw new Error(s)}return a}function defaultMeta(e){var r=e._opts.meta;e._opts.defaultMeta=typeof r=="object"?e._getId(r)||r:e.getSchema(p)?p:undefined;return e._opts.defaultMeta}function getSchema(e){var r=_getSchemaObj(this,e);switch(typeof r){case"object":return r.validate||this._compile(r);case"string":return this.getSchema(r);case"undefined":return _getSchemaFragment(this,e)}}function _getSchemaFragment(e,r){var t=s.schema.call(e,{schema:{}},r);if(t){var i=t.schema,n=t.root,l=t.baseId;var f=a.call(e,i,n,undefined,l);e._fragments[r]=new o({ref:r,fragment:true,schema:i,root:n,baseId:l,validate:f});return f}}function _getSchemaObj(e,r){r=s.normalizeId(r);return e._schemas[r]||e._refs[r]||e._fragments[r]}function removeSchema(e){if(e instanceof RegExp){_removeAllSchemas(this,this._schemas,e);_removeAllSchemas(this,this._refs,e);return this}switch(typeof e){case"undefined":_removeAllSchemas(this,this._schemas);_removeAllSchemas(this,this._refs);this._cache.clear();return this;case"string":var r=_getSchemaObj(this,e);if(r)this._cache.del(r.cacheKey);delete this._schemas[e];delete this._refs[e];return this;case"object":var t=this._opts.serialize;var a=t?t(e):e;this._cache.del(a);var i=this._getId(e);if(i){i=s.normalizeId(i);delete this._schemas[i];delete this._refs[i]}}return this}function _removeAllSchemas(e,r,t){for(var a in r){var s=r[a];if(!s.meta&&(!t||t.test(a))){e._cache.del(s.cacheKey);delete r[a]}}}function _addSchema(e,r,t,a){if(typeof e!="object"&&typeof e!="boolean")throw new Error("schema should be object or boolean");var i=this._opts.serialize;var n=i?i(e):e;var l=this._cache.get(n);if(l)return l;a=a||this._opts.addUsedSchema!==false;var f=s.normalizeId(this._getId(e));if(f&&a)checkUnique(this,f);var c=this._opts.validateSchema!==false&&!r;var u;if(c&&!(u=f&&f==s.normalizeId(e.$schema)))this.validateSchema(e,true);var h=s.ids.call(this,e);var d=new o({id:f,schema:e,localRefs:h,cacheKey:n,meta:t});if(f[0]!="#"&&a)this._refs[f]=d;this._cache.put(n,d);if(c&&u)this.validateSchema(e,true);return d}function _compile(e,r){if(e.compiling){e.validate=callValidate;callValidate.schema=e.schema;callValidate.errors=null;callValidate.root=r?r:callValidate;if(e.schema.$async===true)callValidate.$async=true;return callValidate}e.compiling=true;var t;if(e.meta){t=this._opts;this._opts=this._metaOpts}var s;try{s=a.call(this,e.schema,r,e.localRefs)}catch(r){delete e.validate;throw r}finally{e.compiling=false;if(e.meta)this._opts=t}e.validate=s;e.refs=s.refs;e.refVal=s.refVal;e.root=s.root;return s;function callValidate(){var r=e.validate;var t=r.apply(this,arguments);callValidate.errors=r.errors;return t}}function chooseGetId(e){switch(e.schemaId){case"auto":return _get$IdOrId;case"id":return _getId;default:return _get$Id}}function _getId(e){if(e.$id)this.logger.warn("schema $id ignored",e.$id);return e.id}function _get$Id(e){if(e.id)this.logger.warn("schema id ignored",e.id);return e.$id}function _get$IdOrId(e){if(e.$id&&e.id&&e.$id!=e.id)throw new Error("schema $id is different from id");return e.$id||e.id}function errorsText(e,r){e=e||this.errors;if(!e)return"No errors";r=r||{};var t=r.separator===undefined?", ":r.separator;var a=r.dataVar===undefined?"data":r.dataVar;var s="";for(var i=0;i<e.length;i++){var o=e[i];if(o)s+=a+o.dataPath+" "+o.message+t}return s.slice(0,-t.length)}function addFormat(e,r){if(typeof r=="string")r=new RegExp(r);this._formats[e]=r;return this}function addDefaultMetaSchema(e){var r;if(e._opts.$data){r=t(7664);e.addMetaSchema(r,r.$id,true)}if(e._opts.meta===false)return;var a=t(7136);if(e._opts.$data)a=c(a,m);e.addMetaSchema(a,p,true);e._refs["http://json-schema.org/schema"]=p}function addInitialSchemas(e){var r=e._opts.schemas;if(!r)return;if(Array.isArray(r))e.addSchema(r);else for(var t in r)e.addSchema(r[t],t)}function addInitialFormats(e){for(var r in e._opts.formats){var t=e._opts.formats[r];e.addFormat(r,t)}}function addInitialKeywords(e){for(var r in e._opts.keywords){var t=e._opts.keywords[r];e.addKeyword(r,t)}}function checkUnique(e,r){if(e._schemas[r]||e._refs[r])throw new Error('schema with key or id "'+r+'" already exists')}function getMetaSchemaOptions(e){var r=u.copy(e._opts);for(var t=0;t<v.length;t++)delete r[v[t]];return r}function setLogger(e){var r=e._opts.logger;if(r===false){e.logger={log:noop,warn:noop,error:noop}}else{if(r===undefined)r=console;if(!(typeof r=="object"&&r.log&&r.warn&&r.error))throw new Error("logger must implement log, warn and error methods");e.logger=r}}function noop(){}},7731:e=>{"use strict";var r=e.exports=function Cache(){this._cache={}};r.prototype.put=function Cache_put(e,r){this._cache[e]=r};r.prototype.get=function Cache_get(e){return this._cache[e]};r.prototype.del=function Cache_del(e){delete this._cache[e]};r.prototype.clear=function Cache_clear(){this._cache={}}},2694:(e,r,t)=>{"use strict";var a=t(6343).MissingRef;e.exports=compileAsync;function compileAsync(e,r,t){var s=this;if(typeof this._opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");if(typeof r=="function"){t=r;r=undefined}var i=loadMetaSchemaOf(e).then((function(){var t=s._addSchema(e,undefined,r);return t.validate||_compileAsync(t)}));if(t){i.then((function(e){t(null,e)}),t)}return i;function loadMetaSchemaOf(e){var r=e.$schema;return r&&!s.getSchema(r)?compileAsync.call(s,{$ref:r},true):Promise.resolve()}function _compileAsync(e){try{return s._compile(e)}catch(e){if(e instanceof a)return loadMissingSchema(e);throw e}function loadMissingSchema(t){var a=t.missingSchema;if(added(a))throw new Error("Schema "+a+" is loaded but "+t.missingRef+" cannot be resolved");var i=s._loadingSchemas[a];if(!i){i=s._loadingSchemas[a]=s._opts.loadSchema(a);i.then(removePromise,removePromise)}return i.then((function(e){if(!added(a)){return loadMetaSchemaOf(e).then((function(){if(!added(a))s.addSchema(e,a,undefined,r)}))}})).then((function(){return _compileAsync(e)}));function removePromise(){delete s._loadingSchemas[a]}function added(e){return s._refs[e]||s._schemas[e]}}}}},6343:(e,r,t)=>{"use strict";var a=t(2603);e.exports={Validation:errorSubclass(ValidationError),MissingRef:errorSubclass(MissingRefError)};function ValidationError(e){this.message="validation failed";this.errors=e;this.ajv=this.validation=true}MissingRefError.message=function(e,r){return"can't resolve reference "+r+" from id "+e};function MissingRefError(e,r,t){this.message=t||MissingRefError.message(e,r);this.missingRef=a.url(e,r);this.missingSchema=a.normalizeId(a.fullPath(this.missingRef))}function errorSubclass(e){e.prototype=Object.create(Error.prototype);e.prototype.constructor=e;return e}},2015:(e,r,t)=>{"use strict";var a=t(8716);var s=/^(\d\d\d\d)-(\d\d)-(\d\d)$/;var i=[0,31,28,31,30,31,30,31,31,30,31,30,31];var o=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;var n=/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i;var l=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;var f=/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;var c=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i;var u=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i;var h=/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;var d=/^(?:\/(?:[^~/]|~0|~1)*)*$/;var p=/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;var v=/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/;e.exports=formats;function formats(e){e=e=="full"?"full":"fast";return a.copy(formats[e])}formats.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,"uri-template":c,url:u,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:n,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:regex,uuid:h,"json-pointer":d,"json-pointer-uri-fragment":p,"relative-json-pointer":v};formats.full={date:date,time:time,"date-time":date_time,uri:uri,"uri-reference":f,"uri-template":c,url:u,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:n,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:regex,uuid:h,"json-pointer":d,"json-pointer-uri-fragment":p,"relative-json-pointer":v};function isLeapYear(e){return e%4===0&&(e%100!==0||e%400===0)}function date(e){var r=e.match(s);if(!r)return false;var t=+r[1];var a=+r[2];var o=+r[3];return a>=1&&a<=12&&o>=1&&o<=(a==2&&isLeapYear(t)?29:i[a])}function time(e,r){var t=e.match(o);if(!t)return false;var a=t[1];var s=t[2];var i=t[3];var n=t[5];return(a<=23&&s<=59&&i<=59||a==23&&s==59&&i==60)&&(!r||n)}var m=/t|\s/i;function date_time(e){var r=e.split(m);return r.length==2&&date(r[0])&&time(r[1],true)}var y=/\/|:/;function uri(e){return y.test(e)&&l.test(e)}var g=/[^\\]\\Z/;function regex(e){if(g.test(e))return false;try{new RegExp(e);return true}catch(e){return false}}},9258:(e,r,t)=>{"use strict";var a=t(2603),s=t(8716),i=t(6343),o=t(6424);var n=t(7003);var l=s.ucs2length;var f=t(1230);var c=i.Validation;e.exports=compile;function compile(e,r,t,u){var h=this,d=this._opts,p=[undefined],v={},m=[],y={},g=[],P={},E=[];r=r||{schema:e,refVal:p,refs:v};var b=checkCompiling.call(this,e,r,u);var w=this._compilations[b.index];if(b.compiling)return w.callValidate=callValidate;var S=this._formats;var x=this.RULES;try{var _=localCompile(e,r,t,u);w.validate=_;var j=w.callValidate;if(j){j.schema=_.schema;j.errors=null;j.refs=_.refs;j.refVal=_.refVal;j.root=_.root;j.$async=_.$async;if(d.sourceCode)j.source=_.source}return _}finally{endCompiling.call(this,e,r,u)}function callValidate(){var e=w.validate;var r=e.apply(this,arguments);callValidate.errors=e.errors;return r}function localCompile(e,t,o,u){var y=!t||t&&t.schema==e;if(t.schema!=r.schema)return compile.call(h,e,t,o,u);var P=e.$async===true;var b=n({isTop:true,schema:e,isRoot:y,baseId:u,root:t,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:i.MissingRef,RULES:x,validate:n,util:s,resolve:a,resolveRef:resolveRef,usePattern:usePattern,useDefault:useDefault,useCustomRule:useCustomRule,opts:d,formats:S,logger:h.logger,self:h});b=vars(p,refValCode)+vars(m,patternCode)+vars(g,defaultCode)+vars(E,customRuleCode)+b;if(d.processCode)b=d.processCode(b,e);var w;try{var _=new Function("self","RULES","formats","root","refVal","defaults","customRules","equal","ucs2length","ValidationError",b);w=_(h,x,S,r,p,g,E,f,l,c);p[0]=w}catch(e){h.logger.error("Error compiling schema, function code:",b);throw e}w.schema=e;w.errors=null;w.refs=v;w.refVal=p;w.root=y?w:t;if(P)w.$async=true;if(d.sourceCode===true){w.source={code:b,patterns:m,defaults:g}}return w}function resolveRef(e,s,i){s=a.url(e,s);var o=v[s];var n,l;if(o!==undefined){n=p[o];l="refVal["+o+"]";return resolvedRef(n,l)}if(!i&&r.refs){var f=r.refs[s];if(f!==undefined){n=r.refVal[f];l=addLocalRef(s,n);return resolvedRef(n,l)}}l=addLocalRef(s);var c=a.call(h,localCompile,r,s);if(c===undefined){var u=t&&t[s];if(u){c=a.inlineRef(u,d.inlineRefs)?u:compile.call(h,u,r,t,e)}}if(c===undefined){removeLocalRef(s)}else{replaceLocalRef(s,c);return resolvedRef(c,l)}}function addLocalRef(e,r){var t=p.length;p[t]=r;v[e]=t;return"refVal"+t}function removeLocalRef(e){delete v[e]}function replaceLocalRef(e,r){var t=v[e];p[t]=r}function resolvedRef(e,r){return typeof e=="object"||typeof e=="boolean"?{code:r,schema:e,inline:true}:{code:r,$async:e&&!!e.$async}}function usePattern(e){var r=y[e];if(r===undefined){r=y[e]=m.length;m[r]=e}return"pattern"+r}function useDefault(e){switch(typeof e){case"boolean":case"number":return""+e;case"string":return s.toQuotedString(e);case"object":if(e===null)return"null";var r=o(e);var t=P[r];if(t===undefined){t=P[r]=g.length;g[t]=e}return"default"+t}}function useCustomRule(e,r,t,a){if(h._opts.validateSchema!==false){var s=e.definition.dependencies;if(s&&!s.every((function(e){return Object.prototype.hasOwnProperty.call(t,e)})))throw new Error("parent schema must have all required keywords: "+s.join(","));var i=e.definition.validateSchema;if(i){var o=i(r);if(!o){var n="keyword schema is invalid: "+h.errorsText(i.errors);if(h._opts.validateSchema=="log")h.logger.error(n);else throw new Error(n)}}}var l=e.definition.compile,f=e.definition.inline,c=e.definition.macro;var u;if(l){u=l.call(h,r,t,a)}else if(c){u=c.call(h,r,t,a);if(d.validateSchema!==false)h.validateSchema(u,true)}else if(f){u=f.call(h,a,e.keyword,r,t)}else{u=e.definition.validate;if(!u)return}if(u===undefined)throw new Error('custom keyword "'+e.keyword+'"failed to compile');var p=E.length;E[p]=u;return{code:"customRule"+p,validate:u}}}function checkCompiling(e,r,t){var a=compIndex.call(this,e,r,t);if(a>=0)return{index:a,compiling:true};a=this._compilations.length;this._compilations[a]={schema:e,root:r,baseId:t};return{index:a,compiling:false}}function endCompiling(e,r,t){var a=compIndex.call(this,e,r,t);if(a>=0)this._compilations.splice(a,1)}function compIndex(e,r,t){for(var a=0;a<this._compilations.length;a++){var s=this._compilations[a];if(s.schema==e&&s.root==r&&s.baseId==t)return a}return-1}function patternCode(e,r){return"var pattern"+e+" = new RegExp("+s.toQuotedString(r[e])+");"}function defaultCode(e){return"var default"+e+" = defaults["+e+"];"}function refValCode(e,r){return r[e]===undefined?"":"var refVal"+e+" = refVal["+e+"];"}function customRuleCode(e){return"var customRule"+e+" = customRules["+e+"];"}function vars(e,r){if(!e.length)return"";var t="";for(var a=0;a<e.length;a++)t+=r(a,e);return t}},2603:(e,r,t)=>{"use strict";var a=t(4856),s=t(1230),i=t(8716),o=t(2961),n=t(6042);e.exports=resolve;resolve.normalizeId=normalizeId;resolve.fullPath=getFullPath;resolve.url=resolveUrl;resolve.ids=resolveIds;resolve.inlineRef=inlineRef;resolve.schema=resolveSchema;function resolve(e,r,t){var a=this._refs[t];if(typeof a=="string"){if(this._refs[a])a=this._refs[a];else return resolve.call(this,e,r,a)}a=a||this._schemas[t];if(a instanceof o){return inlineRef(a.schema,this._opts.inlineRefs)?a.schema:a.validate||this._compile(a)}var s=resolveSchema.call(this,r,t);var i,n,l;if(s){i=s.schema;r=s.root;l=s.baseId}if(i instanceof o){n=i.validate||e.call(this,i.schema,r,undefined,l)}else if(i!==undefined){n=inlineRef(i,this._opts.inlineRefs)?i:e.call(this,i,r,undefined,l)}return n}function resolveSchema(e,r){var t=a.parse(r),s=_getFullPath(t),i=getFullPath(this._getId(e.schema));if(Object.keys(e.schema).length===0||s!==i){var n=normalizeId(s);var l=this._refs[n];if(typeof l=="string"){return resolveRecursive.call(this,e,l,t)}else if(l instanceof o){if(!l.validate)this._compile(l);e=l}else{l=this._schemas[n];if(l instanceof o){if(!l.validate)this._compile(l);if(n==normalizeId(r))return{schema:l,root:e,baseId:i};e=l}else{return}}if(!e.schema)return;i=getFullPath(this._getId(e.schema))}return getJsonPointer.call(this,t,i,e.schema,e)}function resolveRecursive(e,r,t){var a=resolveSchema.call(this,e,r);if(a){var s=a.schema;var i=a.baseId;e=a.root;var o=this._getId(s);if(o)i=resolveUrl(i,o);return getJsonPointer.call(this,t,i,s,e)}}var l=i.toHash(["properties","patternProperties","enum","dependencies","definitions"]);function getJsonPointer(e,r,t,a){e.fragment=e.fragment||"";if(e.fragment.slice(0,1)!="/")return;var s=e.fragment.split("/");for(var o=1;o<s.length;o++){var n=s[o];if(n){n=i.unescapeFragment(n);t=t[n];if(t===undefined)break;var f;if(!l[n]){f=this._getId(t);if(f)r=resolveUrl(r,f);if(t.$ref){var c=resolveUrl(r,t.$ref);var u=resolveSchema.call(this,a,c);if(u){t=u.schema;a=u.root;r=u.baseId}}}}}if(t!==undefined&&t!==a.schema)return{schema:t,root:a,baseId:r}}var f=i.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]);function inlineRef(e,r){if(r===false)return false;if(r===undefined||r===true)return checkNoRef(e);else if(r)return countKeys(e)<=r}function checkNoRef(e){var r;if(Array.isArray(e)){for(var t=0;t<e.length;t++){r=e[t];if(typeof r=="object"&&!checkNoRef(r))return false}}else{for(var a in e){if(a=="$ref")return false;r=e[a];if(typeof r=="object"&&!checkNoRef(r))return false}}return true}function countKeys(e){var r=0,t;if(Array.isArray(e)){for(var a=0;a<e.length;a++){t=e[a];if(typeof t=="object")r+=countKeys(t);if(r==Infinity)return Infinity}}else{for(var s in e){if(s=="$ref")return Infinity;if(f[s]){r++}else{t=e[s];if(typeof t=="object")r+=countKeys(t)+1;if(r==Infinity)return Infinity}}}return r}function getFullPath(e,r){if(r!==false)e=normalizeId(e);var t=a.parse(e);return _getFullPath(t)}function _getFullPath(e){return a.serialize(e).split("#")[0]+"#"}var c=/#\/?$/;function normalizeId(e){return e?e.replace(c,""):""}function resolveUrl(e,r){r=normalizeId(r);return a.resolve(e,r)}function resolveIds(e){var r=normalizeId(this._getId(e));var t={"":r};var o={"":getFullPath(r,false)};var l={};var f=this;n(e,{allKeys:true},(function(e,r,n,c,u,h,d){if(r==="")return;var p=f._getId(e);var v=t[c];var m=o[c]+"/"+u;if(d!==undefined)m+="/"+(typeof d=="number"?d:i.escapeFragment(d));if(typeof p=="string"){p=v=normalizeId(v?a.resolve(v,p):p);var y=f._refs[p];if(typeof y=="string")y=f._refs[y];if(y&&y.schema){if(!s(e,y.schema))throw new Error('id "'+p+'" resolves to more than one schema')}else if(p!=normalizeId(m)){if(p[0]=="#"){if(l[p]&&!s(e,l[p]))throw new Error('id "'+p+'" resolves to more than one schema');l[p]=e}else{f._refs[p]=m}}}t[r]=v;o[r]=m}));return l}},9233:(e,r,t)=>{"use strict";var a=t(6964),s=t(8716).toHash;e.exports=function rules(){var e=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","items","contains","uniqueItems"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf","if"]}];var r=["type","$comment"];var t=["$schema","$id","id","$data","$async","title","description","default","definitions","examples","readOnly","writeOnly","contentMediaType","contentEncoding","additionalItems","then","else"];var i=["number","integer","string","array","object","boolean","null"];e.all=s(r);e.types=s(i);e.forEach((function(t){t.rules=t.rules.map((function(t){var s;if(typeof t=="object"){var i=Object.keys(t)[0];s=t[i];t=i;s.forEach((function(t){r.push(t);e.all[t]=true}))}r.push(t);var o=e.all[t]={keyword:t,code:a[t],implements:s};return o}));e.all.$comment={keyword:"$comment",code:a.$comment};if(t.type)e.types[t.type]=t}));e.keywords=s(r.concat(t));e.custom={};return e}},2961:(e,r,t)=>{"use strict";var a=t(8716);e.exports=SchemaObject;function SchemaObject(e){a.copy(e,this)}},2:e=>{"use strict";e.exports=function ucs2length(e){var r=0,t=e.length,a=0,s;while(a<t){r++;s=e.charCodeAt(a++);if(s>=55296&&s<=56319&&a<t){s=e.charCodeAt(a);if((s&64512)==56320)a++}}return r}},8716:(e,r,t)=>{"use strict";e.exports={copy:copy,checkDataType:checkDataType,checkDataTypes:checkDataTypes,coerceToTypes:coerceToTypes,toHash:toHash,getProperty:getProperty,escapeQuotes:escapeQuotes,equal:t(1230),ucs2length:t(2),varOccurences:varOccurences,varReplace:varReplace,schemaHasRules:schemaHasRules,schemaHasRulesExcept:schemaHasRulesExcept,schemaUnknownRules:schemaUnknownRules,toQuotedString:toQuotedString,getPathExpr:getPathExpr,getPath:getPath,getData:getData,unescapeFragment:unescapeFragment,unescapeJsonPointer:unescapeJsonPointer,escapeFragment:escapeFragment,escapeJsonPointer:escapeJsonPointer};function copy(e,r){r=r||{};for(var t in e)r[t]=e[t];return r}function checkDataType(e,r,t,a){var s=a?" !== ":" === ",i=a?" || ":" && ",o=a?"!":"",n=a?"":"!";switch(e){case"null":return r+s+"null";case"array":return o+"Array.isArray("+r+")";case"object":return"("+o+r+i+"typeof "+r+s+'"object"'+i+n+"Array.isArray("+r+"))";case"integer":return"(typeof "+r+s+'"number"'+i+n+"("+r+" % 1)"+i+r+s+r+(t?i+o+"isFinite("+r+")":"")+")";case"number":return"(typeof "+r+s+'"'+e+'"'+(t?i+o+"isFinite("+r+")":"")+")";default:return"typeof "+r+s+'"'+e+'"'}}function checkDataTypes(e,r,t){switch(e.length){case 1:return checkDataType(e[0],r,t,true);default:var a="";var s=toHash(e);if(s.array&&s.object){a=s.null?"(":"(!"+r+" || ";a+="typeof "+r+' !== "object")';delete s.null;delete s.array;delete s.object}if(s.number)delete s.integer;for(var i in s)a+=(a?" && ":"")+checkDataType(i,r,t,true);return a}}var a=toHash(["string","number","integer","boolean","null"]);function coerceToTypes(e,r){if(Array.isArray(r)){var t=[];for(var s=0;s<r.length;s++){var i=r[s];if(a[i])t[t.length]=i;else if(e==="array"&&i==="array")t[t.length]=i}if(t.length)return t}else if(a[r]){return[r]}else if(e==="array"&&r==="array"){return["array"]}}function toHash(e){var r={};for(var t=0;t<e.length;t++)r[e[t]]=true;return r}var s=/^[a-z$_][a-z$_0-9]*$/i;var i=/'|\\/g;function getProperty(e){return typeof e=="number"?"["+e+"]":s.test(e)?"."+e:"['"+escapeQuotes(e)+"']"}function escapeQuotes(e){return e.replace(i,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function varOccurences(e,r){r+="[^0-9]";var t=e.match(new RegExp(r,"g"));return t?t.length:0}function varReplace(e,r,t){r+="([^0-9])";t=t.replace(/\$/g,"$$$$");return e.replace(new RegExp(r,"g"),t+"$1")}function schemaHasRules(e,r){if(typeof e=="boolean")return!e;for(var t in e)if(r[t])return true}function schemaHasRulesExcept(e,r,t){if(typeof e=="boolean")return!e&&t!="not";for(var a in e)if(a!=t&&r[a])return true}function schemaUnknownRules(e,r){if(typeof e=="boolean")return;for(var t in e)if(!r[t])return t}function toQuotedString(e){return"'"+escapeQuotes(e)+"'"}function getPathExpr(e,r,t,a){var s=t?"'/' + "+r+(a?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):a?"'[' + "+r+" + ']'":"'[\\'' + "+r+" + '\\']'";return joinPaths(e,s)}function getPath(e,r,t){var a=t?toQuotedString("/"+escapeJsonPointer(r)):toQuotedString(getProperty(r));return joinPaths(e,a)}var o=/^\/(?:[^~]|~0|~1)*$/;var n=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function getData(e,r,t){var a,s,i,l;if(e==="")return"rootData";if(e[0]=="/"){if(!o.test(e))throw new Error("Invalid JSON-pointer: "+e);s=e;i="rootData"}else{l=e.match(n);if(!l)throw new Error("Invalid JSON-pointer: "+e);a=+l[1];s=l[2];if(s=="#"){if(a>=r)throw new Error("Cannot access property/index "+a+" levels up, current level is "+r);return t[r-a]}if(a>r)throw new Error("Cannot access data "+a+" levels up, current level is "+r);i="data"+(r-a||"");if(!s)return i}var f=i;var c=s.split("/");for(var u=0;u<c.length;u++){var h=c[u];if(h){i+=getProperty(unescapeJsonPointer(h));f+=" && "+i}}return f}function joinPaths(e,r){if(e=='""')return r;return(e+" + "+r).replace(/([^\\])' \+ '/g,"$1")}function unescapeFragment(e){return unescapeJsonPointer(decodeURIComponent(e))}function escapeFragment(e){return encodeURIComponent(escapeJsonPointer(e))}function escapeJsonPointer(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function unescapeJsonPointer(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}},1966:e=>{"use strict";var r=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"];e.exports=function(e,t){for(var a=0;a<t.length;a++){e=JSON.parse(JSON.stringify(e));var s=t[a].split("/");var i=e;var o;for(o=1;o<s.length;o++)i=i[s[o]];for(o=0;o<r.length;o++){var n=r[o];var l=i[n];if(l){i[n]={anyOf:[l,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]}}}}return e}},6686:(e,r,t)=>{"use strict";var a=t(7136);e.exports={$id:"https://github.com/ajv-validator/ajv/blob/master/lib/definition_schema.js",definitions:{simpleTypes:a.definitions.simpleTypes},type:"object",dependencies:{schema:["validate"],$data:["validate"],statements:["inline"],valid:{not:{required:["macro"]}}},properties:{type:a.properties.type,schema:{type:"boolean"},statements:{type:"boolean"},dependencies:{type:"array",items:{type:"string"}},metaSchema:{type:"object"},modifying:{type:"boolean"},valid:{type:"boolean"},$data:{type:"boolean"},async:{type:"boolean"},errors:{anyOf:[{type:"boolean"},{const:"full"}]}}}},4130:e=>{"use strict";e.exports=function generate__limit(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c;var u="data"+(i||"");var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}var p=r=="maximum",v=p?"exclusiveMaximum":"exclusiveMinimum",m=e.schema[v],y=e.opts.$data&&m&&m.$data,g=p?"<":">",P=p?">":"<",c=undefined;if(!(h||typeof o=="number"||o===undefined)){throw new Error(r+" must be number")}if(!(y||m===undefined||typeof m=="number"||typeof m=="boolean")){throw new Error(v+" must be number or boolean")}if(y){var E=e.util.getData(m.$data,i,e.dataPathArr),b="exclusive"+s,w="exclType"+s,S="exclIsNumber"+s,x="op"+s,_="' + "+x+" + '";a+=" var schemaExcl"+s+" = "+E+"; ";E="schemaExcl"+s;a+=" var "+b+"; var "+w+" = typeof "+E+"; if ("+w+" != 'boolean' && "+w+" != 'undefined' && "+w+" != 'number') { ";var c=v;var j=j||[];j.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: '"+v+" should be boolean' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var R=a;a=j.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+R+"]); "}else{a+=" validate.errors = ["+R+"]; return false; "}}else{a+=" var err = "+R+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } else if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}a+=" "+w+" == 'number' ? ( ("+b+" = "+d+" === undefined || "+E+" "+g+"= "+d+") ? "+u+" "+P+"= "+E+" : "+u+" "+P+" "+d+" ) : ( ("+b+" = "+E+" === true) ? "+u+" "+P+"= "+d+" : "+u+" "+P+" "+d+" ) || "+u+" !== "+u+") { var op"+s+" = "+b+" ? '"+g+"' : '"+g+"='; ";if(o===undefined){c=v;l=e.errSchemaPath+"/"+v;d=E;h=y}}else{var S=typeof m=="number",_=g;if(S&&h){var x="'"+_+"'";a+=" if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}a+=" ( "+d+" === undefined || "+m+" "+g+"= "+d+" ? "+u+" "+P+"= "+m+" : "+u+" "+P+" "+d+" ) || "+u+" !== "+u+") { "}else{if(S&&o===undefined){b=true;c=v;l=e.errSchemaPath+"/"+v;d=m;P+="="}else{if(S)d=Math[p?"min":"max"](m,o);if(m===(S?d:true)){b=true;c=v;l=e.errSchemaPath+"/"+v;P+="="}else{b=false;_+="="}}var x="'"+_+"'";a+=" if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}a+=" "+u+" "+P+" "+d+" || "+u+" !== "+u+") { "}}c=c||r;var j=j||[];j.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"_limit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { comparison: "+x+", limit: "+d+", exclusive: "+b+" } ";if(e.opts.messages!==false){a+=" , message: 'should be "+_+" ";if(h){a+="' + "+d}else{a+=""+d+"'"}}if(e.opts.verbose){a+=" , schema:  ";if(h){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var R=a;a=j.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+R+"]); "}else{a+=" validate.errors = ["+R+"]; return false; "}}else{a+=" var err = "+R+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } ";if(f){a+=" else { "}return a}},3472:e=>{"use strict";e.exports=function generate__limitItems(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c;var u="data"+(i||"");var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}if(!(h||typeof o=="number")){throw new Error(r+" must be number")}var p=r=="maxItems"?">":"<";a+="if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}a+=" "+u+".length "+p+" "+d+") { ";var c=r;var v=v||[];v.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"_limitItems")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+d+" } ";if(e.opts.messages!==false){a+=" , message: 'should NOT have ";if(r=="maxItems"){a+="more"}else{a+="fewer"}a+=" than ";if(h){a+="' + "+d+" + '"}else{a+=""+o}a+=" items' "}if(e.opts.verbose){a+=" , schema:  ";if(h){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var m=a;a=v.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+m+"]); "}else{a+=" validate.errors = ["+m+"]; return false; "}}else{a+=" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+="} ";if(f){a+=" else { "}return a}},9018:e=>{"use strict";e.exports=function generate__limitLength(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c;var u="data"+(i||"");var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}if(!(h||typeof o=="number")){throw new Error(r+" must be number")}var p=r=="maxLength"?">":"<";a+="if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}if(e.opts.unicode===false){a+=" "+u+".length "}else{a+=" ucs2length("+u+") "}a+=" "+p+" "+d+") { ";var c=r;var v=v||[];v.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"_limitLength")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+d+" } ";if(e.opts.messages!==false){a+=" , message: 'should NOT be ";if(r=="maxLength"){a+="longer"}else{a+="shorter"}a+=" than ";if(h){a+="' + "+d+" + '"}else{a+=""+o}a+=" characters' "}if(e.opts.verbose){a+=" , schema:  ";if(h){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var m=a;a=v.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+m+"]); "}else{a+=" validate.errors = ["+m+"]; return false; "}}else{a+=" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+="} ";if(f){a+=" else { "}return a}},8740:e=>{"use strict";e.exports=function generate__limitProperties(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c;var u="data"+(i||"");var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}if(!(h||typeof o=="number")){throw new Error(r+" must be number")}var p=r=="maxProperties"?">":"<";a+="if ( ";if(h){a+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "}a+=" Object.keys("+u+").length "+p+" "+d+") { ";var c=r;var v=v||[];v.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"_limitProperties")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+d+" } ";if(e.opts.messages!==false){a+=" , message: 'should NOT have ";if(r=="maxProperties"){a+="more"}else{a+="fewer"}a+=" than ";if(h){a+="' + "+d+" + '"}else{a+=""+o}a+=" properties' "}if(e.opts.verbose){a+=" , schema:  ";if(h){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var m=a;a=v.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+m+"]); "}else{a+=" validate.errors = ["+m+"]; return false; "}}else{a+=" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+="} ";if(f){a+=" else { "}return a}},4378:e=>{"use strict";e.exports=function generate_allOf(e,r,t){var a=" ";var s=e.schema[r];var i=e.schemaPath+e.util.getProperty(r);var o=e.errSchemaPath+"/"+r;var n=!e.opts.allErrors;var l=e.util.copy(e);var f="";l.level++;var c="valid"+l.level;var u=l.baseId,h=true;var d=s;if(d){var p,v=-1,m=d.length-1;while(v<m){p=d[v+=1];if(e.opts.strictKeywords?typeof p=="object"&&Object.keys(p).length>0||p===false:e.util.schemaHasRules(p,e.RULES.all)){h=false;l.schema=p;l.schemaPath=i+"["+v+"]";l.errSchemaPath=o+"/"+v;a+="  "+e.validate(l)+" ";l.baseId=u;if(n){a+=" if ("+c+") { ";f+="}"}}}}if(n){if(h){a+=" if (true) { "}else{a+=" "+f.slice(0,-1)+" "}}return a}},9278:e=>{"use strict";e.exports=function generate_anyOf(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h="errs__"+s;var d=e.util.copy(e);var p="";d.level++;var v="valid"+d.level;var m=o.every((function(r){return e.opts.strictKeywords?typeof r=="object"&&Object.keys(r).length>0||r===false:e.util.schemaHasRules(r,e.RULES.all)}));if(m){var y=d.baseId;a+=" var "+h+" = errors; var "+u+" = false;  ";var g=e.compositeRule;e.compositeRule=d.compositeRule=true;var P=o;if(P){var E,b=-1,w=P.length-1;while(b<w){E=P[b+=1];d.schema=E;d.schemaPath=n+"["+b+"]";d.errSchemaPath=l+"/"+b;a+="  "+e.validate(d)+" ";d.baseId=y;a+=" "+u+" = "+u+" || "+v+"; if (!"+u+") { ";p+="}"}}e.compositeRule=d.compositeRule=g;a+=" "+p+" if (!"+u+") {   var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"anyOf"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: 'should match some schema in anyOf' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; return false; "}}a+=" } else {  errors = "+h+"; if (vErrors !== null) { if ("+h+") vErrors.length = "+h+"; else vErrors = null; } ";if(e.opts.allErrors){a+=" } "}}else{if(f){a+=" if (true) { "}}return a}},9263:e=>{"use strict";e.exports=function generate_comment(e,r,t){var a=" ";var s=e.schema[r];var i=e.errSchemaPath+"/"+r;var o=!e.opts.allErrors;var n=e.util.toQuotedString(s);if(e.opts.$comment===true){a+=" console.log("+n+");"}else if(typeof e.opts.$comment=="function"){a+=" self._opts.$comment("+n+", "+e.util.toQuotedString(i)+", validate.root.schema);"}return a}},5326:e=>{"use strict";e.exports=function generate_const(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}if(!h){a+=" var schema"+s+" = validate.schema"+n+";"}a+="var "+u+" = equal("+c+", schema"+s+"); if (!"+u+") {   ";var p=p||[];p.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"const"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { allowedValue: schema"+s+" } ";if(e.opts.messages!==false){a+=" , message: 'should be equal to constant' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var v=a;a=p.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+v+"]); "}else{a+=" validate.errors = ["+v+"]; return false; "}}else{a+=" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" }";if(f){a+=" else { "}return a}},7922:e=>{"use strict";e.exports=function generate_contains(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h="errs__"+s;var d=e.util.copy(e);var p="";d.level++;var v="valid"+d.level;var m="i"+s,y=d.dataLevel=e.dataLevel+1,g="data"+y,P=e.baseId,E=e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===false:e.util.schemaHasRules(o,e.RULES.all);a+="var "+h+" = errors;var "+u+";";if(E){var b=e.compositeRule;e.compositeRule=d.compositeRule=true;d.schema=o;d.schemaPath=n;d.errSchemaPath=l;a+=" var "+v+" = false; for (var "+m+" = 0; "+m+" < "+c+".length; "+m+"++) { ";d.errorPath=e.util.getPathExpr(e.errorPath,m,e.opts.jsonPointers,true);var w=c+"["+m+"]";d.dataPathArr[y]=m;var S=e.validate(d);d.baseId=P;if(e.util.varOccurences(S,g)<2){a+=" "+e.util.varReplace(S,g,w)+" "}else{a+=" var "+g+" = "+w+"; "+S+" "}a+=" if ("+v+") break; }  ";e.compositeRule=d.compositeRule=b;a+=" "+p+" if (!"+v+") {"}else{a+=" if ("+c+".length == 0) {"}var x=x||[];x.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"contains"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: 'should contain a valid item' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var _=a;a=x.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+_+"]); "}else{a+=" validate.errors = ["+_+"]; return false; "}}else{a+=" var err = "+_+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } else { ";if(E){a+="  errors = "+h+"; if (vErrors !== null) { if ("+h+") vErrors.length = "+h+"; else vErrors = null; } "}if(e.opts.allErrors){a+=" } "}return a}},8029:e=>{"use strict";e.exports=function generate_custom(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c;var u="data"+(i||"");var h="valid"+s;var d="errs__"+s;var p=e.opts.$data&&o&&o.$data,v;if(p){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";v="schema"+s}else{v=o}var m=this,y="definition"+s,g=m.definition,P="";var E,b,w,S,x;if(p&&g.$data){x="keywordValidate"+s;var _=g.validateSchema;a+=" var "+y+" = RULES.custom['"+r+"'].definition; var "+x+" = "+y+".validate;"}else{S=e.useCustomRule(m,o,e.schema,e);if(!S)return;v="validate.schema"+n;x=S.code;E=g.compile;b=g.inline;w=g.macro}var j=x+".errors",R="i"+s,F="ruleErr"+s,D=g.async;if(D&&!e.async)throw new Error("async keyword in sync schema");if(!(b||w)){a+=""+j+" = null;"}a+="var "+d+" = errors;var "+h+";";if(p&&g.$data){P+="}";a+=" if ("+v+" === undefined) { "+h+" = true; } else { ";if(_){P+="}";a+=" "+h+" = "+y+".validateSchema("+v+"); if ("+h+") { "}}if(b){if(g.statements){a+=" "+S.validate+" "}else{a+=" "+h+" = "+S.validate+"; "}}else if(w){var $=e.util.copy(e);var P="";$.level++;var A="valid"+$.level;$.schema=S.validate;$.schemaPath="";var I=e.compositeRule;e.compositeRule=$.compositeRule=true;var C=e.validate($).replace(/validate\.schema/g,x);e.compositeRule=$.compositeRule=I;a+=" "+C}else{var O=O||[];O.push(a);a="";a+="  "+x+".call( ";if(e.opts.passContext){a+="this"}else{a+="self"}if(E||g.schema===false){a+=" , "+u+" "}else{a+=" , "+v+" , "+u+" , validate.schema"+e.schemaPath+" "}a+=" , (dataPath || '')";if(e.errorPath!='""'){a+=" + "+e.errorPath}var k=i?"data"+(i-1||""):"parentData",T=i?e.dataPathArr[i]:"parentDataProperty";a+=" , "+k+" , "+T+" , rootData )  ";var z=a;a=O.pop();if(g.errors===false){a+=" "+h+" = ";if(D){a+="await "}a+=""+z+"; "}else{if(D){j="customErrors"+s;a+=" var "+j+" = null; try { "+h+" = await "+z+"; } catch (e) { "+h+" = false; if (e instanceof ValidationError) "+j+" = e.errors; else throw e; } "}else{a+=" "+j+" = null; "+h+" = "+z+"; "}}}if(g.modifying){a+=" if ("+k+") "+u+" = "+k+"["+T+"];"}a+=""+P;if(g.valid){if(f){a+=" if (true) { "}}else{a+=" if ( ";if(g.valid===undefined){a+=" !";if(w){a+=""+A}else{a+=""+h}}else{a+=" "+!g.valid+" "}a+=") { ";c=m.keyword;var O=O||[];O.push(a);a="";var O=O||[];O.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(c||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { keyword: '"+m.keyword+"' } ";if(e.opts.messages!==false){a+=" , message: 'should pass \""+m.keyword+"\" keyword validation' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}var L=a;a=O.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+L+"]); "}else{a+=" validate.errors = ["+L+"]; return false; "}}else{a+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}var N=a;a=O.pop();if(b){if(g.errors){if(g.errors!="full"){a+="  for (var "+R+"="+d+"; "+R+"<errors; "+R+"++) { var "+F+" = vErrors["+R+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+F+".schemaPath === undefined) { "+F+'.schemaPath = "'+l+'"; } ';if(e.opts.verbose){a+=" "+F+".schema = "+v+"; "+F+".data = "+u+"; "}a+=" } "}}else{if(g.errors===false){a+=" "+N+" "}else{a+=" if ("+d+" == errors) { "+N+" } else {  for (var "+R+"="+d+"; "+R+"<errors; "+R+"++) { var "+F+" = vErrors["+R+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+F+".schemaPath === undefined) { "+F+'.schemaPath = "'+l+'"; } ';if(e.opts.verbose){a+=" "+F+".schema = "+v+"; "+F+".data = "+u+"; "}a+=" } } "}}}else if(w){a+="   var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+(c||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { keyword: '"+m.keyword+"' } ";if(e.opts.messages!==false){a+=" , message: 'should pass \""+m.keyword+"\" keyword validation' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; return false; "}}}else{if(g.errors===false){a+=" "+N+" "}else{a+=" if (Array.isArray("+j+")) { if (vErrors === null) vErrors = "+j+"; else vErrors = vErrors.concat("+j+"); errors = vErrors.length;  for (var "+R+"="+d+"; "+R+"<errors; "+R+"++) { var "+F+" = vErrors["+R+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+";  "+F+'.schemaPath = "'+l+'";  ';if(e.opts.verbose){a+=" "+F+".schema = "+v+"; "+F+".data = "+u+"; "}a+=" } } else { "+N+" } "}}a+=" } ";if(f){a+=" else { "}}return a}},2283:e=>{"use strict";e.exports=function generate_dependencies(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="errs__"+s;var h=e.util.copy(e);var d="";h.level++;var p="valid"+h.level;var v={},m={},y=e.opts.ownProperties;for(b in o){if(b=="__proto__")continue;var g=o[b];var P=Array.isArray(g)?m:v;P[b]=g}a+="var "+u+" = errors;";var E=e.errorPath;a+="var missing"+s+";";for(var b in m){P=m[b];if(P.length){a+=" if ( "+c+e.util.getProperty(b)+" !== undefined ";if(y){a+=" && Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(b)+"') "}if(f){a+=" && ( ";var w=P;if(w){var S,x=-1,_=w.length-1;while(x<_){S=w[x+=1];if(x){a+=" || "}var j=e.util.getProperty(S),R=c+j;a+=" ( ( "+R+" === undefined ";if(y){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(S)+"') "}a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?S:j)+") ) "}}a+=")) {  ";var F="missing"+s,D="' + "+F+" + '";if(e.opts._errorDataPathProperty){e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(E,F,true):E+" + "+F}var $=$||[];$.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"dependencies"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(b)+"', missingProperty: '"+D+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(P.length==1?P[0]:P.join(", "))+"' } ";if(e.opts.messages!==false){a+=" , message: 'should have ";if(P.length==1){a+="property "+e.util.escapeQuotes(P[0])}else{a+="properties "+e.util.escapeQuotes(P.join(", "))}a+=" when property "+e.util.escapeQuotes(b)+" is present' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var A=a;a=$.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+A+"]); "}else{a+=" validate.errors = ["+A+"]; return false; "}}else{a+=" var err = "+A+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}}else{a+=" ) { ";var I=P;if(I){var S,C=-1,O=I.length-1;while(C<O){S=I[C+=1];var j=e.util.getProperty(S),D=e.util.escapeQuotes(S),R=c+j;if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPath(E,S,e.opts.jsonPointers)}a+=" if ( "+R+" === undefined ";if(y){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(S)+"') "}a+=") {  var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"dependencies"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(b)+"', missingProperty: '"+D+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(P.length==1?P[0]:P.join(", "))+"' } ";if(e.opts.messages!==false){a+=" , message: 'should have ";if(P.length==1){a+="property "+e.util.escapeQuotes(P[0])}else{a+="properties "+e.util.escapeQuotes(P.join(", "))}a+=" when property "+e.util.escapeQuotes(b)+" is present' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}}a+=" }   ";if(f){d+="}";a+=" else { "}}}e.errorPath=E;var k=h.baseId;for(var b in v){var g=v[b];if(e.opts.strictKeywords?typeof g=="object"&&Object.keys(g).length>0||g===false:e.util.schemaHasRules(g,e.RULES.all)){a+=" "+p+" = true; if ( "+c+e.util.getProperty(b)+" !== undefined ";if(y){a+=" && Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(b)+"') "}a+=") { ";h.schema=g;h.schemaPath=n+e.util.getProperty(b);h.errSchemaPath=l+"/"+e.util.escapeFragment(b);a+="  "+e.validate(h)+" ";h.baseId=k;a+=" }  ";if(f){a+=" if ("+p+") { ";d+="}"}}}if(f){a+="   "+d+" if ("+u+" == errors) {"}return a}},2783:e=>{"use strict";e.exports=function generate_enum(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}var p="i"+s,v="schema"+s;if(!h){a+=" var "+v+" = validate.schema"+n+";"}a+="var "+u+";";if(h){a+=" if (schema"+s+" === undefined) "+u+" = true; else if (!Array.isArray(schema"+s+")) "+u+" = false; else {"}a+=""+u+" = false;for (var "+p+"=0; "+p+"<"+v+".length; "+p+"++) if (equal("+c+", "+v+"["+p+"])) { "+u+" = true; break; }";if(h){a+="  }  "}a+=" if (!"+u+") {   ";var m=m||[];m.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"enum"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { allowedValues: schema"+s+" } ";if(e.opts.messages!==false){a+=" , message: 'should be equal to one of the allowed values' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var y=a;a=m.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+y+"]); "}else{a+=" validate.errors = ["+y+"]; return false; "}}else{a+=" var err = "+y+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" }";if(f){a+=" else { "}return a}},9175:e=>{"use strict";e.exports=function generate_format(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");if(e.opts.format===false){if(f){a+=" if (true) { "}return a}var u=e.opts.$data&&o&&o.$data,h;if(u){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";h="schema"+s}else{h=o}var d=e.opts.unknownFormats,p=Array.isArray(d);if(u){var v="format"+s,m="isObject"+s,y="formatType"+s;a+=" var "+v+" = formats["+h+"]; var "+m+" = typeof "+v+" == 'object' && !("+v+" instanceof RegExp) && "+v+".validate; var "+y+" = "+m+" && "+v+".type || 'string'; if ("+m+") { ";if(e.async){a+=" var async"+s+" = "+v+".async; "}a+=" "+v+" = "+v+".validate; } if (  ";if(u){a+=" ("+h+" !== undefined && typeof "+h+" != 'string') || "}a+=" (";if(d!="ignore"){a+=" ("+h+" && !"+v+" ";if(p){a+=" && self._opts.unknownFormats.indexOf("+h+") == -1 "}a+=") || "}a+=" ("+v+" && "+y+" == '"+t+"' && !(typeof "+v+" == 'function' ? ";if(e.async){a+=" (async"+s+" ? await "+v+"("+c+") : "+v+"("+c+")) "}else{a+=" "+v+"("+c+") "}a+=" : "+v+".test("+c+"))))) {"}else{var v=e.formats[o];if(!v){if(d=="ignore"){e.logger.warn('unknown format "'+o+'" ignored in schema at path "'+e.errSchemaPath+'"');if(f){a+=" if (true) { "}return a}else if(p&&d.indexOf(o)>=0){if(f){a+=" if (true) { "}return a}else{throw new Error('unknown format "'+o+'" is used in schema at path "'+e.errSchemaPath+'"')}}var m=typeof v=="object"&&!(v instanceof RegExp)&&v.validate;var y=m&&v.type||"string";if(m){var g=v.async===true;v=v.validate}if(y!=t){if(f){a+=" if (true) { "}return a}if(g){if(!e.async)throw new Error("async format in sync schema");var P="formats"+e.util.getProperty(o)+".validate";a+=" if (!(await "+P+"("+c+"))) { "}else{a+=" if (! ";var P="formats"+e.util.getProperty(o);if(m)P+=".validate";if(typeof v=="function"){a+=" "+P+"("+c+") "}else{a+=" "+P+".test("+c+") "}a+=") { "}}var E=E||[];E.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"format"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { format:  ";if(u){a+=""+h}else{a+=""+e.util.toQuotedString(o)}a+="  } ";if(e.opts.messages!==false){a+=" , message: 'should match format \"";if(u){a+="' + "+h+" + '"}else{a+=""+e.util.escapeQuotes(o)}a+="\"' "}if(e.opts.verbose){a+=" , schema:  ";if(u){a+="validate.schema"+n}else{a+=""+e.util.toQuotedString(o)}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var b=a;a=E.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+b+"]); "}else{a+=" validate.errors = ["+b+"]; return false; "}}else{a+=" var err = "+b+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } ";if(f){a+=" else { "}return a}},5859:e=>{"use strict";e.exports=function generate_if(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h="errs__"+s;var d=e.util.copy(e);d.level++;var p="valid"+d.level;var v=e.schema["then"],m=e.schema["else"],y=v!==undefined&&(e.opts.strictKeywords?typeof v=="object"&&Object.keys(v).length>0||v===false:e.util.schemaHasRules(v,e.RULES.all)),g=m!==undefined&&(e.opts.strictKeywords?typeof m=="object"&&Object.keys(m).length>0||m===false:e.util.schemaHasRules(m,e.RULES.all)),P=d.baseId;if(y||g){var E;d.createErrors=false;d.schema=o;d.schemaPath=n;d.errSchemaPath=l;a+=" var "+h+" = errors; var "+u+" = true;  ";var b=e.compositeRule;e.compositeRule=d.compositeRule=true;a+="  "+e.validate(d)+" ";d.baseId=P;d.createErrors=true;a+="  errors = "+h+"; if (vErrors !== null) { if ("+h+") vErrors.length = "+h+"; else vErrors = null; }  ";e.compositeRule=d.compositeRule=b;if(y){a+=" if ("+p+") {  ";d.schema=e.schema["then"];d.schemaPath=e.schemaPath+".then";d.errSchemaPath=e.errSchemaPath+"/then";a+="  "+e.validate(d)+" ";d.baseId=P;a+=" "+u+" = "+p+"; ";if(y&&g){E="ifClause"+s;a+=" var "+E+" = 'then'; "}else{E="'then'"}a+=" } ";if(g){a+=" else { "}}else{a+=" if (!"+p+") { "}if(g){d.schema=e.schema["else"];d.schemaPath=e.schemaPath+".else";d.errSchemaPath=e.errSchemaPath+"/else";a+="  "+e.validate(d)+" ";d.baseId=P;a+=" "+u+" = "+p+"; ";if(y&&g){E="ifClause"+s;a+=" var "+E+" = 'else'; "}else{E="'else'"}a+=" } "}a+=" if (!"+u+") {   var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"if"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { failingKeyword: "+E+" } ";if(e.opts.messages!==false){a+=" , message: 'should match \"' + "+E+" + '\" schema' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; return false; "}}a+=" }   ";if(f){a+=" else { "}}else{if(f){a+=" if (true) { "}}return a}},6964:(e,r,t)=>{"use strict";e.exports={$ref:t(1473),allOf:t(4378),anyOf:t(9278),$comment:t(9263),const:t(5326),contains:t(7922),dependencies:t(2283),enum:t(2783),format:t(9175),if:t(5859),items:t(9187),maximum:t(4130),minimum:t(4130),maxItems:t(3472),minItems:t(3472),maxLength:t(9018),minLength:t(9018),maxProperties:t(8740),minProperties:t(8740),multipleOf:t(2644),not:t(4806),oneOf:t(1853),pattern:t(2944),properties:t(1615),propertyNames:t(6610),required:t(6172),uniqueItems:t(2370),validate:t(7003)}},9187:e=>{"use strict";e.exports=function generate_items(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h="errs__"+s;var d=e.util.copy(e);var p="";d.level++;var v="valid"+d.level;var m="i"+s,y=d.dataLevel=e.dataLevel+1,g="data"+y,P=e.baseId;a+="var "+h+" = errors;var "+u+";";if(Array.isArray(o)){var E=e.schema.additionalItems;if(E===false){a+=" "+u+" = "+c+".length <= "+o.length+"; ";var b=l;l=e.errSchemaPath+"/additionalItems";a+="  if (!"+u+") {   ";var w=w||[];w.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"additionalItems"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+o.length+" } ";if(e.opts.messages!==false){a+=" , message: 'should NOT have more than "+o.length+" items' "}if(e.opts.verbose){a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var S=a;a=w.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+S+"]); "}else{a+=" validate.errors = ["+S+"]; return false; "}}else{a+=" var err = "+S+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } ";l=b;if(f){p+="}";a+=" else { "}}var x=o;if(x){var _,j=-1,R=x.length-1;while(j<R){_=x[j+=1];if(e.opts.strictKeywords?typeof _=="object"&&Object.keys(_).length>0||_===false:e.util.schemaHasRules(_,e.RULES.all)){a+=" "+v+" = true; if ("+c+".length > "+j+") { ";var F=c+"["+j+"]";d.schema=_;d.schemaPath=n+"["+j+"]";d.errSchemaPath=l+"/"+j;d.errorPath=e.util.getPathExpr(e.errorPath,j,e.opts.jsonPointers,true);d.dataPathArr[y]=j;var D=e.validate(d);d.baseId=P;if(e.util.varOccurences(D,g)<2){a+=" "+e.util.varReplace(D,g,F)+" "}else{a+=" var "+g+" = "+F+"; "+D+" "}a+=" }  ";if(f){a+=" if ("+v+") { ";p+="}"}}}}if(typeof E=="object"&&(e.opts.strictKeywords?typeof E=="object"&&Object.keys(E).length>0||E===false:e.util.schemaHasRules(E,e.RULES.all))){d.schema=E;d.schemaPath=e.schemaPath+".additionalItems";d.errSchemaPath=e.errSchemaPath+"/additionalItems";a+=" "+v+" = true; if ("+c+".length > "+o.length+") {  for (var "+m+" = "+o.length+"; "+m+" < "+c+".length; "+m+"++) { ";d.errorPath=e.util.getPathExpr(e.errorPath,m,e.opts.jsonPointers,true);var F=c+"["+m+"]";d.dataPathArr[y]=m;var D=e.validate(d);d.baseId=P;if(e.util.varOccurences(D,g)<2){a+=" "+e.util.varReplace(D,g,F)+" "}else{a+=" var "+g+" = "+F+"; "+D+" "}if(f){a+=" if (!"+v+") break; "}a+=" } }  ";if(f){a+=" if ("+v+") { ";p+="}"}}}else if(e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===false:e.util.schemaHasRules(o,e.RULES.all)){d.schema=o;d.schemaPath=n;d.errSchemaPath=l;a+="  for (var "+m+" = "+0+"; "+m+" < "+c+".length; "+m+"++) { ";d.errorPath=e.util.getPathExpr(e.errorPath,m,e.opts.jsonPointers,true);var F=c+"["+m+"]";d.dataPathArr[y]=m;var D=e.validate(d);d.baseId=P;if(e.util.varOccurences(D,g)<2){a+=" "+e.util.varReplace(D,g,F)+" "}else{a+=" var "+g+" = "+F+"; "+D+" "}if(f){a+=" if (!"+v+") break; "}a+=" }"}if(f){a+=" "+p+" if ("+h+" == errors) {"}return a}},2644:e=>{"use strict";e.exports=function generate_multipleOf(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u=e.opts.$data&&o&&o.$data,h;if(u){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";h="schema"+s}else{h=o}if(!(u||typeof o=="number")){throw new Error(r+" must be number")}a+="var division"+s+";if (";if(u){a+=" "+h+" !== undefined && ( typeof "+h+" != 'number' || "}a+=" (division"+s+" = "+c+" / "+h+", ";if(e.opts.multipleOfPrecision){a+=" Math.abs(Math.round(division"+s+") - division"+s+") > 1e-"+e.opts.multipleOfPrecision+" "}else{a+=" division"+s+" !== parseInt(division"+s+") "}a+=" ) ";if(u){a+="  )  "}a+=" ) {   ";var d=d||[];d.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"multipleOf"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { multipleOf: "+h+" } ";if(e.opts.messages!==false){a+=" , message: 'should be multiple of ";if(u){a+="' + "+h}else{a+=""+h+"'"}}if(e.opts.verbose){a+=" , schema:  ";if(u){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var p=a;a=d.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+p+"]); "}else{a+=" validate.errors = ["+p+"]; return false; "}}else{a+=" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+="} ";if(f){a+=" else { "}return a}},4806:e=>{"use strict";e.exports=function generate_not(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="errs__"+s;var h=e.util.copy(e);h.level++;var d="valid"+h.level;if(e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===false:e.util.schemaHasRules(o,e.RULES.all)){h.schema=o;h.schemaPath=n;h.errSchemaPath=l;a+=" var "+u+" = errors;  ";var p=e.compositeRule;e.compositeRule=h.compositeRule=true;h.createErrors=false;var v;if(h.opts.allErrors){v=h.opts.allErrors;h.opts.allErrors=false}a+=" "+e.validate(h)+" ";h.createErrors=true;if(v)h.opts.allErrors=v;e.compositeRule=h.compositeRule=p;a+=" if ("+d+") {   ";var m=m||[];m.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"not"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: 'should NOT be valid' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var y=a;a=m.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+y+"]); "}else{a+=" validate.errors = ["+y+"]; return false; "}}else{a+=" var err = "+y+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } else {  errors = "+u+"; if (vErrors !== null) { if ("+u+") vErrors.length = "+u+"; else vErrors = null; } ";if(e.opts.allErrors){a+=" } "}}else{a+="  var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"not"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: 'should NOT be valid' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(f){a+=" if (false) { "}}return a}},1853:e=>{"use strict";e.exports=function generate_oneOf(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h="errs__"+s;var d=e.util.copy(e);var p="";d.level++;var v="valid"+d.level;var m=d.baseId,y="prevValid"+s,g="passingSchemas"+s;a+="var "+h+" = errors , "+y+" = false , "+u+" = false , "+g+" = null; ";var P=e.compositeRule;e.compositeRule=d.compositeRule=true;var E=o;if(E){var b,w=-1,S=E.length-1;while(w<S){b=E[w+=1];if(e.opts.strictKeywords?typeof b=="object"&&Object.keys(b).length>0||b===false:e.util.schemaHasRules(b,e.RULES.all)){d.schema=b;d.schemaPath=n+"["+w+"]";d.errSchemaPath=l+"/"+w;a+="  "+e.validate(d)+" ";d.baseId=m}else{a+=" var "+v+" = true; "}if(w){a+=" if ("+v+" && "+y+") { "+u+" = false; "+g+" = ["+g+", "+w+"]; } else { ";p+="}"}a+=" if ("+v+") { "+u+" = "+y+" = true; "+g+" = "+w+"; }"}}e.compositeRule=d.compositeRule=P;a+=""+p+"if (!"+u+") {   var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"oneOf"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { passingSchemas: "+g+" } ";if(e.opts.messages!==false){a+=" , message: 'should match exactly one schema in oneOf' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; return false; "}}a+="} else {  errors = "+h+"; if (vErrors !== null) { if ("+h+") vErrors.length = "+h+"; else vErrors = null; }";if(e.opts.allErrors){a+=" } "}return a}},2944:e=>{"use strict";e.exports=function generate_pattern(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u=e.opts.$data&&o&&o.$data,h;if(u){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";h="schema"+s}else{h=o}var d=u?"(new RegExp("+h+"))":e.usePattern(o);a+="if ( ";if(u){a+=" ("+h+" !== undefined && typeof "+h+" != 'string') || "}a+=" !"+d+".test("+c+") ) {   ";var p=p||[];p.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"pattern"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { pattern:  ";if(u){a+=""+h}else{a+=""+e.util.toQuotedString(o)}a+="  } ";if(e.opts.messages!==false){a+=" , message: 'should match pattern \"";if(u){a+="' + "+h+" + '"}else{a+=""+e.util.escapeQuotes(o)}a+="\"' "}if(e.opts.verbose){a+=" , schema:  ";if(u){a+="validate.schema"+n}else{a+=""+e.util.toQuotedString(o)}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var v=a;a=p.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+v+"]); "}else{a+=" validate.errors = ["+v+"]; return false; "}}else{a+=" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+="} ";if(f){a+=" else { "}return a}},1615:e=>{"use strict";e.exports=function generate_properties(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="errs__"+s;var h=e.util.copy(e);var d="";h.level++;var p="valid"+h.level;var v="key"+s,m="idx"+s,y=h.dataLevel=e.dataLevel+1,g="data"+y,P="dataProperties"+s;var E=Object.keys(o||{}).filter(notProto),b=e.schema.patternProperties||{},w=Object.keys(b).filter(notProto),S=e.schema.additionalProperties,x=E.length||w.length,_=S===false,j=typeof S=="object"&&Object.keys(S).length,R=e.opts.removeAdditional,F=_||j||R,D=e.opts.ownProperties,$=e.baseId;var A=e.schema.required;if(A&&!(e.opts.$data&&A.$data)&&A.length<e.opts.loopRequired){var I=e.util.toHash(A)}function notProto(e){return e!=="__proto__"}a+="var "+u+" = errors;var "+p+" = true;";if(D){a+=" var "+P+" = undefined;"}if(F){if(D){a+=" "+P+" = "+P+" || Object.keys("+c+"); for (var "+m+"=0; "+m+"<"+P+".length; "+m+"++) { var "+v+" = "+P+"["+m+"]; "}else{a+=" for (var "+v+" in "+c+") { "}if(x){a+=" var isAdditional"+s+" = !(false ";if(E.length){if(E.length>8){a+=" || validate.schema"+n+".hasOwnProperty("+v+") "}else{var C=E;if(C){var O,k=-1,T=C.length-1;while(k<T){O=C[k+=1];a+=" || "+v+" == "+e.util.toQuotedString(O)+" "}}}}if(w.length){var z=w;if(z){var L,N=-1,U=z.length-1;while(N<U){L=z[N+=1];a+=" || "+e.usePattern(L)+".test("+v+") "}}}a+=" ); if (isAdditional"+s+") { "}if(R=="all"){a+=" delete "+c+"["+v+"]; "}else{var q=e.errorPath;var V="' + "+v+" + '";if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers)}if(_){if(R){a+=" delete "+c+"["+v+"]; "}else{a+=" "+p+" = false; ";var Q=l;l=e.errSchemaPath+"/additionalProperties";var M=M||[];M.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"additionalProperties"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { additionalProperty: '"+V+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is an invalid additional property"}else{a+="should NOT have additional properties"}a+="' "}if(e.opts.verbose){a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var H=a;a=M.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+H+"]); "}else{a+=" validate.errors = ["+H+"]; return false; "}}else{a+=" var err = "+H+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}l=Q;if(f){a+=" break; "}}}else if(j){if(R=="failing"){a+=" var "+u+" = errors;  ";var K=e.compositeRule;e.compositeRule=h.compositeRule=true;h.schema=S;h.schemaPath=e.schemaPath+".additionalProperties";h.errSchemaPath=e.errSchemaPath+"/additionalProperties";h.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);var B=c+"["+v+"]";h.dataPathArr[y]=v;var J=e.validate(h);h.baseId=$;if(e.util.varOccurences(J,g)<2){a+=" "+e.util.varReplace(J,g,B)+" "}else{a+=" var "+g+" = "+B+"; "+J+" "}a+=" if (!"+p+") { errors = "+u+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+c+"["+v+"]; }  ";e.compositeRule=h.compositeRule=K}else{h.schema=S;h.schemaPath=e.schemaPath+".additionalProperties";h.errSchemaPath=e.errSchemaPath+"/additionalProperties";h.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);var B=c+"["+v+"]";h.dataPathArr[y]=v;var J=e.validate(h);h.baseId=$;if(e.util.varOccurences(J,g)<2){a+=" "+e.util.varReplace(J,g,B)+" "}else{a+=" var "+g+" = "+B+"; "+J+" "}if(f){a+=" if (!"+p+") break; "}}}e.errorPath=q}if(x){a+=" } "}a+=" }  ";if(f){a+=" if ("+p+") { ";d+="}"}}var G=e.opts.useDefaults&&!e.compositeRule;if(E.length){var Z=E;if(Z){var O,X=-1,Y=Z.length-1;while(X<Y){O=Z[X+=1];var W=o[O];if(e.opts.strictKeywords?typeof W=="object"&&Object.keys(W).length>0||W===false:e.util.schemaHasRules(W,e.RULES.all)){var ee=e.util.getProperty(O),B=c+ee,re=G&&W.default!==undefined;h.schema=W;h.schemaPath=n+ee;h.errSchemaPath=l+"/"+e.util.escapeFragment(O);h.errorPath=e.util.getPath(e.errorPath,O,e.opts.jsonPointers);h.dataPathArr[y]=e.util.toQuotedString(O);var J=e.validate(h);h.baseId=$;if(e.util.varOccurences(J,g)<2){J=e.util.varReplace(J,g,B);var te=B}else{var te=g;a+=" var "+g+" = "+B+"; "}if(re){a+=" "+J+" "}else{if(I&&I[O]){a+=" if ( "+te+" === undefined ";if(D){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(O)+"') "}a+=") { "+p+" = false; ";var q=e.errorPath,Q=l,ae=e.util.escapeQuotes(O);if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPath(q,O,e.opts.jsonPointers)}l=e.errSchemaPath+"/required";var M=M||[];M.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+ae+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+ae+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var H=a;a=M.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+H+"]); "}else{a+=" validate.errors = ["+H+"]; return false; "}}else{a+=" var err = "+H+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}l=Q;e.errorPath=q;a+=" } else { "}else{if(f){a+=" if ( "+te+" === undefined ";if(D){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(O)+"') "}a+=") { "+p+" = true; } else { "}else{a+=" if ("+te+" !== undefined ";if(D){a+=" &&   Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes(O)+"') "}a+=" ) { "}}a+=" "+J+" } "}}if(f){a+=" if ("+p+") { ";d+="}"}}}}if(w.length){var se=w;if(se){var L,ie=-1,oe=se.length-1;while(ie<oe){L=se[ie+=1];var W=b[L];if(e.opts.strictKeywords?typeof W=="object"&&Object.keys(W).length>0||W===false:e.util.schemaHasRules(W,e.RULES.all)){h.schema=W;h.schemaPath=e.schemaPath+".patternProperties"+e.util.getProperty(L);h.errSchemaPath=e.errSchemaPath+"/patternProperties/"+e.util.escapeFragment(L);if(D){a+=" "+P+" = "+P+" || Object.keys("+c+"); for (var "+m+"=0; "+m+"<"+P+".length; "+m+"++) { var "+v+" = "+P+"["+m+"]; "}else{a+=" for (var "+v+" in "+c+") { "}a+=" if ("+e.usePattern(L)+".test("+v+")) { ";h.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);var B=c+"["+v+"]";h.dataPathArr[y]=v;var J=e.validate(h);h.baseId=$;if(e.util.varOccurences(J,g)<2){a+=" "+e.util.varReplace(J,g,B)+" "}else{a+=" var "+g+" = "+B+"; "+J+" "}if(f){a+=" if (!"+p+") break; "}a+=" } ";if(f){a+=" else "+p+" = true; "}a+=" }  ";if(f){a+=" if ("+p+") { ";d+="}"}}}}}if(f){a+=" "+d+" if ("+u+" == errors) {"}return a}},6610:e=>{"use strict";e.exports=function generate_propertyNames(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="errs__"+s;var h=e.util.copy(e);var d="";h.level++;var p="valid"+h.level;a+="var "+u+" = errors;";if(e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===false:e.util.schemaHasRules(o,e.RULES.all)){h.schema=o;h.schemaPath=n;h.errSchemaPath=l;var v="key"+s,m="idx"+s,y="i"+s,g="' + "+v+" + '",P=h.dataLevel=e.dataLevel+1,E="data"+P,b="dataProperties"+s,w=e.opts.ownProperties,S=e.baseId;if(w){a+=" var "+b+" = undefined; "}if(w){a+=" "+b+" = "+b+" || Object.keys("+c+"); for (var "+m+"=0; "+m+"<"+b+".length; "+m+"++) { var "+v+" = "+b+"["+m+"]; "}else{a+=" for (var "+v+" in "+c+") { "}a+=" var startErrs"+s+" = errors; ";var x=v;var _=e.compositeRule;e.compositeRule=h.compositeRule=true;var j=e.validate(h);h.baseId=S;if(e.util.varOccurences(j,E)<2){a+=" "+e.util.varReplace(j,E,x)+" "}else{a+=" var "+E+" = "+x+"; "+j+" "}e.compositeRule=h.compositeRule=_;a+=" if (!"+p+") { for (var "+y+"=startErrs"+s+"; "+y+"<errors; "+y+"++) { vErrors["+y+"].propertyName = "+v+"; }   var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"propertyNames"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { propertyName: '"+g+"' } ";if(e.opts.messages!==false){a+=" , message: 'property name \\'"+g+"\\' is invalid' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; return false; "}}if(f){a+=" break; "}a+=" } }"}if(f){a+=" "+d+" if ("+u+" == errors) {"}return a}},1473:e=>{"use strict";e.exports=function generate_ref(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.errSchemaPath+"/"+r;var l=!e.opts.allErrors;var f="data"+(i||"");var c="valid"+s;var u,h;if(o=="#"||o=="#/"){if(e.isRoot){u=e.async;h="validate"}else{u=e.root.schema.$async===true;h="root.refVal[0]"}}else{var d=e.resolveRef(e.baseId,o,e.isRoot);if(d===undefined){var p=e.MissingRefError.message(e.baseId,o);if(e.opts.missingRefs=="fail"){e.logger.error(p);var v=v||[];v.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"$ref"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(n)+" , params: { ref: '"+e.util.escapeQuotes(o)+"' } ";if(e.opts.messages!==false){a+=" , message: 'can\\'t resolve reference "+e.util.escapeQuotes(o)+"' "}if(e.opts.verbose){a+=" , schema: "+e.util.toQuotedString(o)+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+f+" "}a+=" } "}else{a+=" {} "}var m=a;a=v.pop();if(!e.compositeRule&&l){if(e.async){a+=" throw new ValidationError(["+m+"]); "}else{a+=" validate.errors = ["+m+"]; return false; "}}else{a+=" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}if(l){a+=" if (false) { "}}else if(e.opts.missingRefs=="ignore"){e.logger.warn(p);if(l){a+=" if (true) { "}}else{throw new e.MissingRefError(e.baseId,o,p)}}else if(d.inline){var y=e.util.copy(e);y.level++;var g="valid"+y.level;y.schema=d.schema;y.schemaPath="";y.errSchemaPath=o;var P=e.validate(y).replace(/validate\.schema/g,d.code);a+=" "+P+" ";if(l){a+=" if ("+g+") { "}}else{u=d.$async===true||e.async&&d.$async!==false;h=d.code}}if(h){var v=v||[];v.push(a);a="";if(e.opts.passContext){a+=" "+h+".call(this, "}else{a+=" "+h+"( "}a+=" "+f+", (dataPath || '')";if(e.errorPath!='""'){a+=" + "+e.errorPath}var E=i?"data"+(i-1||""):"parentData",b=i?e.dataPathArr[i]:"parentDataProperty";a+=" , "+E+" , "+b+", rootData)  ";var w=a;a=v.pop();if(u){if(!e.async)throw new Error("async schema referenced by sync schema");if(l){a+=" var "+c+"; "}a+=" try { await "+w+"; ";if(l){a+=" "+c+" = true; "}a+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ";if(l){a+=" "+c+" = false; "}a+=" } ";if(l){a+=" if ("+c+") { "}}else{a+=" if (!"+w+") { if (vErrors === null) vErrors = "+h+".errors; else vErrors = vErrors.concat("+h+".errors); errors = vErrors.length; } ";if(l){a+=" else { "}}}return a}},6172:e=>{"use strict";e.exports=function generate_required(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}var p="schema"+s;if(!h){if(o.length<e.opts.loopRequired&&e.schema.properties&&Object.keys(e.schema.properties).length){var v=[];var m=o;if(m){var y,g=-1,P=m.length-1;while(g<P){y=m[g+=1];var E=e.schema.properties[y];if(!(E&&(e.opts.strictKeywords?typeof E=="object"&&Object.keys(E).length>0||E===false:e.util.schemaHasRules(E,e.RULES.all)))){v[v.length]=y}}}}else{var v=o}}if(h||v.length){var b=e.errorPath,w=h||v.length>=e.opts.loopRequired,S=e.opts.ownProperties;if(f){a+=" var missing"+s+"; ";if(w){if(!h){a+=" var "+p+" = validate.schema"+n+"; "}var x="i"+s,_="schema"+s+"["+x+"]",j="' + "+_+" + '";if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPathExpr(b,_,e.opts.jsonPointers)}a+=" var "+u+" = true; ";if(h){a+=" if (schema"+s+" === undefined) "+u+" = true; else if (!Array.isArray(schema"+s+")) "+u+" = false; else {"}a+=" for (var "+x+" = 0; "+x+" < "+p+".length; "+x+"++) { "+u+" = "+c+"["+p+"["+x+"]] !== undefined ";if(S){a+=" &&   Object.prototype.hasOwnProperty.call("+c+", "+p+"["+x+"]) "}a+="; if (!"+u+") break; } ";if(h){a+="  }  "}a+="  if (!"+u+") {   ";var R=R||[];R.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+j+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+j+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var F=a;a=R.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+F+"]); "}else{a+=" validate.errors = ["+F+"]; return false; "}}else{a+=" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } else { "}else{a+=" if ( ";var D=v;if(D){var $,x=-1,A=D.length-1;while(x<A){$=D[x+=1];if(x){a+=" || "}var I=e.util.getProperty($),C=c+I;a+=" ( ( "+C+" === undefined ";if(S){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes($)+"') "}a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?$:I)+") ) "}}a+=") {  ";var _="missing"+s,j="' + "+_+" + '";if(e.opts._errorDataPathProperty){e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(b,_,true):b+" + "+_}var R=R||[];R.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+j+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+j+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var F=a;a=R.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+F+"]); "}else{a+=" validate.errors = ["+F+"]; return false; "}}else{a+=" var err = "+F+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } else { "}}else{if(w){if(!h){a+=" var "+p+" = validate.schema"+n+"; "}var x="i"+s,_="schema"+s+"["+x+"]",j="' + "+_+" + '";if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPathExpr(b,_,e.opts.jsonPointers)}if(h){a+=" if ("+p+" && !Array.isArray("+p+")) {  var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+j+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+j+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+p+" !== undefined) { "}a+=" for (var "+x+" = 0; "+x+" < "+p+".length; "+x+"++) { if ("+c+"["+p+"["+x+"]] === undefined ";if(S){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", "+p+"["+x+"]) "}a+=") {  var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+j+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+j+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ";if(h){a+="  }  "}}else{var O=v;if(O){var $,k=-1,T=O.length-1;while(k<T){$=O[k+=1];var I=e.util.getProperty($),j=e.util.escapeQuotes($),C=c+I;if(e.opts._errorDataPathProperty){e.errorPath=e.util.getPath(b,$,e.opts.jsonPointers)}a+=" if ( "+C+" === undefined ";if(S){a+=" || ! Object.prototype.hasOwnProperty.call("+c+", '"+e.util.escapeQuotes($)+"') "}a+=") {  var err =   ";if(e.createErrors!==false){a+=" { keyword: '"+"required"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+j+"' } ";if(e.opts.messages!==false){a+=" , message: '";if(e.opts._errorDataPathProperty){a+="is a required property"}else{a+="should have required property \\'"+j+"\\'"}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}}}e.errorPath=b}else if(f){a+=" if (true) {"}return a}},2370:e=>{"use strict";e.exports=function generate_uniqueItems(e,r,t){var a=" ";var s=e.level;var i=e.dataLevel;var o=e.schema[r];var n=e.schemaPath+e.util.getProperty(r);var l=e.errSchemaPath+"/"+r;var f=!e.opts.allErrors;var c="data"+(i||"");var u="valid"+s;var h=e.opts.$data&&o&&o.$data,d;if(h){a+=" var schema"+s+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ";d="schema"+s}else{d=o}if((o||h)&&e.opts.uniqueItems!==false){if(h){a+=" var "+u+"; if ("+d+" === false || "+d+" === undefined) "+u+" = true; else if (typeof "+d+" != 'boolean') "+u+" = false; else { "}a+=" var i = "+c+".length , "+u+" = true , j; if (i > 1) { ";var p=e.schema.items&&e.schema.items.type,v=Array.isArray(p);if(!p||p=="object"||p=="array"||v&&(p.indexOf("object")>=0||p.indexOf("array")>=0)){a+=" outer: for (;i--;) { for (j = i; j--;) { if (equal("+c+"[i], "+c+"[j])) { "+u+" = false; break outer; } } } "}else{a+=" var itemIndices = {}, item; for (;i--;) { var item = "+c+"[i]; ";var m="checkDataType"+(v?"s":"");a+=" if ("+e.util[m](p,"item",e.opts.strictNumbers,true)+") continue; ";if(v){a+=" if (typeof item == 'string') item = '\"' + item; "}a+=" if (typeof itemIndices[item] == 'number') { "+u+" = false; j = itemIndices[item]; break; } itemIndices[item] = i; } "}a+=" } ";if(h){a+="  }  "}a+=" if (!"+u+") {   ";var y=y||[];y.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+"uniqueItems"+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { i: i, j: j } ";if(e.opts.messages!==false){a+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' "}if(e.opts.verbose){a+=" , schema:  ";if(h){a+="validate.schema"+n}else{a+=""+o}a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "}a+=" } "}else{a+=" {} "}var g=a;a=y.pop();if(!e.compositeRule&&f){if(e.async){a+=" throw new ValidationError(["+g+"]); "}else{a+=" validate.errors = ["+g+"]; return false; "}}else{a+=" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } ";if(f){a+=" else { "}}else{if(f){a+=" if (true) { "}}return a}},7003:e=>{"use strict";e.exports=function generate_validate(e,r,t){var a="";var s=e.schema.$async===true,i=e.util.schemaHasRulesExcept(e.schema,e.RULES.all,"$ref"),o=e.self._getId(e.schema);if(e.opts.strictKeywords){var n=e.util.schemaUnknownRules(e.schema,e.RULES.keywords);if(n){var l="unknown keyword: "+n;if(e.opts.strictKeywords==="log")e.logger.warn(l);else throw new Error(l)}}if(e.isTop){a+=" var validate = ";if(s){e.async=true;a+="async "}a+="function(data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ";if(o&&(e.opts.sourceCode||e.opts.processCode)){a+=" "+("/*# sourceURL="+o+" */")+" "}}if(typeof e.schema=="boolean"||!(i||e.schema.$ref)){var r="false schema";var f=e.level;var c=e.dataLevel;var u=e.schema[r];var h=e.schemaPath+e.util.getProperty(r);var d=e.errSchemaPath+"/"+r;var p=!e.opts.allErrors;var v;var m="data"+(c||"");var y="valid"+f;if(e.schema===false){if(e.isTop){p=true}else{a+=" var "+y+" = false; "}var g=g||[];g.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(v||"false schema")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: {} ";if(e.opts.messages!==false){a+=" , message: 'boolean schema is false' "}if(e.opts.verbose){a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "}a+=" } "}else{a+=" {} "}var P=a;a=g.pop();if(!e.compositeRule&&p){if(e.async){a+=" throw new ValidationError(["+P+"]); "}else{a+=" validate.errors = ["+P+"]; return false; "}}else{a+=" var err = "+P+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}}else{if(e.isTop){if(s){a+=" return data; "}else{a+=" validate.errors = null; return true; "}}else{a+=" var "+y+" = true; "}}if(e.isTop){a+=" }; return validate; "}return a}if(e.isTop){var E=e.isTop,f=e.level=0,c=e.dataLevel=0,m="data";e.rootId=e.resolve.fullPath(e.self._getId(e.root.schema));e.baseId=e.baseId||e.rootId;delete e.isTop;e.dataPathArr=[""];if(e.schema.default!==undefined&&e.opts.useDefaults&&e.opts.strictDefaults){var b="default is ignored in the schema root";if(e.opts.strictDefaults==="log")e.logger.warn(b);else throw new Error(b)}a+=" var vErrors = null; ";a+=" var errors = 0;     ";a+=" if (rootData === undefined) rootData = data; "}else{var f=e.level,c=e.dataLevel,m="data"+(c||"");if(o)e.baseId=e.resolve.url(e.baseId,o);if(s&&!e.async)throw new Error("async schema in sync schema");a+=" var errs_"+f+" = errors;"}var y="valid"+f,p=!e.opts.allErrors,w="",S="";var v;var x=e.schema.type,_=Array.isArray(x);if(x&&e.opts.nullable&&e.schema.nullable===true){if(_){if(x.indexOf("null")==-1)x=x.concat("null")}else if(x!="null"){x=[x,"null"];_=true}}if(_&&x.length==1){x=x[0];_=false}if(e.schema.$ref&&i){if(e.opts.extendRefs=="fail"){throw new Error('$ref: validation keywords used in schema at path "'+e.errSchemaPath+'" (see option extendRefs)')}else if(e.opts.extendRefs!==true){i=false;e.logger.warn('$ref: keywords ignored in schema at path "'+e.errSchemaPath+'"')}}if(e.schema.$comment&&e.opts.$comment){a+=" "+e.RULES.all.$comment.code(e,"$comment")}if(x){if(e.opts.coerceTypes){var j=e.util.coerceToTypes(e.opts.coerceTypes,x)}var R=e.RULES.types[x];if(j||_||R===true||R&&!$shouldUseGroup(R)){var h=e.schemaPath+".type",d=e.errSchemaPath+"/type";var h=e.schemaPath+".type",d=e.errSchemaPath+"/type",F=_?"checkDataTypes":"checkDataType";a+=" if ("+e.util[F](x,m,e.opts.strictNumbers,true)+") { ";if(j){var D="dataType"+f,$="coerced"+f;a+=" var "+D+" = typeof "+m+"; var "+$+" = undefined; ";if(e.opts.coerceTypes=="array"){a+=" if ("+D+" == 'object' && Array.isArray("+m+") && "+m+".length == 1) { "+m+" = "+m+"[0]; "+D+" = typeof "+m+"; if ("+e.util.checkDataType(e.schema.type,m,e.opts.strictNumbers)+") "+$+" = "+m+"; } "}a+=" if ("+$+" !== undefined) ; ";var A=j;if(A){var I,C=-1,O=A.length-1;while(C<O){I=A[C+=1];if(I=="string"){a+=" else if ("+D+" == 'number' || "+D+" == 'boolean') "+$+" = '' + "+m+"; else if ("+m+" === null) "+$+" = ''; "}else if(I=="number"||I=="integer"){a+=" else if ("+D+" == 'boolean' || "+m+" === null || ("+D+" == 'string' && "+m+" && "+m+" == +"+m+" ";if(I=="integer"){a+=" && !("+m+" % 1)"}a+=")) "+$+" = +"+m+"; "}else if(I=="boolean"){a+=" else if ("+m+" === 'false' || "+m+" === 0 || "+m+" === null) "+$+" = false; else if ("+m+" === 'true' || "+m+" === 1) "+$+" = true; "}else if(I=="null"){a+=" else if ("+m+" === '' || "+m+" === 0 || "+m+" === false) "+$+" = null; "}else if(e.opts.coerceTypes=="array"&&I=="array"){a+=" else if ("+D+" == 'string' || "+D+" == 'number' || "+D+" == 'boolean' || "+m+" == null) "+$+" = ["+m+"]; "}}}a+=" else {   ";var g=g||[];g.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(v||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { type: '";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' } ";if(e.opts.messages!==false){a+=" , message: 'should be ";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "}a+=" } "}else{a+=" {} "}var P=a;a=g.pop();if(!e.compositeRule&&p){if(e.async){a+=" throw new ValidationError(["+P+"]); "}else{a+=" validate.errors = ["+P+"]; return false; "}}else{a+=" var err = "+P+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } if ("+$+" !== undefined) {  ";var k=c?"data"+(c-1||""):"parentData",T=c?e.dataPathArr[c]:"parentDataProperty";a+=" "+m+" = "+$+"; ";if(!c){a+="if ("+k+" !== undefined)"}a+=" "+k+"["+T+"] = "+$+"; } "}else{var g=g||[];g.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(v||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { type: '";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' } ";if(e.opts.messages!==false){a+=" , message: 'should be ";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "}a+=" } "}else{a+=" {} "}var P=a;a=g.pop();if(!e.compositeRule&&p){if(e.async){a+=" throw new ValidationError(["+P+"]); "}else{a+=" validate.errors = ["+P+"]; return false; "}}else{a+=" var err = "+P+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}}a+=" } "}}if(e.schema.$ref&&!i){a+=" "+e.RULES.all.$ref.code(e,"$ref")+" ";if(p){a+=" } if (errors === ";if(E){a+="0"}else{a+="errs_"+f}a+=") { ";S+="}"}}else{var z=e.RULES;if(z){var R,L=-1,N=z.length-1;while(L<N){R=z[L+=1];if($shouldUseGroup(R)){if(R.type){a+=" if ("+e.util.checkDataType(R.type,m,e.opts.strictNumbers)+") { "}if(e.opts.useDefaults){if(R.type=="object"&&e.schema.properties){var u=e.schema.properties,U=Object.keys(u);var q=U;if(q){var V,Q=-1,M=q.length-1;while(Q<M){V=q[Q+=1];var H=u[V];if(H.default!==undefined){var K=m+e.util.getProperty(V);if(e.compositeRule){if(e.opts.strictDefaults){var b="default is ignored for: "+K;if(e.opts.strictDefaults==="log")e.logger.warn(b);else throw new Error(b)}}else{a+=" if ("+K+" === undefined ";if(e.opts.useDefaults=="empty"){a+=" || "+K+" === null || "+K+" === '' "}a+=" ) "+K+" = ";if(e.opts.useDefaults=="shared"){a+=" "+e.useDefault(H.default)+" "}else{a+=" "+JSON.stringify(H.default)+" "}a+="; "}}}}}else if(R.type=="array"&&Array.isArray(e.schema.items)){var B=e.schema.items;if(B){var H,C=-1,J=B.length-1;while(C<J){H=B[C+=1];if(H.default!==undefined){var K=m+"["+C+"]";if(e.compositeRule){if(e.opts.strictDefaults){var b="default is ignored for: "+K;if(e.opts.strictDefaults==="log")e.logger.warn(b);else throw new Error(b)}}else{a+=" if ("+K+" === undefined ";if(e.opts.useDefaults=="empty"){a+=" || "+K+" === null || "+K+" === '' "}a+=" ) "+K+" = ";if(e.opts.useDefaults=="shared"){a+=" "+e.useDefault(H.default)+" "}else{a+=" "+JSON.stringify(H.default)+" "}a+="; "}}}}}}var G=R.rules;if(G){var Z,X=-1,Y=G.length-1;while(X<Y){Z=G[X+=1];if($shouldUseRule(Z)){var W=Z.code(e,Z.keyword,R.type);if(W){a+=" "+W+" ";if(p){w+="}"}}}}}if(p){a+=" "+w+" ";w=""}if(R.type){a+=" } ";if(x&&x===R.type&&!j){a+=" else { ";var h=e.schemaPath+".type",d=e.errSchemaPath+"/type";var g=g||[];g.push(a);a="";if(e.createErrors!==false){a+=" { keyword: '"+(v||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(d)+" , params: { type: '";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' } ";if(e.opts.messages!==false){a+=" , message: 'should be ";if(_){a+=""+x.join(",")}else{a+=""+x}a+="' "}if(e.opts.verbose){a+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "}a+=" } "}else{a+=" {} "}var P=a;a=g.pop();if(!e.compositeRule&&p){if(e.async){a+=" throw new ValidationError(["+P+"]); "}else{a+=" validate.errors = ["+P+"]; return false; "}}else{a+=" var err = "+P+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}a+=" } "}}if(p){a+=" if (errors === ";if(E){a+="0"}else{a+="errs_"+f}a+=") { ";S+="}"}}}}}if(p){a+=" "+S+" "}if(E){if(s){a+=" if (errors === 0) return data;           ";a+=" else throw new ValidationError(vErrors); "}else{a+=" validate.errors = vErrors; ";a+=" return errors === 0;       "}a+=" }; return validate;"}else{a+=" var "+y+" = errors === errs_"+f+";"}function $shouldUseGroup(e){var r=e.rules;for(var t=0;t<r.length;t++)if($shouldUseRule(r[t]))return true}function $shouldUseRule(r){return e.schema[r.keyword]!==undefined||r.implements&&$ruleImplementsSomeKeyword(r)}function $ruleImplementsSomeKeyword(r){var t=r.implements;for(var a=0;a<t.length;a++)if(e.schema[t[a]]!==undefined)return true}return a}},6765:(e,r,t)=>{"use strict";var a=/^[a-z_$][a-z0-9_$-]*$/i;var s=t(8029);var i=t(6686);e.exports={add:addKeyword,get:getKeyword,remove:removeKeyword,validate:validateKeyword};function addKeyword(e,r){var t=this.RULES;if(t.keywords[e])throw new Error("Keyword "+e+" is already defined");if(!a.test(e))throw new Error("Keyword "+e+" is not a valid identifier");if(r){this.validateKeyword(r,true);var i=r.type;if(Array.isArray(i)){for(var o=0;o<i.length;o++)_addRule(e,i[o],r)}else{_addRule(e,i,r)}var n=r.metaSchema;if(n){if(r.$data&&this._opts.$data){n={anyOf:[n,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]}}r.validateSchema=this.compile(n,true)}}t.keywords[e]=t.all[e]=true;function _addRule(e,r,a){var i;for(var o=0;o<t.length;o++){var n=t[o];if(n.type==r){i=n;break}}if(!i){i={type:r,rules:[]};t.push(i)}var l={keyword:e,definition:a,custom:true,code:s,implements:a.implements};i.rules.push(l);t.custom[e]=l}return this}function getKeyword(e){var r=this.RULES.custom[e];return r?r.definition:this.RULES.keywords[e]||false}function removeKeyword(e){var r=this.RULES;delete r.keywords[e];delete r.all[e];delete r.custom[e];for(var t=0;t<r.length;t++){var a=r[t].rules;for(var s=0;s<a.length;s++){if(a[s].keyword==e){a.splice(s,1);break}}}return this}function validateKeyword(e,r){validateKeyword.errors=null;var t=this._validateKeyword=this._validateKeyword||this.compile(i,true);if(t(e))return true;validateKeyword.errors=t.errors;if(r)throw new Error("custom keyword definition is invalid: "+this.errorsText(t.errors));else return false}},9041:(e,r,t)=>{"use strict";e=t.nmd(e);const a=t(7147);const s=t(1017);const i=t(6113);const o=t(9491);const n=t(2361);const l=t(4512);const f=t(5940);const c=t(990);const u=t(5634);const h=t(6363);const d=t(6474);const plainObject=()=>Object.create(null);const p="aes-256-cbc";delete require.cache[__filename];const v=s.dirname(e.parent&&e.parent.filename||".");const checkValueType=(e,r)=>{const t=["undefined","symbol","function"];const a=typeof r;if(t.includes(a)){throw new TypeError(`Setting a value of type \`${a}\` for key \`${e}\` is not allowed as it's not supported by JSON`)}};class Conf{constructor(e){e={configName:"config",fileExtension:"json",projectSuffix:"nodejs",clearInvalidConfig:true,serialize:e=>JSON.stringify(e,null,"\t"),deserialize:JSON.parse,accessPropertiesByDotNotation:true,...e};if(!e.cwd){if(!e.projectName){const r=c.sync(v);e.projectName=r&&JSON.parse(a.readFileSync(r,"utf8")).name}if(!e.projectName){throw new Error("Project name could not be inferred. Please specify the `projectName` option.")}e.cwd=u(e.projectName,{suffix:e.projectSuffix}).config}this._options=e;if(e.schema){if(typeof e.schema!=="object"){throw new TypeError("The `schema` option must be an object.")}const r=new d({allErrors:true,format:"full",useDefaults:true,errorDataPath:"property"});const t={type:"object",properties:e.schema};this._validator=r.compile(t)}this.events=new n;this.encryptionKey=e.encryptionKey;this.serialize=e.serialize;this.deserialize=e.deserialize;const r=e.fileExtension?`.${e.fileExtension}`:"";this.path=s.resolve(e.cwd,`${e.configName}${r}`);const t=this.store;const i=Object.assign(plainObject(),e.defaults,t);this._validate(i);try{o.deepEqual(t,i)}catch(e){this.store=i}}_validate(e){if(!this._validator){return}const r=this._validator(e);if(!r){const e=this._validator.errors.reduce(((e,{dataPath:r,message:t})=>e+` \`${r.slice(1)}\` ${t};`),"");throw new Error("Config schema violation:"+e.slice(0,-1))}}get(e,r){if(this._options.accessPropertiesByDotNotation){return l.get(this.store,e,r)}return e in this.store?this.store[e]:r}set(e,r){if(typeof e!=="string"&&typeof e!=="object"){throw new TypeError(`Expected \`key\` to be of type \`string\` or \`object\`, got ${typeof e}`)}if(typeof e!=="object"&&r===undefined){throw new TypeError("Use `delete()` to clear values")}const{store:t}=this;const set=(e,r)=>{checkValueType(e,r);if(this._options.accessPropertiesByDotNotation){l.set(t,e,r)}else{t[e]=r}};if(typeof e==="object"){const r=e;for(const[e,t]of Object.entries(r)){set(e,t)}}else{set(e,r)}this.store=t}has(e){if(this._options.accessPropertiesByDotNotation){return l.has(this.store,e)}return e in this.store}delete(e){const{store:r}=this;if(this._options.accessPropertiesByDotNotation){l.delete(r,e)}else{delete r[e]}this.store=r}clear(){this.store=plainObject()}onDidChange(e,r){if(typeof e!=="string"){throw new TypeError(`Expected \`key\` to be of type \`string\`, got ${typeof e}`)}if(typeof r!=="function"){throw new TypeError(`Expected \`callback\` to be of type \`function\`, got ${typeof r}`)}const getter=()=>this.get(e);return this.handleChange(getter,r)}onDidAnyChange(e){if(typeof e!=="function"){throw new TypeError(`Expected \`callback\` to be of type \`function\`, got ${typeof e}`)}const getter=()=>this.store;return this.handleChange(getter,e)}handleChange(e,r){let t=e();const onChange=()=>{const a=t;const s=e();try{o.deepEqual(s,a)}catch(e){t=s;r.call(this,s,a)}};this.events.on("change",onChange);return()=>this.events.removeListener("change",onChange)}get size(){return Object.keys(this.store).length}get store(){try{let e=a.readFileSync(this.path,this.encryptionKey?null:"utf8");if(this.encryptionKey){try{if(e.slice(16,17).toString()===":"){const r=e.slice(0,16);const t=i.pbkdf2Sync(this.encryptionKey,r.toString(),1e4,32,"sha512");const a=i.createDecipheriv(p,t,r);e=Buffer.concat([a.update(e.slice(17)),a.final()])}else{const r=i.createDecipher(p,this.encryptionKey);e=Buffer.concat([r.update(e),r.final()])}}catch(e){}}e=this.deserialize(e);this._validate(e);return Object.assign(plainObject(),e)}catch(e){if(e.code==="ENOENT"){f.sync(s.dirname(this.path));return plainObject()}if(this._options.clearInvalidConfig&&e.name==="SyntaxError"){return plainObject()}throw e}}set store(e){f.sync(s.dirname(this.path));this._validate(e);let r=this.serialize(e);if(this.encryptionKey){const e=i.randomBytes(16);const t=i.pbkdf2Sync(this.encryptionKey,e.toString(),1e4,32,"sha512");const a=i.createCipheriv(p,t,e);r=Buffer.concat([e,Buffer.from(":"),a.update(Buffer.from(r)),a.final()])}h.sync(this.path,r);this.events.emit("change")}*[Symbol.iterator](){for(const[e,r]of Object.entries(this.store)){yield[e,r]}}}e.exports=Conf},4512:(e,r,t)=>{"use strict";const a=t(8683);const s=["__proto__","prototype","constructor"];const isValidPath=e=>!e.some((e=>s.includes(e)));function getPathSegments(e){const r=e.split(".");const t=[];for(let e=0;e<r.length;e++){let a=r[e];while(a[a.length-1]==="\\"&&r[e+1]!==undefined){a=a.slice(0,-1)+".";a+=r[++e]}t.push(a)}if(!isValidPath(t)){return[]}return t}e.exports={get(e,r,t){if(!a(e)||typeof r!=="string"){return t===undefined?e:t}const s=getPathSegments(r);if(s.length===0){return}for(let r=0;r<s.length;r++){if(!Object.prototype.propertyIsEnumerable.call(e,s[r])){return t}e=e[s[r]];if(e===undefined||e===null){if(r!==s.length-1){return t}break}}return e},set(e,r,t){if(!a(e)||typeof r!=="string"){return e}const s=e;const i=getPathSegments(r);for(let r=0;r<i.length;r++){const s=i[r];if(!a(e[s])){e[s]={}}if(r===i.length-1){e[s]=t}e=e[s]}return s},delete(e,r){if(!a(e)||typeof r!=="string"){return false}const t=getPathSegments(r);for(let r=0;r<t.length;r++){const s=t[r];if(r===t.length-1){delete e[s];return true}e=e[s];if(!a(e)){return false}}},has(e,r){if(!a(e)||typeof r!=="string"){return false}const t=getPathSegments(r);if(t.length===0){return false}for(let r=0;r<t.length;r++){if(a(e)){if(!(t[r]in e)){return false}e=e[t[r]]}else{return false}}return true}}},5634:(e,r,t)=>{"use strict";const a=t(1017);const s=t(2037);const i=s.homedir();const o=s.tmpdir();const{env:n}=process;const macos=e=>{const r=a.join(i,"Library");return{data:a.join(r,"Application Support",e),config:a.join(r,"Preferences",e),cache:a.join(r,"Caches",e),log:a.join(r,"Logs",e),temp:a.join(o,e)}};const windows=e=>{const r=n.APPDATA||a.join(i,"AppData","Roaming");const t=n.LOCALAPPDATA||a.join(i,"AppData","Local");return{data:a.join(t,e,"Data"),config:a.join(r,e,"Config"),cache:a.join(t,e,"Cache"),log:a.join(t,e,"Log"),temp:a.join(o,e)}};const linux=e=>{const r=a.basename(i);return{data:a.join(n.XDG_DATA_HOME||a.join(i,".local","share"),e),config:a.join(n.XDG_CONFIG_HOME||a.join(i,".config"),e),cache:a.join(n.XDG_CACHE_HOME||a.join(i,".cache"),e),log:a.join(n.XDG_STATE_HOME||a.join(i,".local","state"),e),temp:a.join(o,r,e)}};const envPaths=(e,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected string, got ${typeof e}`)}r=Object.assign({suffix:"nodejs"},r);if(r.suffix){e+=`-${r.suffix}`}if(process.platform==="darwin"){return macos(e)}if(process.platform==="win32"){return windows(e)}return linux(e)};e.exports=envPaths;e.exports["default"]=envPaths},1230:e=>{"use strict";e.exports=function equal(e,r){if(e===r)return true;if(e&&r&&typeof e=="object"&&typeof r=="object"){if(e.constructor!==r.constructor)return false;var t,a,s;if(Array.isArray(e)){t=e.length;if(t!=r.length)return false;for(a=t;a--!==0;)if(!equal(e[a],r[a]))return false;return true}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();s=Object.keys(e);t=s.length;if(t!==Object.keys(r).length)return false;for(a=t;a--!==0;)if(!Object.prototype.hasOwnProperty.call(r,s[a]))return false;for(a=t;a--!==0;){var i=s[a];if(!equal(e[i],r[i]))return false}return true}return e!==e&&r!==r}},6424:e=>{"use strict";e.exports=function(e,r){if(!r)r={};if(typeof r==="function")r={cmp:r};var t=typeof r.cycles==="boolean"?r.cycles:false;var a=r.cmp&&function(e){return function(r){return function(t,a){var s={key:t,value:r[t]};var i={key:a,value:r[a]};return e(s,i)}}}(r.cmp);var s=[];return function stringify(e){if(e&&e.toJSON&&typeof e.toJSON==="function"){e=e.toJSON()}if(e===undefined)return;if(typeof e=="number")return isFinite(e)?""+e:"null";if(typeof e!=="object")return JSON.stringify(e);var r,i;if(Array.isArray(e)){i="[";for(r=0;r<e.length;r++){if(r)i+=",";i+=stringify(e[r])||"null"}return i+"]"}if(e===null)return"null";if(s.indexOf(e)!==-1){if(t)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var o=s.push(e)-1;var n=Object.keys(e).sort(a&&a(e));i="";for(r=0;r<n.length;r++){var l=n[r];var f=stringify(e[l]);if(!f)continue;if(i)i+=",";i+=JSON.stringify(l)+":"+f}s.splice(o,1);return"{"+i+"}"}(e)}},2141:e=>{
/**
 * @preserve
 * JS Implementation of incremental MurmurHash3 (r150) (as of May 10, 2013)
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jens Taylor</a>
 * @see http://github.com/homebrewing/brauhaus-diff
 * <AUTHOR> href="mailto:<EMAIL>">Gary Court</a>
 * @see http://github.com/garycourt/murmurhash-js
 * <AUTHOR> href="mailto:<EMAIL>">Austin Appleby</a>
 * @see http://sites.google.com/site/murmurhash/
 */
(function(){var r;function MurmurHash3(e,t){var a=this instanceof MurmurHash3?this:r;a.reset(t);if(typeof e==="string"&&e.length>0){a.hash(e)}if(a!==this){return a}}MurmurHash3.prototype.hash=function(e){var r,t,a,s,i;i=e.length;this.len+=i;t=this.k1;a=0;switch(this.rem){case 0:t^=i>a?e.charCodeAt(a++)&65535:0;case 1:t^=i>a?(e.charCodeAt(a++)&65535)<<8:0;case 2:t^=i>a?(e.charCodeAt(a++)&65535)<<16:0;case 3:t^=i>a?(e.charCodeAt(a)&255)<<24:0;t^=i>a?(e.charCodeAt(a++)&65280)>>8:0}this.rem=i+this.rem&3;i-=this.rem;if(i>0){r=this.h1;while(1){t=t*11601+(t&65535)*3432906752&4294967295;t=t<<15|t>>>17;t=t*13715+(t&65535)*461832192&4294967295;r^=t;r=r<<13|r>>>19;r=r*5+3864292196&4294967295;if(a>=i){break}t=e.charCodeAt(a++)&65535^(e.charCodeAt(a++)&65535)<<8^(e.charCodeAt(a++)&65535)<<16;s=e.charCodeAt(a++);t^=(s&255)<<24^(s&65280)>>8}t=0;switch(this.rem){case 3:t^=(e.charCodeAt(a+2)&65535)<<16;case 2:t^=(e.charCodeAt(a+1)&65535)<<8;case 1:t^=e.charCodeAt(a)&65535}this.h1=r}this.k1=t;return this};MurmurHash3.prototype.result=function(){var e,r;e=this.k1;r=this.h1;if(e>0){e=e*11601+(e&65535)*3432906752&4294967295;e=e<<15|e>>>17;e=e*13715+(e&65535)*461832192&4294967295;r^=e}r^=this.len;r^=r>>>16;r=r*51819+(r&65535)*2246770688&4294967295;r^=r>>>13;r=r*44597+(r&65535)*3266445312&4294967295;r^=r>>>16;return r>>>0};MurmurHash3.prototype.reset=function(e){this.h1=typeof e==="number"?e:0;this.rem=this.k1=this.len=0;return this};r=new MurmurHash3;if(true){e.exports=MurmurHash3}else{}})()},8683:e=>{"use strict";e.exports=e=>{const r=typeof e;return e!==null&&(r==="object"||r==="function")}},9232:e=>{e.exports=isTypedArray;isTypedArray.strict=isStrictTypedArray;isTypedArray.loose=isLooseTypedArray;var r=Object.prototype.toString;var t={"[object Int8Array]":true,"[object Int16Array]":true,"[object Int32Array]":true,"[object Uint8Array]":true,"[object Uint8ClampedArray]":true,"[object Uint16Array]":true,"[object Uint32Array]":true,"[object Float32Array]":true,"[object Float64Array]":true};function isTypedArray(e){return isStrictTypedArray(e)||isLooseTypedArray(e)}function isStrictTypedArray(e){return e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array}function isLooseTypedArray(e){return t[r.call(e)]}},6042:e=>{"use strict";var r=e.exports=function(e,r,t){if(typeof r=="function"){t=r;r={}}t=r.cb||t;var a=typeof t=="function"?t:t.pre||function(){};var s=t.post||function(){};_traverse(r,a,s,e,"",e)};r.keywords={additionalItems:true,items:true,contains:true,additionalProperties:true,propertyNames:true,not:true};r.arrayKeywords={items:true,allOf:true,anyOf:true,oneOf:true};r.propsKeywords={definitions:true,properties:true,patternProperties:true,dependencies:true};r.skipKeywords={default:true,enum:true,const:true,required:true,maximum:true,minimum:true,exclusiveMaximum:true,exclusiveMinimum:true,multipleOf:true,maxLength:true,minLength:true,pattern:true,format:true,maxItems:true,minItems:true,uniqueItems:true,maxProperties:true,minProperties:true};function _traverse(e,t,a,s,i,o,n,l,f,c){if(s&&typeof s=="object"&&!Array.isArray(s)){t(s,i,o,n,l,f,c);for(var u in s){var h=s[u];if(Array.isArray(h)){if(u in r.arrayKeywords){for(var d=0;d<h.length;d++)_traverse(e,t,a,h[d],i+"/"+u+"/"+d,o,i,u,s,d)}}else if(u in r.propsKeywords){if(h&&typeof h=="object"){for(var p in h)_traverse(e,t,a,h[p],i+"/"+u+"/"+escapeJsonPtr(p),o,i,u,s,p)}}else if(u in r.keywords||e.allKeys&&!(u in r.skipKeywords)){_traverse(e,t,a,h,i+"/"+u,o,i,u,s)}}a(s,i,o,n,l,f,c)}}function escapeJsonPtr(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}},5940:(e,r,t)=>{"use strict";const a=t(7147);const s=t(1017);const{promisify:i}=t(3837);const o=t(7849);const n=o.satisfies(process.version,">=10.12.0");const checkPath=e=>{if(process.platform==="win32"){const r=/[<>:"|?*]/.test(e.replace(s.parse(e).root,""));if(r){const r=new Error(`Path contains invalid characters: ${e}`);r.code="EINVAL";throw r}}};const processOptions=e=>{const r={mode:511,fs:a};return{...r,...e}};const permissionError=e=>{const r=new Error(`operation not permitted, mkdir '${e}'`);r.code="EPERM";r.errno=-4048;r.path=e;r.syscall="mkdir";return r};const makeDir=async(e,r)=>{checkPath(e);r=processOptions(r);const t=i(r.fs.mkdir);const o=i(r.fs.stat);if(n&&r.fs.mkdir===a.mkdir){const a=s.resolve(e);await t(a,{mode:r.mode,recursive:true});return a}const make=async e=>{try{await t(e,r.mode);return e}catch(r){if(r.code==="EPERM"){throw r}if(r.code==="ENOENT"){if(s.dirname(e)===e){throw permissionError(e)}if(r.message.includes("null bytes")){throw r}await make(s.dirname(e));return make(e)}try{const r=await o(e);if(!r.isDirectory()){throw new Error("The path is not a directory")}}catch(e){throw r}return e}};return make(s.resolve(e))};e.exports=makeDir;e.exports.sync=(e,r)=>{checkPath(e);r=processOptions(r);if(n&&r.fs.mkdirSync===a.mkdirSync){const t=s.resolve(e);a.mkdirSync(t,{mode:r.mode,recursive:true});return t}const make=e=>{try{r.fs.mkdirSync(e,r.mode)}catch(t){if(t.code==="EPERM"){throw t}if(t.code==="ENOENT"){if(s.dirname(e)===e){throw permissionError(e)}if(t.message.includes("null bytes")){throw t}make(s.dirname(e));return make(e)}try{if(!r.fs.statSync(e).isDirectory()){throw new Error("The path is not a directory")}}catch(e){throw t}}return e};return make(s.resolve(e))}},990:(e,r,t)=>{"use strict";const a=t(5055);e.exports=async({cwd:e}={})=>a("package.json",{cwd:e});e.exports.sync=({cwd:e}={})=>a.sync("package.json",{cwd:e})},7234:(e,r,t)=>{var a=global.process;const processOk=function(e){return e&&typeof e==="object"&&typeof e.removeListener==="function"&&typeof e.emit==="function"&&typeof e.reallyExit==="function"&&typeof e.listeners==="function"&&typeof e.kill==="function"&&typeof e.pid==="number"&&typeof e.on==="function"};if(!processOk(a)){e.exports=function(){return function(){}}}else{var s=t(9491);var i=t(8986);var o=/^win/i.test(a.platform);var n=t(2361);if(typeof n!=="function"){n=n.EventEmitter}var l;if(a.__signal_exit_emitter__){l=a.__signal_exit_emitter__}else{l=a.__signal_exit_emitter__=new n;l.count=0;l.emitted={}}if(!l.infinite){l.setMaxListeners(Infinity);l.infinite=true}e.exports=function(e,r){if(!processOk(global.process)){return function(){}}s.equal(typeof e,"function","a callback must be provided for exit handler");if(h===false){d()}var t="exit";if(r&&r.alwaysLast){t="afterexit"}var remove=function(){l.removeListener(t,e);if(l.listeners("exit").length===0&&l.listeners("afterexit").length===0){f()}};l.on(t,e);return remove};var f=function unload(){if(!h||!processOk(global.process)){return}h=false;i.forEach((function(e){try{a.removeListener(e,u[e])}catch(e){}}));a.emit=m;a.reallyExit=p;l.count-=1};e.exports.unload=f;var c=function emit(e,r,t){if(l.emitted[e]){return}l.emitted[e]=true;l.emit(e,r,t)};var u={};i.forEach((function(e){u[e]=function listener(){if(!processOk(global.process)){return}var r=a.listeners(e);if(r.length===l.count){f();c("exit",null,e);c("afterexit",null,e);if(o&&e==="SIGHUP"){e="SIGINT"}a.kill(a.pid,e)}}}));e.exports.signals=function(){return i};var h=false;var d=function load(){if(h||!processOk(global.process)){return}h=true;l.count+=1;i=i.filter((function(e){try{a.on(e,u[e]);return true}catch(e){return false}}));a.emit=y;a.reallyExit=v};e.exports.load=d;var p=a.reallyExit;var v=function processReallyExit(e){if(!processOk(global.process)){return}a.exitCode=e||0;c("exit",a.exitCode,null);c("afterexit",a.exitCode,null);p.call(a,a.exitCode)};var m=a.emit;var y=function processEmit(e,r){if(e==="exit"&&processOk(global.process)){if(r!==undefined){a.exitCode=r}var t=m.apply(this,arguments);c("exit",a.exitCode,null);c("afterexit",a.exitCode,null);return t}else{return m.apply(this,arguments)}}}},8986:e=>{e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32"){e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT")}if(process.platform==="linux"){e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}},1449:(e,r,t)=>{var a=t(9232).strict;e.exports=function typedarrayToBuffer(e){if(a(e)){var r=Buffer.from(e.buffer);if(e.byteLength!==e.buffer.byteLength){r=r.slice(e.byteOffset,e.byteOffset+e.byteLength)}return r}else{return Buffer.from(e)}}},4856:function(e,r){
/** @license URI.js v4.4.1 (c) 2011 Gary Court. License: http://github.com/garycourt/uri-js */
(function(e,t){true?t(r):0})(this,(function(e){"use strict";function merge(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++){r[t]=arguments[t]}if(r.length>1){r[0]=r[0].slice(0,-1);var a=r.length-1;for(var s=1;s<a;++s){r[s]=r[s].slice(1,-1)}r[a]=r[a].slice(1);return r.join("")}else{return r[0]}}function subexp(e){return"(?:"+e+")"}function typeOf(e){return e===undefined?"undefined":e===null?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function toUpperCase(e){return e.toUpperCase()}function toArray(e){return e!==undefined&&e!==null?e instanceof Array?e:typeof e.length!=="number"||e.split||e.setInterval||e.call?[e]:Array.prototype.slice.call(e):[]}function assign(e,r){var t=e;if(r){for(var a in r){t[a]=r[a]}}return t}function buildExps(e){var r="[A-Za-z]",t="[\\x0D]",a="[0-9]",s="[\\x22]",i=merge(a,"[A-Fa-f]"),o="[\\x0A]",n="[\\x20]",l=subexp(subexp("%[EFef]"+i+"%"+i+i+"%"+i+i)+"|"+subexp("%[89A-Fa-f]"+i+"%"+i+i)+"|"+subexp("%"+i+i)),f="[\\:\\/\\?\\#\\[\\]\\@]",c="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",u=merge(f,c),h=e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]",d=e?"[\\uE000-\\uF8FF]":"[]",p=merge(r,a,"[\\-\\.\\_\\~]",h),v=subexp(r+merge(r,a,"[\\+\\-\\.]")+"*"),m=subexp(subexp(l+"|"+merge(p,c,"[\\:]"))+"*"),y=subexp(subexp("25[0-5]")+"|"+subexp("2[0-4]"+a)+"|"+subexp("1"+a+a)+"|"+subexp("[1-9]"+a)+"|"+a),g=subexp(subexp("25[0-5]")+"|"+subexp("2[0-4]"+a)+"|"+subexp("1"+a+a)+"|"+subexp("0?[1-9]"+a)+"|0?0?"+a),P=subexp(g+"\\."+g+"\\."+g+"\\."+g),E=subexp(i+"{1,4}"),b=subexp(subexp(E+"\\:"+E)+"|"+P),w=subexp(subexp(E+"\\:")+"{6}"+b),S=subexp("\\:\\:"+subexp(E+"\\:")+"{5}"+b),x=subexp(subexp(E)+"?\\:\\:"+subexp(E+"\\:")+"{4}"+b),_=subexp(subexp(subexp(E+"\\:")+"{0,1}"+E)+"?\\:\\:"+subexp(E+"\\:")+"{3}"+b),j=subexp(subexp(subexp(E+"\\:")+"{0,2}"+E)+"?\\:\\:"+subexp(E+"\\:")+"{2}"+b),R=subexp(subexp(subexp(E+"\\:")+"{0,3}"+E)+"?\\:\\:"+E+"\\:"+b),F=subexp(subexp(subexp(E+"\\:")+"{0,4}"+E)+"?\\:\\:"+b),D=subexp(subexp(subexp(E+"\\:")+"{0,5}"+E)+"?\\:\\:"+E),$=subexp(subexp(subexp(E+"\\:")+"{0,6}"+E)+"?\\:\\:"),A=subexp([w,S,x,_,j,R,F,D,$].join("|")),I=subexp(subexp(p+"|"+l)+"+"),C=subexp(A+"\\%25"+I),O=subexp(A+subexp("\\%25|\\%(?!"+i+"{2})")+I),k=subexp("[vV]"+i+"+\\."+merge(p,c,"[\\:]")+"+"),T=subexp("\\["+subexp(O+"|"+A+"|"+k)+"\\]"),z=subexp(subexp(l+"|"+merge(p,c))+"*"),L=subexp(T+"|"+P+"(?!"+z+")"+"|"+z),N=subexp(a+"*"),U=subexp(subexp(m+"@")+"?"+L+subexp("\\:"+N)+"?"),q=subexp(l+"|"+merge(p,c,"[\\:\\@]")),V=subexp(q+"*"),Q=subexp(q+"+"),M=subexp(subexp(l+"|"+merge(p,c,"[\\@]"))+"+"),H=subexp(subexp("\\/"+V)+"*"),K=subexp("\\/"+subexp(Q+H)+"?"),B=subexp(M+H),J=subexp(Q+H),G="(?!"+q+")",Z=subexp(H+"|"+K+"|"+B+"|"+J+"|"+G),X=subexp(subexp(q+"|"+merge("[\\/\\?]",d))+"*"),Y=subexp(subexp(q+"|[\\/\\?]")+"*"),W=subexp(subexp("\\/\\/"+U+H)+"|"+K+"|"+J+"|"+G),ee=subexp(v+"\\:"+W+subexp("\\?"+X)+"?"+subexp("\\#"+Y)+"?"),re=subexp(subexp("\\/\\/"+U+H)+"|"+K+"|"+B+"|"+G),te=subexp(re+subexp("\\?"+X)+"?"+subexp("\\#"+Y)+"?"),ae=subexp(ee+"|"+te),se=subexp(v+"\\:"+W+subexp("\\?"+X)+"?"),ie="^("+v+")\\:"+subexp(subexp("\\/\\/("+subexp("("+m+")@")+"?("+L+")"+subexp("\\:("+N+")")+"?)")+"?("+H+"|"+K+"|"+J+"|"+G+")")+subexp("\\?("+X+")")+"?"+subexp("\\#("+Y+")")+"?$",oe="^(){0}"+subexp(subexp("\\/\\/("+subexp("("+m+")@")+"?("+L+")"+subexp("\\:("+N+")")+"?)")+"?("+H+"|"+K+"|"+B+"|"+G+")")+subexp("\\?("+X+")")+"?"+subexp("\\#("+Y+")")+"?$",ne="^("+v+")\\:"+subexp(subexp("\\/\\/("+subexp("("+m+")@")+"?("+L+")"+subexp("\\:("+N+")")+"?)")+"?("+H+"|"+K+"|"+J+"|"+G+")")+subexp("\\?("+X+")")+"?$",le="^"+subexp("\\#("+Y+")")+"?$",fe="^"+subexp("("+m+")@")+"?("+L+")"+subexp("\\:("+N+")")+"?$";return{NOT_SCHEME:new RegExp(merge("[^]",r,a,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(merge("[^\\%\\:]",p,c),"g"),NOT_HOST:new RegExp(merge("[^\\%\\[\\]\\:]",p,c),"g"),NOT_PATH:new RegExp(merge("[^\\%\\/\\:\\@]",p,c),"g"),NOT_PATH_NOSCHEME:new RegExp(merge("[^\\%\\/\\@]",p,c),"g"),NOT_QUERY:new RegExp(merge("[^\\%]",p,c,"[\\:\\@\\/\\?]",d),"g"),NOT_FRAGMENT:new RegExp(merge("[^\\%]",p,c,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(merge("[^]",p,c),"g"),UNRESERVED:new RegExp(p,"g"),OTHER_CHARS:new RegExp(merge("[^\\%]",p,u),"g"),PCT_ENCODED:new RegExp(l,"g"),IPV4ADDRESS:new RegExp("^("+P+")$"),IPV6ADDRESS:new RegExp("^\\[?("+A+")"+subexp(subexp("\\%25|\\%(?!"+i+"{2})")+"("+I+")")+"?\\]?$")}}var r=buildExps(false);var t=buildExps(true);var a=function(){function sliceIterator(e,r){var t=[];var a=true;var s=false;var i=undefined;try{for(var o=e[Symbol.iterator](),n;!(a=(n=o.next()).done);a=true){t.push(n.value);if(r&&t.length===r)break}}catch(e){s=true;i=e}finally{try{if(!a&&o["return"])o["return"]()}finally{if(s)throw i}}return t}return function(e,r){if(Array.isArray(e)){return e}else if(Symbol.iterator in Object(e)){return sliceIterator(e,r)}else{throw new TypeError("Invalid attempt to destructure non-iterable instance")}}}();var toConsumableArray=function(e){if(Array.isArray(e)){for(var r=0,t=Array(e.length);r<e.length;r++)t[r]=e[r];return t}else{return Array.from(e)}};var s=2147483647;var i=36;var o=1;var n=26;var l=38;var f=700;var c=72;var u=128;var h="-";var d=/^xn--/;var p=/[^\0-\x7E]/;var v=/[\x2E\u3002\uFF0E\uFF61]/g;var m={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"};var y=i-o;var g=Math.floor;var P=String.fromCharCode;function error$1(e){throw new RangeError(m[e])}function map(e,r){var t=[];var a=e.length;while(a--){t[a]=r(e[a])}return t}function mapDomain(e,r){var t=e.split("@");var a="";if(t.length>1){a=t[0]+"@";e=t[1]}e=e.replace(v,".");var s=e.split(".");var i=map(s,r).join(".");return a+i}function ucs2decode(e){var r=[];var t=0;var a=e.length;while(t<a){var s=e.charCodeAt(t++);if(s>=55296&&s<=56319&&t<a){var i=e.charCodeAt(t++);if((i&64512)==56320){r.push(((s&1023)<<10)+(i&1023)+65536)}else{r.push(s);t--}}else{r.push(s)}}return r}var E=function ucs2encode(e){return String.fromCodePoint.apply(String,toConsumableArray(e))};var b=function basicToDigit(e){if(e-48<10){return e-22}if(e-65<26){return e-65}if(e-97<26){return e-97}return i};var w=function digitToBasic(e,r){return e+22+75*(e<26)-((r!=0)<<5)};var S=function adapt(e,r,t){var a=0;e=t?g(e/f):e>>1;e+=g(e/r);for(;e>y*n>>1;a+=i){e=g(e/y)}return g(a+(y+1)*e/(e+l))};var x=function decode(e){var r=[];var t=e.length;var a=0;var l=u;var f=c;var d=e.lastIndexOf(h);if(d<0){d=0}for(var p=0;p<d;++p){if(e.charCodeAt(p)>=128){error$1("not-basic")}r.push(e.charCodeAt(p))}for(var v=d>0?d+1:0;v<t;){var m=a;for(var y=1,P=i;;P+=i){if(v>=t){error$1("invalid-input")}var E=b(e.charCodeAt(v++));if(E>=i||E>g((s-a)/y)){error$1("overflow")}a+=E*y;var w=P<=f?o:P>=f+n?n:P-f;if(E<w){break}var x=i-w;if(y>g(s/x)){error$1("overflow")}y*=x}var _=r.length+1;f=S(a-m,_,m==0);if(g(a/_)>s-l){error$1("overflow")}l+=g(a/_);a%=_;r.splice(a++,0,l)}return String.fromCodePoint.apply(String,r)};var _=function encode(e){var r=[];e=ucs2decode(e);var t=e.length;var a=u;var l=0;var f=c;var d=true;var p=false;var v=undefined;try{for(var m=e[Symbol.iterator](),y;!(d=(y=m.next()).done);d=true){var E=y.value;if(E<128){r.push(P(E))}}}catch(e){p=true;v=e}finally{try{if(!d&&m.return){m.return()}}finally{if(p){throw v}}}var b=r.length;var x=b;if(b){r.push(h)}while(x<t){var _=s;var j=true;var R=false;var F=undefined;try{for(var D=e[Symbol.iterator](),$;!(j=($=D.next()).done);j=true){var A=$.value;if(A>=a&&A<_){_=A}}}catch(e){R=true;F=e}finally{try{if(!j&&D.return){D.return()}}finally{if(R){throw F}}}var I=x+1;if(_-a>g((s-l)/I)){error$1("overflow")}l+=(_-a)*I;a=_;var C=true;var O=false;var k=undefined;try{for(var T=e[Symbol.iterator](),z;!(C=(z=T.next()).done);C=true){var L=z.value;if(L<a&&++l>s){error$1("overflow")}if(L==a){var N=l;for(var U=i;;U+=i){var q=U<=f?o:U>=f+n?n:U-f;if(N<q){break}var V=N-q;var Q=i-q;r.push(P(w(q+V%Q,0)));N=g(V/Q)}r.push(P(w(N,0)));f=S(l,I,x==b);l=0;++x}}}catch(e){O=true;k=e}finally{try{if(!C&&T.return){T.return()}}finally{if(O){throw k}}}++l;++a}return r.join("")};var j=function toUnicode(e){return mapDomain(e,(function(e){return d.test(e)?x(e.slice(4).toLowerCase()):e}))};var R=function toASCII(e){return mapDomain(e,(function(e){return p.test(e)?"xn--"+_(e):e}))};var F={version:"2.1.0",ucs2:{decode:ucs2decode,encode:E},decode:x,encode:_,toASCII:R,toUnicode:j};var D={};function pctEncChar(e){var r=e.charCodeAt(0);var t=void 0;if(r<16)t="%0"+r.toString(16).toUpperCase();else if(r<128)t="%"+r.toString(16).toUpperCase();else if(r<2048)t="%"+(r>>6|192).toString(16).toUpperCase()+"%"+(r&63|128).toString(16).toUpperCase();else t="%"+(r>>12|224).toString(16).toUpperCase()+"%"+(r>>6&63|128).toString(16).toUpperCase()+"%"+(r&63|128).toString(16).toUpperCase();return t}function pctDecChars(e){var r="";var t=0;var a=e.length;while(t<a){var s=parseInt(e.substr(t+1,2),16);if(s<128){r+=String.fromCharCode(s);t+=3}else if(s>=194&&s<224){if(a-t>=6){var i=parseInt(e.substr(t+4,2),16);r+=String.fromCharCode((s&31)<<6|i&63)}else{r+=e.substr(t,6)}t+=6}else if(s>=224){if(a-t>=9){var o=parseInt(e.substr(t+4,2),16);var n=parseInt(e.substr(t+7,2),16);r+=String.fromCharCode((s&15)<<12|(o&63)<<6|n&63)}else{r+=e.substr(t,9)}t+=9}else{r+=e.substr(t,3);t+=3}}return r}function _normalizeComponentEncoding(e,r){function decodeUnreserved(e){var t=pctDecChars(e);return!t.match(r.UNRESERVED)?e:t}if(e.scheme)e.scheme=String(e.scheme).replace(r.PCT_ENCODED,decodeUnreserved).toLowerCase().replace(r.NOT_SCHEME,"");if(e.userinfo!==undefined)e.userinfo=String(e.userinfo).replace(r.PCT_ENCODED,decodeUnreserved).replace(r.NOT_USERINFO,pctEncChar).replace(r.PCT_ENCODED,toUpperCase);if(e.host!==undefined)e.host=String(e.host).replace(r.PCT_ENCODED,decodeUnreserved).toLowerCase().replace(r.NOT_HOST,pctEncChar).replace(r.PCT_ENCODED,toUpperCase);if(e.path!==undefined)e.path=String(e.path).replace(r.PCT_ENCODED,decodeUnreserved).replace(e.scheme?r.NOT_PATH:r.NOT_PATH_NOSCHEME,pctEncChar).replace(r.PCT_ENCODED,toUpperCase);if(e.query!==undefined)e.query=String(e.query).replace(r.PCT_ENCODED,decodeUnreserved).replace(r.NOT_QUERY,pctEncChar).replace(r.PCT_ENCODED,toUpperCase);if(e.fragment!==undefined)e.fragment=String(e.fragment).replace(r.PCT_ENCODED,decodeUnreserved).replace(r.NOT_FRAGMENT,pctEncChar).replace(r.PCT_ENCODED,toUpperCase);return e}function _stripLeadingZeros(e){return e.replace(/^0*(.*)/,"$1")||"0"}function _normalizeIPv4(e,r){var t=e.match(r.IPV4ADDRESS)||[];var s=a(t,2),i=s[1];if(i){return i.split(".").map(_stripLeadingZeros).join(".")}else{return e}}function _normalizeIPv6(e,r){var t=e.match(r.IPV6ADDRESS)||[];var s=a(t,3),i=s[1],o=s[2];if(i){var n=i.toLowerCase().split("::").reverse(),l=a(n,2),f=l[0],c=l[1];var u=c?c.split(":").map(_stripLeadingZeros):[];var h=f.split(":").map(_stripLeadingZeros);var d=r.IPV4ADDRESS.test(h[h.length-1]);var p=d?7:8;var v=h.length-p;var m=Array(p);for(var y=0;y<p;++y){m[y]=u[y]||h[v+y]||""}if(d){m[p-1]=_normalizeIPv4(m[p-1],r)}var g=m.reduce((function(e,r,t){if(!r||r==="0"){var a=e[e.length-1];if(a&&a.index+a.length===t){a.length++}else{e.push({index:t,length:1})}}return e}),[]);var P=g.sort((function(e,r){return r.length-e.length}))[0];var E=void 0;if(P&&P.length>1){var b=m.slice(0,P.index);var w=m.slice(P.index+P.length);E=b.join(":")+"::"+w.join(":")}else{E=m.join(":")}if(o){E+="%"+o}return E}else{return e}}var $=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i;var A="".match(/(){0}/)[1]===undefined;function parse(e){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var s={};var i=a.iri!==false?t:r;if(a.reference==="suffix")e=(a.scheme?a.scheme+":":"")+"//"+e;var o=e.match($);if(o){if(A){s.scheme=o[1];s.userinfo=o[3];s.host=o[4];s.port=parseInt(o[5],10);s.path=o[6]||"";s.query=o[7];s.fragment=o[8];if(isNaN(s.port)){s.port=o[5]}}else{s.scheme=o[1]||undefined;s.userinfo=e.indexOf("@")!==-1?o[3]:undefined;s.host=e.indexOf("//")!==-1?o[4]:undefined;s.port=parseInt(o[5],10);s.path=o[6]||"";s.query=e.indexOf("?")!==-1?o[7]:undefined;s.fragment=e.indexOf("#")!==-1?o[8]:undefined;if(isNaN(s.port)){s.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?o[4]:undefined}}if(s.host){s.host=_normalizeIPv6(_normalizeIPv4(s.host,i),i)}if(s.scheme===undefined&&s.userinfo===undefined&&s.host===undefined&&s.port===undefined&&!s.path&&s.query===undefined){s.reference="same-document"}else if(s.scheme===undefined){s.reference="relative"}else if(s.fragment===undefined){s.reference="absolute"}else{s.reference="uri"}if(a.reference&&a.reference!=="suffix"&&a.reference!==s.reference){s.error=s.error||"URI is not a "+a.reference+" reference."}var n=D[(a.scheme||s.scheme||"").toLowerCase()];if(!a.unicodeSupport&&(!n||!n.unicodeSupport)){if(s.host&&(a.domainHost||n&&n.domainHost)){try{s.host=F.toASCII(s.host.replace(i.PCT_ENCODED,pctDecChars).toLowerCase())}catch(e){s.error=s.error||"Host's domain name can not be converted to ASCII via punycode: "+e}}_normalizeComponentEncoding(s,r)}else{_normalizeComponentEncoding(s,i)}if(n&&n.parse){n.parse(s,a)}}else{s.error=s.error||"URI can not be parsed."}return s}function _recomposeAuthority(e,a){var s=a.iri!==false?t:r;var i=[];if(e.userinfo!==undefined){i.push(e.userinfo);i.push("@")}if(e.host!==undefined){i.push(_normalizeIPv6(_normalizeIPv4(String(e.host),s),s).replace(s.IPV6ADDRESS,(function(e,r,t){return"["+r+(t?"%25"+t:"")+"]"})))}if(typeof e.port==="number"||typeof e.port==="string"){i.push(":");i.push(String(e.port))}return i.length?i.join(""):undefined}var I=/^\.\.?\//;var C=/^\/\.(\/|$)/;var O=/^\/\.\.(\/|$)/;var k=/^\/?(?:.|\n)*?(?=\/|$)/;function removeDotSegments(e){var r=[];while(e.length){if(e.match(I)){e=e.replace(I,"")}else if(e.match(C)){e=e.replace(C,"/")}else if(e.match(O)){e=e.replace(O,"/");r.pop()}else if(e==="."||e===".."){e=""}else{var t=e.match(k);if(t){var a=t[0];e=e.slice(a.length);r.push(a)}else{throw new Error("Unexpected dot segment condition")}}}return r.join("")}function serialize(e){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var s=a.iri?t:r;var i=[];var o=D[(a.scheme||e.scheme||"").toLowerCase()];if(o&&o.serialize)o.serialize(e,a);if(e.host){if(s.IPV6ADDRESS.test(e.host)){}else if(a.domainHost||o&&o.domainHost){try{e.host=!a.iri?F.toASCII(e.host.replace(s.PCT_ENCODED,pctDecChars).toLowerCase()):F.toUnicode(e.host)}catch(r){e.error=e.error||"Host's domain name can not be converted to "+(!a.iri?"ASCII":"Unicode")+" via punycode: "+r}}}_normalizeComponentEncoding(e,s);if(a.reference!=="suffix"&&e.scheme){i.push(e.scheme);i.push(":")}var n=_recomposeAuthority(e,a);if(n!==undefined){if(a.reference!=="suffix"){i.push("//")}i.push(n);if(e.path&&e.path.charAt(0)!=="/"){i.push("/")}}if(e.path!==undefined){var l=e.path;if(!a.absolutePath&&(!o||!o.absolutePath)){l=removeDotSegments(l)}if(n===undefined){l=l.replace(/^\/\//,"/%2F")}i.push(l)}if(e.query!==undefined){i.push("?");i.push(e.query)}if(e.fragment!==undefined){i.push("#");i.push(e.fragment)}return i.join("")}function resolveComponents(e,r){var t=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};var a=arguments[3];var s={};if(!a){e=parse(serialize(e,t),t);r=parse(serialize(r,t),t)}t=t||{};if(!t.tolerant&&r.scheme){s.scheme=r.scheme;s.userinfo=r.userinfo;s.host=r.host;s.port=r.port;s.path=removeDotSegments(r.path||"");s.query=r.query}else{if(r.userinfo!==undefined||r.host!==undefined||r.port!==undefined){s.userinfo=r.userinfo;s.host=r.host;s.port=r.port;s.path=removeDotSegments(r.path||"");s.query=r.query}else{if(!r.path){s.path=e.path;if(r.query!==undefined){s.query=r.query}else{s.query=e.query}}else{if(r.path.charAt(0)==="/"){s.path=removeDotSegments(r.path)}else{if((e.userinfo!==undefined||e.host!==undefined||e.port!==undefined)&&!e.path){s.path="/"+r.path}else if(!e.path){s.path=r.path}else{s.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+r.path}s.path=removeDotSegments(s.path)}s.query=r.query}s.userinfo=e.userinfo;s.host=e.host;s.port=e.port}s.scheme=e.scheme}s.fragment=r.fragment;return s}function resolve(e,r,t){var a=assign({scheme:"null"},t);return serialize(resolveComponents(parse(e,a),parse(r,a),a,true),a)}function normalize(e,r){if(typeof e==="string"){e=serialize(parse(e,r),r)}else if(typeOf(e)==="object"){e=parse(serialize(e,r),r)}return e}function equal(e,r,t){if(typeof e==="string"){e=serialize(parse(e,t),t)}else if(typeOf(e)==="object"){e=serialize(e,t)}if(typeof r==="string"){r=serialize(parse(r,t),t)}else if(typeOf(r)==="object"){r=serialize(r,t)}return e===r}function escapeComponent(e,a){return e&&e.toString().replace(!a||!a.iri?r.ESCAPE:t.ESCAPE,pctEncChar)}function unescapeComponent(e,a){return e&&e.toString().replace(!a||!a.iri?r.PCT_ENCODED:t.PCT_ENCODED,pctDecChars)}var T={scheme:"http",domainHost:true,parse:function parse(e,r){if(!e.host){e.error=e.error||"HTTP URIs must have a host."}return e},serialize:function serialize(e,r){var t=String(e.scheme).toLowerCase()==="https";if(e.port===(t?443:80)||e.port===""){e.port=undefined}if(!e.path){e.path="/"}return e}};var z={scheme:"https",domainHost:T.domainHost,parse:T.parse,serialize:T.serialize};function isSecure(e){return typeof e.secure==="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}var L={scheme:"ws",domainHost:true,parse:function parse(e,r){var t=e;t.secure=isSecure(t);t.resourceName=(t.path||"/")+(t.query?"?"+t.query:"");t.path=undefined;t.query=undefined;return t},serialize:function serialize(e,r){if(e.port===(isSecure(e)?443:80)||e.port===""){e.port=undefined}if(typeof e.secure==="boolean"){e.scheme=e.secure?"wss":"ws";e.secure=undefined}if(e.resourceName){var t=e.resourceName.split("?"),s=a(t,2),i=s[0],o=s[1];e.path=i&&i!=="/"?i:undefined;e.query=o;e.resourceName=undefined}e.fragment=undefined;return e}};var N={scheme:"wss",domainHost:L.domainHost,parse:L.parse,serialize:L.serialize};var U={};var q=true;var V="[A-Za-z0-9\\-\\.\\_\\~"+(q?"\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF":"")+"]";var Q="[0-9A-Fa-f]";var M=subexp(subexp("%[EFef]"+Q+"%"+Q+Q+"%"+Q+Q)+"|"+subexp("%[89A-Fa-f]"+Q+"%"+Q+Q)+"|"+subexp("%"+Q+Q));var H="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]";var K="[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]";var B=merge(K,'[\\"\\\\]');var J="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]";var G=new RegExp(V,"g");var Z=new RegExp(M,"g");var X=new RegExp(merge("[^]",H,"[\\.]",'[\\"]',B),"g");var Y=new RegExp(merge("[^]",V,J),"g");var W=Y;function decodeUnreserved(e){var r=pctDecChars(e);return!r.match(G)?e:r}var ee={scheme:"mailto",parse:function parse$$1(e,r){var t=e;var a=t.to=t.path?t.path.split(","):[];t.path=undefined;if(t.query){var s=false;var i={};var o=t.query.split("&");for(var n=0,l=o.length;n<l;++n){var f=o[n].split("=");switch(f[0]){case"to":var c=f[1].split(",");for(var u=0,h=c.length;u<h;++u){a.push(c[u])}break;case"subject":t.subject=unescapeComponent(f[1],r);break;case"body":t.body=unescapeComponent(f[1],r);break;default:s=true;i[unescapeComponent(f[0],r)]=unescapeComponent(f[1],r);break}}if(s)t.headers=i}t.query=undefined;for(var d=0,p=a.length;d<p;++d){var v=a[d].split("@");v[0]=unescapeComponent(v[0]);if(!r.unicodeSupport){try{v[1]=F.toASCII(unescapeComponent(v[1],r).toLowerCase())}catch(e){t.error=t.error||"Email address's domain name can not be converted to ASCII via punycode: "+e}}else{v[1]=unescapeComponent(v[1],r).toLowerCase()}a[d]=v.join("@")}return t},serialize:function serialize$$1(e,r){var t=e;var a=toArray(e.to);if(a){for(var s=0,i=a.length;s<i;++s){var o=String(a[s]);var n=o.lastIndexOf("@");var l=o.slice(0,n).replace(Z,decodeUnreserved).replace(Z,toUpperCase).replace(X,pctEncChar);var f=o.slice(n+1);try{f=!r.iri?F.toASCII(unescapeComponent(f,r).toLowerCase()):F.toUnicode(f)}catch(e){t.error=t.error||"Email address's domain name can not be converted to "+(!r.iri?"ASCII":"Unicode")+" via punycode: "+e}a[s]=l+"@"+f}t.path=a.join(",")}var c=e.headers=e.headers||{};if(e.subject)c["subject"]=e.subject;if(e.body)c["body"]=e.body;var u=[];for(var h in c){if(c[h]!==U[h]){u.push(h.replace(Z,decodeUnreserved).replace(Z,toUpperCase).replace(Y,pctEncChar)+"="+c[h].replace(Z,decodeUnreserved).replace(Z,toUpperCase).replace(W,pctEncChar))}}if(u.length){t.query=u.join("&")}return t}};var re=/^([^\:]+)\:(.*)/;var te={scheme:"urn",parse:function parse$$1(e,r){var t=e.path&&e.path.match(re);var a=e;if(t){var s=r.scheme||a.scheme||"urn";var i=t[1].toLowerCase();var o=t[2];var n=s+":"+(r.nid||i);var l=D[n];a.nid=i;a.nss=o;a.path=undefined;if(l){a=l.parse(a,r)}}else{a.error=a.error||"URN can not be parsed."}return a},serialize:function serialize$$1(e,r){var t=r.scheme||e.scheme||"urn";var a=e.nid;var s=t+":"+(r.nid||a);var i=D[s];if(i){e=i.serialize(e,r)}var o=e;var n=e.nss;o.path=(a||r.nid)+":"+n;return o}};var ae=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/;var se={scheme:"urn:uuid",parse:function parse(e,r){var t=e;t.uuid=t.nss;t.nss=undefined;if(!r.tolerant&&(!t.uuid||!t.uuid.match(ae))){t.error=t.error||"UUID is not valid."}return t},serialize:function serialize(e,r){var t=e;t.nss=(e.uuid||"").toLowerCase();return t}};D[T.scheme]=T;D[z.scheme]=z;D[L.scheme]=L;D[N.scheme]=N;D[ee.scheme]=ee;D[te.scheme]=te;D[se.scheme]=se;e.SCHEMES=D;e.pctEncChar=pctEncChar;e.pctDecChars=pctDecChars;e.parse=parse;e.removeDotSegments=removeDotSegments;e.serialize=serialize;e.resolveComponents=resolveComponents;e.resolve=resolve;e.normalize=normalize;e.equal=equal;e.escapeComponent=escapeComponent;e.unescapeComponent=unescapeComponent;Object.defineProperty(e,"__esModule",{value:true})}))},6363:(e,r,t)=>{"use strict";e.exports=writeFile;e.exports.sync=writeFileSync;e.exports._getTmpname=getTmpname;e.exports._cleanupOnExit=cleanupOnExit;const a=t(7147);const s=t(2141);const i=t(7234);const o=t(1017);const n=t(9232);const l=t(1449);const{promisify:f}=t(3837);const c={};const u=function getId(){try{const e=t(1267);return e.threadId}catch(e){return 0}}();let h=0;function getTmpname(e){return e+"."+s(__filename).hash(String(process.pid)).hash(String(u)).hash(String(++h)).result()}function cleanupOnExit(e){return()=>{try{a.unlinkSync(typeof e==="function"?e():e)}catch(e){}}}function serializeActiveFile(e){return new Promise((r=>{if(!c[e])c[e]=[];c[e].push(r);if(c[e].length===1)r()}))}function isChownErrOk(e){if(e.code==="ENOSYS"){return true}const r=!process.getuid||process.getuid()!==0;if(r){if(e.code==="EINVAL"||e.code==="EPERM"){return true}}return false}async function writeFileAsync(e,r,t={}){if(typeof t==="string"){t={encoding:t}}let s;let u;const h=i(cleanupOnExit((()=>u)));const d=o.resolve(e);try{await serializeActiveFile(d);const i=await f(a.realpath)(e).catch((()=>e));u=getTmpname(i);if(!t.mode||!t.chown){const e=await f(a.stat)(i).catch((()=>{}));if(e){if(t.mode==null){t.mode=e.mode}if(t.chown==null&&process.getuid){t.chown={uid:e.uid,gid:e.gid}}}}s=await f(a.open)(u,"w",t.mode);if(t.tmpfileCreated){await t.tmpfileCreated(u)}if(n(r)){r=l(r)}if(Buffer.isBuffer(r)){await f(a.write)(s,r,0,r.length,0)}else if(r!=null){await f(a.write)(s,String(r),0,String(t.encoding||"utf8"))}if(t.fsync!==false){await f(a.fsync)(s)}await f(a.close)(s);s=null;if(t.chown){await f(a.chown)(u,t.chown.uid,t.chown.gid).catch((e=>{if(!isChownErrOk(e)){throw e}}))}if(t.mode){await f(a.chmod)(u,t.mode).catch((e=>{if(!isChownErrOk(e)){throw e}}))}await f(a.rename)(u,i)}finally{if(s){await f(a.close)(s).catch((()=>{}))}h();await f(a.unlink)(u).catch((()=>{}));c[d].shift();if(c[d].length>0){c[d][0]()}else delete c[d]}}function writeFile(e,r,t,a){if(t instanceof Function){a=t;t={}}const s=writeFileAsync(e,r,t);if(a){s.then(a,a)}return s}function writeFileSync(e,r,t){if(typeof t==="string")t={encoding:t};else if(!t)t={};try{e=a.realpathSync(e)}catch(e){}const s=getTmpname(e);if(!t.mode||!t.chown){try{const r=a.statSync(e);t=Object.assign({},t);if(!t.mode){t.mode=r.mode}if(!t.chown&&process.getuid){t.chown={uid:r.uid,gid:r.gid}}}catch(e){}}let o;const f=cleanupOnExit(s);const c=i(f);let u=true;try{o=a.openSync(s,"w",t.mode||438);if(t.tmpfileCreated){t.tmpfileCreated(s)}if(n(r)){r=l(r)}if(Buffer.isBuffer(r)){a.writeSync(o,r,0,r.length,0)}else if(r!=null){a.writeSync(o,String(r),0,String(t.encoding||"utf8"))}if(t.fsync!==false){a.fsyncSync(o)}a.closeSync(o);o=null;if(t.chown){try{a.chownSync(s,t.chown.uid,t.chown.gid)}catch(e){if(!isChownErrOk(e)){throw e}}}if(t.mode){try{a.chmodSync(s,t.mode)}catch(e){if(!isChownErrOk(e)){throw e}}}a.renameSync(s,e);u=false}finally{if(o){try{a.closeSync(o)}catch(e){}}c();if(u){f()}}}},9491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},5055:e=>{"use strict";e.exports=require("next/dist/compiled/find-up")},7849:e=>{"use strict";e.exports=require("next/dist/compiled/semver")},2037:e=>{"use strict";e.exports=require("os")},1017:e=>{"use strict";e.exports=require("path")},3837:e=>{"use strict";e.exports=require("util")},1267:e=>{"use strict";e.exports=require("worker_threads")},7664:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON Schema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},7136:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}};var r={};function __nccwpck_require__(t){var a=r[t];if(a!==undefined){return a.exports}var s=r[t]={id:t,loaded:false,exports:{}};var i=true;try{e[t].call(s.exports,s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete r[t]}s.loaded=true;return s.exports}(()=>{__nccwpck_require__.nmd=e=>{e.paths=[];if(!e.children)e.children=[];return e}})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var t=__nccwpck_require__(9041);module.exports=t})();