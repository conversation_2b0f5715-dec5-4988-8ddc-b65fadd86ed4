(function(){var e={104:function(e,r,n){"use strict";var s=n(37).platform();var t=n(81).spawnSync;var i=n(147).readdirSync;var o="glibc";var a="musl";var c={encoding:"utf8",env:process.env};if(!t){t=function(){return{status:126,stdout:"",stderr:""}}}function contains(e){return function(r){return r.indexOf(e)!==-1}}function versionFromMuslLdd(e){return e.split(/[\r\n]+/)[1].trim().split(/\s/)[1]}function safeReaddirSync(e){try{return i(e)}catch(e){}return[]}var u="";var d="";var f="";if(s==="linux"){var l=t("getconf",["GNU_LIBC_VERSION"],c);if(l.status===0){u=o;d=l.stdout.trim().split(" ")[1];f="getconf"}else{var p=t("ldd",["--version"],c);if(p.status===0&&p.stdout.indexOf(a)!==-1){u=a;d=versionFromMuslLdd(p.stdout);f="ldd"}else if(p.status===1&&p.stderr.indexOf(a)!==-1){u=a;d=versionFromMuslLdd(p.stderr);f="ldd"}else{var y=safeReaddirSync("/lib");if(y.some(contains("-linux-gnu"))){u=o;f="filesystem"}else if(y.some(contains("libc.musl-"))){u=a;f="filesystem"}else if(y.some(contains("ld-musl-"))){u=a;f="filesystem"}else{var v=safeReaddirSync("/usr/sbin");if(v.some(contains("glibc"))){u=o;f="filesystem"}}}}}var _=u!==""&&u!==o;e.exports={GLIBC:o,MUSL:a,family:u,version:d,method:f,isNonGlibcLinux:_}},276:function(e,r,n){if(process.fiberLib){e.exports=process.fiberLib}else{var s=n(147),t=n(17),i=n(104);Math.random();var o=t.join(__dirname,"bin",process.platform+"-"+process.arch+"-"+process.versions.modules+(process.platform==="linux"?"-"+i.family:""),"fibers");try{process.fiberLib=e.exports=require(o).Fiber}catch(e){console.error("## There is an issue with `node-fibers` ##\n"+"`"+o+".node` is missing.\n\n"+"Try running this to fix the issue: "+process.execPath+" "+__dirname.replace(" ","\\ ")+"/build");console.error(e.stack||e.message||e);throw new Error("Missing binary. See message above.")}setupAsyncHacks(e.exports)}function setupAsyncHacks(e){try{var r=process.binding("async_wrap");var n;if(r.asyncIdStackSize instanceof Function){n=r.asyncIdStackSize}else if(r.constants.kStackLength!==undefined){n=function(e){return function(){return r.async_hook_fields[e]}}(r.constants.kStackLength)}else{throw new Error("Couldn't figure out how to get async stack size")}var s=r.popAsyncContext||r.popAsyncIds;var t=r.pushAsyncContext||r.pushAsyncIds;if(!s||!t){throw new Error("Push/pop do not exist")}var i;if(r.constants.kExecutionAsyncId===undefined){i=r.constants.kCurrentAsyncId}else{i=r.constants.kExecutionAsyncId}var o;if(r.constants.kTriggerAsyncId===undefined){o=r.constants.kCurrentTriggerId}else{o=r.constants.kTriggerAsyncId}var a=r.async_id_fields||r.async_uid_fields;function getAndClearStack(){var e=n();var r=new Array(e);for(;e>0;--e){var t=a[i];r[e-1]={asyncId:t,triggerId:a[o]};s(t)}return r}function restoreStack(e){for(var r=0;r<e.length;++r){t(e[r].asyncId,e[r].triggerId)}}function wrapFunction(e){return function(){var r=getAndClearStack();try{return e.apply(this,arguments)}finally{restoreStack(r)}}}e.yield=wrapFunction(e.yield);e.prototype.run=wrapFunction(e.prototype.run);e.prototype.throwInto=wrapFunction(e.prototype.throwInto)}catch(e){return}}},81:function(e){"use strict";e.exports=require("child_process")},147:function(e){"use strict";e.exports=require("fs")},37:function(e){"use strict";e.exports=require("os")},17:function(e){"use strict";e.exports=require("path")}};var r={};function __nccwpck_require__(n){var s=r[n];if(s!==undefined){return s.exports}var t=r[n]={exports:{}};var i=true;try{e[n](t,t.exports,__nccwpck_require__);i=false}finally{if(i)delete r[n]}return t.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(276);module.exports=n})();