{"version": 3, "sources": ["../../src/lib/create-client-router-filter.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDynamicRoute", "removeTrailingSlash", "tryToParsePath", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "createClientRouterFilter", "paths", "redirects", "allowedErrorRate", "staticPaths", "Set", "dynamicPaths", "path", "interceptedRoute", "subPath", "pathParts", "split", "i", "length", "cur<PERSON><PERSON>", "startsWith", "add", "redirect", "source", "tokens", "every", "token", "staticFilter", "from", "dynamicFilter", "data", "export"], "mappings": "AACA,SAASA,WAAW,QAAQ,6BAA4B;AACxD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,mBAAmB,QAAQ,mDAAkD;AAEtF,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SACEC,mCAAmC,EACnCC,0BAA0B,QACrB,+CAA8C;AAErD,OAAO,SAASC,yBACdC,KAAe,EACfC,SAAqB,EACrBC,gBAAyB;IAKzB,MAAMC,cAAc,IAAIC;IACxB,MAAMC,eAAe,IAAID;IAEzB,KAAK,IAAIE,QAAQN,MAAO;QACtB,IAAIN,eAAeY,OAAO;YACxB,IAAIR,2BAA2BQ,OAAO;gBACpCA,OAAOT,oCAAoCS,MAAMC,gBAAgB;YACnE;YAEA,IAAIC,UAAU;YACd,MAAMC,YAAYH,KAAKI,KAAK,CAAC;YAE7B,uDAAuD;YACvD,kDAAkD;YAClD,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,GAAG,GAAGD,IAAK;gBAC7C,MAAME,UAAUJ,SAAS,CAACE,EAAE;gBAE5B,IAAIE,QAAQC,UAAU,CAAC,MAAM;oBAC3B;gBACF;gBACAN,UAAU,CAAC,EAAEA,QAAQ,CAAC,EAAEK,QAAQ,CAAC;YACnC;YAEA,IAAIL,SAAS;gBACXH,aAAaU,GAAG,CAACP;YACnB;QACF,OAAO;YACLL,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,KAAK,MAAMU,YAAYf,UAAW;QAChC,MAAM,EAAEgB,MAAM,EAAE,GAAGD;QACnB,MAAMV,OAAOX,oBAAoBsB;QACjC,IAAIC,SAAkB,EAAE;QAExB,IAAI;YACFA,SAAStB,eAAeqB,QAAQC,MAAM,IAAI,EAAE;QAC9C,EAAE,OAAM,CAAC;QAET,IAAIA,OAAOC,KAAK,CAAC,CAACC,QAAU,OAAOA,UAAU,WAAW;YACtD,0CAA0C;YAC1CjB,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,MAAMe,eAAe5B,YAAY6B,IAAI,CAAC;WAAInB;KAAY,EAAED;IAExD,MAAMqB,gBAAgB9B,YAAY6B,IAAI,CAAC;WAAIjB;KAAa,EAAEH;IAC1D,MAAMsB,OAAO;QACXH,cAAcA,aAAaI,MAAM;QACjCF,eAAeA,cAAcE,MAAM;IACrC;IACA,OAAOD;AACT"}