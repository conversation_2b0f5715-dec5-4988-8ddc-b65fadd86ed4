{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/request-utils.ts"], "names": ["decorateServerError", "PageNotFoundError", "invokeRequest", "deserializeErr", "serializedErr", "stack", "ErrorType", "Error", "name", "err", "message", "digest", "process", "env", "NODE_ENV", "NEXT_RUNTIME", "source", "invokeIpcMethod", "fetchHostname", "method", "args", "ipcPort", "ipcKey", "res", "encodeURIComponent", "JSON", "stringify", "headers", "body", "text", "startsWith", "endsWith", "parsedBody", "parse"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,iBAAiB,QAAQ,4BAA2B;AAC7D,SAASC,aAAa,QAAQ,mBAAkB;AAEhD,OAAO,MAAMC,iBAAiB,CAACC;IAC7B,IACE,CAACA,iBACD,OAAOA,kBAAkB,YACzB,CAACA,cAAcC,KAAK,EACpB;QACA,OAAOD;IACT;IACA,IAAIE,YAAiBC;IAErB,IAAIH,cAAcI,IAAI,KAAK,qBAAqB;QAC9CF,YAAYL;IACd;IAEA,MAAMQ,MAAM,IAAIH,UAAUF,cAAcM,OAAO;IAC/CD,IAAIJ,KAAK,GAAGD,cAAcC,KAAK;IAC/BI,IAAID,IAAI,GAAGJ,cAAcI,IAAI;IAC3BC,IAAYE,MAAM,GAAGP,cAAcO,MAAM;IAE3C,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACE,YAAY,KAAK,QAC7B;QACAf,oBAAoBS,KAAKL,cAAcY,MAAM,IAAI;IACnD;IACA,OAAOP;AACT,EAAC;AAED,OAAO,eAAeQ,gBAAgB,EACpCC,gBAAgB,WAAW,EAC3BC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,MAAM,EAOP;IACC,IAAID,SAAS;QACX,MAAME,MAAM,MAAMrB,cAChB,CAAC,OAAO,EAAEgB,cAAc,CAAC,EAAEG,QAAQ,KAAK,EAAEC,OAAO,QAAQ,EACvDH,OACD,MAAM,EAAEK,mBAAmBC,KAAKC,SAAS,CAACN,OAAO,CAAC,EACnD;YACED,QAAQ;YACRQ,SAAS,CAAC;QACZ;QAEF,MAAMC,OAAO,MAAML,IAAIM,IAAI;QAE3B,IAAID,KAAKE,UAAU,CAAC,QAAQF,KAAKG,QAAQ,CAAC,MAAM;YAC9C,MAAMC,aAAaP,KAAKQ,KAAK,CAACL;YAE9B,IACEI,cACA,OAAOA,eAAe,YACtB,SAASA,cACT,WAAWA,WAAWvB,GAAG,EACzB;gBACA,MAAMN,eAAe6B,WAAWvB,GAAG;YACrC;YACA,OAAOuB;QACT;IACF;AACF"}