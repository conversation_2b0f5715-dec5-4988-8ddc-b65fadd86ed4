{"version": 3, "sources": ["../../../../../src/server/future/route-modules/pages-api/module.ts"], "names": ["PagesAPIRouteModule", "RouteModule", "constructor", "options", "userland", "default", "Error", "definition", "page", "apiResolverWrapped", "wrapApiHandler", "apiResolver", "render", "req", "res", "context", "query", "previewProps", "revalidate", "trustHostHeader", "allowedRevalidateHeaderKeys", "hostname", "multiZoneDraftMode", "minimalMode", "dev"], "mappings": ";;;;;;;;;;;;;;;IA2GaA,mBAAmB;eAAnBA;;IAqDb,OAAkC;eAAlC;;;0BA5JuD;6BAGI;6BAC/B;AAmGrB,MAAMA,4BAA4BC,wBAAW;IAMlDC,YAAYC,OAAmC,CAAE;QAC/C,KAAK,CAACA;QAEN,IAAI,OAAOA,QAAQC,QAAQ,CAACC,OAAO,KAAK,YAAY;YAClD,MAAM,IAAIC,MACR,CAAC,KAAK,EAAEH,QAAQI,UAAU,CAACC,IAAI,CAAC,oCAAoC,CAAC;QAEzE;QAEA,IAAI,CAACC,kBAAkB,GAAGC,IAAAA,wBAAc,EACtCP,QAAQI,UAAU,CAACC,IAAI,EACvBG,wBAAW;IAEf;IAEA;;;;;GAKC,GACD,MAAaC,OACXC,GAAoB,EACpBC,GAAmB,EACnBC,OAAoC,EACrB;QACf,MAAM,EAAEN,kBAAkB,EAAE,GAAG,IAAI;QACnC,MAAMA,mBACJI,KACAC,KACAC,QAAQC,KAAK,EACb,IAAI,CAACZ,QAAQ,EACb;YACE,GAAGW,QAAQE,YAAY;YACvBC,YAAYH,QAAQG,UAAU;YAC9BC,iBAAiBJ,QAAQI,eAAe;YACxCC,6BAA6BL,QAAQK,2BAA2B;YAChEC,UAAUN,QAAQM,QAAQ;YAC1BC,oBAAoBP,QAAQO,kBAAkB;QAChD,GACAP,QAAQQ,WAAW,EACnBR,QAAQS,GAAG,EACXT,QAAQP,IAAI;IAEhB;AACF;MAEA,WAAeR"}