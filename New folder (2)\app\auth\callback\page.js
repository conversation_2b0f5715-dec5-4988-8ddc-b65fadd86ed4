'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { socialAuth } from '@/lib/socialAuth';

export default function SocialLoginCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [status, setStatus] = useState('Processing social login...');

  useEffect(() => {
    async function processSocialLogin() {
      try {
        // Get URL parameters
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        // If there's an error parameter, show the error
        if (error) {
          setStatus(`Authentication error: ${error}`);
          // Redirect to login page after a delay
          setTimeout(() => router.push('/login'), 3000);
          return;
        }

        // Check if we have the necessary parameters
        if (!code || !state) {
          setStatus('Missing required authentication parameters');
          // Redirect to login page after a delay
          setTimeout(() => router.push('/login'), 3000);
          return;
        }

        // Process the social login
        socialAuth.handleCallback(
          { code, state },
          // Success callback
          async (userData, token) => {
            // Use the existing login context to set user state
            await login(userData.email, null, {
              socialLogin: true,
              userData,
              token
            });
            
            setStatus('Login successful! Redirecting...');
            // Redirect to dashboard or home page
            router.push('/dashboard');
          },
          // Error callback
          (errorMessage) => {
            setStatus(`Login failed: ${errorMessage}`);
            // Redirect to login page after a delay
            setTimeout(() => router.push('/login'), 3000);
          }
        );
      } catch (error) {
        setStatus(`Error: ${error.message}`);
        // Redirect to login page after a delay
        setTimeout(() => router.push('/login'), 3000);
      }
    }

    processSocialLogin();
  }, [searchParams, router, login]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold mb-4 text-center text-gray-800 dark:text-white">
          Social Login
        </h1>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
        <p className="text-center text-gray-600 dark:text-gray-300">{status}</p>
      </div>
    </div>
  );
}
