{"version": 3, "sources": ["../../../src/server/web/next-url.ts"], "names": ["NextURL", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "Internal", "Symbol", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyze", "info", "getNextPathnameInfo", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "i18nProvider", "hostname", "getHostname", "headers", "domainLocale", "detectDomainLocale", "i18n", "domains", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "formatNextPathnameInfo", "forceLocale", "undefined", "formatSearch", "search", "locales", "includes", "TypeError", "searchParams", "host", "value", "port", "protocol", "href", "hash", "origin", "password", "username", "startsWith", "toString", "toJSON", "for", "clone"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;oCA7BsB;wCACI;6BACX;qCACQ;AAcpC,MAAMC,2BACJ;AAEF,SAASC,SAASC,GAAiB,EAAEC,IAAmB;IACtD,OAAO,IAAIC,IACTC,OAAOH,KAAKI,OAAO,CAACN,0BAA0B,cAC9CG,QAAQE,OAAOF,MAAMG,OAAO,CAACN,0BAA0B;AAE3D;AAEA,MAAMO,WAAWC,OAAO;AAEjB,MAAMT;IAeXU,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIT;QACJ,IAAIU;QAEJ,IACE,AAAC,OAAOF,eAAe,YAAY,cAAcA,cACjD,OAAOA,eAAe,UACtB;YACAR,OAAOQ;YACPE,UAAUD,QAAQ,CAAC;QACrB,OAAO;YACLC,UAAUD,QAAQD,cAAc,CAAC;QACnC;QAEA,IAAI,CAACJ,SAAS,GAAG;YACfL,KAAKD,SAASS,OAAOP,QAAQU,QAAQV,IAAI;YACzCU,SAASA;YACTC,UAAU;QACZ;QAEA,IAAI,CAACC,OAAO;IACd;IAEQA,UAAU;YAcV,wCAAA,mCAKJ,6BACA,yCAAA;QAnBF,MAAMC,OAAOC,IAAAA,wCAAmB,EAAC,IAAI,CAACV,SAAS,CAACL,GAAG,CAACgB,QAAQ,EAAE;YAC5DC,YAAY,IAAI,CAACZ,SAAS,CAACM,OAAO,CAACM,UAAU;YAC7CC,WAAW,CAACC,QAAQC,GAAG,CAACC,kCAAkC;YAC1DC,cAAc,IAAI,CAACjB,SAAS,CAACM,OAAO,CAACW,YAAY;QACnD;QAEA,MAAMC,WAAWC,IAAAA,wBAAW,EAC1B,IAAI,CAACnB,SAAS,CAACL,GAAG,EAClB,IAAI,CAACK,SAAS,CAACM,OAAO,CAACc,OAAO;QAEhC,IAAI,CAACpB,SAAS,CAACqB,YAAY,GAAG,IAAI,CAACrB,SAAS,CAACM,OAAO,CAACW,YAAY,GAC7D,IAAI,CAACjB,SAAS,CAACM,OAAO,CAACW,YAAY,CAACK,kBAAkB,CAACJ,YACvDI,IAAAA,sCAAkB,GAChB,oCAAA,IAAI,CAACtB,SAAS,CAACM,OAAO,CAACM,UAAU,sBAAjC,yCAAA,kCAAmCW,IAAI,qBAAvC,uCAAyCC,OAAO,EAChDN;QAGN,MAAMO,gBACJ,EAAA,8BAAA,IAAI,CAACzB,SAAS,CAACqB,YAAY,qBAA3B,4BAA6BI,aAAa,OAC1C,qCAAA,IAAI,CAACzB,SAAS,CAACM,OAAO,CAACM,UAAU,sBAAjC,0CAAA,mCAAmCW,IAAI,qBAAvC,wCAAyCE,aAAa;QAExD,IAAI,CAACzB,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAGF,KAAKE,QAAQ;QAC3C,IAAI,CAACX,SAAS,CAACyB,aAAa,GAAGA;QAC/B,IAAI,CAACzB,SAAS,CAACO,QAAQ,GAAGE,KAAKF,QAAQ,IAAI;QAC3C,IAAI,CAACP,SAAS,CAAC0B,OAAO,GAAGjB,KAAKiB,OAAO;QACrC,IAAI,CAAC1B,SAAS,CAAC2B,MAAM,GAAGlB,KAAKkB,MAAM,IAAIF;QACvC,IAAI,CAACzB,SAAS,CAAC4B,aAAa,GAAGnB,KAAKmB,aAAa;IACnD;IAEQC,iBAAiB;QACvB,OAAOC,IAAAA,8CAAsB,EAAC;YAC5BvB,UAAU,IAAI,CAACP,SAAS,CAACO,QAAQ;YACjCmB,SAAS,IAAI,CAAC1B,SAAS,CAAC0B,OAAO;YAC/BD,eAAe,CAAC,IAAI,CAACzB,SAAS,CAACM,OAAO,CAACyB,WAAW,GAC9C,IAAI,CAAC/B,SAAS,CAACyB,aAAa,GAC5BO;YACJL,QAAQ,IAAI,CAAC3B,SAAS,CAAC2B,MAAM;YAC7BhB,UAAU,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;YACrCiB,eAAe,IAAI,CAAC5B,SAAS,CAAC4B,aAAa;QAC7C;IACF;IAEQK,eAAe;QACrB,OAAO,IAAI,CAACjC,SAAS,CAACL,GAAG,CAACuC,MAAM;IAClC;IAEA,IAAWR,UAAU;QACnB,OAAO,IAAI,CAAC1B,SAAS,CAAC0B,OAAO;IAC/B;IAEA,IAAWA,QAAQA,OAA2B,EAAE;QAC9C,IAAI,CAAC1B,SAAS,CAAC0B,OAAO,GAAGA;IAC3B;IAEA,IAAWC,SAAS;QAClB,OAAO,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,IAAI;IAClC;IAEA,IAAWA,OAAOA,MAAc,EAAE;YAG7B,wCAAA;QAFH,IACE,CAAC,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,IACtB,GAAC,oCAAA,IAAI,CAAC3B,SAAS,CAACM,OAAO,CAACM,UAAU,sBAAjC,yCAAA,kCAAmCW,IAAI,qBAAvC,uCAAyCY,OAAO,CAACC,QAAQ,CAACT,UAC3D;YACA,MAAM,IAAIU,UACR,CAAC,8CAA8C,EAAEV,OAAO,CAAC,CAAC;QAE9D;QAEA,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,GAAGA;IAC1B;IAEA,IAAIF,gBAAgB;QAClB,OAAO,IAAI,CAACzB,SAAS,CAACyB,aAAa;IACrC;IAEA,IAAIJ,eAAe;QACjB,OAAO,IAAI,CAACrB,SAAS,CAACqB,YAAY;IACpC;IAEA,IAAIiB,eAAe;QACjB,OAAO,IAAI,CAACtC,SAAS,CAACL,GAAG,CAAC2C,YAAY;IACxC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACvC,SAAS,CAACL,GAAG,CAAC4C,IAAI;IAChC;IAEA,IAAIA,KAAKC,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC4C,IAAI,GAAGC;IAC5B;IAEA,IAAItB,WAAW;QACb,OAAO,IAAI,CAAClB,SAAS,CAACL,GAAG,CAACuB,QAAQ;IACpC;IAEA,IAAIA,SAASsB,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACuB,QAAQ,GAAGsB;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACzC,SAAS,CAACL,GAAG,CAAC8C,IAAI;IAChC;IAEA,IAAIA,KAAKD,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC8C,IAAI,GAAGD;IAC5B;IAEA,IAAIE,WAAW;QACb,OAAO,IAAI,CAAC1C,SAAS,CAACL,GAAG,CAAC+C,QAAQ;IACpC;IAEA,IAAIA,SAASF,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC+C,QAAQ,GAAGF;IAChC;IAEA,IAAIG,OAAO;QACT,MAAMhC,WAAW,IAAI,CAACkB,cAAc;QACpC,MAAMK,SAAS,IAAI,CAACD,YAAY;QAChC,OAAO,CAAC,EAAE,IAAI,CAACS,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,CAAC,EAAE5B,SAAS,EAAEuB,OAAO,EAAE,IAAI,CAACU,IAAI,CAAC,CAAC;IACzE;IAEA,IAAID,KAAKhD,GAAW,EAAE;QACpB,IAAI,CAACK,SAAS,CAACL,GAAG,GAAGD,SAASC;QAC9B,IAAI,CAACa,OAAO;IACd;IAEA,IAAIqC,SAAS;QACX,OAAO,IAAI,CAAC7C,SAAS,CAACL,GAAG,CAACkD,MAAM;IAClC;IAEA,IAAIlC,WAAW;QACb,OAAO,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;IACpC;IAEA,IAAIA,SAAS6B,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAG6B;IAChC;IAEA,IAAII,OAAO;QACT,OAAO,IAAI,CAAC5C,SAAS,CAACL,GAAG,CAACiD,IAAI;IAChC;IAEA,IAAIA,KAAKJ,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACiD,IAAI,GAAGJ;IAC5B;IAEA,IAAIN,SAAS;QACX,OAAO,IAAI,CAAClC,SAAS,CAACL,GAAG,CAACuC,MAAM;IAClC;IAEA,IAAIA,OAAOM,KAAa,EAAE;QACxB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACuC,MAAM,GAAGM;IAC9B;IAEA,IAAIM,WAAW;QACb,OAAO,IAAI,CAAC9C,SAAS,CAACL,GAAG,CAACmD,QAAQ;IACpC;IAEA,IAAIA,SAASN,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACmD,QAAQ,GAAGN;IAChC;IAEA,IAAIO,WAAW;QACb,OAAO,IAAI,CAAC/C,SAAS,CAACL,GAAG,CAACoD,QAAQ;IACpC;IAEA,IAAIA,SAASP,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACoD,QAAQ,GAAGP;IAChC;IAEA,IAAIjC,WAAW;QACb,OAAO,IAAI,CAACP,SAAS,CAACO,QAAQ;IAChC;IAEA,IAAIA,SAASiC,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACO,QAAQ,GAAGiC,MAAMQ,UAAU,CAAC,OAAOR,QAAQ,CAAC,CAAC,EAAEA,MAAM,CAAC;IACvE;IAEAS,WAAW;QACT,OAAO,IAAI,CAACN,IAAI;IAClB;IAEAO,SAAS;QACP,OAAO,IAAI,CAACP,IAAI;IAClB;IAEA,CAAC1C,OAAOkD,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,MAAM,IAAI,CAACA,IAAI;YACfE,QAAQ,IAAI,CAACA,MAAM;YACnBH,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBP,MAAM,IAAI,CAACA,IAAI;YACfrB,UAAU,IAAI,CAACA,QAAQ;YACvBuB,MAAM,IAAI,CAACA,IAAI;YACf9B,UAAU,IAAI,CAACA,QAAQ;YACvBuB,QAAQ,IAAI,CAACA,MAAM;YACnBI,cAAc,IAAI,CAACA,YAAY;YAC/BM,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEAQ,QAAQ;QACN,OAAO,IAAI5D,QAAQM,OAAO,IAAI,GAAG,IAAI,CAACE,SAAS,CAACM,OAAO;IACzD;AACF"}