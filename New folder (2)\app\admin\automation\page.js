'use client';

import { useState, useEffect } from 'react';

export default function AutomationPage() {
  const [autoSyncStatus, setAutoSyncStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [intervalMinutes, setIntervalMinutes] = useState(5);

  // Load status on mount and refresh every 30 seconds
  useEffect(() => {
    loadAutoSyncStatus();
    const interval = setInterval(loadAutoSyncStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAutoSyncStatus = async () => {
    try {
      const response = await fetch('/api/auto-sync');
      const data = await response.json();
      if (data.success) {
        setAutoSyncStatus(data.status);
      }
    } catch (error) {
      console.error('Error loading auto-sync status:', error);
    }
  };

  const startAutoSync = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auto-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'start', 
          intervalMinutes: intervalMinutes 
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('✅ Automation started! Your products will now sync automatically.');
        loadAutoSyncStatus();
      } else {
        alert('❌ Error: ' + data.message);
      }
    } catch (error) {
      alert('❌ Error starting automation: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const stopAutoSync = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auto-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' })
      });

      const data = await response.json();
      if (data.success) {
        alert('⏹️ Automation stopped.');
        loadAutoSyncStatus();
      } else {
        alert('❌ Error: ' + data.message);
      }
    } catch (error) {
      alert('❌ Error stopping automation: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const forceSyncNow = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/auto-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'force-sync' })
      });

      const data = await response.json();
      if (data.success) {
        alert(`🚀 Force sync completed! ${data.result?.message || 'Check console for details'}`);
        loadAutoSyncStatus();
      } else {
        alert('❌ Error: ' + data.message);
      }
    } catch (error) {
      alert('❌ Error running force sync: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const runFastSync = async () => {
    if (!confirm('🚀 This will run ULTRA-FAST parallel sync on ALL products. Continue?')) {
      return;
    }

    setLoading(true);
    const startTime = Date.now();

    try {
      const response = await fetch('/api/fast-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });

      const data = await response.json();
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      if (data.success) {
        alert(`🚀 ULTRA-FAST SYNC COMPLETE!\n\n` +
              `✅ Processed: ${data.processed}/${data.total} products\n` +
              `📢 PUBLISHED: ${data.published} products (now live on website!)\n` +
              `🔄 Updated: ${data.updated} products with new images\n` +
              `⚡ Time: ${data.timeElapsed}\n` +
              `🏃 Speed: ${data.speed}\n` +
              `📊 Total time: ${totalTime}ms\n\n` +
              `🎉 Check your website - products should now be visible!`);
        loadAutoSyncStatus();
      } else {
        alert('❌ Error: ' + data.error);
      }
    } catch (error) {
      alert('❌ Error running fast sync: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🤖 Product Sync Automation
          </h1>

          {/* Status Display */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-8 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-blue-900">Automation Status</h2>
              <div className={`flex items-center space-x-2 ${autoSyncStatus?.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                <div className={`w-3 h-3 rounded-full ${autoSyncStatus?.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="font-medium">
                  {autoSyncStatus?.isRunning ? 'RUNNING' : 'STOPPED'}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white rounded-lg p-4">
                <div className="text-sm text-gray-600">Last Sync</div>
                <div className="font-medium text-gray-900">
                  {autoSyncStatus?.lastSyncTime 
                    ? new Date(autoSyncStatus.lastSyncTime).toLocaleString()
                    : 'Never'
                  }
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-4">
                <div className="text-sm text-gray-600">Products Processed</div>
                <div className="font-medium text-gray-900">
                  {autoSyncStatus?.processedProductsCount || 0}
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-4">
                <div className="text-sm text-gray-600">Next Check</div>
                <div className="font-medium text-gray-900">
                  {autoSyncStatus?.nextSyncIn || 'Not scheduled'}
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="space-y-6">
            {/* Interval Setting */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Sync Interval</h3>
              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">
                  Check for new products every:
                </label>
                <select
                  value={intervalMinutes}
                  onChange={(e) => setIntervalMinutes(parseInt(e.target.value))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={autoSyncStatus?.isRunning}
                >
                  <option value={1}>1 minute</option>
                  <option value={2}>2 minutes</option>
                  <option value={5}>5 minutes</option>
                  <option value={10}>10 minutes</option>
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                </select>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              {!autoSyncStatus?.isRunning ? (
                <button
                  onClick={startAutoSync}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-3 px-6 rounded-lg transition-colors"
                >
                  {loading ? 'Starting...' : '🚀 Start Automation'}
                </button>
              ) : (
                <button
                  onClick={stopAutoSync}
                  disabled={loading}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-3 px-6 rounded-lg transition-colors"
                >
                  {loading ? 'Stopping...' : '⏹️ Stop Automation'}
                </button>
              )}

              <button
                onClick={runFastSync}
                disabled={loading}
                className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white font-bold py-3 px-6 rounded-lg transition-colors shadow-lg"
              >
                {loading ? 'FAST SYNCING...' : '🚀 ULTRA-FAST SYNC ALL'}
              </button>

              <button
                onClick={forceSyncNow}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {loading ? 'Syncing...' : '⚡ Force Sync Now'}
              </button>

              <button
                onClick={loadAutoSyncStatus}
                disabled={loading}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                🔄 Refresh Status
              </button>
            </div>
          </div>

          {/* How It Works */}
          <div className="mt-8 bg-green-50 rounded-lg p-6 border border-green-200">
            <h3 className="text-lg font-semibold text-green-900 mb-4">🎯 True Automation - How It Works</h3>
            <div className="space-y-3 text-green-800">
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">1.</span>
                <p><strong>Import products in WordPress</strong> - Use AliDrop or any method to import 1000+ products</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">2.</span>
                <p><strong>Automation detects new products</strong> - Checks every few minutes for recently modified products</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">3.</span>
                <p><strong>Smart image processing</strong> - Automatically removes size charts and finds product photos</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-green-600 font-bold">4.</span>
                <p><strong>Products appear on website</strong> - No manual clicking required!</p>
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-green-100 rounded-lg">
              <p className="text-green-900 font-medium">
                ✨ <strong>Set it and forget it!</strong> Once started, the automation runs continuously in the background.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
