import { notFound } from 'next/navigation';
import { wooCommerceApi } from '@/lib/woocommerce';
import ProductDetail from '@/components/product/ProductDetail';
import RelatedProducts from '@/components/product/RelatedProducts';

export async function generateMetadata({ params }) {
  try {
    const product = await wooCommerceApi.getProduct(params.id);
    
    if (!product) {
      return {
        title: 'Product Not Found - Deal4u',
      };
    }

    return {
      title: `${product.name} - Deal4u`,
      description: product.short_description || product.description || `Buy ${product.name} at Deal4u`,
      openGraph: {
        title: product.name,
        description: product.short_description || product.description,
        images: product.images ? [product.images[0]] : [],
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: product.name,
        description: product.short_description || product.description,
        images: product.images ? [product.images[0]] : [],
      },
    };
  } catch (error) {
    return {
      title: 'Product Not Found - Deal4u',
    };
  }
}

// Dynamic route - fetch product data on each request for real-time sync
// Removed generateStaticParams to enable dynamic data fetching

async function getProduct(id) {
  try {
    const product = await wooCommerceApi.getProduct(id);
    if (!product) {
      console.error('Product not found:', id);
      return null;
    }
    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

async function getRelatedProducts(categoryName, currentProductId) {
  try {
    if (!categoryName) return [];
    
    const products = await wooCommerceApi.getProducts({ 
      per_page: 4,
      exclude: [currentProductId]
    });
    
    return products.filter(product => 
      product.categories?.some(cat => 
        cat.name.toLowerCase() === categoryName.toLowerCase()
      )
    ).slice(0, 4);
  } catch (error) {
    console.error('Error fetching related products:', error);
    return [];
  }
}

export default async function ProductPage({ params }) {
  try {
    if (!params?.id) {
      console.error('Missing product ID');
      notFound();
    }

    const productId = parseInt(params.id);
    if (isNaN(productId) || productId <= 0) {
      console.error('Invalid product ID:', params.id);
      notFound();
    }

    const product = await getProduct(productId);
    if (!product) {
      console.error('Product not found:', productId);
      notFound();
    }

    // Get related products
    const relatedProducts = await getRelatedProducts(
      product.categories?.[0]?.name,
      product.id
    );

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ProductDetail product={product} />
          
          {relatedProducts.length > 0 && (
            <div className="mt-16">
              <RelatedProducts products={relatedProducts} />
            </div>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in product page:', error);
    notFound();
  }
}