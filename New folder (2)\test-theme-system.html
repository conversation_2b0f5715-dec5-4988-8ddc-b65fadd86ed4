<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme System Test - Deal4u</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .hero-section {
            padding: 60px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
        }
        
        .sale-banner {
            background: #ff6b35;
            color: white;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .product-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
            display: inline-block;
            width: 250px;
            vertical-align: top;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        
        .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #059669;
        }
        
        .sale-badge {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .theme-info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.loading { background: #dbeafe; color: #1e40af; }
    </style>
    
    <!-- Load theme CSS dynamically -->
    <link id="dynamic-theme-css" rel="stylesheet" href="">
</head>
<body>
    <div class="container">
        <h1>🎨 Theme System Test - Deal4u</h1>
        
        <div class="theme-info">
            <h3>Current Theme Status:</h3>
            <div id="theme-status" class="status loading">Loading theme...</div>
            <div id="theme-details"></div>
        </div>
        
        <div class="sale-banner">
            🔥 SUMMER MEGA SALE - UP TO 50% OFF EVERYTHING! 🔥
        </div>
        
        <div class="hero-section">
            <h1>Premium Products, Amazing Deals</h1>
            <p>Discover thousands of high-quality products at unbeatable prices. Get shipping, competitive deals, and exceptional customer service - that's the Deal4u promise.</p>
            <button class="btn-primary">Shop Summer Sale →</button>
            <button class="btn-primary">Browse All</button>
        </div>
        
        <h2>Featured Products</h2>
        
        <div class="product-card">
            <h3>Summer Dress Collection</h3>
            <div class="price">$29.99 <span class="sale-badge">50% OFF</span></div>
            <p>Beautiful summer dresses perfect for hot weather</p>
            <button class="btn-primary">Add to Cart</button>
        </div>
        
        <div class="product-card">
            <h3>Electronics Bundle</h3>
            <div class="price">$199.99 <span class="sale-badge">MEGA DEAL</span></div>
            <p>Latest tech gadgets at incredible prices</p>
            <button class="btn-primary">Add to Cart</button>
        </div>
        
        <div class="product-card">
            <h3>Fashion Accessories</h3>
            <div class="price">$15.99 <span class="sale-badge">HOT</span></div>
            <p>Trendy accessories for men and women</p>
            <button class="btn-primary">Add to Cart</button>
        </div>
        
        <div style="margin-top: 40px;">
            <h3>🧪 Theme Test Controls:</h3>
            <button onclick="testTheme('summer')" class="btn-primary">Test Summer Theme</button>
            <button onclick="testTheme('black-friday')" class="btn-primary">Test Black Friday Theme</button>
            <button onclick="loadCurrentTheme()" class="btn-primary">Reload Current Theme</button>
        </div>
    </div>

    <script>
        // Load current theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentTheme();
        });
        
        async function loadCurrentTheme() {
            const statusDiv = document.getElementById('theme-status');
            const detailsDiv = document.getElementById('theme-details');
            
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Loading current theme...';
            
            try {
                const response = await fetch('/api/admin/theme/current');
                if (response.ok) {
                    const themeData = await response.json();
                    const activeTheme = themeData.theme || 'summer';
                    
                    // Apply theme
                    document.documentElement.setAttribute('data-theme', activeTheme);
                    
                    // Load theme CSS
                    const themeLink = document.getElementById('dynamic-theme-css');
                    themeLink.href = `/themes/${activeTheme}.css`;
                    
                    // Update status
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Theme loaded successfully: ${activeTheme}`;
                    
                    detailsDiv.innerHTML = `
                        <strong>Theme:</strong> ${activeTheme}<br>
                        <strong>Last Changed:</strong> ${new Date(themeData.lastChanged).toLocaleString()}<br>
                        <strong>Applied By:</strong> ${themeData.appliedBy}
                    `;
                    
                    console.log('Theme applied:', activeTheme);
                } else {
                    throw new Error('Failed to fetch theme data');
                }
            } catch (error) {
                console.error('Error loading theme:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Error loading theme - using default';
                
                // Fallback to summer theme
                testTheme('summer');
            }
        }
        
        function testTheme(themeName) {
            const statusDiv = document.getElementById('theme-status');
            const detailsDiv = document.getElementById('theme-details');
            
            // Apply theme
            document.documentElement.setAttribute('data-theme', themeName);
            
            // Load theme CSS
            const themeLink = document.getElementById('dynamic-theme-css');
            themeLink.href = `/themes/${themeName}.css`;
            
            // Update status
            statusDiv.className = 'status success';
            statusDiv.textContent = `🎨 Testing theme: ${themeName}`;
            
            detailsDiv.innerHTML = `
                <strong>Test Theme:</strong> ${themeName}<br>
                <strong>Status:</strong> Applied for testing<br>
                <strong>Note:</strong> This is a temporary test - reload to get actual theme
            `;
            
            console.log('Test theme applied:', themeName);
        }
    </script>
</body>
</html>
