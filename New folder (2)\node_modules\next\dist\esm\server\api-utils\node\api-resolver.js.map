{"version": 3, "sources": ["../../../../src/server/api-utils/node/api-resolver.ts"], "names": ["bytes", "generateETag", "sendEtagResponse", "Stream", "isError", "isResSent", "interopDefault", "setLazyProp", "sendStatusCode", "redirect", "clearPreviewData", "sendError", "ApiError", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "tryGetPreviewData", "parseBody", "getMaxContentLength", "responseLimit", "parse", "sendData", "req", "res", "body", "undefined", "end", "statusCode", "removeHeader", "process", "env", "NODE_ENV", "console", "warn", "url", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "isJSONLike", "includes", "stringifiedBody", "JSON", "stringify", "etag", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "byteLength", "<PERSON><PERSON><PERSON>", "jsonBody", "send", "isValidData", "str", "setDraftMode", "options", "previewModeId", "Error", "expires", "enable", "Date", "serialize", "require", "previous", "Array", "isArray", "httpOnly", "sameSite", "secure", "path", "setPreviewData", "data", "previewModeEncryptionKey", "previewModeSigningKey", "jsonwebtoken", "encryptWithSecret", "payload", "sign", "from", "algorithm", "maxAge", "expiresIn", "revalidate", "url<PERSON><PERSON>", "opts", "context", "startsWith", "revalidateHeaders", "unstable_onlyGenerated", "allowedRevalidateHeaderKeys", "trustHostHeader", "key", "Object", "keys", "headers", "fetch", "host", "method", "cacheHeader", "get", "toUpperCase", "status", "err", "message", "apiResolver", "query", "resolverModule", "apiContext", "propagateError", "dev", "page", "apiReq", "apiRes", "config", "<PERSON><PERSON><PERSON><PERSON>", "api", "externalResolver", "multiZoneDraftMode", "previewData", "preview", "sizeLimit", "contentLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writeData", "write", "endResponse", "args", "apply", "format", "json", "statusOrUrl", "assign", "resolver", "wasPiped", "once", "apiRouteResult", "Response", "error"], "mappings": "AAMA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;AACrD,SAASC,MAAM,QAAQ,SAAQ;AAC/B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,SAAS,QAAQ,4BAA2B;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SACEC,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,QAAQ,EACRC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,sBAAsB,QACjB,aAAY;AACnB,SAASC,eAAe,QAAQ,yBAAwB;AACxD,SACEC,2BAA2B,EAC3BC,0CAA0C,QACrC,yBAAwB;AAC/B,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,SAAS,QAAQ,eAAc;AAgBxC,SAASC,oBAAoBC,aAA6B;IACxD,IAAIA,iBAAiB,OAAOA,kBAAkB,WAAW;QACvD,OAAOtB,MAAMuB,KAAK,CAACD;IACrB;IACA,OAAOP;AACT;AAEA;;;;;CAKC,GACD,SAASS,SAASC,GAAmB,EAAEC,GAAoB,EAAEC,IAAS;IACpE,IAAIA,SAAS,QAAQA,SAASC,WAAW;QACvCF,IAAIG,GAAG;QACP;IACF;IAEA,gCAAgC;IAChC,IAAIH,IAAII,UAAU,KAAK,OAAOJ,IAAII,UAAU,KAAK,KAAK;QACpDJ,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QAEjB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBP,MAAM;YAClDQ,QAAQC,IAAI,CACV,CAAC,yDAAyD,EAAEX,IAAIY,GAAG,CAAC,6CAA6C,CAAC,GAChH,CAAC,2EAA2E,CAAC;QAEnF;QACAX,IAAIG,GAAG;QACP;IACF;IAEA,MAAMS,cAAcZ,IAAIa,SAAS,CAAC;IAElC,IAAIZ,gBAAgBxB,QAAQ;QAC1B,IAAI,CAACmC,aAAa;YAChBZ,IAAIc,SAAS,CAAC,gBAAgB;QAChC;QACAb,KAAKc,IAAI,CAACf;QACV;IACF;IAEA,MAAMgB,aAAa;QAAC;QAAU;QAAU;KAAU,CAACC,QAAQ,CAAC,OAAOhB;IACnE,MAAMiB,kBAAkBF,aAAaG,KAAKC,SAAS,CAACnB,QAAQA;IAC5D,MAAMoB,OAAO9C,aAAa2C;IAC1B,IAAI1C,iBAAiBuB,KAAKC,KAAKqB,OAAO;QACpC;IACF;IAEA,IAAIC,OAAOC,QAAQ,CAACtB,OAAO;QACzB,IAAI,CAACW,aAAa;YAChBZ,IAAIc,SAAS,CAAC,gBAAgB;QAChC;QACAd,IAAIc,SAAS,CAAC,kBAAkBb,KAAKuB,MAAM;QAC3CxB,IAAIG,GAAG,CAACF;QACR;IACF;IAEA,IAAIe,YAAY;QACdhB,IAAIc,SAAS,CAAC,gBAAgB;IAChC;IAEAd,IAAIc,SAAS,CAAC,kBAAkBQ,OAAOG,UAAU,CAACP;IAClDlB,IAAIG,GAAG,CAACe;AACV;AAEA;;;;CAIC,GACD,SAASQ,SAAS1B,GAAoB,EAAE2B,QAAa;IACnD,iCAAiC;IACjC3B,IAAIc,SAAS,CAAC,gBAAgB;IAE9B,6BAA6B;IAC7Bd,IAAI4B,IAAI,CAACT,KAAKC,SAAS,CAACO;AAC1B;AAEA,SAASE,YAAYC,GAAQ;IAC3B,OAAO,OAAOA,QAAQ,YAAYA,IAAIN,MAAM,IAAI;AAClD;AAEA,SAASO,aACP/B,GAAuB,EACvBgC,OAGC;IAED,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,IAAIC,MAAM;IAClB;IACA,MAAMC,UAAUH,QAAQI,MAAM,GAAGlC,YAAY,IAAImC,KAAK;IACtD,2DAA2D;IAC3D,oDAAoD;IACpD,wEAAwE;IACxE,MAAM,EAAEC,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWxC,IAAIa,SAAS,CAAC;IAC/Bb,IAAIc,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO0B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACdA,WACA,EAAE;QACNF,UAAUnD,8BAA8B6C,QAAQC,aAAa,EAAE;YAC7DU,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACNX;QACF;KACD;IACD,OAAOnC;AACT;AAEA,SAAS+C,eACP/C,GAAuB,EACvBgD,IAAqB,EACrBhB,OAGqB;IAErB,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,IAAIC,MAAM;IAClB;IACA,IAAI,CAACL,YAAYG,QAAQiB,wBAAwB,GAAG;QAClD,MAAM,IAAIf,MAAM;IAClB;IACA,IAAI,CAACL,YAAYG,QAAQkB,qBAAqB,GAAG;QAC/C,MAAM,IAAIhB,MAAM;IAClB;IAEA,MAAMiB,eACJZ,QAAQ;IACV,MAAM,EAAEa,iBAAiB,EAAE,GACzBb,QAAQ;IACV,MAAMc,UAAUF,aAAaG,IAAI,CAC/B;QACEN,MAAMI,kBACJ9B,OAAOiC,IAAI,CAACvB,QAAQiB,wBAAwB,GAC5C9B,KAAKC,SAAS,CAAC4B;IAEnB,GACAhB,QAAQkB,qBAAqB,EAC7B;QACEM,WAAW;QACX,GAAIxB,QAAQyB,MAAM,KAAKvD,YACnB;YAAEwD,WAAW1B,QAAQyB,MAAM;QAAC,IAC5BvD,SAAS;IACf;IAGF,qEAAqE;IACrE,+CAA+C;IAC/C,IAAImD,QAAQ7B,MAAM,GAAG,MAAM;QACzB,MAAM,IAAIU,MACR,CAAC,0GAA0G,CAAC;IAEhH;IAEA,MAAM,EAAEI,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWxC,IAAIa,SAAS,CAAC;IAC/Bb,IAAIc,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO0B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACdA,WACA,EAAE;QACNF,UAAUnD,8BAA8B6C,QAAQC,aAAa,EAAE;YAC7DU,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACN,GAAId,QAAQyB,MAAM,KAAKvD,YAClB;gBAAEuD,QAAQzB,QAAQyB,MAAM;YAAC,IAC1BvD,SAAS;YACb,GAAI8B,QAAQc,IAAI,KAAK5C,YAChB;gBAAE4C,MAAMd,QAAQc,IAAI;YAAC,IACtB5C,SAAS;QACf;QACAoC,UAAUlD,4BAA4BiE,SAAS;YAC7CV,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACN,GAAId,QAAQyB,MAAM,KAAKvD,YAClB;gBAAEuD,QAAQzB,QAAQyB,MAAM;YAAC,IAC1BvD,SAAS;YACb,GAAI8B,QAAQc,IAAI,KAAK5C,YAChB;gBAAE4C,MAAMd,QAAQc,IAAI;YAAC,IACtB5C,SAAS;QACf;KACD;IACD,OAAOF;AACT;AAEA,eAAe2D,WACbC,OAAe,EACfC,IAEC,EACD9D,GAAoB,EACpB+D,OAAmB;IAEnB,IAAI,OAAOF,YAAY,YAAY,CAACA,QAAQG,UAAU,CAAC,MAAM;QAC3D,MAAM,IAAI7B,MACR,CAAC,qFAAqF,EAAE0B,QAAQ,CAAC;IAErG;IACA,MAAMI,oBAAiC;QACrC,CAACzE,4BAA4B,EAAEuE,QAAQ7B,aAAa;QACpD,GAAI4B,KAAKI,sBAAsB,GAC3B;YACE,CAACzE,2CAA2C,EAAE;QAChD,IACA,CAAC,CAAC;IACR;IACA,MAAM0E,8BAA8B;WAC9BJ,QAAQI,2BAA2B,IAAI,EAAE;WACzCJ,QAAQK,eAAe,GACvB;YAAC;YAAU;SAA6B,GACxC,EAAE;KACP;IAED,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACvE,IAAIwE,OAAO,EAAG;QAC1C,IAAIL,4BAA4BjD,QAAQ,CAACmD,MAAM;YAC7CJ,iBAAiB,CAACI,IAAI,GAAGrE,IAAIwE,OAAO,CAACH,IAAI;QAC3C;IACF;IAEA,IAAI;QACF,IAAIN,QAAQK,eAAe,EAAE;YAC3B,MAAMnE,MAAM,MAAMwE,MAAM,CAAC,QAAQ,EAAEzE,IAAIwE,OAAO,CAACE,IAAI,CAAC,EAAEb,QAAQ,CAAC,EAAE;gBAC/Dc,QAAQ;gBACRH,SAASP;YACX;YACA,gEAAgE;YAChE,qEAAqE;YACrE,gEAAgE;YAChE,MAAMW,cACJ3E,IAAIuE,OAAO,CAACK,GAAG,CAAC,qBAAqB5E,IAAIuE,OAAO,CAACK,GAAG,CAAC;YAEvD,IACED,CAAAA,+BAAAA,YAAaE,WAAW,QAAO,iBAC/B,CAAE7E,CAAAA,IAAI8E,MAAM,KAAK,OAAOjB,KAAKI,sBAAsB,AAAD,GAClD;gBACA,MAAM,IAAI/B,MAAM,CAAC,iBAAiB,EAAElC,IAAI8E,MAAM,CAAC,CAAC;YAClD;QACF,OAAO,IAAIhB,QAAQH,UAAU,EAAE;YAC7B,MAAMG,QAAQH,UAAU,CAAC;gBACvBC;gBACAI;gBACAH;YACF;QACF,OAAO;YACL,MAAM,IAAI3B,MACR,CAAC,sEAAsE,CAAC;QAE5E;IACF,EAAE,OAAO6C,KAAc;QACrB,MAAM,IAAI7C,MACR,CAAC,qBAAqB,EAAE0B,QAAQ,EAAE,EAAElF,QAAQqG,OAAOA,IAAIC,OAAO,GAAGD,IAAI,CAAC;IAE1E;AACF;AAEA,OAAO,eAAeE,YACpBlF,GAAoB,EACpBC,GAAmB,EACnBkF,KAAU,EACVC,cAAmB,EACnBC,UAAsB,EACtBC,cAAuB,EACvBC,GAAa,EACbC,IAAa;IAEb,MAAMC,SAASzF;IACf,MAAM0F,SAASzF;IAEf,IAAI;YAOiB0F,aACGA,cACGA;QARzB,IAAI,CAACP,gBAAgB;YACnBnF,IAAII,UAAU,GAAG;YACjBJ,IAAIG,GAAG,CAAC;YACR;QACF;QACA,MAAMuF,SAAqBP,eAAeO,MAAM,IAAI,CAAC;QACrD,MAAMC,aAAaD,EAAAA,cAAAA,OAAOE,GAAG,qBAAVF,YAAYC,UAAU,MAAK;QAC9C,MAAM/F,gBAAgB8F,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAY9F,aAAa,KAAI;QACnD,MAAMiG,mBAAmBH,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAYG,gBAAgB,KAAI;QAEzD,qBAAqB;QACrBhH,YAAY;YAAEkB,KAAKyF;QAAO,GAAG,WAAWlG,gBAAgBS,IAAIwE,OAAO;QACnE,uBAAuB;QACvBiB,OAAON,KAAK,GAAGA;QACf,uBAAuB;QACvBrG,YAAY;YAAEkB,KAAKyF;QAAO,GAAG,eAAe,IAC1C/F,kBAAkBM,KAAKC,KAAKoF,YAAY,CAAC,CAACA,WAAWU,kBAAkB;QAEzE,sCAAsC;QACtCjH,YAAY;YAAEkB,KAAKyF;QAAO,GAAG,WAAW,IACtCA,OAAOO,WAAW,KAAK,QAAQ,OAAO7F;QAExC,6CAA6C;QAC7CrB,YAAY;YAAEkB,KAAKyF;QAAO,GAAG,aAAa,IAAMA,OAAOQ,OAAO;QAE9D,kBAAkB;QAClB,IAAIL,cAAc,CAACH,OAAOvF,IAAI,EAAE;YAC9BuF,OAAOvF,IAAI,GAAG,MAAMP,UAClB8F,QACAE,OAAOE,GAAG,IAAIF,OAAOE,GAAG,CAACD,UAAU,IAAID,OAAOE,GAAG,CAACD,UAAU,CAACM,SAAS,GAClEP,OAAOE,GAAG,CAACD,UAAU,CAACM,SAAS,GAC/B;QAER;QAEA,IAAIC,gBAAgB;QACpB,MAAMC,mBAAmBxG,oBAAoBC;QAC7C,MAAMwG,YAAYX,OAAOY,KAAK;QAC9B,MAAMC,cAAcb,OAAOtF,GAAG;QAC9BsF,OAAOY,KAAK,GAAG,CAAC,GAAGE;YACjBL,iBAAiB5E,OAAOG,UAAU,CAAC8E,IAAI,CAAC,EAAE,IAAI;YAC9C,OAAOH,UAAUI,KAAK,CAACf,QAAQc;QACjC;QACAd,OAAOtF,GAAG,GAAG,CAAC,GAAGoG;YACf,IAAIA,KAAK/E,MAAM,IAAI,OAAO+E,IAAI,CAAC,EAAE,KAAK,YAAY;gBAChDL,iBAAiB5E,OAAOG,UAAU,CAAC8E,IAAI,CAAC,EAAE,IAAI;YAChD;YAEA,IAAI3G,iBAAiBsG,iBAAiBC,kBAAkB;gBACtD1F,QAAQC,IAAI,CACV,CAAC,iBAAiB,EAAEX,IAAIY,GAAG,CAAC,SAAS,EAAErC,MAAMmI,MAAM,CACjDN,kBACA,0GAA0G,CAAC;YAEjH;YAEA,OAAOG,YAAYE,KAAK,CAACf,QAAQc;QACnC;QACAd,OAAOX,MAAM,GAAG,CAAC1E,aAAetB,eAAe2G,QAAQrF;QACvDqF,OAAO7D,IAAI,GAAG,CAACoB,OAASlD,SAAS0F,QAAQC,QAAQzC;QACjDyC,OAAOiB,IAAI,GAAG,CAAC1D,OAAStB,SAAS+D,QAAQzC;QACzCyC,OAAO1G,QAAQ,GAAG,CAAC4H,aAA8BhG,MAC/C5B,SAAS0G,QAAQkB,aAAahG;QAChC8E,OAAO1D,YAAY,GAAG,CAACC,UAAU;YAAEI,QAAQ;QAAK,CAAC,GAC/CL,aAAa0D,QAAQpB,OAAOuC,MAAM,CAAC,CAAC,GAAGxB,YAAYpD;QACrDyD,OAAO1C,cAAc,GAAG,CAACC,MAAMhB,UAAU,CAAC,CAAC,GACzCe,eAAe0C,QAAQzC,MAAMqB,OAAOuC,MAAM,CAAC,CAAC,GAAGxB,YAAYpD;QAC7DyD,OAAOzG,gBAAgB,GAAG,CAACgD,UAAU,CAAC,CAAC,GACrChD,iBAAiByG,QAAQzD;QAC3ByD,OAAO9B,UAAU,GAAG,CAClBC,SACAC,OAGGF,WAAWC,SAASC,QAAQ,CAAC,GAAG9D,KAAKqF;QAE1C,MAAMyB,WAAWjI,eAAeuG;QAChC,IAAI2B,WAAW;QAEf,IAAIxG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,uDAAuD;YACvDR,IAAI+G,IAAI,CAAC,QAAQ,IAAOD,WAAW;QACrC;QAEA,MAAME,iBAAiB,MAAMH,SAAS9G,KAAKC;QAE3C,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAOwG,mBAAmB,aAAa;gBACzC,IAAIA,0BAA0BC,UAAU;oBACtC,MAAM,IAAI/E,MACR;gBAEJ;gBACAzB,QAAQC,IAAI,CACV,CAAC,gDAAgD,EAAE,OAAOsG,eAAe,CAAC,CAAC;YAE/E;YAEA,IAAI,CAACnB,oBAAoB,CAAClH,UAAUqB,QAAQ,CAAC8G,UAAU;gBACrDrG,QAAQC,IAAI,CACV,CAAC,4CAA4C,EAAEX,IAAIY,GAAG,CAAC,sCAAsC,CAAC;YAElG;QACF;IACF,EAAE,OAAOoE,KAAK;QACZ,IAAIA,eAAe7F,UAAU;YAC3BD,UAAUwG,QAAQV,IAAI3E,UAAU,EAAE2E,IAAIC,OAAO;QAC/C,OAAO;YACL,IAAIM,KAAK;gBACP,IAAI5G,QAAQqG,MAAM;oBAChBA,IAAIQ,IAAI,GAAGA;gBACb;gBACA,MAAMR;YACR;YAEAtE,QAAQyG,KAAK,CAACnC;YACd,IAAIM,gBAAgB;gBAClB,MAAMN;YACR;YACA9F,UAAUwG,QAAQ,KAAK;QACzB;IACF;AACF"}