{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/app-page-route-matcher-provider.ts"], "names": ["AppPageRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "APP_PATHS_MANIFEST", "normalizers", "AppNormalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "isAppPageRoute", "allAppPaths", "pathname", "normalize", "push", "matchers", "appPaths", "entries", "filename", "bundlePath", "AppPageRouteMatcher", "kind", "RouteKind", "APP_PAGE"], "mappings": ";;;;+BAYaA;;;eAAAA;;;gCAZkB;2BAEI;qBACJ;2BACL;qCACU;8CAKS;AAEtC,MAAMA,oCAAoCC,0DAA4B;IAG3EC,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACC,6BAAkB,EAAED;QAE1B,IAAI,CAACE,WAAW,GAAG,IAAIC,mBAAc,CAACJ;IACxC;IAEA,MAAgBK,UACdC,QAAkB,EAC2B;QAC7C,uCAAuC;QACvC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASC,IAAAA,8BAAc,EAACD;QAEpE,2EAA2E;QAC3E,UAAU;QACV,MAAME,cAAwC,CAAC;QAC/C,KAAK,MAAMF,QAAQJ,MAAO;YACxB,MAAMO,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACC,SAAS,CAACJ;YACrD,IAAIG,YAAYD,aAAaA,WAAW,CAACC,SAAS,CAACE,IAAI,CAACL;iBACnDE,WAAW,CAACC,SAAS,GAAG;gBAACH;aAAK;QACrC;QAEA,qBAAqB;QACrB,MAAMM,WAAuC,EAAE;QAC/C,KAAK,MAAM,CAACH,UAAUI,SAAS,IAAIV,OAAOW,OAAO,CAACN,aAAc;YAC9D,8EAA8E;YAC9E,MAAMF,OAAOO,QAAQ,CAAC,EAAE;YAExB,MAAME,WAAW,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACL,SAAS,CAACT,QAAQ,CAACK,KAAK;YACnE,MAAMU,aAAa,IAAI,CAAClB,WAAW,CAACkB,UAAU,CAACN,SAAS,CAACJ;YAEzDM,SAASD,IAAI,CACX,IAAIM,wCAAmB,CAAC;gBACtBC,MAAMC,oBAAS,CAACC,QAAQ;gBACxBX;gBACAH;gBACAU;gBACAD;gBACAF;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}