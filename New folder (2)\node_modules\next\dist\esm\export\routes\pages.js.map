{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "names": ["RenderResult", "join", "isInAmpMode", "NEXT_DATA_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "isBailoutToCSRError", "AmpHtmlValidator", "FileType", "fileExists", "lazyRenderPagesPage", "ExportedPagesFiles", "exportPages", "req", "res", "path", "page", "query", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "hybridAmp", "getServerSideProps", "Error", "getStaticProps", "endsWith", "renderResult", "Component", "fromStatic", "optimizeFonts", "process", "env", "__NEXT_OPTIMIZE_FONTS", "JSON", "stringify", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "err", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "getInstance", "result", "validateString", "errors", "filter", "e", "severity", "warnings", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "ampHtmlFilepath", "exists", "File", "ampHtml", "pageData", "dataFile", "replace", "revalidate"], "mappings": "AAMA,OAAOA,kBAAkB,6BAA4B;AACrD,SAASC,IAAI,QAAQ,OAAM;AAK3B,SAASC,WAAW,QAAQ,4BAA2B;AACvD,SACEC,gBAAgB,EAChBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,OAAOC,sBAAsB,uCAAsC;AACnE,SAASC,QAAQ,EAAEC,UAAU,QAAQ,wBAAuB;AAC5D,SAASC,mBAAmB,QAAQ,wDAAuD;;UAEzEC;;;;;GAAAA,uBAAAA;AAOlB,OAAO,eAAeC,YACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAAsB;QAGVD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQnB,MAAMiB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,MAAMI,YAAYnC,YAAY4B;IAC9B,MAAMQ,YAAYR,SAASM,MAAM;IAEjC,IAAIR,WAAWW,kBAAkB,EAAE;QACjC,MAAM,IAAIC,MAAM,CAAC,eAAe,EAAEzB,KAAK,EAAE,EAAEX,0BAA0B,CAAC;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAACoB,eAAeI,WAAWa,cAAc,IAAI,CAAChB,WAAW;QAC3D;IACF;IAEA,IAAIG,WAAWa,cAAc,IAAI,CAACxB,aAAayB,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1DzB,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAIyB;IAEJ,IAAI,OAAOf,WAAWgB,SAAS,KAAK,UAAU;QAC5CD,eAAe3C,aAAa6C,UAAU,CAACjB,WAAWgB,SAAS;QAE3D,IAAIlB,oBAAoB;YACtB,MAAM,IAAIc,MACR,CAAC,uCAAuC,EAAE1B,KAAK,mLAAmL,CAAC;QAEvO;IACF,OAAO;QACL;;;;;KAKC,GACD,IAAIa,WAAWmB,aAAa,EAAE;YAC5BC,QAAQC,GAAG,CAACC,qBAAqB,GAAGC,KAAKC,SAAS,CAChDxB,WAAWmB,aAAa;QAE5B;QACA,IAAInB,WAAWyB,WAAW,EAAE;YAC1BL,QAAQC,GAAG,CAACK,mBAAmB,GAAGH,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFR,eAAe,MAAMlC,oBACnBG,KACAC,KACAE,MACAC,OACAW;QAEJ,EAAE,OAAO2B,KAAK;YACZ,IAAI,CAACjD,oBAAoBiD,MAAM,MAAMA;QACvC;IACF;IAEA,MAAMC,cAAcZ,gCAAAA,aAAca,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAMzD,iBAAiB0D,WAAW,CAACF;QACrD,MAAMG,SAASF,UAAUG,cAAc,CAACN;QACxC,MAAMO,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAC1D,MAAMC,WAAWN,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAE5D,IAAIC,SAASC,MAAM,IAAIL,OAAOK,MAAM,EAAE;YACpCd,eAAee,IAAI,CAAC;gBAClB1D,MAAM8C;gBACNI,QAAQ;oBACNE;oBACAI;gBACF;YACF;QACF;IACF;IAEA,MAAMG,OACJ/B,gBAAgB,CAACA,aAAagC,MAAM,GAAGhC,aAAaiC,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAIxC,aAAa,CAACV,WAAWmD,iBAAiB,EAAE;QAC9C,IAAI,CAACvB,aAAa;YAChB,MAAMI,YAAYe,MAAM5D,MAAMQ;QAChC;IACF,OAAO,IAAIgB,WAAW;QACpB,MAAMyC,kBAAkB3D,aACpBnB,KAAKkB,SAAS,gBACd,CAAC,EAAEA,QAAQ,KAAK,CAAC;QAErB,MAAM6D,kBAAkB/E,KAAKoB,QAAQ0D;QAErC,MAAME,SAAS,MAAMzE,WAAWwE,iBAAiBzE,SAAS2E,IAAI;QAC9D,IAAI,CAACD,QAAQ;YACX,IAAI;gBACFJ,kBAAkB,MAAMpE,oBACtBG,KACAC,KACAE,MACA;oBAAE,GAAGC,KAAK;oBAAEiB,KAAK;gBAAI,GACrBN;YAEJ,EAAE,OAAO2B,KAAK;gBACZ,IAAI,CAACjD,oBAAoBiD,MAAM,MAAMA;YACvC;YAEA,MAAM6B,UACJN,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAACjD,WAAWmD,iBAAiB,EAAE;gBACjC,MAAMnB,YAAYwB,SAASpE,OAAO,UAAUO;YAC9C;YAEA,MAAMO,uBAEJmD,iBACAG,SACA;QAEJ;IACF;IAEA,MAAM3B,WAAWb,CAAAA,gCAAAA,aAAca,QAAQ,MAAIqB,mCAAAA,gBAAiBrB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAAS4B,QAAQ,EAAE;QACrB,MAAMC,WAAWpF,KACfsB,cACAL,aAAaoE,OAAO,CAAC,WAAWnF;QAGlC,MAAM0B,mBAEJwD,UACAnC,KAAKC,SAAS,CAACK,SAAS4B,QAAQ,GAChC;QAGF,IAAI9C,WAAW;YACb,MAAMT,4BAEJwD,SAASC,OAAO,CAAC,WAAW,cAC5BpC,KAAKC,SAAS,CAACK,SAAS4B,QAAQ,GAChC;QAEJ;IACF;IAEA,IAAI,CAAC7B,aAAa;QAChB,qEAAqE;QACrE,MAAM1B,mBAAoCZ,cAAcyD,MAAM;IAChE;IAEA,OAAO;QACLhB;QACA6B,YAAY/B,SAAS+B,UAAU,IAAI;QACnChC;IACF;AACF"}