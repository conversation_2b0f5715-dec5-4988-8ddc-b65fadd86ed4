import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('id');
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID required' }, { status: 400 });
    }

    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    // Get single product
    const response = await api.get(`products/${productId}`);
    const product = response.data;

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Debug image extraction
    const debug = {
      productId: product.id,
      productName: product.name,
      galleryImages: product.images || [],
      descriptionLength: product.description ? product.description.length : 0,
      extractedImages: []
    };

    if (product.description) {
      const description = product.description;
      const imageRegex = /<img[^>]+src="([^">]+)"[^>]*>/gi;
      const allMatches = [...description.matchAll(imageRegex)];
      
      debug.totalImagesFound = allMatches.length;
      debug.allImages = [];
      debug.filteredImages = [];
      
      for (let i = 0; i < allMatches.length; i++) {
        const match = allMatches[i];
        let src = match[1];
        const fullImgTag = match[0];
        
        if (src.startsWith('//')) {
          src = 'https:' + src;
        }
        
        const lowerSrc = src.toLowerCase();
        const lowerImgTag = fullImgTag.toLowerCase();
        
        const sizeChartIndicators = [
          'size', 'chart', 'table', 'measurement', 'guide', 'dimension',
          'cm', 'inch', 'waist', 'hip', 'bust', 'length', 'width', 'height',
          'fitting', 'fit', 'measure', 'ruler', 'scale', 'reference',
          'xl', 'xxl', 'xxxl', 'small', 'medium', 'large', 'xs',
          'info', 'information', 'detail', 'notice', 'attention'
        ];
        
        const isSizeChart = sizeChartIndicators.some(indicator =>
          lowerSrc.includes(indicator) || lowerImgTag.includes(indicator)
        );
        
        const imageInfo = {
          index: i + 1,
          src: src,
          isSizeChart: isSizeChart,
          matchedIndicators: sizeChartIndicators.filter(indicator =>
            lowerSrc.includes(indicator) || lowerImgTag.includes(indicator)
          ),
          fullImgTag: fullImgTag.substring(0, 200) + '...'
        };
        
        debug.allImages.push(imageInfo);
        
        if (!isSizeChart) {
          debug.filteredImages.push(imageInfo);
        }
      }
    }

    return NextResponse.json(debug);
    
  } catch (error) {
    console.error('❌ Debug images API error:', error);
    return NextResponse.json({
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
