import axios from 'axios';

/**
 * Social authentication handler for Next.js app integration with Nextend Social Login WordPress plugin
 */
export const socialAuth = {
  /**
   * Initiate social login by redirecting to the appropriate provider
   * @param {string} provider - 'google' or 'facebook'
   * @param {string} redirectUrl - URL to redirect back to after authentication
   */
  initiateLogin: (provider, redirectUrl = null) => {
    // Get the app URL from environment or use current location
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
    
    // The URL to redirect back to after social authentication
    const finalRedirectUrl = redirectUrl || `${appUrl}/auth/callback`;
    
    // Encode the redirect URL
    const encodedRedirect = encodeURIComponent(finalRedirectUrl);
    
    // WordPress site URL from environment variables
    const wpUrl = process.env.NEXT_PUBLIC_WORDPRESS_URL;
    
    // Construct the authentication URL based on the provider
    let authUrl;
    
    if (provider === 'google') {
      authUrl = `${wpUrl}/wp-login.php?loginSocial=google&redirect=${encodedRedirect}`;
    } else if (provider === 'facebook') {
      authUrl = `${wpUrl}/wp-login.php?loginSocial=facebook&redirect=${encodedRedirect}`;
    } else {
      console.error('Unsupported social login provider:', provider);
      return;
    }
    
    // Redirect to the authentication URL
    window.location.href = authUrl;
  },
  
  /**
   * Process the social login callback
   * @param {Object} params - URL parameters from the callback
   * @param {Function} onSuccess - Function to call on successful authentication
   * @param {Function} onError - Function to call on authentication error
   */
  handleCallback: async (params, onSuccess, onError) => {
    try {
      // Check if we have the necessary parameters
      if (!params.code || !params.state) {
        throw new Error('Missing required authentication parameters');
      }
      
      // Exchange the code for user data and token
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/nextend-social-login/v1/verify`,
        {
          code: params.code,
          state: params.state
        }
      );
      
      // If the response is successful
      if (response.data && response.data.success) {
        // Get WooCommerce customer data
        const customerResponse = await axios.get(
          `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/wc/v3/customers`,
          {
            params: { email: response.data.user.email },
            auth: {
              username: process.env.WOOCOMMERCE_CONSUMER_KEY,
              password: process.env.WOOCOMMERCE_CONSUMER_SECRET
            }
          }
        );
        
        // Format the user data for our application
        const userData = {
          id: customerResponse.data[0]?.id || response.data.user.id,
          email: response.data.user.email,
          firstName: response.data.user.first_name || response.data.user.display_name.split(' ')[0],
          lastName: response.data.user.last_name || response.data.user.display_name.split(' ').slice(1).join(' '),
          name: response.data.user.display_name,
          avatar: response.data.user.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(response.data.user.display_name)}&background=3b82f6&color=fff`,
          // Include other user data as needed
          socialProvider: response.data.provider
        };
        
        // Call the success handler with the user data and token
        onSuccess(userData, response.data.token);
      } else {
        throw new Error('Authentication failed');
      }
    } catch (error) {
      console.error('Social login error:', error);
      onError(error.message || 'Social login failed');
    }
  }
};

export default socialAuth;
