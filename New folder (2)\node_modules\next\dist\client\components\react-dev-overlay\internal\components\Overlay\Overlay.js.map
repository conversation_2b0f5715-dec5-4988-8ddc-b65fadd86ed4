{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Overlay/Overlay.tsx"], "names": ["Overlay", "className", "children", "fixed", "React", "useEffect", "lock", "unlock", "overlay", "set<PERSON><PERSON>lay", "useState", "onOverlay", "useCallback", "el", "handle2", "allyTrap", "context", "disengage", "div", "data-nextjs-dialog-overlay", "ref", "data-nextjs-dialog-backdrop", "data-nextjs-dialog-backdrop-fixed", "undefined"], "mappings": ";;;;+BAiDSA;;;eAAAA;;;;;;2EAjDY;iEACE;4BACM;AAQ7B,MAAMA,UAAkC,SAASA,QAAQ,KAIxD;IAJwD,IAAA,EACvDC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACN,GAJwD;IAKvDC,OAAMC,SAAS,CAAC;QACdC,IAAAA,gBAAI;QACJ,OAAO;YACLC,IAAAA,kBAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,CAACC,SAASC,WAAW,GAAGL,OAAMM,QAAQ,CAAwB;IACpE,MAAMC,YAAYP,OAAMQ,WAAW,CAAC,CAACC;QACnCJ,WAAWI;IACb,GAAG,EAAE;IAELT,OAAMC,SAAS,CAAC;QACd,IAAIG,WAAW,MAAM;YACnB;QACF;QAEA,MAAMM,UAAUC,IAAAA,yBAAQ,EAAC;YAAEC,SAASR;QAAQ;QAC5C,OAAO;YACLM,QAAQG,SAAS;QACnB;IACF,GAAG;QAACT;KAAQ;IAEZ,qBACE,sBAACU;QAAIC,4BAA0B;QAAClB,WAAWA;QAAWmB,KAAKT;;0BACzD,qBAACO;gBACCG,6BAA2B;gBAC3BC,qCAAmCnB,QAAQ,OAAOoB;;YAEnDrB;;;AAGP"}