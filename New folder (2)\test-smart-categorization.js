// Test script for Smart Categorization System
const { smartCategorizer } = require('./lib/smartCategorizer');
const { wooCommerceApi } = require('./lib/woocommerce');

// Test products with various categories
const testProducts = [
  {
    id: 1,
    name: "iPhone 15 Pro Max 256GB Smartphone",
    description: "Latest Apple iPhone with A17 Pro chip, 48MP camera, and titanium design. Features 5G connectivity, wireless charging, and iOS 17.",
    categories: [{ name: "Uncategorized", slug: "uncategorized" }],
    attributes: [
      { name: "Storage", options: ["256GB"] },
      { name: "Color", options: ["Natural Titanium", "Blue Titanium"] }
    ]
  },
  {
    id: 2,
    name: "Women's Summer Floral Dress",
    description: "Beautiful cotton summer dress with floral print. Perfect for casual wear or special occasions. Available in multiple sizes.",
    categories: [{ name: "Clothing", slug: "clothing" }],
    attributes: [
      { name: "Size", options: ["S", "M", "L", "XL"] },
      { name: "Material", options: ["100% Cotton"] }
    ]
  },
  {
    id: 3,
    name: "Coffee Mug with Handle",
    description: "Ceramic coffee mug perfect for your morning coffee. Dishwasher safe and microwave friendly.",
    categories: [{ name: "Electronics", slug: "electronics" }], // Wrong category!
    attributes: [
      { name: "Material", options: ["Ceramic"] },
      { name: "Capacity", options: ["350ml"] }
    ]
  },
  {
    id: 4,
    name: "Wireless Bluetooth Gaming Headphones",
    description: "High-quality gaming headphones with noise cancellation, RGB lighting, and long battery life. Compatible with PC, PS5, Xbox.",
    categories: [{ name: "Uncategorized", slug: "uncategorized" }],
    attributes: [
      { name: "Connectivity", options: ["Bluetooth 5.0", "USB-C"] },
      { name: "Battery Life", options: ["30 hours"] }
    ]
  },
  {
    id: 5,
    name: "Yoga Mat Non-Slip Exercise Mat",
    description: "Premium yoga mat for fitness, pilates, and meditation. Non-slip surface, eco-friendly material, comes with carrying strap.",
    categories: [{ name: "Home", slug: "home" }], // Wrong category!
    attributes: [
      { name: "Thickness", options: ["6mm"] },
      { name: "Material", options: ["TPE"] }
    ]
  }
];

async function testSmartCategorization() {
  console.log('🧠 Testing Smart Categorization System');
  console.log('=====================================\n');

  try {
    // Test individual product categorization
    console.log('1️⃣ Testing Individual Product Categorization:\n');
    
    for (const product of testProducts) {
      console.log(`📦 Analyzing: ${product.name}`);
      console.log(`Current category: ${product.categories[0].name}`);
      
      const analysis = await smartCategorizer.categorizeProduct(product);
      
      console.log(`✨ Analysis Results:`);
      console.log(`   Suggested Category: ${analysis.suggestedCategory}`);
      console.log(`   Confidence Score: ${analysis.confidence}`);
      console.log(`   Needs Correction: ${analysis.needsCorrection ? '✅ YES' : '❌ NO'}`);
      
      if (analysis.analysis.keywordMatches.length > 0) {
        console.log(`   Keyword Matches: ${analysis.analysis.keywordMatches.join(', ')}`);
      }
      
      if (analysis.analysis.patternMatches.length > 0) {
        console.log(`   Pattern Matches: ${analysis.analysis.patternMatches.join(', ')}`);
      }
      
      console.log('');
    }

    // Test batch categorization
    console.log('2️⃣ Testing Batch Categorization:\n');
    
    const batchResults = await smartCategorizer.batchCategorize(testProducts);
    const stats = smartCategorizer.getCategoryStats(batchResults);
    
    console.log(`📊 Batch Analysis Statistics:`);
    console.log(`   Total Products: ${stats.total}`);
    console.log(`   Need Correction: ${stats.needsCorrection}`);
    console.log(`   Categories Distribution:`);
    
    Object.entries(stats.byCategory).forEach(([category, count]) => {
      console.log(`     ${category}: ${count} products`);
    });
    
    if (stats.corrections.length > 0) {
      console.log(`\n🔧 Recommended Corrections:`);
      stats.corrections.forEach(correction => {
        console.log(`   ${correction.productName}:`);
        console.log(`     ${correction.from} → ${correction.to} (confidence: ${correction.confidence})`);
      });
    }

    console.log('\n3️⃣ Testing Real Products from WooCommerce:\n');
    
    // Test with real products from your store
    try {
      const realProducts = await wooCommerceApi.getProducts({ per_page: 3 });
      
      if (realProducts && realProducts.length > 0) {
        console.log(`Found ${realProducts.length} real products to analyze:\n`);
        
        for (const product of realProducts) {
          console.log(`📦 Real Product: ${product.name}`);
          console.log(`Current category: ${product.category || 'None'}`);
          
          const analysis = await smartCategorizer.categorizeProduct(product);
          
          console.log(`✨ Analysis Results:`);
          console.log(`   Suggested Category: ${analysis.suggestedCategory}`);
          console.log(`   Confidence Score: ${analysis.confidence}`);
          console.log(`   Needs Correction: ${analysis.needsCorrection ? '✅ YES' : '❌ NO'}`);
          console.log('');
        }
      } else {
        console.log('No real products found in WooCommerce store.');
      }
    } catch (error) {
      console.log('Could not fetch real products:', error.message);
    }

    console.log('4️⃣ Testing API Endpoints:\n');
    
    // Test the API endpoints
    console.log('🔗 Smart Categorization API Endpoints:');
    console.log(`   POST /api/smart-categorize - Main categorization API`);
    console.log(`   POST /api/alidrop-webhook - AliDrop integration webhook`);
    console.log('');
    
    console.log('📋 Usage Examples:');
    console.log('');
    console.log('// Categorize a single product');
    console.log('fetch("/api/smart-categorize", {');
    console.log('  method: "POST",');
    console.log('  headers: { "Content-Type": "application/json" },');
    console.log('  body: JSON.stringify({');
    console.log('    action: "categorize-single",');
    console.log('    productId: 123');
    console.log('  })');
    console.log('});');
    console.log('');
    
    console.log('// Categorize all products');
    console.log('fetch("/api/smart-categorize", {');
    console.log('  method: "POST",');
    console.log('  headers: { "Content-Type": "application/json" },');
    console.log('  body: JSON.stringify({');
    console.log('    action: "categorize-all",');
    console.log('    limit: 50');
    console.log('  })');
    console.log('});');
    console.log('');

    console.log('✅ Smart Categorization System Test Complete!');
    console.log('');
    console.log('🎯 Next Steps:');
    console.log('1. Configure AliDrop to send webhooks to /api/alidrop-webhook');
    console.log('2. Use /api/smart-categorize to analyze existing products');
    console.log('3. Set up automatic categorization for new imports');
    console.log('4. Monitor and adjust category mappings as needed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testSmartCategorization();
}

module.exports = { testSmartCategorization };
