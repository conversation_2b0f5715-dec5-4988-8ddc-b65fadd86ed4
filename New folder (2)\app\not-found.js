'use client';

import Link from 'next/link';
import { Home, Search, ArrowLeft, ShoppingBag } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full text-center">
        {/* 404 Animation */}
        <div className="mb-8">
          <div className="relative">
            <h1 className="text-9xl font-bold text-gray-200 select-none">404</h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-blue-100 rounded-full p-4">
                <ShoppingBag className="w-12 h-12 text-blue-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            The page you're looking for doesn't exist or has been moved.
          </p>
          <p className="text-gray-500">
            Don't worry, it happens to the best of us. Let's get you back on track!
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <Home className="w-5 h-5 mr-2" />
              Go Home
            </Link>
            
            <Link
              href="/shop"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <ShoppingBag className="w-5 h-5 mr-2" />
              Browse Products
            </Link>
          </div>

          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back to Previous Page
          </button>
        </div>

        {/* Popular Links */}
        <div className="border-t border-gray-200 pt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Popular Pages
          </h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <Link 
              href="/shop" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              All Products
            </Link>
            <Link 
              href="/shop?category=electronics" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              Electronics
            </Link>
            <Link 
              href="/about" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              About Us
            </Link>
            <Link 
              href="/contact" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              Contact
            </Link>
            <Link 
              href="/faq" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              FAQ
            </Link>
            <Link 
              href="/account" 
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              My Account
            </Link>
          </div>
        </div>

        {/* Search Suggestion */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-blue-700 text-sm mb-3">
            Looking for something specific? Try searching:
          </p>
          <div className="flex">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-2 border border-blue-200 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    window.location.href = `/shop?search=${encodeURIComponent(e.target.value.trim())}`;
                  }
                }}
              />
            </div>
            <button
              onClick={(e) => {
                const input = e.target.previousElementSibling.querySelector('input');
                if (input.value.trim()) {
                  window.location.href = `/shop?search=${encodeURIComponent(input.value.trim())}`;
                }
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-8 text-xs text-gray-500">
          <p>
            If you think this is an error, please{' '}
            <Link href="/contact" className="text-blue-600 hover:text-blue-700">
              contact our support team
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
}