{"version": 3, "sources": ["../../../../src/client/components/router-reducer/prefetch-cache-utils.ts"], "names": ["createHrefFromUrl", "fetchServerResponse", "PrefetchCacheEntryStatus", "PrefetchKind", "prefetchQueue", "createPrefetchCacheKey", "url", "nextUrl", "pathnameFromUrl", "getOrCreatePrefetchCacheEntry", "tree", "buildId", "prefetchCache", "kind", "existingCacheEntry", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interceptionData", "get", "prefetchCache<PERSON>ey", "prefetchData", "status", "getPrefetchEntryCacheStatus", "switchedToFullPrefetch", "FULL", "createLazyPrefetchEntry", "TEMPORARY", "process", "env", "NODE_ENV", "AUTO", "prefixExistingPrefetchCacheEntry", "existingCacheKey", "newCache<PERSON>ey", "set", "delete", "createPrefetchCacheEntryForInitialLoad", "data", "intercept", "prefetchEntry", "treeAtTimeOfPrefetch", "Promise", "resolve", "prefetchTime", "Date", "now", "lastUsedTime", "key", "fresh", "enqueue", "then", "prefetchResponse", "intercepted", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "href", "prefetchCacheEntry", "expired", "DYNAMIC_STALETIME_MS", "Number", "__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME", "STATIC_STALETIME_MS", "__NEXT_CLIENT_ROUTER_STATIC_STALETIME", "reusable", "stale"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,yBAAwB;AAC1D,SACEC,mBAAmB,QAEd,0BAAyB;AAChC,SACEC,wBAAwB,EAExBC,YAAY,QAEP,yBAAwB;AAC/B,SAASC,aAAa,QAAQ,8BAA6B;AAE3D;;;;;;CAMC,GACD,SAASC,uBAAuBC,GAAQ,EAAEC,OAAuB;IAC/D,MAAMC,kBAAkBR,kBACtBM,KACA,uFAAuF;IACvF;IAGF,+FAA+F;IAC/F,IAAIC,SAAS;QACX,OAAO,AAAGA,UAAQ,MAAGC;IACvB;IAEA,OAAOA;AACT;AAEA;;;CAGC,GACD,OAAO,SAASC,8BAA8B,KAa7C;IAb6C,IAAA,EAC5CH,GAAG,EACHC,OAAO,EACPG,IAAI,EACJC,OAAO,EACPC,aAAa,EACbC,IAAI,EAOL,GAb6C;IAc5C,IAAIC,qBAAqDC;IACzD,8EAA8E;IAC9E,kJAAkJ;IAClJ,iIAAiI;IACjI,MAAMC,uBAAuBX,uBAAuBC,KAAKC;IACzD,MAAMU,mBAAmBL,cAAcM,GAAG,CAACF;IAE3C,IAAIC,kBAAkB;QACpBH,qBAAqBG;IACvB,OAAO;QACL,2GAA2G;QAC3G,MAAME,mBAAmBd,uBAAuBC;QAChD,MAAMc,eAAeR,cAAcM,GAAG,CAACC;QACvC,IAAIC,cAAc;YAChBN,qBAAqBM;QACvB;IACF;IAEA,IAAIN,oBAAoB;QACtB,0DAA0D;QAC1DA,mBAAmBO,MAAM,GAAGC,4BAA4BR;QAExD,+DAA+D;QAC/D,qHAAqH;QACrH,MAAMS,yBACJT,mBAAmBD,IAAI,KAAKV,aAAaqB,IAAI,IAC7CX,SAASV,aAAaqB,IAAI;QAE5B,IAAID,wBAAwB;YAC1B,OAAOE,wBAAwB;gBAC7Bf;gBACAJ;gBACAK;gBACAJ;gBACAK;gBACA,8EAA8E;gBAC9E,2FAA2F;gBAC3F,kEAAkE;gBAClEC,MAAMA,eAAAA,OAAQV,aAAauB,SAAS;YACtC;QACF;QAEA,uHAAuH;QACvH,4IAA4I;QAC5I,IAAIb,QAAQC,mBAAmBD,IAAI,KAAKV,aAAauB,SAAS,EAAE;YAC9DZ,mBAAmBD,IAAI,GAAGA;QAC5B;QAEA,qFAAqF;QACrF,OAAOC;IACT;IAEA,kDAAkD;IAClD,OAAOW,wBAAwB;QAC7Bf;QACAJ;QACAK;QACAJ;QACAK;QACAC,MACEA,QACA,8EAA8E;QAC7Ec,CAAAA,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACtB1B,aAAa2B,IAAI,GACjB3B,aAAauB,SAAS,AAAD;IAC7B;AACF;AAEA;;;CAGC,GACD,SAASK,iCAAiC,KAMzC;IANyC,IAAA,EACxCzB,GAAG,EACHC,OAAO,EACPK,aAAa,EAGd,GANyC;IAOxC,MAAMoB,mBAAmB3B,uBAAuBC;IAChD,MAAMQ,qBAAqBF,cAAcM,GAAG,CAACc;IAC7C,IAAI,CAAClB,oBAAoB;QACvB,yCAAyC;QACzC;IACF;IAEA,MAAMmB,cAAc5B,uBAAuBC,KAAKC;IAChDK,cAAcsB,GAAG,CAACD,aAAanB;IAC/BF,cAAcuB,MAAM,CAACH;AACvB;AAEA;;CAEC,GACD,OAAO,SAASI,uCAAuC,KAWtD;IAXsD,IAAA,EACrD7B,OAAO,EACPG,IAAI,EACJE,aAAa,EACbN,GAAG,EACHO,IAAI,EACJwB,IAAI,EAKL,GAXsD;IAYrD,MAAM,OAAOC,UAAU,GAAGD;IAC1B,qGAAqG;IACrG,MAAMlB,mBAAmBmB,YACrBjC,uBAAuBC,KAAKC,WAC5BF,uBAAuBC;IAE3B,MAAMiC,gBAAgB;QACpBC,sBAAsB9B;QACtB2B,MAAMI,QAAQC,OAAO,CAACL;QACtBxB;QACA8B,cAAcC,KAAKC,GAAG;QACtBC,cAAcF,KAAKC,GAAG;QACtBE,KAAK5B;QACLE,QAAQnB,yBAAyB8C,KAAK;IACxC;IAEApC,cAAcsB,GAAG,CAACf,kBAAkBoB;IAEpC,OAAOA;AACT;AAEA;;CAEC,GACD,SAASd,wBAAwB,KAahC;IAbgC,IAAA,EAC/BnB,GAAG,EACHO,IAAI,EACJH,IAAI,EACJH,OAAO,EACPI,OAAO,EACPC,aAAa,EAOd,GAbgC;IAc/B,MAAMO,mBAAmBd,uBAAuBC;IAEhD,uEAAuE;IACvE,6FAA6F;IAC7F,MAAM+B,OAAOjC,cAAc6C,OAAO,CAAC,IACjChD,oBAAoBK,KAAKI,MAAMH,SAASI,SAASE,MAAMqC,IAAI,CACzD,CAACC;YACC,+FAA+F;YAC/F,wDAAwD;YACxD,kEAAkE;YAClE,MAAM,OAAOC,YAAY,GAAGD;YAC5B,IAAIC,aAAa;gBACfrB,iCAAiC;oBAAEzB;oBAAKC;oBAASK;gBAAc;YACjE;YAEA,OAAOuC;QACT;IAIJ,MAAMZ,gBAAgB;QACpBC,sBAAsB9B;QACtB2B;QACAxB;QACA8B,cAAcC,KAAKC,GAAG;QACtBC,cAAc;QACdC,KAAK5B;QACLE,QAAQnB,yBAAyB8C,KAAK;IACxC;IAEApC,cAAcsB,GAAG,CAACf,kBAAkBoB;IAEpC,OAAOA;AACT;AAEA,OAAO,SAASc,mBACdzC,aAAoD;IAEpD,KAAK,MAAM,CAAC0C,MAAMC,mBAAmB,IAAI3C,cAAe;QACtD,IACEU,4BAA4BiC,wBAC5BrD,yBAAyBsD,OAAO,EAChC;YACA5C,cAAcuB,MAAM,CAACmB;QACvB;IACF;AACF;AAEA,8FAA8F;AAC9F,2DAA2D;AAC3D,MAAMG,uBACJC,OAAO/B,QAAQC,GAAG,CAAC+B,sCAAsC,IAAI;AAE/D,MAAMC,sBACJF,OAAO/B,QAAQC,GAAG,CAACiC,qCAAqC,IAAI;AAE9D,SAASvC,4BAA4B,KAIhB;IAJgB,IAAA,EACnCT,IAAI,EACJ8B,YAAY,EACZG,YAAY,EACO,GAJgB;IAKnC,gFAAgF;IAChF,IAAIF,KAAKC,GAAG,KAAK,AAACC,CAAAA,uBAAAA,eAAgBH,YAAW,IAAKc,sBAAsB;QACtE,OAAOX,eACH5C,yBAAyB4D,QAAQ,GACjC5D,yBAAyB8C,KAAK;IACpC;IAEA,sGAAsG;IACtG,4EAA4E;IAC5E,sDAAsD;IACtD,IAAInC,SAAS,QAAQ;QACnB,IAAI+B,KAAKC,GAAG,KAAKF,eAAeiB,qBAAqB;YACnD,OAAO1D,yBAAyB6D,KAAK;QACvC;IACF;IAEA,iGAAiG;IACjG,IAAIlD,SAAS,QAAQ;QACnB,IAAI+B,KAAKC,GAAG,KAAKF,eAAeiB,qBAAqB;YACnD,OAAO1D,yBAAyB4D,QAAQ;QAC1C;IACF;IAEA,OAAO5D,yBAAyBsD,OAAO;AACzC"}