'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { Search, Filter, X, Image as ImageIcon } from 'lucide-react';
import { useState } from 'react';

const ShopHeader = ({ searchParams = {}, totalProducts = 0 }) => {
  const router = useRouter();
  const currentSearchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.search || '');

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    const params = new URLSearchParams(currentSearchParams);
    
    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    } else {
      params.delete('search');
    }
    
    // Reset to page 1 when searching
    params.delete('page');
    
    router.push(`/shop?${params.toString()}`);
  };

  const clearSearch = () => {
    setSearchQuery('');
    const params = new URLSearchParams(currentSearchParams);
    params.delete('search');
    params.delete('page');
    router.push(`/shop?${params.toString()}`);
  };

  const clearAllFilters = () => {
    router.push('/shop');
  };

  const hasActiveFilters = () => {
    const { search, category, sort, minPrice, maxPrice, rating, inStock, onSale, imageSearch } = searchParams;
    return search || (category && category !== 'all') || sort || minPrice || maxPrice || rating || inStock || onSale || imageSearch;
  };

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Page Title and Search */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-3xl font-bold text-gray-900">
              {searchParams.imageSearch ? (
                <span className="flex items-center">
                  <ImageIcon className="w-6 h-6 mr-2 text-blue-600" />
                  Image Search Results
                </span>
              ) : searchParams.search ? (
                `Search Results for "${searchParams.search}"`
              ) : (
                'Shop All Products'
              )}
            </h1>
            <p className="text-gray-600 mt-1">
              {totalProducts} {totalProducts === 1 ? 'product' : 'products'} found
              {searchParams.category && searchParams.category !== 'all' && (
                <span> in {searchParams.category}</span>
              )}
            </p>
          </div>

          {/* Search Bar */}
          <div className="lg:w-96">
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={clearSearch}
                    className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm hover:bg-blue-700 transition-colors"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Active Filters */}
        {hasActiveFilters() && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700">Active Filters:</h3>
              <button
                onClick={clearAllFilters}
                className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
              >
                <X className="w-4 h-4 mr-1" />
                Clear All
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {searchParams.imageSearch && (
                <FilterTag
                  label={(
                    <span className="flex items-center">
                      <ImageIcon className="w-3 h-3 mr-1" />
                      Image Search
                    </span>
                  )}
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('imageSearch');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {searchParams.search && (
                <FilterTag
                  label={`Search: ${searchParams.search}`}
                  onRemove={clearSearch}
                />
              )}
              
              {searchParams.category && searchParams.category !== 'all' && (
                <FilterTag
                  label={`Category: ${searchParams.category}`}
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('category');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {searchParams.sort && (
                <FilterTag
                  label={`Sort: ${getSortLabel(searchParams.sort)}`}
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('sort');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {(searchParams.minPrice || searchParams.maxPrice) && (
                <FilterTag
                  label={`Price: $${searchParams.minPrice || 0} - $${searchParams.maxPrice || '∞'}`}
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('minPrice');
                    params.delete('maxPrice');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {searchParams.rating && (
                <FilterTag
                  label={`Rating: ${searchParams.rating}+ stars`}
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('rating');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {searchParams.inStock === 'true' && (
                <FilterTag
                  label="In Stock Only"
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('inStock');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
              
              {searchParams.onSale === 'true' && (
                <FilterTag
                  label="On Sale"
                  onRemove={() => {
                    const params = new URLSearchParams(currentSearchParams);
                    params.delete('onSale');
                    router.push(`/shop?${params.toString()}`);
                  }}
                />
              )}
            </div>
          </div>
        )}

        {/* Results Summary */}
        {totalProducts === 0 && hasActiveFilters() && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Filter className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  No products found with the current filters. Try adjusting your search criteria.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Helper component for filter tags
const FilterTag = ({ label, onRemove }) => (
  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
    {label}
    <button
      onClick={onRemove}
      className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 hover:bg-blue-200"
    >
      <X className="w-3 h-3" />
    </button>
  </span>
);

// Helper function to get readable sort labels
const getSortLabel = (sortValue) => {
  const sortLabels = {
    'name': 'Name',
    'price-low': 'Price: Low to High',
    'price-high': 'Price: High to Low',
    'rating': 'Rating',
    'popularity': 'Popularity',
    'newest': 'Newest'
  };
  return sortLabels[sortValue] || sortValue;
};

export default ShopHeader;