# cPanel Node.js Deployment Guide for Next.js App

## Common Issues and Solutions

### 1. **Node.js Version Compatibility**
- Most cPanel hosts support Node.js 14.x, 16.x, 18.x, or 20.x
- Check your hosting provider's supported versions
- Update your `package.json` engines field accordingly

### 2. **Port Configuration Issues**
**Problem**: Using hardcoded ports or localhost
**Solution**: Use environment variables and proper host binding

### 3. **File Path Issues**
**Problem**: Incorrect static file serving paths
**Solution**: Use proper path resolution for cPanel directory structure

### 4. **Missing Dependencies**
**Problem**: Dev dependencies not installed in production
**Solution**: Ensure all required dependencies are in "dependencies", not "devDependencies"

## Step-by-Step Deployment Process

### Step 1: Prepare Your Application

1. **Build your Next.js app for static export:**
   ```bash
   npm run build
   ```

2. **Verify the `out` folder is created with all static files**

### Step 2: Upload Files to cPanel

1. **Create a Node.js app in cPanel:**
   - Go to cPanel → Node.js Apps
   - Click "Create Application"
   - Choose Node.js version (18.x recommended)
   - Set Application Root: `/public_html/your-app-name`
   - Set Application URL: `your-domain.com/your-app-name`
   - Set Startup File: `server.js`

2. **Upload your files:**
   - Upload all files from your `out` folder to the Application Root
   - Upload `server.js`, `package.json` to the Application Root
   - Upload any additional assets (images, etc.)

### Step 3: Install Dependencies

1. **In cPanel Node.js Apps:**
   - Click on your app
   - Go to "Package.json" tab
   - Click "Run NPM Install"

2. **Or via Terminal (if available):**
   ```bash
   cd /home/<USER>/public_html/your-app-name
   npm install --production
   ```

### Step 4: Configure Environment Variables

In cPanel Node.js Apps → Environment Variables:
- `NODE_ENV`: `production`
- `PORT`: (leave empty - cPanel will set this)

### Step 5: Start the Application

1. Click "Start App" in cPanel Node.js Apps
2. Monitor the logs for any errors

## Troubleshooting Common Errors

### Error: "Cannot find module"
**Solution**: Ensure the module is in "dependencies", not "devDependencies"

### Error: "Port already in use"
**Solution**: Don't hardcode ports, use `process.env.PORT`

### Error: "ENOENT: no such file or directory"
**Solution**: Check file paths and ensure all files are uploaded

### Error: "Application failed to start"
**Solution**: Check the startup file path and Node.js version compatibility

## File Structure for cPanel

```
/public_html/your-app-name/
├── server.js (startup file)
├── package.json
├── index.html (from Next.js build)
├── _next/ (Next.js assets)
├── static/ (if any)
└── other static files...
```

## Testing Your Deployment

1. **Check if the app is running:**
   - Visit your domain/app-url
   - Check cPanel Node.js Apps status

2. **Debug issues:**
   - Check application logs in cPanel
   - Use browser developer tools
   - Test API endpoints if any

## Performance Optimization

1. **Enable compression** (already included in server.js)
2. **Use CDN** for static assets if possible
3. **Optimize images** before deployment
4. **Minify CSS/JS** (Next.js does this automatically)

## Security Considerations

1. **Environment variables** for sensitive data
2. **HTTPS** configuration
3. **Security headers** (Helmet.js included)
4. **Input validation** for any forms

## Backup Strategy

1. **Keep local copy** of your project
2. **Version control** with Git
3. **Database backups** if using databases
4. **Regular file backups** from cPanel
