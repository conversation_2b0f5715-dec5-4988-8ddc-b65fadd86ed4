{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["createInlinedDataReadableStream", "flightRenderComplete", "useFlightStream", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "flightResponses", "WeakMap", "encoder", "TextEncoder", "flightStream", "clientReferenceManifest", "nonce", "response", "get", "createFromReadableStream", "TURBOPACK", "require", "newResponse", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "set", "flightReader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "formState", "startScriptTag", "JSON", "stringify", "decoder", "TextDecoder", "fatal", "decoderOptions", "stream", "readable", "ReadableStream", "type", "start", "controller", "writeInitialInstructions", "error", "pull", "value", "tail", "decode", "length", "writeFlightDataInstruction", "close", "chunkAsString", "scriptStart", "enqueue", "encode", "htmlEscapeJsonString"], "mappings": ";;;;;;;;;;;;;;;;IAyFgBA,+BAA+B;eAA/BA;;IAtBMC,oBAAoB;eAApBA;;IAhDNC,eAAe;eAAfA;;;4BAhBqB;AAGrC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAMb,SAASX,gBACdY,YAA+B,EAC/BC,uBAA8D,EAC9DC,KAAc;IAEd,MAAMC,WAAWP,gBAAgBQ,GAAG,CAACJ;IAErC,IAAIG,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,IAAIE;IACJ,uGAAuG;IACvG,IAAIf,QAAQC,GAAG,CAACe,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAMG,cAAcH,yBAAyBL,cAAc;QACzDS,aAAa;YACXC,eAAeT,wBAAwBS,aAAa;YACpDC,WAAWtB,gBACPY,wBAAwBW,oBAAoB,GAC5CX,wBAAwBY,gBAAgB;QAC9C;QACAX;IACF;IAEAN,gBAAgBkB,GAAG,CAACd,cAAcQ;IAElC,OAAOA;AACT;AAWO,eAAerB,qBACpBa,YAAwC;IAExC,MAAMe,eAAef,aAAagB,SAAS;IAE3C,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAE,GAAG,MAAMF,aAAaG,IAAI;QACxC,IAAID,MAAM;YACR;QACF;IACF;AACF;AAWO,SAAS/B,gCACdc,YAAwC,EACxCE,KAAyB,EACzBiB,SAAyB;IAEzB,MAAMC,iBAAiBlB,QACnB,CAAC,cAAc,EAAEmB,KAAKC,SAAS,CAACpB,OAAO,CAAC,CAAC,GACzC;IAEJ,MAAMqB,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,MAAMC,iBAAiB;QAAEC,QAAQ;IAAK;IAEtC,MAAMZ,eAAef,aAAagB,SAAS;IAE3C,MAAMY,WAAW,IAAIC,eAAe;QAClCC,MAAM;QACNC,OAAMC,UAAU;YACd,IAAI;gBACFC,yBAAyBD,YAAYZ,gBAAgBD;YACvD,EAAE,OAAOe,OAAO;gBACd,6DAA6D;gBAC7DF,WAAWE,KAAK,CAACA;YACnB;QACF;QACA,MAAMC,MAAKH,UAAU;YACnB,IAAI;gBACF,MAAM,EAAEf,IAAI,EAAEmB,KAAK,EAAE,GAAG,MAAMrB,aAAaG,IAAI;gBAC/C,IAAID,MAAM;oBACR,MAAMoB,OAAOd,QAAQe,MAAM,CAACF,OAAO;wBAAET,QAAQ;oBAAM;oBACnD,IAAIU,KAAKE,MAAM,EAAE;wBACfC,2BAA2BR,YAAYZ,gBAAgBiB;oBACzD;oBACAL,WAAWS,KAAK;gBAClB,OAAO;oBACL,MAAMC,gBAAgBnB,QAAQe,MAAM,CAACF,OAAOV;oBAC5Cc,2BAA2BR,YAAYZ,gBAAgBsB;gBACzD;YACF,EAAE,OAAOR,OAAO;gBACd,6EAA6E;gBAC7E,+BAA+B;gBAC/BF,WAAWE,KAAK,CAACA;YACnB;QACF;IACF;IAEA,OAAON;AACT;AAEA,SAASK,yBACPD,UAA2C,EAC3CW,WAAmB,EACnBxB,SAAyB;IAEzBa,WAAWY,OAAO,CAChB9C,QAAQ+C,MAAM,CACZ,CAAC,EAAEF,YAAY,uCAAuC,EAAEG,IAAAA,gCAAoB,EAC1EzB,KAAKC,SAAS,CAAC;QAAC7B;KAAgC,GAChD,qBAAqB,EAAEqD,IAAAA,gCAAoB,EAC3CzB,KAAKC,SAAS,CAAC;QAAC3B;QAAkCwB;KAAU,GAC5D,UAAU,CAAC;AAGnB;AAEA,SAASqB,2BACPR,UAA2C,EAC3CW,WAAmB,EACnBD,aAAqB;IAErBV,WAAWY,OAAO,CAChB9C,QAAQ+C,MAAM,CACZ,CAAC,EAAEF,YAAY,mBAAmB,EAAEG,IAAAA,gCAAoB,EACtDzB,KAAKC,SAAS,CAAC;QAAC5B;QAA4BgD;KAAc,GAC1D,UAAU,CAAC;AAGnB"}