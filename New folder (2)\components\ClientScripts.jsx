'use client';

import { useEffect } from 'react';
import { registerServiceWorker } from '@/lib/pwa';

// Client-side scripts for performance monitoring and smooth scroll
export default function ClientScripts() {
  useEffect(() => {
    // Register Service Worker for PWA
    registerServiceWorker();
    // Smooth scroll for anchor links
    const handleAnchorClick = function(e) {
      if (e.target.tagName === 'A' && e.target.getAttribute('href') && e.target.getAttribute('href').startsWith('#')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      }
    };
    
    document.addEventListener('click', handleAnchorClick);
    
    // Performance monitoring
    if ('performance' in window) {
      const handleLoad = function() {
        setTimeout(function() {
          if (window.performance.timing) {
            const perfData = window.performance.timing;
            const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
            console.log('Page load time:', pageLoadTime + 'ms');
          }
        }, 0);
      };
      
      window.addEventListener('load', handleLoad);
      
      // Cleanup function
      return () => {
        document.removeEventListener('click', handleAnchorClick);
        window.removeEventListener('load', handleLoad);
      };
    }
  }, []);
  
  return null;
}
