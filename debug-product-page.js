const axios = require('axios');
const fs = require('fs');

// Debug the product page HTML content
async function debugProductPage() {
  try {
    console.log('🔍 Debugging Product Page Content...\n');
    
    const response = await axios.get('http://localhost:3000/shop/product/13619', { 
      timeout: 15000 
    });
    
    const content = response.data;
    
    // Save the full HTML to a file for inspection
    fs.writeFileSync('product-page-debug.html', content);
    console.log('📄 Full HTML saved to product-page-debug.html');
    
    // Check for specific elements
    console.log('\n🔍 Element Detection:');
    console.log(`✅ Total HTML length: ${content.length} characters`);
    console.log(`✅ Contains React app: ${content.includes('__NEXT_DATA__') ? 'Yes' : 'No'}`);
    console.log(`✅ Contains product data: ${content.includes('product') ? 'Yes' : 'No'}`);
    
    // Check for our specific design elements
    const elements = {
      'rounded-3xl': content.includes('rounded-3xl'),
      'rounded-2xl': content.includes('rounded-2xl'),
      'Free Shipping': content.includes('Free Shipping'),
      'Secure Payment': content.includes('Secure Payment'),
      'people are viewing': content.includes('people') && content.includes('viewing'),
      'Customer Activity': content.includes('Customer Activity'),
      'ArrowRight': content.includes('ArrowRight'),
      'breadcrumb nav': content.includes('Home') && content.includes('Products')
    };
    
    console.log('\n📋 Specific Elements:');
    Object.entries(elements).forEach(([element, found]) => {
      console.log(`   ${found ? '✅' : '❌'} ${element}: ${found ? 'Found' : 'Missing'}`);
    });
    
    // Check if it's a server-side rendering issue
    if (content.includes('__NEXT_DATA__')) {
      console.log('\n🔄 This appears to be a Next.js SSR page');
      console.log('   The missing elements might be client-side rendered');
    }
    
    // Look for any error indicators
    if (content.includes('error') || content.includes('Error')) {
      console.log('\n⚠️  Potential errors detected in HTML');
    }
    
    // Check for the main product container
    if (content.includes('ProductDetail') || content.includes('product-detail')) {
      console.log('\n✅ Product detail component container found');
    } else {
      console.log('\n❌ Product detail component container not found');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugProductPage();
