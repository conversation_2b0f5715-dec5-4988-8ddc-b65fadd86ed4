import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function POST(request) {
  try {
    const startTime = Date.now();
    console.log('🚀 FAST SYNC: Starting ultra-fast parallel processing...');
    
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    // STEP 1: Get ALL products FAST (parallel page fetching)
    console.log('📡 FAST SYNC: Fetching all products in parallel...');
    const allProducts = await fetchAllProductsFast(api);
    
    const nonTrashProducts = allProducts.filter(product => product.status !== 'trash');
    console.log(`📦 FAST SYNC: Found ${allProducts.length} total, ${nonTrashProducts.length} non-trash products`);
    
    if (nonTrashProducts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No products to process',
        processed: 0,
        timeElapsed: `${Date.now() - startTime}ms`
      });
    }

    // STEP 2: Process products in LARGE parallel batches
    console.log('⚡ FAST SYNC: Processing products in large parallel batches...');
    const results = await processProductsUltraFast(nonTrashProducts, api);
    
    const successCount = results.filter(r => r.success).length;
    const publishedCount = results.filter(r => r.published).length;
    const updatedCount = results.filter(r => r.action?.includes('updated')).length;
    const timeElapsed = Date.now() - startTime;

    console.log(`🎉 FAST SYNC COMPLETE: ${successCount}/${nonTrashProducts.length} products in ${timeElapsed}ms`);
    console.log(`📢 PUBLISHED: ${publishedCount} products are now live on your website!`);

    return NextResponse.json({
      success: true,
      message: `Ultra-fast sync completed in ${timeElapsed}ms - ${publishedCount} products published!`,
      processed: successCount,
      published: publishedCount,
      updated: updatedCount,
      total: nonTrashProducts.length,
      timeElapsed: `${timeElapsed}ms`,
      speed: `${Math.round(successCount / (timeElapsed / 1000))} products/second`,
      results: results.slice(0, 10) // First 10 for preview
    });
    
  } catch (error) {
    console.error('❌ FAST SYNC ERROR:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

// Fetch all products using parallel page requests
async function fetchAllProductsFast(api) {
  console.log('🔥 Fetching products with parallel page loading...');
  
  // First, get total count
  const firstPage = await api.get('products', {
    per_page: 100,
    page: 1,
    status: 'any'
  });
  
  const totalProducts = parseInt(firstPage.headers['x-wp-total'] || '0');
  const totalPages = Math.ceil(totalProducts / 100);
  
  console.log(`📊 Total: ${totalProducts} products across ${totalPages} pages`);
  
  if (totalPages <= 1) {
    return firstPage.data;
  }
  
  // Create parallel requests for all remaining pages
  const pagePromises = [];
  for (let page = 2; page <= Math.min(totalPages, 20); page++) { // Limit to 20 pages for safety
    pagePromises.push(
      api.get('products', {
        per_page: 100,
        page: page,
        status: 'any'
      }).then(response => response.data)
    );
  }
  
  // Execute all page requests in parallel
  const allPageResults = await Promise.all(pagePromises);
  
  // Combine all results
  let allProducts = [...firstPage.data];
  allPageResults.forEach(pageData => {
    allProducts = [...allProducts, ...pageData];
  });
  
  console.log(`⚡ Parallel fetch complete: ${allProducts.length} products loaded`);
  return allProducts;
}

// Process products with maximum parallelization
async function processProductsUltraFast(products, api) {
  const batchSize = 20; // Process 20 products simultaneously
  const results = [];
  
  console.log(`🚀 Processing ${products.length} products in batches of ${batchSize}...`);
  
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    const batchNumber = Math.floor(i / batchSize) + 1;
    const totalBatches = Math.ceil(products.length / batchSize);
    
    console.log(`⚡ Processing batch ${batchNumber}/${totalBatches} (${batch.length} products)...`);
    
    // Process entire batch in parallel
    const batchPromises = batch.map(product => processProductUltraFast(product, api));
    
    try {
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      const batchSuccessCount = batchResults.filter(r => r.success).length;
      console.log(`✅ Batch ${batchNumber} complete: ${batchSuccessCount}/${batch.length} successful`);
      
    } catch (error) {
      console.error(`❌ Batch ${batchNumber} failed:`, error);
      // Add failed results for this batch
      batch.forEach(product => {
        results.push({
          id: product.id,
          name: product.name,
          success: false,
          error: 'Batch processing failed'
        });
      });
    }
    
    // Minimal delay between batches
    if (i + batchSize < products.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  return results;
}

// Ultra-fast single product processing
async function processProductUltraFast(product, api) {
  try {
    // Quick image analysis - no complex processing
    let needsUpdate = false;
    let newImages = [];
    
    // ADVANCED IMAGE FILTERING - Remove size charts and keep ONLY real product images
    if (product.images && product.images.length > 0) {
      console.log(`🔍 Product ${product.id}: Analyzing ${product.images.length} images...`);

      // STRICT filter for size charts and bad images
      const realProductImages = product.images.filter((img, index) => {
        const src = (img.src || '').toLowerCase();
        const alt = (img.alt || '').toLowerCase();
        const name = (img.name || '').toLowerCase();

        // Check all text for size chart indicators
        const allText = `${src} ${alt} ${name}`;

        // Comprehensive size chart detection
        const sizeChartKeywords = [
          'size', 'chart', 'table', 'measurement', 'guide', 'sizing', 'dimension', 'spec',
          'cm', 'inch', 'mm', 'meter', 'length', 'width', 'height', 'weight',
          'beainmmry', 'uniform', 'information', 'details', 'specification',
          'how to', 'measure', 'fit', 'fitting', 'choose', 'select'
        ];

        const isSizeChart = sizeChartKeywords.some(keyword => allText.includes(keyword));

        // Additional checks for image content
        const isTextImage = allText.includes('text') || allText.includes('info');
        const isLogoOnly = allText.includes('logo') || allText.includes('brand');

        // Keep only images that are clearly product photos
        const isGoodImage = !isSizeChart && !isTextImage && !isLogoOnly;

        console.log(`  Image ${index + 1}: ${isGoodImage ? '✅ KEEP' : '❌ REMOVE'} - ${src.substring(0, 50)}...`);

        return isGoodImage;
      });

      // If we found real product images, REORDER them so real product photos come FIRST
      if (realProductImages.length > 0) {
        needsUpdate = true;

        // Put the BEST real product images FIRST (main preview)
        // Sort by image quality indicators
        const sortedImages = realProductImages.sort((a, b) => {
          const aScore = getImageQualityScore(a, product);
          const bScore = getImageQualityScore(b, product);
          return bScore - aScore; // Higher score first
        });

        newImages = sortedImages.slice(0, 5); // Keep max 5 best images

        console.log(`🖼️ Product ${product.id}: Reordered ${newImages.length} real product images (BEST image now FIRST for preview)`);
      } else {
        // If no good images found, extract from description
        console.log(`⚠️ Product ${product.id}: No clear product images found, trying description extraction...`);

        // Try to extract images from description
        if (product.description) {
          const extractedImages = extractImagesFromDescription(product);
          if (extractedImages.length > 0) {
            needsUpdate = true;
            newImages = extractedImages.slice(0, 3);
            console.log(`🖼️ Extracted ${newImages.length} images from description`);
          }
        }
      }
    }
    
    // Quick description image extraction
    if (product.description) {
      const imageRegex = /<img[^>]+src="([^">]+)"/gi;
      const matches = [...product.description.matchAll(imageRegex)];
      
      if (matches.length > 0) {
        // Take first 3 images quickly
        newImages = matches.slice(0, 3).map(match => {
          let src = match[1];
          if (src.startsWith('//')) src = 'https:' + src;
          return {
            src: src,
            name: `${product.name} - Image`,
            alt: product.name
          };
        });
        needsUpdate = true;
      }
    }
    
    // CLEAN PRODUCT DATA - Remove wrong descriptions and videos
    let cleanedData = {
      images: newImages,
      status: 'publish'
    };

    // Clean product name
    if (product.name && product.name.includes('BEAINMMRY')) {
      cleanedData.name = product.name.replace(/BEAINMMRY/gi, '').replace(/uniform/gi, '').trim();
      console.log(`🏷️ Cleaned product name: ${product.name} → ${cleanedData.name}`);
    }

    // Clean description - remove size chart content and videos
    if (product.description) {
      let cleanDesc = product.description;

      // Remove video embeds and iframes
      cleanDesc = cleanDesc.replace(/<iframe[^>]*>.*?<\/iframe>/gi, '');
      cleanDesc = cleanDesc.replace(/<video[^>]*>.*?<\/video>/gi, '');
      cleanDesc = cleanDesc.replace(/\[video[^\]]*\]/gi, '');

      // Remove size chart references
      cleanDesc = cleanDesc.replace(/size\s*chart/gi, '');
      cleanDesc = cleanDesc.replace(/measurement\s*guide/gi, '');
      cleanDesc = cleanDesc.replace(/BEAINMMRY/gi, 'Deal4U');
      cleanDesc = cleanDesc.replace(/uniform/gi, 'clothing');

      // Clean up extra whitespace
      cleanDesc = cleanDesc.replace(/\s+/g, ' ').trim();

      if (cleanDesc !== product.description && cleanDesc.length > 10) {
        cleanedData.description = cleanDesc;
        console.log(`📝 Cleaned product description`);
      }
    }

    // Update product if needed AND publish it
    if (needsUpdate && newImages.length > 0) {
      await api.put(`products/${product.id}`, cleanedData);

      return {
        id: product.id,
        name: cleanedData.name || product.name,
        success: true,
        action: 'cleaned_and_published',
        imagesAdded: newImages.length,
        dataCleanedUp: Object.keys(cleanedData).length > 2,
        published: true
      };
    }

    // If product has good images but is not published, publish it
    if (product.status !== 'publish' && product.status !== 'trash') {
      await api.put(`products/${product.id}`, {
        status: 'publish'
      });

      return {
        id: product.id,
        name: product.name,
        success: true,
        action: 'published_only',
        published: true
      };
    }
    
    return {
      id: product.id,
      name: product.name,
      success: true,
      action: 'no_changes',
      reason: 'No images found'
    };
    
  } catch (error) {
    return {
      id: product.id,
      name: product.name,
      success: false,
      error: error.message
    };
  }
}

// Helper function to score image quality for sorting
function getImageQualityScore(image, product) {
  let score = 0;
  const src = (image.src || '').toLowerCase();
  const alt = (image.alt || '').toLowerCase();
  const name = (image.name || '').toLowerCase();

  // Higher score for images that look like main product photos
  if (src.includes('main') || src.includes('primary') || src.includes('hero')) score += 10;
  if (alt.includes(product.name.toLowerCase().substring(0, 10))) score += 8;
  if (src.includes('product') && !src.includes('size')) score += 6;
  if (src.includes('.jpg') || src.includes('.jpeg')) score += 2; // Prefer JPEG over other formats

  // Lower score for obvious secondary images
  if (src.includes('detail') || src.includes('zoom')) score -= 2;
  if (src.includes('back') || src.includes('side')) score -= 3;
  if (name.includes('2') || name.includes('3') || name.includes('secondary')) score -= 4;

  return score;
}

// Helper function to extract images from product description
function extractImagesFromDescription(product) {
  try {
    const description = product.description || '';
    const imageRegex = /<img[^>]+src="([^">]+)"/gi;
    const matches = [...description.matchAll(imageRegex)];

    return matches.slice(0, 3).map((match, index) => {
      let src = match[1];
      if (src.startsWith('//')) src = 'https:' + src;
      return {
        src: src,
        name: `${product.name} - Product Image ${index + 1}`,
        alt: `${product.name} - High Quality Image`
      };
    });
  } catch (error) {
    console.error('Error extracting images from description:', error);
    return [];
  }
}
