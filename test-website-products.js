const axios = require('axios');

// Test the website's product endpoints
async function testWebsiteProducts() {
  const baseURL = 'http://localhost:3000';
  
  console.log('🧪 Testing Deal4u Website Product Integration...\n');

  try {
    // Test 1: Check if homepage loads
    console.log('1. Testing Homepage...');
    const homeResponse = await axios.get(baseURL, { timeout: 10000 });
    console.log('✅ Homepage loaded successfully');
    console.log(`   Status: ${homeResponse.status}`);
    
    // Check if homepage contains real product data (not demo)
    const homeContent = homeResponse.data;
    const hasDemoProducts = homeContent.includes('Premium Wireless Headphones') || 
                           homeContent.includes('Smart Fitness Watch') ||
                           homeContent.includes('Ergonomic Office Chair');
    
    if (hasDemoProducts) {
      console.log('⚠️  Homepage still contains demo product names');
    } else {
      console.log('✅ Homepage appears to be free of demo products');
    }

    // Test 2: Check shop page
    console.log('\n2. Testing Shop Page...');
    const shopResponse = await axios.get(`${baseURL}/shop`, { timeout: 15000 });
    console.log('✅ Shop page loaded successfully');
    console.log(`   Status: ${shopResponse.status}`);
    
    // Check if shop page contains real product data
    const shopContent = shopResponse.data;
    const shopHasDemoProducts = shopContent.includes('Premium Wireless Headphones') || 
                               shopContent.includes('Smart Fitness Watch') ||
                               shopContent.includes('Ergonomic Office Chair');
    
    if (shopHasDemoProducts) {
      console.log('⚠️  Shop page still contains demo product names');
    } else {
      console.log('✅ Shop page appears to be free of demo products');
    }

    // Test 3: Check if real WooCommerce products are displayed
    if (shopContent.includes('Yoga Suit') || shopContent.includes('Sports &amp; Outdoors')) {
      console.log('✅ Shop page contains real WooCommerce products!');
    } else {
      console.log('⚠️  Could not detect real WooCommerce products on shop page');
    }

    // Test 4: Test API endpoint directly
    console.log('\n3. Testing WooCommerce API Endpoint...');
    try {
      const apiResponse = await axios.get(`${baseURL}/api/woocommerce?endpoint=products&per_page=5`, { 
        timeout: 10000 
      });
      console.log('✅ WooCommerce API endpoint working');
      console.log(`   Status: ${apiResponse.status}`);
      console.log(`   Products returned: ${apiResponse.data.length || 'Unknown'}`);
      
      if (apiResponse.data && apiResponse.data.length > 0) {
        console.log(`   First product: ${apiResponse.data[0].name || 'Unknown'}`);
      }
    } catch (apiError) {
      console.log('❌ WooCommerce API endpoint failed');
      console.log(`   Error: ${apiError.message}`);
    }

    console.log('\n🎉 Website Testing Complete!');
    console.log('\n📋 Summary:');
    console.log('- Homepage: ✅ Loading');
    console.log('- Shop Page: ✅ Loading');
    console.log('- Demo Data: ' + (hasDemoProducts || shopHasDemoProducts ? '⚠️  Still present' : '✅ Removed'));
    console.log('- Real Products: ' + (shopContent.includes('Yoga Suit') ? '✅ Detected' : '⚠️  Not detected'));

  } catch (error) {
    console.log('❌ Website testing failed!');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Connection refused - Make sure the development server is running on http://localhost:3000');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏰ Request timed out - The server might be slow to respond');
    } else {
      console.log(`Error: ${error.message}`);
    }
  }
}

// Run the test
testWebsiteProducts();
