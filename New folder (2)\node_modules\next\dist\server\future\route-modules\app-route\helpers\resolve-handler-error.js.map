{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/resolve-handler-error.ts"], "names": ["resolveHandlerError", "err", "isRedirectError", "redirect", "getURLFromRedirectError", "Error", "status", "getRedirectStatusCodeFromError", "handleRedirectResponse", "mutableCookies", "isNotFoundError", "handleNotFoundResponse"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;0BAXgB;0BAKzB;kCAIA;AAEA,SAASA,oBAAoBC,GAAQ;IAC1C,IAAIC,IAAAA,yBAAe,EAACD,MAAM;QACxB,MAAME,WAAWC,IAAAA,iCAAuB,EAACH;QACzC,IAAI,CAACE,UAAU;YACb,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMC,SAASC,IAAAA,wCAA8B,EAACN;QAE9C,wDAAwD;QACxD,OAAOO,IAAAA,wCAAsB,EAACL,UAAUF,IAAIQ,cAAc,EAAEH;IAC9D;IAEA,IAAII,IAAAA,yBAAe,EAACT,MAAM;QACxB,0DAA0D;QAC1D,OAAOU,IAAAA,wCAAsB;IAC/B;IAEA,6DAA6D;IAC7D,OAAO;AACT"}