/**
 * lucide-react v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const Cake = createLucideIcon("Cake", [
  ["path", { d: "M20 21v-8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8", key: "1w3rig" }],
  [
    "path",
    {
      d: "M4 16s.5-1 2-1 2.5 2 4 2 2.5-2 4-2 2.5 2 4 2 2-1 2-1",
      key: "n2jgmb"
    }
  ],
  ["path", { d: "M2 21h20", key: "1nyx9w" }],
  ["path", { d: "M7 8v3", key: "1qtyvj" }],
  ["path", { d: "M12 8v3", key: "hwp4zt" }],
  ["path", { d: "M17 8v3", key: "1i6e5u" }],
  ["path", { d: "M7 4h0.01", key: "hsw7lv" }],
  ["path", { d: "M12 4h0.01", key: "1e3d8f" }],
  ["path", { d: "M17 4h0.01", key: "p7cxgy" }]
]);

export { Cake as default };
//# sourceMappingURL=cake.js.map
