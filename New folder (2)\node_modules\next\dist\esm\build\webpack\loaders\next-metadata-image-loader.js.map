{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-image-loader.ts"], "names": ["existsSync", "promises", "fs", "path", "loaderUtils", "getImageSize", "imageExtMimeTypeMap", "WEBPACK_RESOURCE_QUERIES", "normalizePathSep", "getLoaderModuleNamedExports", "nextMetadataImageLoader", "content", "options", "getOptions", "type", "segment", "pageExtensions", "basePath", "resourcePath", "rootContext", "context", "name", "fileNameBase", "ext", "parse", "useNumericSizes", "extension", "slice", "opts", "contentHash", "interpolateName", "interpolatedName", "isDynamicResource", "includes", "pageSegment", "hash<PERSON><PERSON><PERSON>", "pathnamePrefix", "join", "exportedFieldsExcludingDefault", "filter", "map", "field", "JSON", "stringify", "metadataImageMeta", "imageSize", "catch", "err", "Error", "imageData", "width", "height", "sizes", "altPath", "dirname", "alt", "readFile", "raw"], "mappings": "AAAA;;CAEC,GAOD,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,UAAU,OAAM;AACvB,OAAOC,iBAAiB,mCAAkC;AAC1D,SAASC,YAAY,QAAQ,kCAAiC;AAC9D,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,gBAAgB,QAAQ,mDAAkD;AAEnF,SAASC,2BAA2B,QAAQ,UAAS;AASrD,uBAAuB;AACvB,+EAA+E;AAC/E,eAAeC,wBAEbC,OAAe;IAEf,MAAMC,UAAmB,IAAI,CAACC,UAAU;IACxC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAE,GAAGL;IACpD,MAAM,EAAEM,YAAY,EAAEC,aAAaC,OAAO,EAAE,GAAG,IAAI;IACnD,MAAM,EAAEC,MAAMC,YAAY,EAAEC,GAAG,EAAE,GAAGpB,KAAKqB,KAAK,CAACN;IAC/C,MAAMO,kBAAkBX,SAAS,aAAaA,SAAS;IAEvD,IAAIY,YAAYH,IAAII,KAAK,CAAC;IAC1B,IAAID,cAAc,OAAO;QACvBA,YAAY;IACd;IAEA,MAAME,OAAO;QAAER;QAAST;IAAQ;IAEhC,gCAAgC;IAChC,MAAMkB,cACJf,SAAS,YACL,KACAV,YAAY0B,eAAe,CAAC,IAAI,EAAE,iBAAiBF;IAEzD,MAAMG,mBAAmB3B,YAAY0B,eAAe,CAClD,IAAI,EACJ,gBACAF;IAGF,MAAMI,oBAAoBhB,eAAeiB,QAAQ,CAACP;IAClD,MAAMQ,cAAcF,oBAAoBV,eAAeS;IACvD,MAAMI,YAAYN,cAAc,MAAMA,cAAc;IACpD,MAAMO,iBAAiB5B,iBAAiBL,KAAKkC,IAAI,CAACpB,UAAUF;IAE5D,IAAIiB,mBAAmB;QACrB,MAAMM,iCAAiC,AACrC,CAAA,MAAM7B,4BAA4BS,cAAc,IAAI,CAAA,EACpDqB,MAAM,CAAC,CAAClB,OAASA,SAAS;QAE5B,0EAA0E;QAC1E,OAAO,CAAC;;MAEN,EAAEiB,+BACCE,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,KAAK,EAAEA,MAAM,CAAC,EACtCJ,IAAI,CAAC,KAAK;WACR,EAAEK,KAAKC,SAAS,CACrB,4EAA4E;QAC5E,8DAA8D;QAC9D,kEAAkE;QAClE,6EAA6E;QAC7E,WAAW;QACXzB,eAAe,MAAMX,yBAAyBqC,iBAAiB,EAC/D;;;;MAIA,EAAEN,+BACCE,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,GAAG,EAAEA,MAAM,CAAC,EACpCJ,IAAI,CAAC,KAAK;;;;;2CAKwB,EAAEK,KAAKC,SAAS,CACnDP,gBACA,UAAU,EAAEM,KAAKC,SAAS,CAACT,aAAa;;;;;;;;6DAQa,EAAEQ,KAAKC,SAAS,CACjER,WACA;;;;UAIF,EACErB,SAAS,aAAaA,SAAS,cAC3B,wDACA,+CACL;;;;;;;;;;;;;;KAcN,CAAC;IACJ;IAEA,MAAM+B,YAAiD,MAAMxC,aAC3DM,SACAe,WACAoB,KAAK,CAAC,CAACC,MAAQA;IAEjB,IAAIF,qBAAqBG,OAAO;QAC9B,MAAMD,MAAMF;QACZE,IAAI1B,IAAI,GAAG;QACX,MAAM0B;IACR;IAEA,MAAME,YAA8C;QAClD,GAAIvB,aAAapB,uBAAuB;YACtCQ,MAAMR,mBAAmB,CAACoB,UAA8C;QAC1E,CAAC;QACD,GAAID,mBAAmBoB,UAAUK,KAAK,IAAI,QAAQL,UAAUM,MAAM,IAAI,OAClEN,YACA;YACEO,OACE,sFAAsF;YACtF,uEAAuE;YACvE,+DAA+D;YAC/D1B,cAAc,SACdmB,UAAUK,KAAK,IAAI,QACnBL,UAAUM,MAAM,IAAI,OAChB,CAAC,EAAEN,UAAUK,KAAK,CAAC,CAAC,EAAEL,UAAUM,MAAM,CAAC,CAAC,GACxC;QACR,CAAC;IACP;IACA,IAAIrC,SAAS,eAAeA,SAAS,WAAW;QAC9C,MAAMuC,UAAUlD,KAAKkC,IAAI,CACvBlC,KAAKmD,OAAO,CAACpC,eACbI,eAAe;QAGjB,IAAItB,WAAWqD,UAAU;YACvBJ,UAAUM,GAAG,GAAG,MAAMrD,GAAGsD,QAAQ,CAACH,SAAS;QAC7C;IACF;IAEA,OAAO,CAAC;;;;sBAIY,EAAEX,KAAKC,SAAS,CAACM,WAAW;yCACT,EAAEP,KAAKC,SAAS,CACnDP,gBACA,gBAAgB,EAAEM,KAAKC,SAAS,CAACT,aAAa;;;;sBAI9B,EAAEQ,KAAKC,SAAS,CAAC7B,SAAS,YAAY,KAAKqB,WAAW;;GAEzE,CAAC;AACJ;AAEA,OAAO,MAAMsB,MAAM,KAAI;AACvB,eAAe/C,wBAAuB"}