{"version": 3, "sources": ["../../../src/build/analysis/extract-const-value.ts"], "names": ["NoSuchDeclarationError", "Error", "isExportDeclaration", "node", "type", "isVariableDeclaration", "isIdentifier", "isBooleanLiteral", "is<PERSON>ull<PERSON>iteral", "isStringLiteral", "isNumericLiteral", "isArrayExpression", "isObjectExpression", "isKeyValueProperty", "isRegExpLiteral", "isTemplateLiteral", "UnsupportedValueError", "constructor", "message", "paths", "codePath", "path", "extractValue", "value", "RegExp", "pattern", "flags", "undefined", "arr", "i", "len", "elements", "length", "elem", "spread", "push", "expression", "obj", "prop", "properties", "key", "expressions", "cooked", "raw", "quasis", "extractExportedConstValue", "module", "exportedName", "moduleItem", "body", "declaration", "kind", "decl", "declarations", "id", "init"], "mappings": "AAiBA,OAAO,MAAMA,+BAA+BC;AAAO;AAEnD,SAASC,oBAAoBC,IAAU;IACrC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASC,sBAAsBF,IAAU;IACvC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASE,aAAaH,IAAU;IAC9B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASG,iBAAiBJ,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASI,cAAcL,IAAU;IAC/B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASK,gBAAgBN,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASM,iBAAiBP,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASO,kBAAkBR,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASQ,mBAAmBT,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASS,mBAAmBV,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASU,gBAAgBX,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASW,kBAAkBZ,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,OAAO,MAAMY,8BAA8Bf;IAIzCgB,YAAYC,OAAe,EAAEC,KAAgB,CAAE;QAC7C,KAAK,CAACD;QAEN,8DAA8D;QAC9D,IAAIE;QACJ,IAAID,OAAO;YACTC,WAAW;YACX,KAAK,MAAMC,QAAQF,MAAO;gBACxB,IAAIE,IAAI,CAAC,EAAE,KAAK,KAAK;oBACnB,kBAAkB;oBAClBD,YAAYC;gBACd,OAAO;oBACL,IAAID,aAAa,IAAI;wBACnBA,WAAWC;oBACb,OAAO;wBACL,oBAAoB;wBACpBD,YAAY,CAAC,CAAC,EAAEC,KAAK,CAAC;oBACxB;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAI,GAAGD;IACd;AACF;AAEA,SAASE,aAAanB,IAAU,EAAEkB,IAAe;IAC/C,IAAIb,cAAcL,OAAO;QACvB,OAAO;IACT,OAAO,IAAII,iBAAiBJ,OAAO;QACjC,oBAAoB;QACpB,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAId,gBAAgBN,OAAO;QAChC,aAAa;QACb,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAIb,iBAAiBP,OAAO;QACjC,WAAW;QACX,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAIT,gBAAgBX,OAAO;QAChC,cAAc;QACd,OAAO,IAAIqB,OAAOrB,KAAKsB,OAAO,EAAEtB,KAAKuB,KAAK;IAC5C,OAAO,IAAIpB,aAAaH,OAAO;QAC7B,OAAQA,KAAKoB,KAAK;YAChB,KAAK;gBACH,OAAOI;YACT;gBACE,MAAM,IAAIX,sBACR,CAAC,oBAAoB,EAAEb,KAAKoB,KAAK,CAAC,CAAC,CAAC,EACpCF;QAEN;IACF,OAAO,IAAIV,kBAAkBR,OAAO;QAClC,iBAAiB;QACjB,MAAMyB,MAAM,EAAE;QACd,IAAK,IAAIC,IAAI,GAAGC,MAAM3B,KAAK4B,QAAQ,CAACC,MAAM,EAAEH,IAAIC,KAAKD,IAAK;YACxD,MAAMI,OAAO9B,KAAK4B,QAAQ,CAACF,EAAE;YAC7B,IAAII,MAAM;gBACR,IAAIA,KAAKC,MAAM,EAAE;oBACf,gBAAgB;oBAChB,MAAM,IAAIlB,sBACR,uDACAK;gBAEJ;gBAEAO,IAAIO,IAAI,CAACb,aAAaW,KAAKG,UAAU,EAAEf,QAAQ;uBAAIA;oBAAM,CAAC,CAAC,EAAEQ,EAAE,CAAC,CAAC;iBAAC;YACpE,OAAO;gBACL,gBAAgB;gBAChB,aAAa;gBACbD,IAAIO,IAAI,CAACR;YACX;QACF;QACA,OAAOC;IACT,OAAO,IAAIhB,mBAAmBT,OAAO;QACnC,sBAAsB;QACtB,MAAMkC,MAAW,CAAC;QAClB,KAAK,MAAMC,QAAQnC,KAAKoC,UAAU,CAAE;YAClC,IAAI,CAAC1B,mBAAmByB,OAAO;gBAC7B,gBAAgB;gBAChB,MAAM,IAAItB,sBACR,wDACAK;YAEJ;YAEA,IAAImB;YACJ,IAAIlC,aAAagC,KAAKE,GAAG,GAAG;gBAC1B,sBAAsB;gBACtBA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO,IAAId,gBAAgB6B,KAAKE,GAAG,GAAG;gBACpC,0BAA0B;gBAC1BA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO;gBACL,MAAM,IAAIP,sBACR,CAAC,sBAAsB,EAAEsB,KAAKE,GAAG,CAACpC,IAAI,CAAC,0BAA0B,CAAC,EAClEiB;YAEJ;YAEAgB,GAAG,CAACG,IAAI,GAAGlB,aAAagB,KAAKf,KAAK,EAAEF,QAAQ;mBAAIA;gBAAMmB;aAAI;QAC5D;QAEA,OAAOH;IACT,OAAO,IAAItB,kBAAkBZ,OAAO;QAClC,aAAa;QACb,IAAIA,KAAKsC,WAAW,CAACT,MAAM,KAAK,GAAG;YACjC,sDAAsD;YACtD,MAAM,IAAIhB,sBACR,iDACAK;QAEJ;QAEA,4EAA4E;QAC5E,2EAA2E;QAC3E,kFAAkF;QAClF,wBAAwB;QACxB,mFAAmF;QACnF,EAAE;QACF,4EAA4E;QAC5E,qEAAqE;QACrE,gGAAgG;QAChG,MAAM,CAAC,EAAEqB,MAAM,EAAEC,GAAG,EAAE,CAAC,GAAGxC,KAAKyC,MAAM;QAErC,OAAOF,UAAUC;IACnB,OAAO;QACL,MAAM,IAAI3B,sBACR,CAAC,uBAAuB,EAAEb,KAAKC,IAAI,CAAC,CAAC,CAAC,EACtCiB;IAEJ;AACF;AAEA;;;;;;;;;;;;;CAaC,GACD,OAAO,SAASwB,0BACdC,MAAc,EACdC,YAAoB;IAEpB,KAAK,MAAMC,cAAcF,OAAOG,IAAI,CAAE;QACpC,IAAI,CAAC/C,oBAAoB8C,aAAa;YACpC;QACF;QAEA,MAAME,cAAcF,WAAWE,WAAW;QAC1C,IAAI,CAAC7C,sBAAsB6C,cAAc;YACvC;QACF;QAEA,IAAIA,YAAYC,IAAI,KAAK,SAAS;YAChC;QACF;QAEA,KAAK,MAAMC,QAAQF,YAAYG,YAAY,CAAE;YAC3C,IACE/C,aAAa8C,KAAKE,EAAE,KACpBF,KAAKE,EAAE,CAAC/B,KAAK,KAAKwB,gBAClBK,KAAKG,IAAI,EACT;gBACA,OAAOjC,aAAa8B,KAAKG,IAAI,EAAE;oBAACR;iBAAa;YAC/C;QACF;IACF;IAEA,MAAM,IAAI/C;AACZ"}