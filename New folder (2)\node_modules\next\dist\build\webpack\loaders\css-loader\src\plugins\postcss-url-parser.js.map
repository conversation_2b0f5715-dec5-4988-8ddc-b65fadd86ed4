{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-url-parser.ts"], "names": ["isUrlFunc", "isImageSetFunc", "needParseDeclaration", "getNodeFromUrlFunc", "node", "nodes", "getWebpackIgnoreCommentValue", "index", "inBetween", "prevValueNode", "type", "matched", "value", "match", "WEBPACK_IGNORE_COMMENT_REGEXP", "shouldHandleURL", "url", "declaration", "result", "isSupportDataURLInNewURL", "length", "warn", "toString", "isDataUrl", "decodeURIComponent", "ignoreError", "isUrlRequestable", "parseDeclaration", "key", "test", "parsed", "valueParser", "raws", "raw", "between", "lastCommentIndex", "lastIndexOf", "slice", "isIgnoreOnDeclaration", "prevNode", "prev", "text", "needIgnore", "parsedURLs", "walk", "valueNode", "valueNodes", "undefined", "isStringValue", "stringify", "normalizeUrl", "queryParts", "split", "prefix", "pop", "join", "push", "needQuotes", "innerIndex", "nNode", "entries", "plugin", "options", "postcssPlugin", "prepare", "parsedDeclarations", "Declaration", "parsedURL", "OnceExit", "resolvedDeclarations", "Promise", "all", "map", "parsedDeclaration", "filter", "<PERSON><PERSON><PERSON>", "pathname", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "hash", "needToResolveURL", "rootContext", "request", "requestify", "resolver", "context", "resolvedUrl", "resolveRequests", "Set", "urlToNameMap", "Map", "urlToReplacementMap", "hasUrlImportHelper", "item", "imports", "importName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "resolve", "newUrl", "get", "size", "set", "JSON", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "replacements", "postcss"], "mappings": ";;;;+BA2bA;;;eAAA;;;2EA3bwB;uBAUjB;;;;;;AAEP,MAAMA,YAAY;AAClB,MAAMC,iBAAiB;AACvB,MAAMC,uBAAuB;AAE7B,SAASC,mBAAmBC,IAAS;IACnC,OAAOA,KAAKC,KAAK,IAAID,KAAKC,KAAK,CAAC,EAAE;AACpC;AAEA,SAASC,6BAA6BC,KAAU,EAAEF,KAAU,EAAEG,SAAe;IAC3E,IAAID,UAAU,KAAK,OAAOC,cAAc,aAAa;QACnD,OAAOA;IACT;IAEA,IAAIC,gBAAgBJ,KAAK,CAACE,QAAQ,EAAE;IAEpC,IAAI,CAACE,eAAe;QAClB,6CAA6C;QAC7C;IACF;IAEA,IAAIA,cAAcC,IAAI,KAAK,SAAS;QAClC,IAAI,CAACL,KAAK,CAACE,QAAQ,EAAE,EAAE;YACrB,6CAA6C;YAC7C;QACF;QAEAE,gBAAgBJ,KAAK,CAACE,QAAQ,EAAE;IAClC;IAEA,IAAIE,cAAcC,IAAI,KAAK,WAAW;QACpC,6CAA6C;QAC7C;IACF;IAEA,MAAMC,UAAUF,cAAcG,KAAK,CAACC,KAAK,CAACC,oCAA6B;IAEvE,OAAOH,WAAWA,OAAO,CAAC,EAAE,KAAK;AACnC;AAEA,SAASI,gBACPC,GAAQ,EACRC,WAAgB,EAChBC,MAAW,EACXC,wBAA6B;IAE7B,IAAIH,IAAII,MAAM,KAAK,GAAG;QACpBF,OAAOG,IAAI,CAAC,CAAC,uBAAuB,EAAEJ,YAAYK,QAAQ,GAAG,CAAC,CAAC,EAAE;YAC/DlB,MAAMa;QACR;QAEA,OAAO;IACT;IAEA,IAAIM,IAAAA,gBAAS,EAACP,QAAQG,0BAA0B;QAC9C,IAAI;YACFK,mBAAmBR;QACrB,EAAE,OAAOS,aAAa;YACpB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAI,CAACC,IAAAA,uBAAgB,EAACV,MAAM;QAC1B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASW,iBACPV,WAAgB,EAChBW,GAAQ,EACRV,MAAW,EACXC,wBAA6B;IAE7B,IAAI,CAACjB,qBAAqB2B,IAAI,CAACZ,WAAW,CAACW,IAAI,GAAG;QAChD;IACF;IAEA,MAAME,SAASC,IAAAA,2BAAW,EACxBd,YAAYe,IAAI,IAAIf,YAAYe,IAAI,CAACpB,KAAK,IAAIK,YAAYe,IAAI,CAACpB,KAAK,CAACqB,GAAG,GACpEhB,YAAYe,IAAI,CAACpB,KAAK,CAACqB,GAAG,GAC1BhB,WAAW,CAACW,IAAI;IAGtB,IAAIpB;IAEJ,IAAIS,YAAYe,IAAI,IAAIf,YAAYe,IAAI,CAACE,OAAO,EAAE;QAChD,MAAMC,mBAAmBlB,YAAYe,IAAI,CAACE,OAAO,CAACE,WAAW,CAAC;QAE9D,MAAMzB,UAAUM,YAAYe,IAAI,CAACE,OAAO,CACrCG,KAAK,CAACF,kBACNtB,KAAK,CAACC,oCAA6B;QAEtC,IAAIH,SAAS;YACXH,YAAYG,OAAO,CAAC,EAAE,KAAK;QAC7B;IACF;IAEA,IAAI2B,wBAAwB;IAE5B,MAAMC,WAAWtB,YAAYuB,IAAI;IAEjC,IAAID,YAAYA,SAAS7B,IAAI,KAAK,WAAW;QAC3C,MAAMC,UAAU4B,SAASE,IAAI,CAAC5B,KAAK,CAACC,oCAA6B;QAEjE,IAAIH,SAAS;YACX2B,wBAAwB3B,OAAO,CAAC,EAAE,KAAK;QACzC;IACF;IAEA,IAAI+B;IAEJ,MAAMC,aAAoB,EAAE;IAE5Bb,OAAOc,IAAI,CAAC,CAACC,WAAgBtC,OAAYuC;QACvC,IAAID,UAAUnC,IAAI,KAAK,YAAY;YACjC;QACF;QAEA,IAAIV,UAAU6B,IAAI,CAACgB,UAAUjC,KAAK,GAAG;YACnC8B,aAAapC,6BAA6BC,OAAOuC,YAAYtC;YAE7D,IACE,AAAC8B,yBAAyB,OAAOI,eAAe,eAChDA,YACA;gBACA,IAAIA,YAAY;oBACd,wCAAwC;oBACxCA,aAAaK;gBACf;gBAEA;YACF;YAEA,MAAM,EAAE1C,KAAK,EAAE,GAAGwC;YAClB,MAAMG,gBAAgB3C,MAAMe,MAAM,KAAK,KAAKf,KAAK,CAAC,EAAE,CAACK,IAAI,KAAK;YAC9D,IAAIM,MAAMgC,gBAAgB3C,KAAK,CAAC,EAAE,CAACO,KAAK,GAAGmB,2BAAW,CAACkB,SAAS,CAAC5C;YACjEW,MAAMkC,IAAAA,mBAAY,EAAClC,KAAKgC;YAExB,+BAA+B;YAC/B,IACE,CAACjC,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;gBACA,6CAA6C;gBAC7C,OAAO;YACT;YAEA,MAAMgC,aAAanC,IAAIoC,KAAK,CAAC;YAC7B,IAAIC;YAEJ,IAAIF,WAAW/B,MAAM,GAAG,GAAG;gBACzBJ,MAAMmC,WAAWG,GAAG;gBACpBD,SAASF,WAAWI,IAAI,CAAC;YAC3B;YAEAZ,WAAWa,IAAI,CAAC;gBACdvC;gBACAa;gBACA1B,MAAMD,mBAAmB0C;gBACzBQ;gBACArC;gBACAyC,YAAY;YACd;YAEA,6CAA6C;YAC7C,OAAO;QACT,OAAO,IAAIxD,eAAe4B,IAAI,CAACgB,UAAUjC,KAAK,GAAG;YAC/C,KAAK,MAAM,CAAC8C,YAAYC,MAAM,IAAId,UAAUxC,KAAK,CAACuD,OAAO,GAAI;gBAC3D,MAAM,EAAElD,IAAI,EAAEE,KAAK,EAAE,GAAG+C;gBAExB,IAAIjD,SAAS,cAAcV,UAAU6B,IAAI,CAACjB,QAAQ;oBAChD8B,aAAapC,6BAA6BoD,YAAYb,UAAUxC,KAAK;oBAErE,IACE,AAACiC,yBAAyB,OAAOI,eAAe,eAChDA,YACA;wBACA,IAAIA,YAAY;4BACd,wCAAwC;4BACxCA,aAAaK;wBACf;wBAGA;oBACF;oBAEA,MAAM,EAAE1C,KAAK,EAAE,GAAGsD;oBAClB,MAAMX,gBAAgB3C,MAAMe,MAAM,KAAK,KAAKf,KAAK,CAAC,EAAE,CAACK,IAAI,KAAK;oBAC9D,IAAIM,MAAMgC,gBACN3C,KAAK,CAAC,EAAE,CAACO,KAAK,GACdmB,2BAAW,CAACkB,SAAS,CAAC5C;oBAC1BW,MAAMkC,IAAAA,mBAAY,EAAClC,KAAKgC;oBAExB,+BAA+B;oBAC/B,IACE,CAACjC,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;wBACA,6CAA6C;wBAC7C,OAAO;oBACT;oBAEA,MAAMgC,aAAanC,IAAIoC,KAAK,CAAC;oBAC7B,IAAIC;oBAEJ,IAAIF,WAAW/B,MAAM,GAAG,GAAG;wBACzBJ,MAAMmC,WAAWG,GAAG;wBACpBD,SAASF,WAAWI,IAAI,CAAC;oBAC3B;oBAEAZ,WAAWa,IAAI,CAAC;wBACdvC;wBACAa;wBACA1B,MAAMD,mBAAmBwD;wBACzBN;wBACArC;wBACAyC,YAAY;oBACd;gBACF,OAAO,IAAI/C,SAAS,UAAU;oBAC5BgC,aAAapC,6BAA6BoD,YAAYb,UAAUxC,KAAK;oBAErE,IACE,AAACiC,yBAAyB,OAAOI,eAAe,eAChDA,YACA;wBACA,IAAIA,YAAY;4BACd,wCAAwC;4BACxCA,aAAaK;wBACf;wBAGA;oBACF;oBAEA,IAAI/B,MAAMkC,IAAAA,mBAAY,EAACtC,OAAO;oBAE9B,+BAA+B;oBAC/B,IACE,CAACG,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;wBACA,6CAA6C;wBAC7C,OAAO;oBACT;oBAEA,MAAMgC,aAAanC,IAAIoC,KAAK,CAAC;oBAC7B,IAAIC;oBAEJ,IAAIF,WAAW/B,MAAM,GAAG,GAAG;wBACzBJ,MAAMmC,WAAWG,GAAG;wBACpBD,SAASF,WAAWI,IAAI,CAAC;oBAC3B;oBAEAZ,WAAWa,IAAI,CAAC;wBACdvC;wBACAa;wBACA1B,MAAMuD;wBACNN;wBACArC;wBACAyC,YAAY;oBACd;gBACF;YACF;YAEA,qCAAqC;YACrC,6CAA6C;YAC7C,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,OAAOd;AACT;AAEA,MAAMkB,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQ9C,MAAW;YACjB,MAAM+C,qBAA4B,EAAE;YAEpC,OAAO;gBACLC,aAAYjD,WAAgB;oBAC1B,MAAM,EAAEE,wBAAwB,EAAE,GAAG2C;oBACrC,MAAMK,YAAYxC,iBAChBV,aACA,SACAC,QACAC;oBAGF,IAAI,CAACgD,WAAW;wBACd;oBACF;oBAEAF,mBAAmBT,IAAI,IAAIW;gBAC7B;gBACA,MAAMC;oBACJ,IAAIH,mBAAmB7C,MAAM,KAAK,GAAG;wBACnC;oBACF;oBAEA,MAAMiD,uBAAuB,MAAMC,QAAQC,GAAG,CAC5CN,mBAAmBO,GAAG,CAAC,OAAOC;wBAC5B,MAAM,EAAEzD,GAAG,EAAE,GAAGyD;wBAEhB,IAAIX,QAAQY,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMb,QAAQY,MAAM,CAAC1D;4BAEtC,IAAI,CAAC2D,UAAU;gCACb,6CAA6C;gCAC7C;4BACF;wBACF;wBAEA,IAAIpD,IAAAA,gBAAS,EAACP,MAAM;4BAClB,6CAA6C;4BAC7C,OAAOyD;wBACT;wBAEA,MAAM,CAACG,UAAUC,OAAOC,YAAY,GAAG9D,IAAIoC,KAAK,CAAC,UAAU;wBAE3D,IAAI2B,OAAOF,QAAQ,MAAM;wBACzBE,QAAQD,cAAc,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG;wBAE1C,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGnB;wBAC1C,MAAMoB,UAAUC,IAAAA,iBAAU,EACxBP,UACAK,aACA,mDAAmD;wBACnDD;wBAGF,IAAI,CAACA,kBAAkB;4BACrB,6CAA6C;4BAC7C,OAAO;gCAAE,GAAGP,iBAAiB;gCAAEzD,KAAKkE;gCAASH;4BAAK;wBACpD;wBAEA,MAAM,EAAEK,QAAQ,EAAEC,OAAO,EAAE,GAAGvB;wBAC9B,MAAMwB,cAAc,MAAMC,IAAAA,sBAAe,EAACH,UAAUC,SAAS;+BACxD,IAAIG,IAAI;gCAACN;gCAASlE;6BAAI;yBAC1B;wBAED,IAAI,CAACsE,aAAa;4BAChB,6CAA6C;4BAC7C;wBACF;wBAEA,6CAA6C;wBAC7C,OAAO;4BAAE,GAAGb,iBAAiB;4BAAEzD,KAAKsE;4BAAaP;wBAAK;oBACxD;oBAGF,MAAMU,eAAe,IAAIC;oBACzB,MAAMC,sBAAsB,IAAID;oBAEhC,IAAIE,qBAAqB;oBAEzB,IACE,IAAIrF,QAAQ,GACZA,SAAS8D,qBAAqBjD,MAAM,GAAG,GACvCb,QACA;wBACA,MAAMsF,OAAOxB,oBAAoB,CAAC9D,MAAM;wBAExC,IAAI,CAACsF,MAAM;4BAET;wBACF;wBAEA,IAAI,CAACD,oBAAoB;4BACvB9B,QAAQgC,OAAO,CAACtC,IAAI,CAAC;gCACnB9C,MAAM;gCACNqF,YAAY;gCACZ/E,KAAK8C,QAAQkC,UAAU,CACrBC,QAAQC,OAAO,CAAC;gCAElB3F,OAAO,CAAC;4BACV;4BAEAqF,qBAAqB;wBACvB;wBAEA,MAAM,EAAE5E,GAAG,EAAEqC,MAAM,EAAE,GAAGwC;wBACxB,MAAMM,SAAS9C,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAErC,IAAI,CAAC,GAAGA;wBAC7C,IAAI+E,aAAaN,aAAaW,GAAG,CAACD;wBAElC,IAAI,CAACJ,YAAY;4BACfA,aAAa,CAAC,yBAAyB,EAAEN,aAAaY,IAAI,CAAC,GAAG,CAAC;4BAC/DZ,aAAaa,GAAG,CAACH,QAAQJ;4BAEzBjC,QAAQgC,OAAO,CAACtC,IAAI,CAAC;gCACnB9C,MAAM;gCACNqF;gCACA/E,KAAK8C,QAAQkB,gBAAgB,GACzBlB,QAAQkC,UAAU,CAACG,UACnBI,KAAKtD,SAAS,CAACkD;gCACnB5F;4BACF;wBACF;wBAEA,MAAM,EAAEwE,IAAI,EAAEtB,UAAU,EAAE,GAAGoC;wBAC7B,MAAMW,iBAAiBD,KAAKtD,SAAS,CAAC;4BAAEkD;4BAAQpB;4BAAMtB;wBAAW;wBACjE,IAAIgD,kBAAkBd,oBAAoBS,GAAG,CAACI;wBAE9C,IAAI,CAACC,iBAAiB;4BACpBA,kBAAkB,CAAC,8BAA8B,EAAEd,oBAAoBU,IAAI,CAAC,GAAG,CAAC;4BAChFV,oBAAoBW,GAAG,CAACE,gBAAgBC;4BAExC3C,QAAQ4C,YAAY,CAAClD,IAAI,CAAC;gCACxBiD;gCACAV;gCACAhB;gCACAtB;4BACF;wBACF;wBAEA,6CAA6C;wBAC7CoC,KAAKzF,IAAI,CAACM,IAAI,GAAG;wBACjB,6CAA6C;wBAC7CmF,KAAKzF,IAAI,CAACQ,KAAK,GAAG6F;wBAClB,6CAA6C;wBAC7CZ,KAAK5E,WAAW,CAACL,KAAK,GAAGiF,KAAK/D,MAAM,CAACR,QAAQ;oBAC/C;gBACF;YACF;QACF;IACF;AACF;AAEAuC,OAAO8C,OAAO,GAAG;MAEjB,WAAe9C"}