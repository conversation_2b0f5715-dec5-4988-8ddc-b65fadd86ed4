{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["getDefineEnv", "getDefineEnvPlugin", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "qualities", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "ppr", "deploymentId", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "undefined", "needsExperimentalReact", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;IAgIgBA,YAAY;eAAZA;;IAuIAC,kBAAkB;eAAlBA;;;yBAlQQ;wCACe;AAEvC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AAuCA;;CAEC,GACD,SAASC;IACP,MAAMC,YAAuB,CAAC;IAC9B,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASK,iBAAiBb,MAA0B;IAClD,sCAAsC;IACtC,MAAMQ,YAAuB,CAAC;IAC9B,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,SAAS,MAAM;YACjBb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASM,mBAAmBN,SAAoB;IAC9C,MAAMO,uBAA4C,CAAC;IACnD,IAAK,MAAMd,OAAOO,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACP,IAAI;QAC5Bc,oBAAoB,CAACd,IAAI,GAAGe,KAAKC,SAAS,CAACL;IAC7C;IAEA,OAAOG;AACT;AAEA,SAASG,eACPlB,MAA0B,EAC1BmB,GAAY;QAUKnB,gBAKSA,iBACDA;IAdzB,OAAO;QACL,iCAAiC;YAC/BoB,aAAapB,OAAOqB,MAAM,CAACD,WAAW;YACtCE,YAAYtB,OAAOqB,MAAM,CAACC,UAAU;YACpCC,WAAWvB,OAAOqB,MAAM,CAACE,SAAS;YAClCC,MAAMxB,OAAOqB,MAAM,CAACG,IAAI;YACxBC,QAAQzB,OAAOqB,MAAM,CAACI,MAAM;YAC5BC,qBAAqB1B,OAAOqB,MAAM,CAACK,mBAAmB;YACtDC,WAAW,EAAE3B,2BAAAA,iBAAAA,OAAQqB,MAAM,qBAAdrB,eAAgB2B,WAAW;YACxC,GAAIR,MACA;gBACE,6DAA6D;gBAC7DS,SAAS5B,OAAOqB,MAAM,CAACO,OAAO;gBAC9BC,cAAc,GAAE7B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe6B,cAAc;gBAC7CC,aAAa,GAAE9B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe8B,aAAa;gBAC3CC,QAAQ/B,OAAO+B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEO,SAASlC,aAAa,EAC3BmC,WAAW,EACXC,mBAAmB,EACnBjC,MAAM,EACNmB,GAAG,EACHe,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QAmCNzC,iCAETA,kCAGSA,kCAETA,kCA6C6BA;IAtFrC,MAAMQ,YAAuB;QAC3B,+CAA+C;QAC/CkC,mBAAmB;QAEnB,GAAGnC,mCAAmC;QACtC,GAAGM,iBAAiBb,OAAO;QAC3B,GAAI,CAACsC,eACD,CAAC,IACD;YACEK,aACE;;;;aAIC,GACDlC,QAAQC,GAAG,CAACkC,0BAA0B,IAAI;QAC9C,CAAC;QACL,qBAAqBZ;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBAAwBb,MAAM,gBAAgB;QAC9C,4BAA4BmB,eACxB,SACAE,eACA,WACA;QACJ,4BAA4B;QAC5B,0BAA0BxC,OAAO6C,YAAY,CAACC,GAAG,KAAK;QACtD,kCAAkC9C,OAAO+C,YAAY,IAAI;QACzD,6CAA6CZ,uBAAuB;QACpE,0CAA0CM,sBAAsB,EAAE;QAClE,8CACEzC,OAAO6C,YAAY,CAACG,oBAAoB,IAAI;QAC9C,sDAAsDhC,KAAKC,SAAS,CAClEgC,MAAMC,QAAOlD,kCAAAA,OAAO6C,YAAY,CAACM,UAAU,qBAA9BnD,gCAAgCoD,OAAO,KAChD,GAAG,aAAa;YAChBpD,mCAAAA,OAAO6C,YAAY,CAACM,UAAU,qBAA9BnD,iCAAgCoD,OAAO;QAE7C,qDAAqDpC,KAAKC,SAAS,CACjEgC,MAAMC,QAAOlD,mCAAAA,OAAO6C,YAAY,CAACM,UAAU,qBAA9BnD,iCAAgCqD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBrD,mCAAAA,OAAO6C,YAAY,CAACM,UAAU,qBAA9BnD,iCAAgCqD,MAAM;QAE5C,mDACErD,OAAO6C,YAAY,CAACS,kBAAkB,IAAI;QAC5C,6CACErB,CAAAA,uCAAAA,oBAAqBsB,YAAY,KAAI;QACvC,6CACEtB,CAAAA,uCAAAA,oBAAqBuB,aAAa,KAAI;QACxC,8CACExD,OAAO6C,YAAY,CAACY,qBAAqB,IAAI;QAC/C,0CACEzD,OAAO6C,YAAY,CAACa,kBAAkB,IAAI;QAC5C,mCAAmC1D,OAAO2D,WAAW;QACrD,mBAAmBtB;QACnB,gCAAgC5B,QAAQC,GAAG,CAACkD,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAIzC,OAAQkB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqClC,OAAO6D,aAAa;QACzD,sCACE7D,OAAO8D,aAAa,CAACC,aAAa,IAAI;QACxC,+CACE/D,OAAO8D,aAAa,CAACE,qBAAqB,IAAI;QAChD,kCACEhE,OAAOiE,eAAe,KAAK,OAAO,QAAQjE,OAAOiE,eAAe;QAClE,sCACE,6EAA6E;QAC7EjE,OAAOiE,eAAe,KAAK,OAAO,OAAOjE,OAAOiE,eAAe;QACjE,qCAAqC,CAAC9C,OAAOnB,OAAOkE,aAAa;QACjE,mCACE,AAAClE,CAAAA,OAAO6C,YAAY,CAACsB,WAAW,IAAI,CAAChD,GAAE,KAAM;QAC/C,qCACE,AAACnB,CAAAA,OAAO6C,YAAY,CAACuB,iBAAiB,IAAI,CAACjD,GAAE,KAAM;QACrD,yCACEnB,OAAO6C,YAAY,CAACwB,iBAAiB,IAAI;QAC3C,GAAGnD,eAAelB,QAAQmB,IAAI;QAC9B,sCAAsCnB,OAAOsE,QAAQ;QACrD,uCACEtE,OAAO6C,YAAY,CAAC0B,cAAc,IAAI;QACxC,mCAAmCnC;QACnC,oCAAoCpC,OAAO+B,MAAM;QACjD,mCAAmC,CAAC,CAAC/B,OAAOwE,IAAI;QAChD,mCAAmCxE,EAAAA,eAAAA,OAAOwE,IAAI,qBAAXxE,aAAa4B,OAAO,KAAI;QAC3D,mCAAmC5B,OAAOyE,WAAW;QACrD,kDACEzE,OAAO0E,0BAA0B;QACnC,0DACE1E,OAAO6C,YAAY,CAAC8B,iCAAiC,IAAI;QAC3D,4CACE3E,OAAO4E,yBAAyB;QAClC,iDACE,AAAC5E,CAAAA,OAAO6C,YAAY,CAACgC,oBAAoB,IACvC7E,OAAO6C,YAAY,CAACgC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACE9E,OAAO6C,YAAY,CAACgC,oBAAoB,IAAI;QAC9C,0CACE7E,OAAO6C,YAAY,CAACkC,gBAAgB,IAAI;QAC1C,mCAAmC/E,OAAOgF,WAAW;QACrD,GAAIzC,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACA0C,SAAS;QACb,GAAI1C,0BACA;YACE,yCACE2C,IAAAA,8CAAsB,EAAClF;QAC3B,IACAiF,SAAS;IACf;IACA,OAAOnE,mBAAmBN;AAC5B;AAEO,SAASV,mBAAmBqF,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAACxF,aAAasF;AAC/C"}