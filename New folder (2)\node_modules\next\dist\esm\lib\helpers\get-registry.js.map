{"version": 3, "sources": ["../../../src/lib/helpers/get-registry.ts"], "names": ["execSync", "getPkgManager", "getNodeOptionsWithoutInspect", "getRegistry", "baseDir", "process", "cwd", "registry", "pkgManager", "output", "env", "NODE_OPTIONS", "toString", "trim", "startsWith", "endsWith"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAe;AACxC,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,4BAA4B,QAAQ,yBAAwB;AAErE;;;;CAIC,GACD,OAAO,SAASC,YAAYC,UAAkBC,QAAQC,GAAG,EAAE;IACzD,IAAIC,WAAW,CAAC,2BAA2B,CAAC;IAC5C,IAAI;QACF,MAAMC,aAAaP,cAAcG;QACjC,MAAMK,SAAST,SAAS,CAAC,EAAEQ,WAAW,oBAAoB,CAAC,EAAE;YAC3DE,KAAK;gBACH,GAAGL,QAAQK,GAAG;gBACdC,cAAcT;YAChB;QACF,GACGU,QAAQ,GACRC,IAAI;QAEP,IAAIJ,OAAOK,UAAU,CAAC,SAAS;YAC7BP,WAAWE,OAAOM,QAAQ,CAAC,OAAON,SAAS,CAAC,EAAEA,OAAO,CAAC,CAAC;QACzD;IACF,SAAU;QACR,OAAOF;IACT;AACF"}