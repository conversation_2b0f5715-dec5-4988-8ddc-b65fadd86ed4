"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
require("./webpack");
const _ = require("./");
window.next = {
    version: _.version,
    // router is initialized later so it has to be live-binded
    get router () {
        return _.router;
    },
    emitter: _.emitter
};
(0, _.initialize)({}).then(()=>(0, _.hydrate)()).catch(console.error);

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=next.js.map