{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/styles/ComponentStyles.tsx"], "names": ["ComponentStyles", "style", "css", "overlay", "toast", "dialog", "leftRightDialogHeader", "codeFrame", "terminal", "buildErrorStyles", "containerErrorStyles", "containerRuntimeErrorStyles", "versionStaleness"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;;;wBAZoB;wBACH;yBACe;yBACd;yBACC;uBACH;sCACW;4BACA;wBACI;8BACO;8BAC1B;;;;;;;;;;;;;;;;;;;;AAErB,SAASA;IACd,qBACE,qBAACC;sBACEC,kBAAG,qBACAC,eAAO,EACPC,aAAK,EACLC,cAAM,EACNC,eAAqB,EACrBC,cAAS,EACTC,eAAQ,EACRC,kBAAgB,EAChBC,cAAoB,EACpBC,oBAA2B,EAC3BC,4BAAgB;;AAI1B"}