<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal4u Admin Dashboard - Test Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .toggle {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .toggle.active {
            background: #27ae60;
        }
        .toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        .toggle.active::after {
            transform: translateX(26px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <div class="text-6xl mb-4">🛡️</div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Deal4u Admin</h1>
                <p class="text-gray-600">Secure Admin Dashboard</p>
            </div>
            
            <form onsubmit="handleLogin(event)" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                    <input type="text" id="username" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter username" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter password" required>
                </div>
                
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                    Login
                </button>
            </form>
            
            <div class="mt-6 text-center text-sm text-gray-500">
                <p>Demo Credentials:</p>
                <p><strong>Username:</strong> admin | <strong>Password:</strong> admin</p>
            </div>
        </div>
    </div>

    <!-- OTP Screen -->
    <div id="otpScreen" class="hidden min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <div class="text-6xl mb-4">📱</div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Enter OTP</h1>
                <p class="text-gray-600">Your OTP is: <strong id="generatedOTP"></strong></p>
                <p class="text-sm text-gray-500 mt-2">In production: SMS sent to +96171172405</p>
            </div>
            
            <form onsubmit="handleOTP(event)" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OTP Code</label>
                    <input type="text" id="otpInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-center text-2xl tracking-widest" placeholder="000000" maxlength="6" required>
                </div>
                
                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                    Verify OTP
                </button>
                
                <button type="button" onclick="showLogin()" class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors">
                    Back to Login
                </button>
            </form>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="hidden min-h-screen bg-gray-100">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">🎛️ Deal4u Admin Dashboard</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-400 hover:text-gray-600 relative">
                            🔔
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                        </button>
                        <button onclick="logout()" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                            <span>🚪</span>
                            <span>Logout</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <nav class="w-64 bg-white shadow-sm h-screen sticky top-0">
                <div class="p-4">
                    <div class="space-y-2">
                        <button onclick="showTab('dashboard')" class="tab-btn w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors bg-blue-100 text-blue-700">
                            <span>📊</span>
                            <span>Dashboard</span>
                        </button>
                        <button onclick="showTab('features')" class="tab-btn w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors text-gray-700 hover:bg-gray-100">
                            <span>⚡</span>
                            <span>Features</span>
                        </button>
                        <button onclick="showTab('products')" class="tab-btn w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors text-gray-700 hover:bg-gray-100">
                            <span>📦</span>
                            <span>Products</span>
                        </button>
                        <button onclick="showTab('customers')" class="tab-btn w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors text-gray-700 hover:bg-gray-100">
                            <span>👥</span>
                            <span>Customers</span>
                        </button>
                        <button onclick="showTab('analytics')" class="tab-btn w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors text-gray-700 hover:bg-gray-100">
                            <span>📈</span>
                            <span>Analytics</span>
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="flex-1 p-6">
                <!-- Dashboard Tab -->
                <div id="dashboardTab" class="tab-content">
                    <div class="mb-6">
                        <h2 class="text-3xl font-bold text-gray-900">Dashboard Overview</h2>
                        <p class="text-gray-600">Welcome back! Here's what's happening with your store.</p>
                    </div>

                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-green-100">
                                    <span class="text-2xl">💰</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Sales</p>
                                    <p class="text-2xl font-bold text-gray-900">£15,420.50</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-blue-100">
                                    <span class="text-2xl">🛒</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                                    <p class="text-2xl font-bold text-gray-900">89</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-purple-100">
                                    <span class="text-2xl">👥</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Customers</p>
                                    <p class="text-2xl font-bold text-gray-900">156</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-orange-100">
                                    <span class="text-2xl">📦</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Products</p>
                                    <p class="text-2xl font-bold text-gray-900">159</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-red-100">
                                    <span class="text-2xl">📈</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Conversion Rate</p>
                                    <p class="text-2xl font-bold text-gray-900">3.2%</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 rounded-md bg-indigo-100">
                                    <span class="text-2xl">💳</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Avg Order Value</p>
                                    <p class="text-2xl font-bold text-gray-900">£173.15</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button onclick="alert('Sync Products feature ready!')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                🔄 Sync Products
                            </button>
                            <button onclick="alert('View Orders feature ready!')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                🛒 View Orders
                            </button>
                            <button onclick="alert('Customer Support feature ready!')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                🎧 Customer Support
                            </button>
                            <button onclick="alert('Analytics feature ready!')" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                📊 Analytics
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Features Tab -->
                <div id="featuresTab" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-3xl font-bold text-gray-900">Feature Management</h2>
                        <p class="text-gray-600">Enable or disable website features to enhance customer experience.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Live Chat -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-green-100">
                                        <span class="text-2xl">💬</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">Live Chat Support</h4>
                                        <p class="text-sm text-gray-600 mt-1">Real-time customer support with WhatsApp integration</p>
                                    </div>
                                </div>
                                <div class="toggle active" onclick="toggleFeature(this, 'liveChat')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Enabled
                                </span>
                            </div>
                        </div>

                        <!-- Product Recommendations -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-blue-100">
                                        <span class="text-2xl">🎯</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">Smart Product Recommendations</h4>
                                        <p class="text-sm text-gray-600 mt-1">AI-powered "You might also like" suggestions</p>
                                    </div>
                                </div>
                                <div class="toggle active" onclick="toggleFeature(this, 'productRecommendations')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Enabled
                                </span>
                            </div>
                        </div>

                        <!-- Loyalty Program -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-purple-100">
                                        <span class="text-2xl">🎁</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">Loyalty & Rewards Program</h4>
                                        <p class="text-sm text-gray-600 mt-1">Points, VIP tiers, and exclusive benefits</p>
                                    </div>
                                </div>
                                <div class="toggle" onclick="toggleFeature(this, 'loyaltyProgram')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Disabled
                                </span>
                            </div>
                        </div>

                        <!-- Voice Search -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-yellow-100">
                                        <span class="text-2xl">🎤</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">Voice Search</h4>
                                        <p class="text-sm text-gray-600 mt-1">Search products using voice commands</p>
                                    </div>
                                </div>
                                <div class="toggle" onclick="toggleFeature(this, 'voiceSearch')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Disabled
                                </span>
                            </div>
                        </div>

                        <!-- Social Commerce -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-pink-100">
                                        <span class="text-2xl">📱</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">Social Commerce</h4>
                                        <p class="text-sm text-gray-600 mt-1">Instagram integration and social sharing</p>
                                    </div>
                                </div>
                                <div class="toggle active" onclick="toggleFeature(this, 'socialCommerce')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Enabled
                                </span>
                            </div>
                        </div>

                        <!-- One-Click Reorder -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-3">
                                    <div class="p-2 rounded-md bg-green-100">
                                        <span class="text-2xl">⚡</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">One-Click Reorder</h4>
                                        <p class="text-sm text-gray-600 mt-1">Quick reorder from purchase history</p>
                                    </div>
                                </div>
                                <div class="toggle active" onclick="toggleFeature(this, 'oneClickReorder')"></div>
                            </div>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Enabled
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other Tabs -->
                <div id="productsTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Products Management</h2>
                        <p class="text-gray-600 mb-4">Product management features will be implemented here.</p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-blue-800">🚧 This section is under development. Coming soon!</p>
                        </div>
                    </div>
                </div>

                <div id="customersTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Customers Management</h2>
                        <p class="text-gray-600 mb-4">Customer management features will be implemented here.</p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-blue-800">🚧 This section is under development. Coming soon!</p>
                        </div>
                    </div>
                </div>

                <div id="analyticsTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Analytics</h2>
                        <p class="text-gray-600 mb-4">Analytics features will be implemented here.</p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-blue-800">🚧 This section is under development. Coming soon!</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let generatedOTP = '';
        let currentTab = 'dashboard';

        function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'admin') {
                // Generate OTP
                generatedOTP = Math.floor(100000 + Math.random() * 900000).toString();
                document.getElementById('generatedOTP').textContent = generatedOTP;
                
                // Show OTP screen
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('otpScreen').classList.remove('hidden');
            } else {
                alert('Invalid credentials. Use admin/admin');
            }
        }

        function handleOTP(event) {
            event.preventDefault();
            const otp = document.getElementById('otpInput').value;
            
            if (otp === generatedOTP) {
                // Show dashboard
                document.getElementById('otpScreen').classList.add('hidden');
                document.getElementById('dashboard').classList.remove('hidden');
                
                // Show success message
                setTimeout(() => {
                    alert('🎉 Welcome to Deal4u Admin Dashboard!\n\nYou can now:\n• Toggle features ON/OFF\n• Monitor your store statistics\n• Manage products and customers\n\nAll features integrate with your WooCommerce store!');
                }, 500);
            } else {
                alert('Invalid OTP. Please try again.');
            }
        }

        function showLogin() {
            document.getElementById('otpScreen').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                document.getElementById('dashboard').classList.add('hidden');
                document.getElementById('loginScreen').classList.remove('hidden');
                
                // Reset forms
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('otpInput').value = '';
            }
        }

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('bg-blue-100', 'text-blue-700');
                btn.classList.add('text-gray-700', 'hover:bg-gray-100');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // Add active class to selected button
            event.target.classList.add('bg-blue-100', 'text-blue-700');
            event.target.classList.remove('text-gray-700', 'hover:bg-gray-100');
            
            currentTab = tabName;
        }

        function toggleFeature(toggleElement, featureName) {
            toggleElement.classList.toggle('active');
            
            const isEnabled = toggleElement.classList.contains('active');
            const statusElement = toggleElement.parentElement.parentElement.querySelector('span');
            
            if (isEnabled) {
                statusElement.textContent = 'Enabled';
                statusElement.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
                
                // Show feature activation message
                showFeatureMessage(featureName, true);
            } else {
                statusElement.textContent = 'Disabled';
                statusElement.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800';
                
                // Show feature deactivation message
                showFeatureMessage(featureName, false);
            }
        }

        function showFeatureMessage(featureName, enabled) {
            const messages = {
                liveChat: enabled ? '💬 Live Chat enabled! Chat widget will appear on your website.' : '💬 Live Chat disabled.',
                productRecommendations: enabled ? '🎯 Product Recommendations enabled! "You might also like" sections will appear.' : '🎯 Product Recommendations disabled.',
                loyaltyProgram: enabled ? '🎁 Loyalty Program enabled! Points widget will appear for customers.' : '🎁 Loyalty Program disabled.',
                voiceSearch: enabled ? '🎤 Voice Search enabled! Microphone icons will appear in search bars.' : '🎤 Voice Search disabled.',
                socialCommerce: enabled ? '📱 Social Commerce enabled! Share buttons will appear on products.' : '📱 Social Commerce disabled.',
                oneClickReorder: enabled ? '⚡ One-Click Reorder enabled! Reorder buttons will appear in order history.' : '⚡ One-Click Reorder disabled.'
            };
            
            const message = messages[featureName] || (enabled ? 'Feature enabled!' : 'Feature disabled!');
            
            // Create notification
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 ${enabled ? 'bg-green-600' : 'bg-gray-600'} text-white p-4 rounded-lg shadow-lg z-50 max-w-sm`;
            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">${enabled ? '✅' : '❌'}</span>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">${message}</p>
                        <p class="text-sm opacity-90 mt-1">Changes apply instantly on your website!</p>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 4 seconds
            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎛️ Deal4u Admin Dashboard Test Version Loaded');
            console.log('✅ All features are ready for testing!');
        });
    </script>
</body>
</html>
