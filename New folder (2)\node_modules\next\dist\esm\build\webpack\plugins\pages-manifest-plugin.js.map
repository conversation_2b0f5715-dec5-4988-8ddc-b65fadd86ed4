{"version": 3, "sources": ["../../../../src/build/webpack/plugins/pages-manifest-plugin.ts"], "names": ["path", "fs", "webpack", "sources", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "getRouteFromEntrypoint", "normalizePathSep", "edgeServerPages", "nodeServerPages", "edgeServerAppPaths", "nodeServerAppPaths", "PagesManifestPlugin", "constructor", "dev", "distDir", "isEdgeRuntime", "appDirEnabled", "createAssets", "compilation", "assets", "entrypoints", "pages", "appPaths", "entrypoint", "values", "pagePath", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "length", "slice", "startsWith", "writeMergedManifest", "manifestPath", "entries", "mkdir", "dirname", "recursive", "writeFile", "JSON", "stringify", "readFile", "then", "res", "parse", "catch", "pagesManifestPath", "join", "RawSource", "appPathsManifestPath", "apply", "compiler", "hooks", "make", "tap", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,kBAAkB,QACb,gCAA+B;AACtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,gBAAgB,QAAQ,mDAAkD;AAInF,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAClC,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAElC,mEAAmE;AACnE,2GAA2G;AAC3G,0DAA0D;AAC1D,eAAe,MAAMC;IAQnBC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,aAAa,EACbC,aAAa,EAMd,CAAE;QACD,IAAI,CAACH,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEA,MAAMC,aAAaC,WAAgB,EAAEC,MAAW,EAAE;QAChD,MAAMC,cAAcF,YAAYE,WAAW;QAC3C,MAAMC,QAAuB,CAAC;QAC9B,MAAMC,WAA0B,CAAC;QAEjC,KAAK,MAAMC,cAAcH,YAAYI,MAAM,GAAI;YAC7C,MAAMC,WAAWpB,uBACfkB,WAAWG,IAAI,EACf,IAAI,CAACV,aAAa;YAGpB,IAAI,CAACS,UAAU;gBACb;YACF;YAEA,MAAME,QAAQJ,WACXK,QAAQ,GACRC,MAAM,CACL,CAACC,OACC,CAACA,KAAKC,QAAQ,CAAC,sBACf,CAACD,KAAKC,QAAQ,CAAC,0BACfD,KAAKE,QAAQ,CAAC;YAGpB,+BAA+B;YAC/B,IAAI,CAACL,MAAMM,MAAM,EAAE;gBACjB;YACF;YACA,mHAAmH;YACnH,IAAIH,OAAOH,KAAK,CAACA,MAAMM,MAAM,GAAG,EAAE;YAElC,IAAI,CAAC,IAAI,CAACpB,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;oBACvBe,OAAOA,KAAKI,KAAK,CAAC;gBACpB;YACF;YACAJ,OAAOxB,iBAAiBwB;YAExB,IAAIP,WAAWG,IAAI,CAACS,UAAU,CAAC,SAAS;gBACtCb,QAAQ,CAACG,SAAS,GAAGK;YACvB,OAAO;gBACLT,KAAK,CAACI,SAAS,GAAGK;YACpB;QACF;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,IAAI,CAACf,aAAa,EAAE;YACtBR,kBAAkBc;YAClBZ,qBAAqBa;QACvB,OAAO;YACLd,kBAAkBa;YAClBX,qBAAqBY;QACvB;QAEA,gDAAgD;QAChD,sDAAsD;QACtD,MAAMc,sBAAsB,OAC1BC,cACAC;YAEA,MAAMtC,GAAGuC,KAAK,CAACxC,KAAKyC,OAAO,CAACH,eAAe;gBAAEI,WAAW;YAAK;YAC7D,MAAMzC,GAAG0C,SAAS,CAChBL,cACAM,KAAKC,SAAS,CACZ;gBACE,GAAI,MAAM5C,GACP6C,QAAQ,CAACR,cAAc,QACvBS,IAAI,CAAC,CAACC,MAAQJ,KAAKK,KAAK,CAACD,MACzBE,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA,EAAG;gBACpB,GAAGX,OAAO;YACZ,GACA,MACA;QAGN;QAEA,IAAI,IAAI,CAACxB,OAAO,EAAE;YAChB,MAAMoC,oBAAoBnD,KAAKoD,IAAI,CACjC,IAAI,CAACrC,OAAO,EACZ,UACAX;YAEF,MAAMiC,oBAAoBc,mBAAmB;gBAC3C,GAAG3C,eAAe;gBAClB,GAAGC,eAAe;YACpB;QACF,OAAO;YACL,MAAM0C,oBACJ,AAAC,CAAA,CAAC,IAAI,CAACrC,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKZ;YACpDgB,MAAM,CAAC+B,kBAAkB,GAAG,IAAIhD,QAAQkD,SAAS,CAC/CT,KAAKC,SAAS,CACZ;gBACE,GAAGrC,eAAe;gBAClB,GAAGC,eAAe;YACpB,GACA,MACA;QAGN;QAEA,IAAI,IAAI,CAACQ,aAAa,EAAE;YACtB,IAAI,IAAI,CAACF,OAAO,EAAE;gBAChB,MAAMuC,uBAAuBtD,KAAKoD,IAAI,CACpC,IAAI,CAACrC,OAAO,EACZ,UACAV;gBAEF,MAAMgC,oBAAoBiB,sBAAsB;oBAC9C,GAAG5C,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB;YACF,OAAO;gBACLS,MAAM,CACJ,AAAC,CAAA,CAAC,IAAI,CAACN,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKX,mBACnD,GAAG,IAAIF,QAAQkD,SAAS,CACvBT,KAAKC,SAAS,CACZ;oBACE,GAAGnC,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB,GACA,MACA;YAGN;QACF;IACF;IAEA4C,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACxC;YAC9CA,YAAYsC,KAAK,CAACG,aAAa,CAACC,UAAU,CACxC;gBACElC,MAAM;gBACNmC,OAAO5D,QAAQ6D,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAAC5C,SAAW,IAAI,CAACF,YAAY,CAACC,aAAaC;QAE/C;IACF;AACF"}