/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Removed 'output: export' to enable dynamic data fetching
  trailingSlash: true,
  images: {
    unoptimized: true,
    domains: [
      'deal4u.co',
      'wp.com',
      'wordpress.com',
      'gravatar.com',
      'secure.gravatar.com',
      'images.unsplash.com',
      'i0.wp.com',
      'i1.wp.com',
      'i2.wp.com',
      'i3.wp.com',
      'i4.wp.com',
      'i5.wp.com',
      'i6.wp.com',
      'i7.wp.com',
      'i8.wp.com',
      'i9.wp.com',
      'woocommerce.com',
      'secure.woocommerce.com',
      'cdn.woocommerce.com',
      'placehold.co',
      'placekitten.com',
      'picsum.photos',
      'loremflickr.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.wp.com'
      },
      {
        protocol: 'https',
        hostname: '**.wordpress.com'
      },
      {
        protocol: 'https',
        hostname: 'deal4u.co'
      }
    ]
  },
  // Enable dynamic features for real-time data fetching
  // experimental: {
  //   // appDir is now stable in Next.js 14 and no longer needed
  // },
  // Add basePath if you're not deploying to the root domain
  // basePath: '/your-subdirectory', // Uncomment and modify if needed
}

module.exports = nextConfig