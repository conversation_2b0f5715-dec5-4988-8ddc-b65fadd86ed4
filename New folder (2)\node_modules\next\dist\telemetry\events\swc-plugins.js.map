{"version": 3, "sources": ["../../../src/telemetry/events/swc-plugins.ts"], "names": ["eventSwcPlugins", "EVENT_SWC_PLUGIN_PRESENT", "dir", "config", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "swcPluginPackages", "experimental", "swcPlugins", "map", "name", "_", "plugin", "version", "undefined", "pluginName", "fs", "existsSync", "path", "basename", "eventName", "payload", "pluginVersion"], "mappings": ";;;;+BAcsBA;;;eAAAA;;;+DAdH;6DACF;2DACF;;;;;;AAGf,MAAMC,2BAA2B;AAS1B,eAAeD,gBACpBE,GAAW,EACXC,MAAkB;IAElB,IAAI;YAUAA,iCAAAA;QATF,MAAMC,kBAAkB,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YAAEC,KAAKJ;QAAI;QAChE,IAAI,CAACE,iBAAiB;YACpB,OAAO,EAAE;QACX;QAEA,MAAM,EAAEG,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,QAAQL;QAE5D,MAAMM,OAAO;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;QAAC;QACnD,MAAMI,oBACJR,EAAAA,uBAAAA,OAAOS,YAAY,sBAAnBT,kCAAAA,qBAAqBU,UAAU,qBAA/BV,gCAAiCW,GAAG,CAAC,CAAC,CAACC,MAAMC,EAAE,GAAKD,UAAS,EAAE;QAEjE,OAAOJ,kBAAkBG,GAAG,CAAC,CAACG;YAC5B,0EAA0E;YAC1E,MAAMC,UAAUR,IAAI,CAACO,OAAO,IAAIE;YAChC,IAAIC,aAAaH;YACjB,IAAII,WAAE,CAACC,UAAU,CAACF,aAAa;gBAC7BA,aAAaG,aAAI,CAACC,QAAQ,CAACP,QAAQ;YACrC;YAEA,OAAO;gBACLQ,WAAWxB;gBACXyB,SAAS;oBACPN,YAAYA;oBACZO,eAAeT;gBACjB;YACF;QACF;IACF,EAAE,OAAOF,GAAG;QACV,OAAO,EAAE;IACX;AACF"}