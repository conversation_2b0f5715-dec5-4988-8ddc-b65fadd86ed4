{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "names": ["BARREL_OPTIMIZATION_PREFIX", "RSC_MODULE_TYPES", "getModuleBuildInfo", "regexCSS", "transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "code", "map", "x", "JSON", "parse", "filter", "request", "test", "ids", "importPath", "stringify", "startsWith", "replace", "length", "includes", "join", "buildInfo", "_module", "rsc", "type", "client"], "mappings": "AACA,SACEA,0BAA0B,EAC1BC,gBAAgB,QACX,gCAA+B;AACtC,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,QAAQ,QAAQ,UAAS;AAqBlC,eAAe,SAASC;IAGtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,IAAI,CAACC,UAAU;IACzC,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,OAAON,QACVO,GAAG,CAAC,CAACC,IAAMC,KAAKC,KAAK,CAACF,GACvB,8CAA8C;KAC7CG,MAAM,CAAC,CAAC,EAAEC,OAAO,EAAE,GAAMT,WAAW,CAACL,SAASe,IAAI,CAACD,WAAW,MAC9DL,GAAG,CAAC,CAAC,EAAEK,OAAO,EAAEE,GAAG,EAA+B;QACjD,MAAMC,aAAaN,KAAKO,SAAS,CAC/BJ,QAAQK,UAAU,CAACtB,8BACfiB,QAAQM,OAAO,CAAC,KAAK,SACrBN;QAGN,4FAA4F;QAC5F,0FAA0F;QAC1F,wDAAwD;QACxD,IAAIE,IAAIK,MAAM,KAAK,KAAKL,IAAIM,QAAQ,CAAC,MAAM;YACzC,OAAO,CAAC,kCAAkC,EAAEL,WAAW,IAAI,CAAC;QAC9D,OAAO;YACL,OAAO,CAAC,gDAAgD,EAAEN,KAAKO,SAAS,CACtEF,KACA,IAAI,EAAEC,WAAW,IAAI,CAAC;QAC1B;IACF,GACCM,IAAI,CAAC;IAER,MAAMC,YAAYzB,mBAAmB,IAAI,CAAC0B,OAAO;IAEjDD,UAAUE,GAAG,GAAG;QACdC,MAAM7B,iBAAiB8B,MAAM;IAC/B;IAEA,OAAOpB;AACT"}