import { NextResponse } from 'next/server';
import { wooCommerceApi } from '@/lib/woocommerce';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const status = searchParams.get('status') || 'publish';
    const perPage = parseInt(searchParams.get('per_page')) || 20;
    const page = parseInt(searchParams.get('page')) || 1;
    
    console.log(`🔍 Fetching products with status: ${status}, per_page: ${perPage}, page: ${page}`);
    
    let products = [];
    
    // Handle multiple statuses (comma-separated)
    if (status.includes(',')) {
      const statuses = status.split(',').map(s => s.trim());
      console.log('Multiple statuses requested:', statuses);
      
      for (const singleStatus of statuses) {
        const statusProducts = await wooCommerceApi.getProducts({
          per_page: perPage,
          status: singleStatus,
          page: page
        });
        
        if (Array.isArray(statusProducts)) {
          products.push(...statusProducts);
        }
      }
    } else {
      // Single status
      products = await wooCommerceApi.getProducts({
        per_page: perPage,
        status: status,
        page: page
      });
    }
    
    // Ensure products is an array
    if (!Array.isArray(products)) {
      products = [];
    }
    
    console.log(`✅ Found ${products.length} products with status: ${status}`);
    
    // Group by status for debugging
    const statusCounts = {};
    products.forEach(product => {
      const productStatus = product.status || 'unknown';
      statusCounts[productStatus] = (statusCounts[productStatus] || 0) + 1;
    });
    
    return NextResponse.json({
      success: true,
      products: products,
      count: products.length,
      statusCounts: statusCounts,
      requestedStatus: status,
      pagination: {
        page: page,
        perPage: perPage,
        total: products.length
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error fetching products:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
