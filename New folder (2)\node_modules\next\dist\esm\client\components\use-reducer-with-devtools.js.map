{"version": 3, "sources": ["../../../src/client/components/use-reducer-with-devtools.ts"], "names": ["React", "use", "useContext", "useRef", "useEffect", "useCallback", "isThenable", "ActionQueueContext", "normalizeRouterState", "val", "Map", "obj", "key", "value", "entries", "$$typeof", "toString", "_bundlerConfig", "hasOwnProperty", "Array", "isArray", "map", "useUnwrapState", "state", "result", "useReducerWithReduxDevtoolsNoop", "initialState", "useReducerWithReduxDevtoolsImpl", "setState", "useState", "actionQueue", "Error", "devtoolsConnectionRef", "enabledRef", "current", "undefined", "window", "__REDUX_DEVTOOLS_EXTENSION__", "connect", "instanceId", "name", "init", "devToolsInstance", "dispatch", "action", "sync", "resolvedState", "send", "type", "useReducerWithReduxDevtools"], "mappings": "AACA,OAAOA,SAASC,GAAG,EAAEC,UAAU,QAAQ,QAAO;AAC9C,SAASC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,QAAO;AACtD,SACEC,UAAU,QAIL,wCAAuC;AAC9C,SAASC,kBAAkB,QAAQ,uCAAsC;AAIzE,SAASC,qBAAqBC,GAAQ;IACpC,IAAIA,eAAeC,KAAK;QACtB,MAAMC,MAA8B,CAAC;QACrC,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIJ,IAAIK,OAAO,GAAI;YACxC,IAAI,OAAOD,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMI,cAAc,EAAE;oBACxBN,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YACAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAI,OAAOF,QAAQ,YAAYA,QAAQ,MAAM;QAC3C,MAAME,MAA8B,CAAC;QACrC,IAAK,MAAMC,OAAOH,IAAK;YACrB,MAAMI,QAAQJ,GAAG,CAACG,IAAI;YACtB,IAAI,OAAOC,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMK,cAAc,CAAC,mBAAmB;oBAC1CP,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YAEAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAIQ,MAAMC,OAAO,CAACX,MAAM;QACtB,OAAOA,IAAIY,GAAG,CAACb;IACjB;IAEA,OAAOC;AACT;AAaA,OAAO,SAASa,eAAeC,KAAmB;IAChD,4FAA4F;IAC5F,IAAIjB,WAAWiB,QAAQ;QACrB,MAAMC,SAASvB,IAAIsB;QACnB,OAAOC;IACT;IAEA,OAAOD;AACT;AAEA,SAASE,gCACPC,YAA4B;IAE5B,OAAO;QAACA;QAAc,KAAO;QAAG,KAAO;KAAE;AAC3C;AAEA,SAASC,gCACPD,YAA4B;IAE5B,MAAM,CAACH,OAAOK,SAAS,GAAG5B,MAAM6B,QAAQ,CAAeH;IAEvD,MAAMI,cAAc5B,WAAWK;IAE/B,IAAI,CAACuB,aAAa;QAChB,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAMC,wBAAwB7B;IAC9B,MAAM8B,aAAa9B;IAEnBC,UAAU;QACR,IAAI4B,sBAAsBE,OAAO,IAAID,WAAWC,OAAO,KAAK,OAAO;YACjE;QACF;QAEA,IACED,WAAWC,OAAO,KAAKC,aACvB,OAAOC,OAAOC,4BAA4B,KAAK,aAC/C;YACAJ,WAAWC,OAAO,GAAG;YACrB;QACF;QAEAF,sBAAsBE,OAAO,GAAGE,OAAOC,4BAA4B,CAACC,OAAO,CACzE;YACEC,YAAY;YACZC,MAAM;QACR;QAEF,IAAIR,sBAAsBE,OAAO,EAAE;YACjCF,sBAAsBE,OAAO,CAACO,IAAI,CAACjC,qBAAqBkB;YAExD,IAAII,aAAa;gBACfA,YAAYY,gBAAgB,GAAGV,sBAAsBE,OAAO;YAC9D;QACF;QAEA,OAAO;YACLF,sBAAsBE,OAAO,GAAGC;QAClC;IACF,GAAG;QAACT;QAAcI;KAAY;IAE9B,MAAMa,WAAWtC,YACf,CAACuC;QACC,IAAI,CAACd,YAAYP,KAAK,EAAE;YACtB,0EAA0E;YAC1E,iFAAiF;YACjFO,YAAYP,KAAK,GAAGG;QACtB;QAEAI,YAAYa,QAAQ,CAACC,QAAQhB;IAC/B,GACA;QAACE;QAAaJ;KAAa;IAG7B,6DAA6D;IAC7D,oEAAoE;IACpE,mEAAmE;IACnE,qBAAqB;IACrB,mEAAmE;IACnE,8CAA8C;IAC9C,MAAMmB,OAAOxC,YAAiC,CAACyC;QAC7C,IAAId,sBAAsBE,OAAO,EAAE;YACjCF,sBAAsBE,OAAO,CAACa,IAAI,CAChC;gBAAEC,MAAM;YAAc,GACtBxC,qBAAqBsC;QAEzB;IACF,GAAG,EAAE;IAEL,OAAO;QAACvB;QAAOoB;QAAUE;KAAK;AAChC;AAEA,OAAO,MAAMI,8BACX,OAAOb,WAAW,cACdT,kCACAF,gCAA+B"}