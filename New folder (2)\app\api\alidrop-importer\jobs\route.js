import { NextResponse } from 'next/server';

// Import the jobs storage from the start route
// In production, this would be a database
let importJobs = new Map();

export async function GET() {
  try {
    // Convert Map to Array for JSON response
    const jobs = Array.from(importJobs.values());
    
    // Sort by start time (newest first)
    jobs.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
    
    return NextResponse.json({
      success: true,
      jobs: jobs,
      count: jobs.length
    });

  } catch (error) {
    console.error('❌ Error fetching import jobs:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Export the jobs Map so other modules can access it
export { importJobs };
