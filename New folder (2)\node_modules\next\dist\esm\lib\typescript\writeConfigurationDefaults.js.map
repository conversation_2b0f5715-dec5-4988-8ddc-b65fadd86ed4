{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "names": ["promises", "fs", "bold", "cyan", "white", "CommentJson", "semver", "os", "getTypeScriptConfiguration", "Log", "getDesiredCompilerOptions", "ts", "tsOptions", "o", "lib", "suggested", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "Preserve", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "value", "reason", "esModuleInterop", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "getRequiredConfiguration", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "writeConfigurationDefaults", "tsConfigPath", "isFirstTimeSetup", "hasAppDir", "distDir", "hasPagesDir", "writeFile", "EOL", "options", "raw", "rawConfig", "userTsConfigContent", "readFile", "encoding", "userTsConfig", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "includes", "_", "nextAppTypes", "include", "plugins", "Array", "isArray", "hasNextPlugin", "some", "name", "length", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "stringify", "for<PERSON>ach", "action"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,gBAAe;AACjD,YAAYC,iBAAiB,kCAAiC;AAC9D,OAAOC,YAAY,4BAA2B;AAC9C,OAAOC,QAAQ,KAAI;AAEnB,SAASC,0BAA0B,QAAQ,+BAA8B;AACzE,YAAYC,SAAS,yBAAwB;AAa7C,SAASC,0BACPC,EAA+B,EAC/BC,SAA2B;IAE3B,MAAMC,IAAiC;QACrC,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEC,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDC,SAAS;YAAED,WAAW;QAAK;QAC3BE,cAAc;YAAEF,WAAW;QAAK;QAChCG,QAAQ;YAAEH,WAAW;QAAM;QAC3B,GAAIT,OAAOa,EAAE,CAACR,GAAGS,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAEN,WAAW;YAAK;QAAE,IACxDO,SAAS;QACbC,QAAQ;YAAER,WAAW;QAAK;QAC1B,GAAIT,OAAOkB,GAAG,CAACb,GAAGS,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEV,WAAW;YAAK;QAAE,IACnCO,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,QAAQ;YACNC,aAAahB,GAAGiB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZxB,OAAOkB,GAAG,CAACb,GAAGS,OAAO,EAAE,YAAY,AAACT,GAAGiB,UAAU,CAASG,QAAQ;gBAClEpB,GAAGiB,UAAU,CAACI,MAAM;gBACpBrB,GAAGiB,UAAU,CAACC,MAAM;gBACpBlB,GAAGiB,UAAU,CAACK,QAAQ;gBACtBtB,GAAGiB,UAAU,CAACM,GAAG;gBACjBvB,GAAGiB,UAAU,CAACO,QAAQ;gBACtBxB,GAAGiB,UAAU,CAACQ,MAAM;aACrB;YACDC,OAAO;YACPC,QAAQ;QACV;QACA,4DAA4D;QAC5D,GAAIhC,OAAOkB,GAAG,CAACb,GAAGS,OAAO,EAAE,YAC3BR,CAAAA,6BAAAA,UAAWc,MAAM,MAAK,AAACf,GAAGiB,UAAU,CAASG,QAAQ,GACjD;QAMA,IACA;YACEQ,iBAAiB;gBACfF,OAAO;gBACPC,QAAQ;YACV;YACAE,kBAAkB;gBAChB,sDAAsD;gBACtDb,aACEhB,GAAG8B,oBAAoB,CAACC,OAAO,IAC/B/B,GAAG8B,oBAAoB,CAACN,QAAQ,IAChC,AAACxB,GAAG8B,oBAAoB,CAASE,MAAM,IACvChC,GAAG8B,oBAAoB,CAACG,MAAM;gBAChC,4BAA4B;gBAC5Bd,cAAc;oBACXnB,GAAG8B,oBAAoB,CAASE,MAAM,IACrChC,GAAG8B,oBAAoB,CAACG,MAAM;oBAChC,qDAAqD;oBACrD,kDAAkD;oBACjDjC,GAAG8B,oBAAoB,CAASI,MAAM;oBACvClC,GAAG8B,oBAAoB,CAACL,MAAM;oBAC9BzB,GAAG8B,oBAAoB,CAACN,QAAQ;oBAChCxB,GAAG8B,oBAAoB,CAACC,OAAO;iBAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;gBACjCV,OAAO;gBACPC,QAAQ;YACV;YACAU,mBAAmB;gBACjBX,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACL,GAAI1B,CAAAA,6BAAAA,UAAWqC,oBAAoB,MAAK,OACpC3B,YACA;YACE4B,iBAAiB;gBACfb,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACLa,KAAK;YACHxB,aAAahB,GAAGyC,OAAO,CAACrB,QAAQ;YAChCM,OAAO;YACPC,QAAQ;QACV;IACF;IAEA,OAAOzB;AACT;AAEA,OAAO,SAASwC,yBACd1C,EAA+B;IAE/B,MAAM2C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB7C,0BAA0BC;IACzD,KAAK,MAAM6C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAGhC,WAAW,IAAIgC,GAAGtB,KAAK;IAC7C;IAEA,OAAOiB;AACT;AAEA,OAAO,eAAeM,2BACpBjD,EAA+B,EAC/BkD,YAAoB,EACpBC,gBAAyB,EACzBC,SAAkB,EAClBC,OAAe,EACfC,WAAoB;IAEpB,IAAIH,kBAAkB;QACpB,MAAM7D,GAAGiE,SAAS,CAACL,cAAc,OAAOtD,GAAG4D,GAAG;IAChD;IAEA,MAAM,EAAEC,SAASxD,SAAS,EAAEyD,KAAKC,SAAS,EAAE,GAC1C,MAAM9D,2BAA2BG,IAAIkD,cAAc;IAErD,MAAMU,sBAAsB,MAAMtE,GAAGuE,QAAQ,CAACX,cAAc;QAC1DY,UAAU;IACZ;IACA,MAAMC,eAAerE,YAAYsE,KAAK,CAACJ;IACvC,IAAIG,aAAaE,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAaN,SAAQ,GAAI;QACrEI,aAAaE,eAAe,GAAG,CAAC;QAChCd,mBAAmB;IACrB;IAEA,MAAMP,yBAAyB7C,0BAA0BC,IAAIC;IAE7D,MAAMiE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAMtB,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMwB,QAAQxB,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAeuB,OAAO;YACxB,IAAI,CAAEvB,CAAAA,aAAa5C,SAAQ,GAAI;gBAC7B,IAAI,CAAC8D,aAAaE,eAAe,EAAE;oBACjCF,aAAaE,eAAe,GAAG,CAAC;gBAClC;gBACAF,aAAaE,eAAe,CAACpB,UAAU,GAAGuB,MAAMhE,SAAS;gBACzD8D,iBAAiBG,IAAI,CACnB7E,KAAKqD,aAAa,iBAAiBtD,KAAK6E,MAAMhE,SAAS;YAE3D;QACF,OAAO,IAAI,WAAWgE,OAAO;gBAIrBA;YAHN,MAAMpB,KAAK/C,SAAS,CAAC4C,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkBuB,SAChBA,sBAAAA,MAAMjD,YAAY,qBAAlBiD,oBAAoBE,QAAQ,CAACtB,MAC7B,iBAAiBoB,QACjBA,MAAMpD,WAAW,KAAKgC,KACtBoB,MAAM1C,KAAK,KAAKsB,EAAC,GACrB;gBACA,IAAI,CAACe,aAAaE,eAAe,EAAE;oBACjCF,aAAaE,eAAe,GAAG,CAAC;gBAClC;gBACAF,aAAaE,eAAe,CAACpB,UAAU,GAAGuB,MAAM1C,KAAK;gBACrDyC,gBAAgBE,IAAI,CAClB7E,KAAKqD,aACH,iBACAtD,KAAK6E,MAAM1C,KAAK,IAChB,CAAC,EAAE,EAAE0C,MAAMzC,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAM4C,IAAWH;QACnB;IACF;IAEA,MAAMI,eAAe,CAAC,EAAEnB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaM,SAAQ,GAAI;QAC7BI,aAAaU,OAAO,GAAGrB,YACnB;YAAC;YAAiBoB;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CN,iBAAiBG,IAAI,CACnB7E,KAAK,aACH,iBACAD,KACE6D,YACI,CAAC,mBAAmB,EAAEoB,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAIpB,aAAa,CAACO,UAAUc,OAAO,CAACH,QAAQ,CAACE,eAAe;QACjET,aAAaU,OAAO,CAACJ,IAAI,CAACG;QAC1BN,iBAAiBG,IAAI,CACnB7E,KAAK,aAAa,yBAAyBD,KAAK,CAAC,CAAC,EAAEiF,aAAa,CAAC,CAAC;IAEvE;IAEA,wCAAwC;IACxC,IAAIpB,WAAW;QACb,qEAAqE;QACrE,MAAMsB,UAAU;eACVC,MAAMC,OAAO,CAAC3E,UAAUyE,OAAO,IAAIzE,UAAUyE,OAAO,GAAG,EAAE;eACzDX,aAAaE,eAAe,IAChCU,MAAMC,OAAO,CAACb,aAAaE,eAAe,CAACS,OAAO,IAC9CX,aAAaE,eAAe,CAACS,OAAO,GACpC,EAAE;SACP;QACD,MAAMG,gBAAgBH,QAAQI,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAAChB,aAAaE,eAAe,IAC5BS,QAAQM,MAAM,IACb,CAACH,iBACD,aAAalB,aACZ,CAAA,CAACA,UAAUM,eAAe,IAAI,CAACN,UAAUM,eAAe,CAACS,OAAO,AAAD,GAClE;YACA5E,IAAImF,IAAI,CACN,CAAC,OAAO,EAAE1F,KACR,iBACA,yLAAyL,EAAEC,KAC3L,mCACA,+JAA+J,CAAC;QAEtK,OAAO,IAAI,CAACqF,eAAe;YACzB,IAAI,CAAE,CAAA,aAAad,aAAaE,eAAe,AAAD,GAAI;gBAChDF,aAAaE,eAAe,CAACS,OAAO,GAAG,EAAE;YAC3C;YACAX,aAAaE,eAAe,CAACS,OAAO,CAACL,IAAI,CAAC;gBAAEU,MAAM;YAAO;YACzDb,iBAAiBG,IAAI,CACnB7E,KAAK,aAAa,yBAAyBD,KAAK,CAAC,gBAAgB,CAAC;QAEtE;QAEA,yEAAyE;QACzE,yCAAyC;QACzC,IACE+D,eACAF,aACAW,aAAaE,eAAe,IAC5B,CAACF,aAAaE,eAAe,CAAC1D,MAAM,IACpC,CAAE,CAAA,sBAAsBwD,aAAaE,eAAe,AAAD,GACnD;YACAF,aAAaE,eAAe,CAACiB,gBAAgB,GAAG;YAChDhB,iBAAiBG,IAAI,CACnB7E,KAAK,sBAAsB,iBAAiBD,KAAK,CAAC,IAAI,CAAC;QAE3D;IACF;IAEA,IAAI,CAAE,CAAA,aAAaoE,SAAQ,GAAI;QAC7BI,aAAaoB,OAAO,GAAG;YAAC;SAAe;QACvCjB,iBAAiBG,IAAI,CACnB7E,KAAK,aAAa,iBAAiBD,KAAK,CAAC,gBAAgB,CAAC;IAE9D;IAEA,IAAI2E,iBAAiBc,MAAM,GAAG,KAAKb,gBAAgBa,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAM1F,GAAGiE,SAAS,CAChBL,cACAxD,YAAY0F,SAAS,CAACrB,cAAc,MAAM,KAAKnE,GAAG4D,GAAG;IAGvD1D,IAAImF,IAAI,CAAC;IACT,IAAI9B,kBAAkB;QACpBrD,IAAImF,IAAI,CACN,CAAC,qDAAqD,EAAEzF,KACtD,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAM,IAAImF,IAAI,CACN,CAAC,6DAA6D,EAAEzF,KAC9D,iBACA,qCAAqC,EAAEA,KAAK,SAAS,YAAY,CAAC;IAEtE,IAAI0E,iBAAiBc,MAAM,EAAE;QAC3BlF,IAAImF,IAAI,CACN,CAAC,kDAAkD,EAAEzF,KACnD,iBACA,eAAe,EAAEA,KAAK,kBAAkB,+BAA+B,CAAC;QAG5E0E,iBAAiBmB,OAAO,CAAC,CAACC,SAAWxF,IAAImF,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE7DxF,IAAImF,IAAI,CAAC;IACX;IAEA,IAAId,gBAAgBa,MAAM,EAAE;QAC1BlF,IAAImF,IAAI,CACN,CAAC,cAAc,EAAExF,MAAM,qBAAqB,mBAAmB,EAAED,KAC/D,iBACA,GAAG,CAAC;QAGR2E,gBAAgBkB,OAAO,CAAC,CAACC,SAAWxF,IAAImF,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE5DxF,IAAImF,IAAI,CAAC;IACX;AACF"}