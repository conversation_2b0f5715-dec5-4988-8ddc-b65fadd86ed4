{"version": 3, "sources": ["../../src/telemetry/detached-flush.ts"], "names": ["args", "process", "argv", "dir", "pop", "mode", "Error", "getProjectDir", "config", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "distDir", "path", "join", "eventsPath", "events", "JSON", "parse", "fs", "readFileSync", "err", "code", "exit", "telemetry", "Telemetry", "record", "flush", "unlinkSync"], "mappings": ";;;;2DAAe;6DACE;yBAES;+DACH;+BACO;2BACW;;;;;;AAKvC,CAAA;IACA,MAAMA,OAAO;WAAIC,QAAQC,IAAI;KAAC;IAC9B,IAAIC,MAAMH,KAAKI,GAAG;IAClB,MAAMC,OAAOL,KAAKI,GAAG;IAErB,IAAI,CAACD,OAAOE,SAAS,OAAO;QAC1B,MAAM,IAAIC,MACR,CAAC,wEAAwE,CAAC;IAE9E;IACAH,MAAMI,IAAAA,4BAAa,EAACJ;IAEpB,MAAMK,SAAS,MAAMC,IAAAA,eAAU,EAACC,mCAAwB,EAAEP;IAC1D,MAAMQ,UAAUC,aAAI,CAACC,IAAI,CAACV,KAAKK,OAAOG,OAAO,IAAI;IACjD,MAAMG,aAAaF,aAAI,CAACC,IAAI,CAACF,SAAS;IAEtC,IAAII;IACJ,IAAI;QACFA,SAASC,KAAKC,KAAK,CAACC,WAAE,CAACC,YAAY,CAACL,YAAY;IAClD,EAAE,OAAOM,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,uCAAuC;YACvCpB,QAAQqB,IAAI,CAAC;QACf;QACA,MAAMF;IACR;IAEA,MAAMG,YAAY,IAAIC,kBAAS,CAAC;QAAEb;IAAQ;IAC1C,MAAMY,UAAUE,MAAM,CAACV;IACvB,MAAMQ,UAAUG,KAAK;IAErB,yCAAyC;IACzCR,WAAE,CAACS,UAAU,CAACb;IACdb,QAAQqB,IAAI,CAAC;AACf,CAAA"}