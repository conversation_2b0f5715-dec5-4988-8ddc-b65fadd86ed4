'use client';

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { X, Star, Heart, ShoppingCart, Minus, Plus, Check } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { toast } from 'react-hot-toast';

const QuickViewModal = ({ product, onClose }) => {
  const modalRef = useRef(null);
  const { addToCart, isInCart, getItemQuantity, updateQuantity } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  const handleQuantityChange = (amount) => {
    const newQuantity = Math.max(1, quantity + amount);
    if (product.stockQuantity && newQuantity > product.stockQuantity) {
      toast.error('Not enough stock available');
      return;
    }
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    try {
      setIsLoading(true);

      // Check stock status
      const isInStock = product.inStock || product.stockStatus === 'instock';
      if (!isInStock) {
        toast.error('Product is out of stock');
        return;
      }

      // Check stock availability
      if (product.stockQuantity && product.stockQuantity > 0) {
        const currentQuantity = getItemQuantity(product.id);
        if (currentQuantity + quantity > product.stockQuantity) {
          toast.error('Not enough stock available');
          return;
        }
      }

      // Prepare the item to add to cart
      const itemToAdd = {
        id: product.id,
        name: product.name,
        price: parseFloat(product.price) || 0,
        image: product.images?.[0]?.src || product.image || '/placeholder.jpg',
        quantity: quantity,
        stockQuantity: product.stockQuantity || 0,
        inStock: product.inStock
      };

      // If a variant is selected, include that information
      if (selectedVariant) {
        itemToAdd.selectedVariant = selectedVariant;
        itemToAdd.name = `${product.name} - ${selectedVariant.name || 'Selected Option'}`;
        itemToAdd.price = parseFloat(selectedVariant.price) || itemToAdd.price;
      }

      await addToCart(itemToAdd, quantity);
      toast.success(`Added ${itemToAdd.name} to cart`);
      onClose();
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Format price with currency
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Get product image with proper fallback
  const getProductImage = (image) => {
    if (imageError) return '/placeholder.jpg';
    
    // If image is already a string URL, use it directly
    if (typeof image === 'string') return image;
    
    // Check for WooCommerce image object
    if (image && image.src) return image.src;
    
    return '/placeholder.jpg';
  };

  const currentPrice = selectedVariant ? selectedVariant.price : product.price;
  const regularPrice = selectedVariant ? selectedVariant.regular_price : product.regular_price;
  const salePrice = selectedVariant ? selectedVariant.sale_price : product.sale_price;
  const isOnSale = regularPrice > currentPrice;
  const hasVariants = product.variations && product.variations.length > 0;
  const isInStock = product.inStock || product.stockStatus === 'instock';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div
        ref={modalRef}
        className="relative bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
          {/* Product Image */}
          <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
            <Image
              src={getProductImage(product.images?.[0] || product.image)}
              alt={product.name}
              fill
              className="object-cover"
              onError={() => setImageError(true)}
            />
          </div>

          {/* Product Info */}
          <div className="flex flex-col">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{product.name}</h2>

            {/* Rating */}
            <div className="flex items-center mb-4">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(product.rating || 0) ? 'fill-current' : ''
                    }`}
                  />
                ))}
              </div>
              <span className="ml-2 text-sm text-gray-600">
                ({product.reviews || 0} reviews)
              </span>
            </div>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-gray-900">
                  {formatPrice(currentPrice)}
                </span>
                {isOnSale && (
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(regularPrice)}
                  </span>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="prose prose-sm mb-6">
              <div dangerouslySetInnerHTML={{ __html: product.description }} />
            </div>

            {/* Variants */}
            {product.variations && product.variations.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Options</h3>
                <div className="flex flex-wrap gap-2">
                  {product.variations.map((variant, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedVariant(variant)}
                      className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                        selectedVariant === variant
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {variant.name || `Option ${index + 1}`}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity Selector */}
            <div className="flex items-center mb-6">
              <span className="mr-4 text-gray-700">Quantity:</span>
              <div className="flex items-center border rounded-lg">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  className="p-2 hover:bg-gray-100 rounded-l-lg"
                  disabled={quantity <= 1 || isLoading}
                >
                  <Minus className="w-4 h-4" />
                </button>
                <span className="px-4 py-2 text-center min-w-[3rem]">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  className="p-2 hover:bg-gray-100 rounded-r-lg"
                  disabled={isLoading || (product.stockQuantity && quantity >= product.stockQuantity)}
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Add to Cart Button */}
            <button
              onClick={handleAddToCart}
              disabled={!isInStock || isLoading}
              className={`w-full py-4 px-6 rounded-xl flex items-center justify-center gap-2 text-white font-semibold transition-all ${
                isInStock
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              <ShoppingCart className="w-5 h-5" />
              {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickViewModal;
