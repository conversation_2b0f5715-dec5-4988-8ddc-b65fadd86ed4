{"version": 3, "sources": ["../../../../../../../src/server/future/route-matcher-providers/dev/helpers/file-reader/default-file-reader.ts"], "names": ["DefaultFileReader", "constructor", "options", "read", "dir", "recursiveReadDir", "pathnameFilter", "ignoreFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortPathnames", "relativePathnames"], "mappings": ";;;;+BAaaA;;;eAAAA;;;kCAXoB;AAW1B,MAAMA;IAOX;;;;;;GAMC,GACDC,YAAYC,OAA2C,CAAE;QACvD,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA;;;;;;GAMC,GACD,MAAaC,KAAKC,GAAW,EAAkC;QAC7D,OAAOC,IAAAA,kCAAgB,EAACD,KAAK;YAC3BE,gBAAgB,IAAI,CAACJ,OAAO,CAACI,cAAc;YAC3CC,cAAc,IAAI,CAACL,OAAO,CAACK,YAAY;YACvCC,kBAAkB,IAAI,CAACN,OAAO,CAACM,gBAAgB;YAE/C,uEAAuE;YACvE,wBAAwB;YACxBC,eAAe;YAEf,sEAAsE;YACtE,iCAAiC;YACjCC,mBAAmB;QACrB;IACF;AACF"}