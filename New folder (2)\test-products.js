#!/usr/bin/env node

/**
 * Product Data Accuracy Test for New folder (2)
 * Tests WooCommerce product sync and verifies correct display of names, prices, and descriptions
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

console.log('🛍️ Product Data Accuracy Test - New folder (2)');
console.log('================================================\n');

// Load environment variables
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ Environment variables loaded from .env.local');
} else {
  console.log('⚠️ .env.local not found, using default values');
}

// Configuration
const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WORDPRESS_URL 
    ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/wc/v3`
    : 'https://deal4u.co/wp-json/wc/v3',
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET
};

console.log('📋 Configuration Check:');
console.log(`   WordPress URL: ${process.env.NEXT_PUBLIC_WORDPRESS_URL || 'https://deal4u.co'}`);
console.log(`   API Base URL: ${WC_CONFIG.baseURL}`);
console.log(`   Consumer Key: ${WC_CONFIG.consumerKey ? '✅ Set' : '❌ Missing'}`);
console.log(`   Consumer Secret: ${WC_CONFIG.consumerSecret ? '✅ Set' : '❌ Missing'}`);
console.log('');

// Transform function (same as in woocommerce.js)
const transformProduct = (product) => {
  const price = parseFloat(product.price) || 0;
  const regularPrice = parseFloat(product.regular_price) || price;
  const salePrice = parseFloat(product.sale_price) || null;
  
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    price,
    regular_price: regularPrice,
    sale_price: salePrice,
    image: product.images?.[0]?.src || '/placeholder.jpg',
    images: product.images || [],
    category: product.categories?.[0]?.name || 'Uncategorized',
    categories: product.categories || [],
    description: product.description || '',
    short_description: product.short_description || '',
    stock_status: product.stock_status || 'instock',
    stock_quantity: product.stock_quantity || 0,
    inStock: product.stock_status === 'instock',
    rating: parseFloat(product.average_rating) || 0,
    reviews: parseInt(product.rating_count) || 0,
    sku: product.sku || ''
  };
};

async function testProductDataAccuracy() {
  if (!WC_CONFIG.consumerKey || !WC_CONFIG.consumerSecret) {
    console.log('❌ WooCommerce credentials missing!');
    console.log('📝 No products will be available - WooCommerce required');
    console.log('');
    return;
  }

  try {
    // Create API instance
    const api = axios.create({
      baseURL: WC_CONFIG.baseURL,
      timeout: 15000,
      auth: {
        username: WC_CONFIG.consumerKey,
        password: WC_CONFIG.consumerSecret
      }
    });

    console.log('🔌 Testing WooCommerce Connection...');
    
    // Test 1: Basic connection
    const systemResponse = await api.get('/system_status');
    console.log('✅ WooCommerce API Connection: Success');
    console.log(`   WooCommerce Version: ${systemResponse.data.environment?.version || 'Unknown'}`);
    console.log('');

    // Test 2: Fetch products
    console.log('📦 Fetching Products...');
    const productsResponse = await api.get('/products', {
      params: {
        per_page: 5,
        status: 'publish'
      }
    });

    const products = productsResponse.data;
    console.log(`✅ Products Retrieved: ${products.length} products found`);
    console.log('');

    if (products.length === 0) {
      console.log('⚠️ No products found in your WooCommerce store');
      console.log('💡 Add some products to your WooCommerce store to test the sync');
      return;
    }

    // Test 3: Product data accuracy
    console.log('🔍 Product Data Accuracy Test:');
    console.log('================================');

    products.forEach((rawProduct, index) => {
      const product = transformProduct(rawProduct);
      
      console.log(`\n📦 Product ${index + 1}:`);
      console.log(`   🏷️  Name: "${product.name}"`);
      console.log(`   💰 Price: $${product.price.toFixed(2)}`);
      
      if (product.sale_price && product.sale_price < product.regular_price) {
        const discount = Math.round(((product.regular_price - product.sale_price) / product.regular_price) * 100);
        console.log(`   🏷️  Regular Price: $${product.regular_price.toFixed(2)}`);
        console.log(`   🔥 Sale Price: $${product.sale_price.toFixed(2)} (${discount}% off)`);
      }
      
      console.log(`   📝 Short Description: ${product.short_description ? '✅ Present' : '❌ Missing'}`);
      console.log(`   📖 Full Description: ${product.description ? '✅ Present' : '❌ Missing'}`);
      console.log(`   📷 Images: ${product.images.length} image(s)`);
      console.log(`   📂 Category: ${product.category}`);
      console.log(`   📦 Stock: ${product.stock_status} (${product.stock_quantity} available)`);
      console.log(`   ⭐ Rating: ${product.rating}/5 (${product.reviews} reviews)`);
      console.log(`   🔗 SKU: ${product.sku || 'Not set'}`);
      
      // Validate data integrity
      const issues = [];
      if (!product.name) issues.push('Missing product name');
      if (product.price <= 0) issues.push('Invalid price');
      if (!product.short_description) issues.push('Missing short description');
      if (product.images.length === 0) issues.push('No product images');
      
      if (issues.length > 0) {
        console.log(`   ⚠️  Issues: ${issues.join(', ')}`);
      } else {
        console.log(`   ✅ Data Quality: Perfect`);
      }
    });

    // Test 4: Categories
    console.log('\n\n📂 Testing Categories...');
    const categoriesResponse = await api.get('/products/categories', {
      params: {
        per_page: 10,
        hide_empty: true
      }
    });

    console.log(`✅ Categories Retrieved: ${categoriesResponse.data.length} categories found`);
    categoriesResponse.data.slice(0, 5).forEach((category, index) => {
      console.log(`   ${index + 1}. ${category.name} (${category.count} products)`);
    });

    // Summary
    console.log('\n\n🎉 Product Data Test Complete!');
    console.log('===============================');
    console.log('✅ WooCommerce integration is working correctly');
    console.log('✅ Product names, prices, and descriptions will display accurately');
    console.log('✅ Your website will sync with WooCommerce in real-time');
    console.log('');
    console.log('💡 What this means for your website:');
    console.log('   • Products will show correct names from WooCommerce');
    console.log('   • Prices will be accurate and up-to-date');
    console.log('   • Descriptions will display properly');
    console.log('   • Images will load from your WooCommerce store');
    console.log('   • Stock status will be real-time');
    console.log('');
    console.log('🚀 Next Steps:');
    console.log('   1. Run: npm run dev');
    console.log('   2. Visit: http://localhost:3000/shop');
    console.log('   3. Verify products display correctly');

  } catch (error) {
    console.log('❌ WooCommerce Test Failed!');
    console.log('===========================');
    
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || error.message}`);
      
      if (error.response.status === 401) {
        console.log('\n🔧 Authentication Error Solutions:');
        console.log('   1. Check Consumer Key and Secret in .env.local');
        console.log('   2. Verify keys are active in WooCommerce > Settings > Advanced > REST API');
        console.log('   3. Ensure keys have Read/Write permissions');
      }
    } else {
      console.log(`   Error: ${error.message}`);
    }
    
    console.log('\n📝 No products available - WooCommerce connection required');
  }
}

// Demo products removed - WooCommerce only mode

// Run the test
testProductDataAccuracy();
