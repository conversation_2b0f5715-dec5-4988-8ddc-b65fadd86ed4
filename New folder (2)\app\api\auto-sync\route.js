import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

// Global automation state
let autoSyncRunning = false;
let autoSyncInterval = null;
let lastSyncTime = null;
let processedProductIds = new Set();

export async function POST(request) {
  try {
    const { action, intervalMinutes = 5 } = await request.json();
    
    if (action === 'start') {
      return startAutoSync(intervalMinutes);
    } else if (action === 'stop') {
      return stopAutoSync();
    } else if (action === 'status') {
      return getAutoSyncStatus();
    } else if (action === 'force-sync') {
      return forceSyncNow();
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    
  } catch (error) {
    console.error('❌ Auto-sync API error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return getAutoSyncStatus();
}

// Start automatic sync service
function startAutoSync(intervalMinutes) {
  if (autoSyncRunning) {
    return NextResponse.json({
      success: false,
      message: 'Auto-sync is already running'
    });
  }
  
  autoSyncRunning = true;
  const intervalMs = intervalMinutes * 60 * 1000;
  
  console.log(`🚀 Starting auto-sync service - checking every ${intervalMinutes} minutes`);
  
  // Run initial sync
  performAutoSync();
  
  // Set up interval
  autoSyncInterval = setInterval(() => {
    performAutoSync();
  }, intervalMs);
  
  return NextResponse.json({
    success: true,
    message: `Auto-sync started - checking every ${intervalMinutes} minutes`,
    intervalMinutes: intervalMinutes
  });
}

// Stop automatic sync service
function stopAutoSync() {
  if (!autoSyncRunning) {
    return NextResponse.json({
      success: false,
      message: 'Auto-sync is not running'
    });
  }
  
  autoSyncRunning = false;
  if (autoSyncInterval) {
    clearInterval(autoSyncInterval);
    autoSyncInterval = null;
  }
  
  console.log('⏹️ Auto-sync service stopped');
  
  return NextResponse.json({
    success: true,
    message: 'Auto-sync stopped'
  });
}

// Get auto-sync status
function getAutoSyncStatus() {
  return NextResponse.json({
    success: true,
    status: {
      isRunning: autoSyncRunning,
      lastSyncTime: lastSyncTime,
      processedProductsCount: processedProductIds.size,
      nextSyncIn: autoSyncRunning ? 'Every 5 minutes' : 'Not scheduled'
    }
  });
}

// Force sync now
async function forceSyncNow() {
  console.log('🔄 Force sync requested...');
  const result = await performAutoSync();
  
  return NextResponse.json({
    success: true,
    message: 'Force sync completed',
    result: result
  });
}

// Main auto-sync function
async function performAutoSync() {
  try {
    console.log('🔍 Auto-sync: Checking for new products...');
    
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    // Get products modified in the last 10 minutes (to catch new imports)
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
    
    let newProducts = [];
    let page = 1;
    let hasMore = true;
    
    while (hasMore) {
      const response = await api.get('products', {
        per_page: 100,
        page: page,
        status: 'any',
        modified_after: tenMinutesAgo
      });
      
      const products = response.data.filter(p => 
        p.status !== 'trash' && !processedProductIds.has(p.id)
      );
      
      newProducts = [...newProducts, ...products];
      hasMore = products.length === 100;
      page++;
    }
    
    if (newProducts.length === 0) {
      console.log('✅ Auto-sync: No new products found');
      lastSyncTime = new Date().toISOString();
      return { processed: 0, message: 'No new products' };
    }
    
    console.log(`🆕 Auto-sync: Found ${newProducts.length} new products to process`);
    
    // Process products in PARALLEL batches for SPEED
    console.log(`🚀 Processing ${newProducts.length} products in parallel batches...`);

    const batchSize = 10; // Process 10 products simultaneously
    let processedCount = 0;

    for (let i = 0; i < newProducts.length; i += batchSize) {
      const batch = newProducts.slice(i, i + batchSize);

      // Process entire batch in parallel
      const batchPromises = batch.map(async (product) => {
        try {
          await processProductWithSmartImages(product, api);
          processedProductIds.add(product.id);
          console.log(`✅ Auto-processed: ${product.name}`);
          return { success: true, id: product.id };
        } catch (error) {
          console.error(`❌ Auto-sync failed for product ${product.id}:`, error.message);
          return { success: false, id: product.id, error: error.message };
        }
      });

      // Wait for entire batch to complete
      const batchResults = await Promise.all(batchPromises);
      const batchSuccessCount = batchResults.filter(r => r.success).length;
      processedCount += batchSuccessCount;

      console.log(`📦 Batch ${Math.floor(i/batchSize) + 1}: ${batchSuccessCount}/${batch.length} processed`);

      // Very short delay between batches (not between individual products)
      if (i + batchSize < newProducts.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    lastSyncTime = new Date().toISOString();
    console.log(`🎉 Auto-sync completed: ${processedCount}/${newProducts.length} products processed`);
    
    return {
      processed: processedCount,
      total: newProducts.length,
      message: `Auto-processed ${processedCount} new products`
    };
    
  } catch (error) {
    console.error('❌ Auto-sync error:', error);
    return { error: error.message };
  }
}

// Process product with smart image detection
async function processProductWithSmartImages(product, api) {
  const productImages = [];
  
  // Smart image extraction (same logic as before)
  if (product.images && product.images.length > 0) {
    for (const img of product.images) {
      const imgSrc = img.src || '';
      const imgName = img.name || '';
      const imgAlt = img.alt || '';
      
      const isSizeChart = [imgSrc, imgName, imgAlt].some(text => {
        const lower = text.toLowerCase();
        return ['size', 'chart', 'table', 'measurement', 'guide', 'cm', 'inch'].some(term => 
          lower.includes(term)
        );
      });
      
      if (!isSizeChart) {
        productImages.push(imgSrc);
      }
    }
  }
  
  // Extract from description if no good gallery images
  if (productImages.length === 0 && product.description) {
    const smartImages = extractSmartImages(product.description);
    productImages.push(...smartImages);
  }
  
  // Update product if we found better images
  if (productImages.length > 0) {
    const imageObjects = productImages.slice(0, 5).map((src, index) => ({
      src: src,
      name: `${product.name} - Image ${index + 1}`,
      alt: product.name,
      position: index
    }));
    
    await api.put(`products/${product.id}`, {
      images: imageObjects
    });
  }
}

// Smart image extraction (same as before)
function extractSmartImages(description) {
  const imageRegex = /<img[^>]+src="([^">]+)"[^>]*>/gi;
  const matches = [...description.matchAll(imageRegex)];
  
  if (matches.length === 0) return [];
  
  const firstHalf = description.substring(0, Math.floor(description.length / 2));
  const firstHalfMatches = [...firstHalf.matchAll(imageRegex)];
  
  if (firstHalfMatches.length > 0) {
    return firstHalfMatches.slice(0, 3).map(match => {
      let src = match[1];
      if (src.startsWith('//')) {
        src = 'https:' + src;
      }
      return src;
    });
  }
  
  const goodImages = [];
  for (const match of matches) {
    let src = match[1];
    if (src.startsWith('//')) {
      src = 'https:' + src;
    }
    
    const lowerSrc = src.toLowerCase();
    const isSizeChart = ['size', 'chart', 'table', 'cm', 'inch', 'waist', 'hip'].some(term => 
      lowerSrc.includes(term)
    );
    
    if (!isSizeChart) {
      goodImages.push(src);
      if (goodImages.length >= 2) break;
    }
  }
  
  return goodImages;
}
