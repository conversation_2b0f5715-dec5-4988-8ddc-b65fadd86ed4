{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "spans", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "ModuleFilenameHelpers", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "JSON", "stringify", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "mangle", "comments", "Worker", "path", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "pLimit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "webpack", "sources", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": ";;;;+BAkDaA;;;eAAAA;;;8DAlDS;yBAKf;+DACY;4BACI;iCACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAE1C,MAAMxB;IAEXyB,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBC,sBAAK,CAACC,GAAG,CAACR,gBAAiBO,sBAAK,CAACC,GAAG,CAACT;QAC7D,MAAMU,aAAaH,gBAAgBI,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBX,YAAYY,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAACjB,OAAO,CAACG,SAAS;QAE3D,OAAOY,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAAChB;YAE/B,MAAMiB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAACU,8BAAqB,CAACC,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bd,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMe,MAAM3B,YAAY4B,QAAQ,CAAChB;gBACjC,IAAI,CAACe,KAAK;oBACRE,QAAQC,GAAG,CAAClB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEmB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOrB;gBACV,MAAM,EAAEmB,IAAI,EAAEG,MAAM,EAAE,GAAGlC,YAAY4B,QAAQ,CAAChB;gBAE9C,MAAMuB,OAAOhC,MAAMiC,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMlC,MAAMmC,UAAU,CAAC1B,MAAMuB;gBAE5C,IAAI,CAACE,QAAQ;oBACXvB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCwC,QAAQC,GAAG,CACTS,KAAKC,SAAS,CAAC;wBACb5B;wBACAsB,QAAQA,OAAOA,MAAM,GAAGO,QAAQ;oBAClC,IACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAE/B;oBAAMmB;oBAAMc,aAAaX;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMW,kBAAkBC,KAAKC,GAAG,CAC9BlC,yBACAZ,gBAAgB+C,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAACzD,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLuD,QAAQ,OAAO1D;4BACb,MAAM2D,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpD1D,QAAQ6D,KAAK,EACb;gCACE,GAAI7D,QAAQ8D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASnB,KAAKC,SAAS,CAAC9C,QAAQ8D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNG,UAAU;gCACVC,QAAQ;gCACRvB,QAAQ;oCACNwB,UAAU;gCACZ;4BACF;4BAGF,OAAOR;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIY,kBAAM,CAACC,MAAK3E,IAAI,CAAC4E,WAAW,gBAAgB;oBAClEC,YAAYnB;oBACZoB,qBAAqB;gBACvB;gBAEAhB,kBAAkBiB,SAAS,GAAGC,IAAI,CAAC9E,QAAQ+E,MAAM;gBACjDnB,kBAAkBoB,SAAS,GAAGF,IAAI,CAAC9E,QAAQiF,MAAM;gBAEjD,OAAOrB;YACT;YAEA,MAAMsB,QAAQC,IAAAA,eAAM,EAClB,mEAAmE;YACnE,IAAI,CAAC/E,OAAO,CAACG,SAAS,GAClB8C,WACA7B,0BAA0B,IAC1BgC,kBACAH;YAEN,MAAM+B,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAASzD,gBAAiB;gBACnCwD,eAAeE,IAAI,CACjBJ,MAAM;oBACJ,MAAM,EAAE5D,IAAI,EAAEiC,WAAW,EAAEd,IAAI,EAAEI,IAAI,EAAE,GAAGwC;oBAC1C,IAAI,EAAEtC,MAAM,EAAE,GAAGsC;oBAEjB,MAAME,aAAapE,WAAWC,UAAU,CAAC;oBACzCmE,WAAWlE,YAAY,CAAC,QAAQC;oBAChCiE,WAAWlE,YAAY,CACrB,SACA,OAAO0B,WAAW,cAAc,SAAS;oBAG3C,OAAOwC,WAAWhE,YAAY,CAAC;wBAC7B,IAAI,CAACwB,QAAQ;4BACX,MAAM,EAAEH,QAAQ4C,qBAAqB,EAAE7C,KAAKuB,cAAc,EAAE,GAC1DX,YAAYkC,YAAY;4BAE1B,MAAMxB,QAAQyB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBrC,QAAQ,KAC9BqC;4BAEJ,MAAMpF,UAAU;gCACdkB;gCACA2C;gCACAC;gCACA7D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACpB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOwD,KAAKmD,gBAAgB,KAAK,aAAa;oCAChDxF,QAAQC,aAAa,CAACpB,MAAM,GAAGwD,KAAKmD,gBAAgB;gCACtD,OAAO,IAAI,iBAAiBxD,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBmD,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF8D,SAAS,MAAMc,YAAYC,MAAM,CAAC1D;4BACpC,EAAE,OAAOf,OAAO;gCACdqB,YAAYmF,MAAM,CAACP,IAAI,CAAClG,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAIyB,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI9B,gBAClBiC,OAAO+C,IAAI,EACXxE,MACAyB,OAAOJ,GAAG,EACVsB,OACAC,gBACA;4BAEJ,OAAO;gCACLnB,OAAOH,MAAM,GAAG,IAAI7B,UAAUgC,OAAO+C,IAAI;4BAC3C;4BAEA,MAAMjF,MAAMkF,YAAY,CAACzE,MAAMuB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMoD,UAAU;4BAAEtD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBrC,YAAYuF,WAAW,CAAC3E,MAAMsB,QAAQoD;oBACxC;gBACF;YAEJ;YAEA,MAAMnE,QAAQC,GAAG,CAACsD;YAElB,IAAIxB,mBAAmB;gBACrB,MAAMA,kBAAkBsC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAM1F,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAU2F,OAAO,qBAAjB3F,kBAAmB4F,OAAO,KAAIA,gBAAO;QAC5E,MAAM,EAAEtD,MAAM,EAAE,GAAGtC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAACiG,IAAI,KAAK,aAAa;YAC1D,IAAI,CAAClG,OAAO,CAACC,aAAa,CAACiG,IAAI,GAAG3H,eAAeoE,OAAOnE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAM2H,aAAa,IAAI,CAACpG,WAAW,CAACmB,IAAI;QACxC,MAAMqC,yBAAyB,IAAI,CAACvD,OAAO,CAACE,QAAQ;QAEpDG,SAAS+F,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAAC7F;YAC9C,MAAMG,QAAQH,YAAYiG,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJZ,gBAAO,CAACa,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DzG;YAEJsG,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEA3G,YAAY8F,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACElG,MAAMiF;gBACNkB,OAAOrB,gBAAO,CAACsB,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAAChH,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACEgD;gBACF,GACA9C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAY8F,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAChE,WAAgB,EAAEsF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCvF,YAAYsF,MAAMC,WAAW,gBAAgB9F;YAErD;QACF;IACF;AACF"}