{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts"], "names": ["hasInterceptionRouteInCurrentTree", "segment", "parallelRoutes", "Array", "isArray", "isInterceptionRouteAppPath", "key"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;oCAF2B;AAEpC,SAASA,kCAAkC,KAG9B;IAH8B,IAAA,CAChDC,SACAC,eACkB,GAH8B;IAIhD,wGAAwG;IACxG,IAAIC,MAAMC,OAAO,CAACH,YAAaA,CAAAA,OAAO,CAAC,EAAE,KAAK,QAAQA,OAAO,CAAC,EAAE,KAAK,IAAG,GAAI;QAC1E,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,OAAOA,YAAY,YAAYI,IAAAA,8CAA0B,EAACJ,UAAU;QACtE,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAIC,gBAAgB;QAClB,IAAK,MAAMI,OAAOJ,eAAgB;YAChC,IAAIF,kCAAkCE,cAAc,CAACI,IAAI,GAAG;gBAC1D,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT"}