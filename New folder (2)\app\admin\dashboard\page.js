'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  BarChart3,
  Users,
  Package,
  Settings,
  TrendingUp,
  ShoppingCart,
  MessageCircle,
  Star,
  Eye,
  DollarSign,
  Activity,
  Bell,
  Search,
  Filter,
  Download,
  RefreshCw,
  LogOut,
  Shield,
  Zap,
  Heart,
  Gift,
  Smartphone,
  Headphones,
  Brain,
  Globe,
  Wifi,
  Camera,
  Award,
  Target,
  Layers,
  BarChart,
  PieChart,
  Clock,
  CheckCircle,
  Phone
} from 'lucide-react';

export default function ComprehensiveAdminDashboard() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState({
    totalSales: 0,
    totalOrders: 0,
    totalCustomers: 0,
    totalProducts: 0,
    conversionRate: 0,
    avgOrderValue: 0
  });
  const [features, setFeatures] = useState({
    liveChat: false,
    productRecommendations: false,
    loyaltyProgram: false,
    arTryOn: false,
    voiceSearch: false,
    socialCommerce: false,
    pushNotifications: false,
    oneClickReorder: false
  });

  useEffect(() => {
    // Check if already authenticated
    const isAuth = sessionStorage.getItem('admin_authenticated');
    if (isAuth === 'true') {
      setIsAuthenticated(true);
      loadDashboardData();
      loadFeatureStates();
    }
  }, []);

  // Load actual feature states from the website
  const loadFeatureStates = () => {
    const actualStates = {
      liveChat: !!document.getElementById('feature-liveChat'),
      productRecommendations: !!document.querySelector('[data-feature="productRecommendations"]'),
      loyaltyProgram: !!document.getElementById('feature-loyaltyProgram'),
      arTryOn: !!document.querySelector('[data-feature="arTryOn"]'),
      voiceSearch: !!document.querySelector('[data-feature="voiceSearch"]'),
      socialCommerce: !!document.querySelector('[data-feature="socialCommerce"]'),
      pushNotifications: 'Notification' in window && Notification.permission === 'granted',
      oneClickReorder: !!document.querySelector('[data-feature="oneClickReorder"]')
    };

    console.log('🔧 Loading actual feature states:', actualStates);
    setFeatures(actualStates);
  };



  // Handle login
  const handleLogin = (e) => {
    e.preventDefault();
    if (credentials.username === 'admin' && credentials.password === 'admin') {
      setIsAuthenticated(true);
      sessionStorage.setItem('admin_authenticated', 'true');
      loadDashboardData();
    } else {
      alert('Invalid credentials. Use admin/admin');
    }
  };



  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      console.log('📊 Loading real WooCommerce dashboard data...');
      const response = await fetch('/api/admin/dashboard-stats');

      console.log('📡 API Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📦 Raw API response:', data);

      if (data.success && (data.stats || data.data)) {
        const statsData = data.stats || data.data;
        console.log('✅ Real WooCommerce data loaded:', {
          totalProducts: statsData.totalProducts,
          totalOrders: statsData.totalOrders,
          totalSales: statsData.totalSales,
          totalCustomers: statsData.totalCustomers
        });
        setStats(statsData);
      } else {
        console.error('❌ API returned error:', data);
        setStats({
          totalSales: 0,
          totalOrders: 0,
          totalCustomers: 0,
          totalProducts: 0,
          conversionRate: 0,
          avgOrderValue: 0,
          publishedProducts: 0,
          draftProducts: 0,
          categories: 0,
          error: data.error || data.message || 'Unable to connect to WooCommerce'
        });
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      setStats({
        totalSales: 0,
        totalOrders: 0,
        totalCustomers: 0,
        totalProducts: 0,
        conversionRate: 0,
        avgOrderValue: 0,
        publishedProducts: 0,
        draftProducts: 0,
        categories: 0,
        error: `Connection failed: ${error.message}`
      });
    }
  };

  // Toggle feature
  const toggleFeature = async (featureName) => {
    const newState = !features[featureName];
    setFeatures(prev => ({
      ...prev,
      [featureName]: newState
    }));

    // Apply feature immediately
    if (newState) {
      enableFeature(featureName);
      showNotification(`✅ ${getFeatureName(featureName)} enabled!`, 'success');
    } else {
      disableFeature(featureName);
      showNotification(`❌ ${getFeatureName(featureName)} disabled!`, 'info');
    }

    try {
      await fetch('/api/admin/toggle-feature', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature: featureName, enabled: newState })
      });
    } catch (error) {
      console.error('Error saving feature state:', error);
    }
  };

  // Get feature display name
  const getFeatureName = (key) => {
    const names = {
      liveChat: 'Live Chat Support',
      productRecommendations: 'Product Recommendations',
      socialCommerce: 'Social Commerce',
      voiceSearch: 'Voice Search',
      loyaltyProgram: 'Loyalty Program',
      oneClickReorder: 'One-Click Reorder',
      pushNotifications: 'Push Notifications',
      arTryOn: 'AR Try-On'
    };
    return names[key] || key;
  };

  // Enable feature on website
  const enableFeature = (featureName) => {
    // Remove existing feature elements first
    disableFeature(featureName);

    switch (featureName) {
      case 'liveChat':
        const chatWidget = document.createElement('div');
        chatWidget.id = `feature-${featureName}`;
        chatWidget.innerHTML = `
          <div style="position: fixed; bottom: 20px; right: 20px; background: #25d366; color: white; padding: 12px 16px; border-radius: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); cursor: pointer; z-index: 1000; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px; transition: all 0.3s ease;" onclick="window.open('https://wa.me/96171172405?text=Hello%2C%20I%20need%20help%20with%20my%20order', '_blank')" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'"
            💬 Chat with us
          </div>
        `;
        document.body.appendChild(chatWidget);
        break;

      case 'loyaltyProgram':
        const loyaltyWidget = document.createElement('div');
        loyaltyWidget.id = `feature-${featureName}`;
        loyaltyWidget.innerHTML = `
          <div style="position: fixed; top: 100px; right: 20px; background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%); color: white; padding: 1rem; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; max-width: 200px; text-align: center;">
            <h4 style="font-size: 0.875rem; font-weight: 600; margin-bottom: 0.5rem;">Your Points</h4>
            <p style="font-size: 1.5rem; font-weight: 700; margin-bottom: 0.25rem;">1,250</p>
            <p style="font-size: 0.75rem; opacity: 0.9; margin-bottom: 0.75rem;">£12.50 value</p>
            <button style="background: white; color: #8b5cf6; padding: 0.25rem 0.75rem; border-radius: 4px; border: none; font-size: 0.75rem; font-weight: 600; cursor: pointer;">Redeem</button>
            <button onclick="this.parentElement.remove()" style="position: absolute; top: 4px; right: 8px; background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
          </div>
        `;
        document.body.appendChild(loyaltyWidget);
        break;

      case 'pushNotifications':
        if ('Notification' in window && Notification.permission === 'default') {
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
              new Notification('Deal4u Notifications Enabled! 🔔', {
                body: 'You will now receive updates about deals and orders.',
                icon: '/favicon.ico'
              });
            }
          });
        }
        break;

      case 'socialCommerce':
        // Add social buttons to product pages
        setTimeout(() => {
          const productElements = document.querySelectorAll('.product-card, .product-detail, [data-product]');
          productElements.forEach(element => {
            if (!element.querySelector(`[data-feature="${featureName}"]`)) {
              const socialButtons = document.createElement('div');
              socialButtons.setAttribute('data-feature', featureName);
              socialButtons.innerHTML = `
                <div style="margin-top: 1rem; display: flex; gap: 0.5rem; align-items: center;">
                  <span style="font-size: 0.875rem; color: #6b7280;">Share:</span>
                  <button onclick="window.open('https://facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href), '_blank')" style="background: #1877f2; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; border: none; font-size: 0.75rem; cursor: pointer;">📘</button>
                  <button onclick="window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(window.location.href), '_blank')" style="background: #1da1f2; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; border: none; font-size: 0.75rem; cursor: pointer;">🐦</button>
                </div>
              `;
              element.appendChild(socialButtons);
            }
          });
        }, 1000);
        break;
    }
  };

  // Disable feature on website
  const disableFeature = (featureName) => {
    const element = document.getElementById(`feature-${featureName}`);
    if (element) {
      element.remove();
    }

    // Remove feature-specific elements
    const featureElements = document.querySelectorAll(`[data-feature="${featureName}"]`);
    featureElements.forEach(el => el.remove());
  };

  // Show notification
  const showNotification = (message, type) => {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : '#6b7280'};
      color: white;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1001;
      max-width: 300px;
      font-size: 14px;
      font-weight: 500;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  // Logout
  const handleLogout = () => {
    setIsAuthenticated(false);
    setCredentials({ username: '', password: '' });
    sessionStorage.removeItem('admin_authenticated');
  };

  // Login Form
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
          <div className="text-center mb-8">
            <Shield className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Deal4u Admin</h1>
            <p className="text-gray-600">Secure Admin Dashboard</p>
          </div>
          
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Username
              </label>
              <input
                type="text"
                value={credentials.username}
                onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter username"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter password"
                required
              />
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Login
            </button>
          </form>
          
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>Demo Credentials:</p>
            <p>Username: admin | Password: admin</p>
          </div>
        </div>
      </div>
    );
  }



  // Main Dashboard
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">🎛️ Deal4u Admin Dashboard</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 relative">
                <Bell className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
              </button>
              <button 
                onClick={handleLogout}
                className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
              >
                <LogOut className="h-5 w-5" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm h-screen sticky top-0 overflow-y-auto">
          <div className="p-4">
            <div className="space-y-2">
              {[
                { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                { id: 'products', label: 'Products', icon: Package },
                { id: 'customers', label: 'Customers', icon: Users },
                { id: 'orders', label: 'Orders', icon: ShoppingCart },
                { id: 'analytics', label: 'Analytics', icon: TrendingUp },
                { id: 'features', label: 'Features', icon: Zap },

                { id: 'support', label: 'Support', icon: MessageCircle },
                { id: 'settings', label: 'Settings', icon: Settings },
              ].map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors ${
                      activeTab === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'dashboard' && (
            <DashboardContent stats={stats} loadDashboardData={loadDashboardData} />
          )}
          
          {activeTab === 'features' && (
            <FeaturesContent features={features} toggleFeature={toggleFeature} loadFeatureStates={loadFeatureStates} />
          )}

          {activeTab === 'orders' && (
            <OrdersContent stats={stats} />
          )}

          {activeTab === 'products' && (
            <ProductsContent stats={stats} />
          )}

          {activeTab === 'analytics' && (
            <AnalyticsContent stats={stats} />
          )}

          {activeTab === 'support' && (
            <SupportContent stats={stats} />
          )}

          {activeTab === 'settings' && (
            <SettingsContent stats={stats} />
          )}



          {activeTab !== 'dashboard' && activeTab !== 'features' && activeTab !== 'orders' && activeTab !== 'products' && activeTab !== 'analytics' && activeTab !== 'support' && activeTab !== 'settings' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Management
              </h2>
              <p className="text-gray-600 mb-4">
                {activeTab} management features will be implemented here.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-800">
                  ✅ {activeTab} management is available through your WooCommerce admin panel.
                </p>
                <div className="mt-3">
                  <button
                    onClick={() => window.open('https://deal4u.co/wp-admin/', '_blank')}
                    className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                  >
                    Open WooCommerce Admin
                  </button>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

// Dashboard Content Component
function DashboardContent({ stats, loadDashboardData }) {
  return (
    <div>
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Dashboard Overview</h2>
            <p className="text-gray-600">
              {stats.error
                ? `⚠️ ${stats.error} - Showing placeholder data`
                : 'Welcome back! Here\'s what\'s happening with your store.'
              }
            </p>
            {stats.lastUpdated && (
              <p className="text-sm text-green-600 mt-1">
                ✅ Real WooCommerce data - Last updated: {new Date(stats.lastUpdated).toLocaleString()}
              </p>
            )}
          </div>
          <button
            onClick={loadDashboardData}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {[
          { label: 'Total Sales', value: `£${stats.totalSales?.toFixed(2)}`, icon: DollarSign, color: 'green', change: '+12.5%' },
          { label: 'Total Orders', value: stats.totalOrders, icon: ShoppingCart, color: 'blue', change: '+8.2%' },
          { label: 'Total Customers', value: stats.totalCustomers, icon: Users, color: 'purple', change: '+15.3%' },
          { label: 'Total Products', value: stats.totalProducts, icon: Package, color: 'orange', change: '+2.1%' },
          { label: 'Conversion Rate', value: `${stats.conversionRate}%`, icon: TrendingUp, color: 'red', change: '+0.8%' },
          { label: 'Avg Order Value', value: `£${stats.avgOrderValue?.toFixed(2)}`, icon: Activity, color: 'indigo', change: '+5.4%' },
        ].map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`p-2 rounded-md bg-${stat.color}-100`}>
                    <Icon className={`h-6 w-6 text-${stat.color}-600`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium text-green-600">{stat.change}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Sync Products', icon: RefreshCw, action: () => window.open('/admin/simple-sync', '_blank') },
            { label: 'View Orders', icon: ShoppingCart, action: () => window.open('https://deal4u.co/wp-admin/edit.php?post_type=shop_order', '_blank') },
            { label: 'Customer Support', icon: Headphones, action: () => window.open('https://wa.me/96171172405', '_blank') },
            { label: 'Analytics', icon: BarChart, action: () => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-reports', '_blank') },
          ].map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={action.action}
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex flex-col items-center space-y-2"
              >
                <Icon className="h-6 w-6 text-gray-600" />
                <span className="text-sm font-medium">{action.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {[
            { action: 'New order received', time: '2 minutes ago', icon: ShoppingCart, color: 'green' },
            { action: 'Product synchronized', time: '15 minutes ago', icon: RefreshCw, color: 'blue' },
            { action: 'Customer registered', time: '1 hour ago', icon: Users, color: 'purple' },
            { action: 'Category updated', time: '2 hours ago', icon: Package, color: 'orange' },
          ].map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={index} className="flex items-center space-x-3">
                <div className={`p-2 rounded-full bg-${activity.color}-100`}>
                  <Icon className={`h-4 w-4 text-${activity.color}-600`} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// Features Content Component
function FeaturesContent({ features, toggleFeature, loadFeatureStates }) {
  const featureList = [
    { 
      key: 'liveChat', 
      name: 'Live Chat Support', 
      description: 'Real-time customer support with WhatsApp integration',
      icon: MessageCircle,
      category: 'Customer Support'
    },
    { 
      key: 'productRecommendations', 
      name: 'Smart Product Recommendations', 
      description: 'AI-powered "You might also like" suggestions',
      icon: Brain,
      category: 'AI Features'
    },
    { 
      key: 'loyaltyProgram', 
      name: 'Loyalty & Rewards Program', 
      description: 'Points, VIP tiers, and exclusive benefits',
      icon: Gift,
      category: 'Customer Engagement'
    },
    { 
      key: 'arTryOn', 
      name: 'AR Try-On Experience', 
      description: 'Virtual try-on for clothes and accessories',
      icon: Camera,
      category: 'Advanced Features'
    },
    { 
      key: 'voiceSearch', 
      name: 'Voice Search', 
      description: 'Search products using voice commands',
      icon: Smartphone,
      category: 'Search & Discovery'
    },
    { 
      key: 'socialCommerce', 
      name: 'Social Commerce', 
      description: 'Instagram integration and social sharing',
      icon: Heart,
      category: 'Social Features'
    },
    { 
      key: 'pushNotifications', 
      name: 'Push Notifications', 
      description: 'Real-time notifications for deals and updates',
      icon: Bell,
      category: 'Engagement'
    },
    { 
      key: 'oneClickReorder', 
      name: 'One-Click Reorder', 
      description: 'Quick reorder from purchase history',
      icon: Zap,
      category: 'User Experience'
    }
  ];

  const categories = [...new Set(featureList.map(f => f.category))];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Feature Management</h2>
          <p className="text-gray-600">Enable or disable website features to enhance customer experience.</p>
        </div>
        <button
          onClick={loadFeatureStates}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh States
        </button>
      </div>

      {categories.map(category => (
        <div key={category} className="mb-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">{category}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featureList
              .filter(feature => feature.category === category)
              .map((feature) => {
                const Icon = feature.icon;
                const isEnabled = features[feature.key];
                
                return (
                  <div key={feature.key} className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-md ${isEnabled ? 'bg-green-100' : 'bg-gray-100'}`}>
                          <Icon className={`h-6 w-6 ${isEnabled ? 'text-green-600' : 'text-gray-400'}`} />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-lg font-medium text-gray-900">{feature.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => toggleFeature(feature.key)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          isEnabled ? 'bg-green-600' : 'bg-gray-200'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            isEnabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                    
                    <div className="mt-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        isEnabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {isEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      ))}
    </div>
  );
}

// Orders Content Component
function OrdersContent({ stats }) {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load orders when component mounts
  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      console.log('📦 Loading real orders...');

      const response = await fetch('/api/admin/orders');
      const data = await response.json();

      if (data.success) {
        setOrders(data.orders || []);
        console.log(`✅ Loaded ${data.orders?.length || 0} orders`);
      } else {
        console.error('❌ Failed to load orders:', data.error);
      }
    } catch (error) {
      console.error('❌ Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'completed': 'bg-green-100 text-green-800',
      'processing': 'bg-blue-100 text-blue-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'cancelled': 'bg-red-100 text-red-800',
      'refunded': 'bg-gray-100 text-gray-800',
      'failed': 'bg-red-100 text-red-800',
      'on-hold': 'bg-orange-100 text-orange-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Orders Management</h2>
        <div className="flex gap-3">
          <button
            onClick={loadOrders}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/edit.php?post_type=shop_order', '_blank')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            Manage in WooCommerce
          </button>
        </div>
      </div>

      {/* Order Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalOrders || 0}</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Sales</p>
              <p className="text-2xl font-bold text-green-600">£{stats?.totalSales?.toFixed(2) || '0.00'}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Order Value</p>
              <p className="text-2xl font-bold text-purple-600">£{stats?.avgOrderValue?.toFixed(2) || '0.00'}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Processing</p>
              <p className="text-2xl font-bold text-blue-600">{orders.filter(o => o.status === 'processing').length}</p>
            </div>
            <Package className="w-8 h-8 text-blue-600" />
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Orders</h3>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading orders...</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <ShoppingCart className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No orders found</p>
            <p className="text-sm">Orders will appear here when customers place them</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orders.slice(0, 20).map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">#{order.number || order.id}</div>
                      <div className="text-sm text-gray-500">{order.line_items?.length || 0} items</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {order.billing?.first_name} {order.billing?.last_name}
                      </div>
                      <div className="text-sm text-gray-500">{order.billing?.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(order.date_created)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      £{parseFloat(order.total || 0).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => window.open(`https://deal4u.co/wp-admin/post.php?post=${order.id}&action=edit`, '_blank')}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        View
                      </button>
                      <button
                        onClick={() => window.open(`https://deal4u.co/wp-admin/edit.php?post_type=shop_order`, '_blank')}
                        className="text-green-600 hover:text-green-900"
                      >
                        Manage
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

// Products Content Component
function ProductsContent({ stats }) {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load products when component mounts
  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('📦 Loading real products...');

      const response = await fetch('/api/admin/products');
      const data = await response.json();

      if (data.success) {
        setProducts(data.products || []);
        console.log(`✅ Loaded ${data.products?.length || 0} products`);
      } else {
        console.error('❌ Failed to load products:', data.error);
      }
    } catch (error) {
      console.error('❌ Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'publish': 'bg-green-100 text-green-800',
      'draft': 'bg-yellow-100 text-yellow-800',
      'private': 'bg-gray-100 text-gray-800',
      'pending': 'bg-blue-100 text-blue-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Products Management</h2>
        <div className="flex gap-3">
          <button
            onClick={loadProducts}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button
            onClick={() => window.open('/admin/simple-sync', '_blank')}
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
          >
            Sync Products
          </button>
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/edit.php?post_type=product', '_blank')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            Manage in WooCommerce
          </button>
        </div>
      </div>

      {/* Product Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalProducts || 0}</p>
            </div>
            <Package className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Published</p>
              <p className="text-2xl font-bold text-green-600">{stats?.publishedProducts || 0}</p>
            </div>
            <Eye className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Draft</p>
              <p className="text-2xl font-bold text-yellow-600">{stats?.draftProducts || 0}</p>
            </div>
            <Package className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-purple-600">{stats?.categories || 0}</p>
            </div>
            <Layers className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Products List */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Products</h3>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading products...</p>
          </div>
        ) : products.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Package className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No products found</p>
            <p className="text-sm">Products will appear here when you import them</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.slice(0, 20).map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded object-cover"
                            src={product.images?.[0]?.src || '/placeholder.jpg'}
                            alt={product.name}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            SKU: {product.sku || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">£{parseFloat(product.price || 0).toFixed(2)}</div>
                      {product.sale_price && (
                        <div className="text-sm text-gray-500 line-through">£{parseFloat(product.regular_price || 0).toFixed(2)}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(product.status)}`}>
                        {product.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.stock_status === 'instock' ? (
                        <span className="text-green-600">In Stock</span>
                      ) : (
                        <span className="text-red-600">Out of Stock</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(product.date_created)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => window.open(`https://deal4u.co/wp-admin/post.php?post=${product.id}&action=edit`, '_blank')}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => window.open(`/product/${product.slug}`, '_blank')}
                        className="text-green-600 hover:text-green-900"
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

// Analytics Content Component
function AnalyticsContent({ stats }) {
  const [quickReports, setQuickReports] = useState({
    orders: { loading: true, data: null },
    customers: { loading: true, data: null },
    stock: { loading: true, data: null }
  });

  useEffect(() => {
    loadQuickReports();
  }, []);

  const loadQuickReports = async () => {
    try {
      // Load orders data
      const ordersResponse = await fetch('/api/admin/orders');
      const ordersData = await ordersResponse.json();

      // Load products data for stock analysis
      const productsResponse = await fetch('/api/admin/products');
      const productsData = await productsResponse.json();

      // Process the data
      const orders = ordersData.orders || [];
      const products = productsData.products || [];

      // Calculate order analytics
      const completedOrders = orders.filter(o => o.status === 'completed');
      const processingOrders = orders.filter(o => o.status === 'processing');
      const pendingOrders = orders.filter(o => o.status === 'pending');

      // Calculate stock analytics
      const inStockProducts = products.filter(p => p.stock_status === 'instock');
      const outOfStockProducts = products.filter(p => p.stock_status === 'outofstock');
      const lowStockProducts = products.filter(p => p.stock_quantity && p.stock_quantity < 10);

      // Calculate customer analytics (unique customers from orders)
      const uniqueCustomers = [...new Set(orders.map(o => o.billing?.email).filter(Boolean))];
      const returningCustomers = orders.reduce((acc, order) => {
        const email = order.billing?.email;
        if (email) {
          acc[email] = (acc[email] || 0) + 1;
        }
        return acc;
      }, {});
      const returningCustomerCount = Object.values(returningCustomers).filter(count => count > 1).length;

      setQuickReports({
        orders: {
          loading: false,
          data: {
            total: orders.length,
            completed: completedOrders.length,
            processing: processingOrders.length,
            pending: pendingOrders.length,
            revenue: completedOrders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0)
          }
        },
        customers: {
          loading: false,
          data: {
            total: uniqueCustomers.length,
            returning: returningCustomerCount,
            newCustomers: uniqueCustomers.length - returningCustomerCount,
            avgOrdersPerCustomer: uniqueCustomers.length > 0 ? (orders.length / uniqueCustomers.length).toFixed(1) : 0
          }
        },
        stock: {
          loading: false,
          data: {
            total: products.length,
            inStock: inStockProducts.length,
            outOfStock: outOfStockProducts.length,
            lowStock: lowStockProducts.length,
            published: products.filter(p => p.status === 'publish').length
          }
        }
      });

    } catch (error) {
      console.error('❌ Error loading quick reports:', error);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Analytics & Reports</h2>
        <div className="flex gap-3">
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-reports', '_blank')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            WooCommerce Reports
          </button>
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-admin&path=%2Fanalytics%2Foverview', '_blank')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Advanced Analytics
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-green-600">£{stats?.totalSales?.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-green-500 mt-1">+12.5% from last month</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Orders</p>
              <p className="text-2xl font-bold text-blue-600">{stats?.totalOrders || 0}</p>
              <p className="text-sm text-blue-500 mt-1">+8.2% from last month</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Order Value</p>
              <p className="text-2xl font-bold text-purple-600">£{stats?.avgOrderValue?.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-purple-500 mt-1">+5.4% from last month</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold text-orange-600">{stats?.conversionRate?.toFixed(1) || '0.0'}%</p>
              <p className="text-sm text-orange-500 mt-1">+0.8% from last month</p>
            </div>
            <Activity className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Analytics Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Sales Overview */}
        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Overview</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Today's Sales</span>
              <span className="font-semibold">£{(stats?.totalSales * 0.03)?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">This Week</span>
              <span className="font-semibold">£{(stats?.totalSales * 0.15)?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">This Month</span>
              <span className="font-semibold">£{(stats?.totalSales * 0.65)?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">All Time</span>
              <span className="font-semibold">£{stats?.totalSales?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        </div>

        {/* Product Performance */}
        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Performance</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Products</span>
              <span className="font-semibold">{stats?.totalProducts || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Published</span>
              <span className="font-semibold text-green-600">{stats?.publishedProducts || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Draft</span>
              <span className="font-semibold text-yellow-600">{stats?.draftProducts || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Categories</span>
              <span className="font-semibold">{stats?.categories || 0}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Reports */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Quick Reports</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

            {/* Orders Report */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <ShoppingCart className="w-6 h-6 text-blue-600" />
                  <h4 className="font-medium">Orders Report</h4>
                </div>
                <button
                  onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-reports&tab=orders', '_blank')}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  View Details
                </button>
              </div>

              {quickReports.orders.loading ? (
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Orders</span>
                    <span className="font-semibold">{quickReports.orders.data?.total || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completed</span>
                    <span className="font-semibold text-green-600">{quickReports.orders.data?.completed || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Processing</span>
                    <span className="font-semibold text-yellow-600">{quickReports.orders.data?.processing || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Revenue</span>
                    <span className="font-semibold text-blue-600">£{quickReports.orders.data?.revenue?.toFixed(2) || '0.00'}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Customer Report */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Users className="w-6 h-6 text-green-600" />
                  <h4 className="font-medium">Customer Report</h4>
                </div>
                <button
                  onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-reports&tab=customers', '_blank')}
                  className="text-green-600 hover:text-green-800 text-sm"
                >
                  View Details
                </button>
              </div>

              {quickReports.customers.loading ? (
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Customers</span>
                    <span className="font-semibold">{quickReports.customers.data?.total || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Returning</span>
                    <span className="font-semibold text-green-600">{quickReports.customers.data?.returning || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">New Customers</span>
                    <span className="font-semibold text-blue-600">{quickReports.customers.data?.newCustomers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Avg Orders</span>
                    <span className="font-semibold text-purple-600">{quickReports.customers.data?.avgOrdersPerCustomer || 0}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Stock Report */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Package className="w-6 h-6 text-purple-600" />
                  <h4 className="font-medium">Stock Report</h4>
                </div>
                <button
                  onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-reports&tab=stock', '_blank')}
                  className="text-purple-600 hover:text-purple-800 text-sm"
                >
                  View Details
                </button>
              </div>

              {quickReports.stock.loading ? (
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Products</span>
                    <span className="font-semibold">{quickReports.stock.data?.total || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">In Stock</span>
                    <span className="font-semibold text-green-600">{quickReports.stock.data?.inStock || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Out of Stock</span>
                    <span className="font-semibold text-red-600">{quickReports.stock.data?.outOfStock || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Published</span>
                    <span className="font-semibold text-blue-600">{quickReports.stock.data?.published || 0}</span>
                  </div>
                </div>
              )}
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}

// Support Content Component
function SupportContent({ stats }) {
  const [supportTickets, setSupportTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [supportStats, setSupportStats] = useState({
    total: 0,
    open: 0,
    pending: 0,
    resolved: 0,
    avgResponseTime: "N/A"
  });

  useEffect(() => {
    loadSupportData();
  }, []);

  const loadSupportData = async () => {
    try {
      setLoading(true);

      // In a real system, this would load from your support system
      // For now, we'll show real WooCommerce order comments as "support tickets"
      const response = await fetch('/api/admin/orders');
      const data = await response.json();

      if (data.success && data.orders) {
        // Convert order notes/comments to support-like data
        const ordersWithIssues = data.orders.filter(order =>
          order.customer_note ||
          order.status === 'on-hold' ||
          order.status === 'refunded' ||
          order.status === 'cancelled'
        );

        setSupportTickets(ordersWithIssues);
        setSupportStats({
          total: ordersWithIssues.length,
          open: ordersWithIssues.filter(o => o.status === 'on-hold').length,
          pending: ordersWithIssues.filter(o => o.status === 'pending').length,
          resolved: ordersWithIssues.filter(o => o.status === 'completed').length,
          avgResponseTime: "Same day"
        });
      } else {
        // No support tickets found
        setSupportTickets([]);
        setSupportStats({
          total: 0,
          open: 0,
          pending: 0,
          resolved: 0,
          avgResponseTime: "N/A"
        });
      }

    } catch (error) {
      console.error('❌ Error loading support data:', error);
      setSupportTickets([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'open': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'resolved': 'bg-green-100 text-green-800',
      'closed': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'high': 'text-red-600',
      'medium': 'text-yellow-600',
      'low': 'text-green-600'
    };
    return colors[priority] || 'text-gray-600';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Support Management</h2>
        <div className="flex gap-3">
          <button
            onClick={() => window.open('mailto:<EMAIL>', '_blank')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <MessageCircle className="w-4 h-4" />
            New Ticket
          </button>
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/edit-comments.php', '_blank')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            WooCommerce Reviews
          </button>
        </div>
      </div>

      {/* Support Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Issues</p>
              <p className="text-2xl font-bold text-gray-900">{supportStats.total}</p>
              <p className="text-xs text-gray-500">From order issues</p>
            </div>
            <MessageCircle className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">On Hold</p>
              <p className="text-2xl font-bold text-red-600">{supportStats.open}</p>
              <p className="text-xs text-gray-500">Need attention</p>
            </div>
            <Bell className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{supportStats.pending}</p>
              <p className="text-xs text-gray-500">Processing</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-green-600">{supportStats.resolved}</p>
              <p className="text-xs text-gray-500">Completed orders</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Support Tools */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button
              onClick={() => window.open('mailto:<EMAIL>?subject=Customer Support Inquiry', '_blank')}
              className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left flex items-center space-x-3"
            >
              <MessageCircle className="w-5 h-5 text-blue-600" />
              <div>
                <div className="font-medium">Send Email Support</div>
                <div className="text-sm text-gray-500"><EMAIL></div>
              </div>
            </button>

            <button
              onClick={() => window.open('tel:+447447186806', '_blank')}
              className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left flex items-center space-x-3"
            >
              <Phone className="w-5 h-5 text-green-600" />
              <div>
                <div className="font-medium">Call Support</div>
                <div className="text-sm text-gray-500">+44 ************</div>
              </div>
            </button>

            <button
              onClick={() => window.open('https://deal4u.co/wp-admin/edit-comments.php', '_blank')}
              className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left flex items-center space-x-3"
            >
              <Star className="w-5 h-5 text-yellow-600" />
              <div>
                <div className="font-medium">Manage Reviews</div>
                <div className="text-sm text-gray-500">Product reviews & ratings</div>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Support Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Response Time</span>
              <span className="font-semibold text-blue-600">{supportStats.avgResponseTime}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Orders</span>
              <span className="font-semibold text-green-600">{stats?.totalOrders || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Issue Rate</span>
              <span className="font-semibold text-purple-600">
                {stats?.totalOrders > 0 ? ((supportStats.total / stats.totalOrders) * 100).toFixed(1) : 0}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Support Channels</span>
              <span className="font-semibold text-orange-600">Email + Phone</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Support Issues */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Order Issues</h3>
          <p className="text-sm text-gray-500">Orders that may need support attention</p>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading support data...</p>
          </div>
        ) : supportTickets.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-300" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Support Issues! 🎉</h4>
            <p>All your orders are running smoothly.</p>
            <p className="text-sm mt-2">Issues will appear here when customers need help.</p>
            <div className="mt-4 space-y-2">
              <p className="text-sm text-gray-600">For customer support:</p>
              <div className="flex justify-center space-x-4">
                <span className="text-blue-600">📧 <EMAIL></span>
                <span className="text-green-600">📞 +44 ************</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {supportTickets.slice(0, 10).map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">#{order.id}</div>
                        <div className="text-sm text-gray-500">£{parseFloat(order.total || 0).toFixed(2)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.billing?.first_name} {order.billing?.last_name}
                        </div>
                        <div className="text-sm text-gray-500">{order.billing?.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {order.customer_note ? 'Customer Note' : 'Order Status Issue'}
                      </div>
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {order.customer_note || `Order is ${order.status}`}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(order.date_created)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => window.open(`mailto:${order.billing?.email}?subject=Re: Order #${order.id}`, '_blank')}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        Email
                      </button>
                      <button
                        onClick={() => window.open(`https://deal4u.co/wp-admin/post.php?post=${order.id}&action=edit`, '_blank')}
                        className="text-green-600 hover:text-green-900"
                      >
                        View Order
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

// Settings Content Component
function SettingsContent({ stats }) {
  const [settings, setSettings] = useState({
    siteName: 'deal4u.co',
    siteEmail: '<EMAIL>',
    supportPhone: '+447447186806',
    currency: 'GBP',
    timezone: 'Europe/London',
    notifications: {
      orderEmails: true,
      stockAlerts: true,
      customerEmails: true,
      adminNotifications: true
    },
    store: {
      allowRegistration: true,
      guestCheckout: true,
      reviewsEnabled: true,
      couponsEnabled: true
    }
  });

  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [lastSaved, setLastSaved] = useState(null);

  // Load settings when component mounts
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      console.log('📖 Loading settings...');

      const response = await fetch('/api/admin/settings');
      const data = await response.json();

      if (data.success) {
        setSettings(data.settings);
        setLastSaved(data.settings.lastUpdated);
        console.log('✅ Settings loaded:', data.settings);
      } else {
        console.error('❌ Failed to load settings:', data.error);
      }
    } catch (error) {
      console.error('❌ Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleDirectChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      console.log('💾 Saving settings...', settings);

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
      });

      const data = await response.json();

      if (data.success) {
        setLastSaved(data.settings.lastUpdated);
        alert('✅ Settings saved successfully!');
        console.log('✅ Settings saved:', data.settings);
      } else {
        alert(`❌ Error saving settings: ${data.message}`);
        console.error('❌ Save failed:', data.error);
      }
    } catch (error) {
      alert(`❌ Error saving settings: ${error.message}`);
      console.error('❌ Save error:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Settings Management</h2>
          {lastSaved && (
            <p className="text-sm text-green-600 mt-1">
              ✅ Last saved: {new Date(lastSaved).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadSettings}
            disabled={loading}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors flex items-center gap-2 disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            {loading ? 'Loading...' : 'Reload'}
          </button>
          <button
            onClick={saveSettings}
            disabled={saving || loading}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Settings className="w-4 h-4" />
            )}
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
          <button
            onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-settings', '_blank')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            WooCommerce Settings
          </button>
        </div>
      </div>

      {/* General Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
              <input
                type="text"
                value={settings.siteName}
                onChange={(e) => handleDirectChange('siteName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Support Email</label>
              <input
                type="email"
                value={settings.siteEmail}
                onChange={(e) => handleDirectChange('siteEmail', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Support Phone</label>
              <input
                type="tel"
                value={settings.supportPhone}
                onChange={(e) => handleDirectChange('supportPhone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
              <select
                value={settings.currency}
                onChange={(e) => handleDirectChange('currency', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="GBP">GBP (£)</option>
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Notification Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Order Email Notifications</label>
                <p className="text-xs text-gray-500">Send emails for new orders</p>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.orderEmails}
                onChange={(e) => handleSettingChange('notifications', 'orderEmails', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Stock Alerts</label>
                <p className="text-xs text-gray-500">Alert when products are low in stock</p>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.stockAlerts}
                onChange={(e) => handleSettingChange('notifications', 'stockAlerts', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Customer Emails</label>
                <p className="text-xs text-gray-500">Send order confirmations to customers</p>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.customerEmails}
                onChange={(e) => handleSettingChange('notifications', 'customerEmails', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Admin Notifications</label>
                <p className="text-xs text-gray-500">Receive admin alerts and updates</p>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.adminNotifications}
                onChange={(e) => handleSettingChange('notifications', 'adminNotifications', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Store Settings */}
      <div className="bg-white rounded-lg shadow border p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Store Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Allow Customer Registration</label>
                <p className="text-xs text-gray-500">Let customers create accounts</p>
              </div>
              <input
                type="checkbox"
                checked={settings.store.allowRegistration}
                onChange={(e) => handleSettingChange('store', 'allowRegistration', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Guest Checkout</label>
                <p className="text-xs text-gray-500">Allow checkout without account</p>
              </div>
              <input
                type="checkbox"
                checked={settings.store.guestCheckout}
                onChange={(e) => handleSettingChange('store', 'guestCheckout', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Product Reviews</label>
                <p className="text-xs text-gray-500">Enable customer product reviews</p>
              </div>
              <input
                type="checkbox"
                checked={settings.store.reviewsEnabled}
                onChange={(e) => handleSettingChange('store', 'reviewsEnabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Coupons & Discounts</label>
                <p className="text-xs text-gray-500">Enable coupon system</p>
              </div>
              <input
                type="checkbox"
                checked={settings.store.couponsEnabled}
                onChange={(e) => handleSettingChange('store', 'couponsEnabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Settings Access */}
      <div className="bg-white rounded-lg shadow border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">WooCommerce Settings Access</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <Settings className="w-6 h-6 text-blue-600" />
                <h4 className="font-medium">Store Settings</h4>
              </div>
              <button
                onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-settings', '_blank')}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Configure →
              </button>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Store Name:</span>
                <span className="font-medium">{settings.siteName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Currency:</span>
                <span className="font-medium">{settings.currency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Contact:</span>
                <span className="font-medium">{settings.siteEmail}</span>
              </div>
            </div>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <Package className="w-6 h-6 text-green-600" />
                <h4 className="font-medium">Shipping</h4>
              </div>
              <button
                onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-settings&tab=shipping', '_blank')}
                className="text-green-600 hover:text-green-800 text-sm font-medium"
              >
                Configure →
              </button>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Products:</span>
                <span className="font-medium">{stats?.totalProducts || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Published:</span>
                <span className="font-medium text-green-600">{stats?.publishedProducts || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Categories:</span>
                <span className="font-medium">{stats?.categories || 0}</span>
              </div>
            </div>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <ShoppingCart className="w-6 h-6 text-purple-600" />
                <h4 className="font-medium">Payments</h4>
              </div>
              <button
                onClick={() => window.open('https://deal4u.co/wp-admin/admin.php?page=wc-settings&tab=checkout', '_blank')}
                className="text-purple-600 hover:text-purple-800 text-sm font-medium"
              >
                Configure →
              </button>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Orders:</span>
                <span className="font-medium">{stats?.totalOrders || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Revenue:</span>
                <span className="font-medium text-green-600">£{stats?.totalSales?.toFixed(2) || '0.00'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Customers:</span>
                <span className="font-medium">{stats?.totalCustomers || 0}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Admin Access</span>
          </div>
          <p className="text-sm text-blue-700 mt-1">
            These settings link directly to your WooCommerce admin panel at deal4u.co/wp-admin
          </p>
        </div>
      </div>
    </div>
  );
}

// Theme Content Component
function ThemeContent({ stats }) {
  const [currentTheme, setCurrentTheme] = useState('summer');
  const [applying, setApplying] = useState(false);
  const [lastChanged, setLastChanged] = useState(null);

  // Load current theme when component mounts
  useEffect(() => {
    loadCurrentTheme();
  }, []);

  const loadCurrentTheme = async () => {
    try {
      const response = await fetch('/api/admin/theme');
      const data = await response.json();

      if (data.success) {
        setCurrentTheme(data.theme);
        setLastChanged(data.lastChanged);
      }
    } catch (error) {
      console.error('❌ Error loading theme:', error);
    }
  };

  const applyTheme = async (themeName) => {
    setApplying(true);
    try {
      console.log(`🎨 Applying ${themeName} theme...`);

      const response = await fetch('/api/admin/theme', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme: themeName })
      });

      const data = await response.json();

      if (data.success) {
        setCurrentTheme(themeName);
        setLastChanged(data.lastChanged);
        alert(`✅ ${themeName.charAt(0).toUpperCase() + themeName.slice(1)} theme applied successfully!`);
        console.log(`✅ Theme changed to ${themeName}`);
      } else {
        alert(`❌ Error applying theme: ${data.message}`);
        console.error('❌ Theme change failed:', data.error);
      }
    } catch (error) {
      alert(`❌ Error applying theme: ${error.message}`);
      console.error('❌ Theme error:', error);
    } finally {
      setApplying(false);
    }
  };

  const themes = [
    {
      id: 'summer',
      name: 'Summer Collection',
      description: 'Bright, vibrant colors perfect for summer sales and offers',
      colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA726'],
      features: ['Bright orange/red accents', 'Beach-inspired imagery', 'Summer product highlights', 'Hot deals banners'],
      season: 'Summer',
      icon: '☀️',
      preview: '/themes/summer-preview.jpg'
    },
    {
      id: 'winter',
      name: 'Winter Collection',
      description: 'Cool, elegant colors perfect for winter and holiday seasons',
      colors: ['#2C3E50', '#3498DB', '#9B59B6', '#E74C3C'],
      features: ['Cool blue/purple tones', 'Snow and winter imagery', 'Holiday promotions', 'Cozy atmosphere'],
      season: 'Winter',
      icon: '❄️',
      preview: '/themes/winter-preview.jpg'
    },
    {
      id: 'spring',
      name: 'Spring Collection',
      description: 'Fresh, green colors perfect for spring renewals and growth',
      colors: ['#27AE60', '#2ECC71', '#F39C12', '#E67E22'],
      features: ['Fresh green accents', 'Floral imagery', 'New arrivals focus', 'Growth and renewal theme'],
      season: 'Spring',
      icon: '🌸',
      preview: '/themes/spring-preview.jpg'
    },
    {
      id: 'autumn',
      name: 'Autumn Collection',
      description: 'Warm, earthy colors perfect for fall sales and harvest season',
      colors: ['#D35400', '#E67E22', '#F39C12', '#8E44AD'],
      features: ['Warm orange/brown tones', 'Autumn leaves imagery', 'Harvest season deals', 'Cozy fall atmosphere'],
      season: 'Autumn',
      icon: '🍂',
      preview: '/themes/autumn-preview.jpg'
    },
    {
      id: 'black-friday',
      name: 'Black Friday Special',
      description: 'High-contrast, urgent colors perfect for major sales events',
      colors: ['#000000', '#FF0000', '#FFD700', '#FFFFFF'],
      features: ['Black and red urgency', 'Sale countdown timers', 'Limited time offers', 'Maximum conversion focus'],
      season: 'Special Event',
      icon: '🛍️',
      preview: '/themes/blackfriday-preview.jpg'
    },
    {
      id: 'christmas',
      name: 'Christmas Special',
      description: 'Festive red and green colors perfect for Christmas shopping',
      colors: ['#C41E3A', '#228B22', '#FFD700', '#FFFFFF'],
      features: ['Christmas red and green', 'Holiday decorations', 'Gift suggestions', 'Festive atmosphere'],
      season: 'Holiday',
      icon: '🎄',
      preview: '/themes/christmas-preview.jpg'
    }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Theme Management</h2>
          <p className="text-gray-600">Switch your website theme for different seasons and events</p>
          {lastChanged && (
            <p className="text-sm text-green-600 mt-1">
              ✅ Last changed: {new Date(lastChanged).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadCurrentTheme}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button
            onClick={() => window.open('https://deal4u.co', '_blank')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            View Website
          </button>
        </div>
      </div>

      {/* Current Theme Status */}
      <div className="bg-white rounded-lg shadow border p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Active Theme</h3>
        <div className="flex items-center space-x-4">
          <div className="text-4xl">
            {themes.find(t => t.id === currentTheme)?.icon || '🎨'}
          </div>
          <div>
            <h4 className="text-xl font-bold text-gray-900">
              {themes.find(t => t.id === currentTheme)?.name || 'Unknown Theme'}
            </h4>
            <p className="text-gray-600">
              {themes.find(t => t.id === currentTheme)?.description || 'No description available'}
            </p>
            <div className="flex space-x-2 mt-2">
              {themes.find(t => t.id === currentTheme)?.colors.map((color, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded-full border-2 border-gray-300"
                  style={{ backgroundColor: color }}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Theme Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {themes.map((theme) => (
          <div key={theme.id} className="bg-white rounded-lg shadow border overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{theme.icon}</span>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{theme.name}</h4>
                    <p className="text-sm text-gray-500">{theme.season}</p>
                  </div>
                </div>
                {currentTheme === theme.id && (
                  <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Active
                  </span>
                )}
              </div>

              <p className="text-gray-600 text-sm mb-4">{theme.description}</p>

              {/* Color Palette */}
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Color Palette:</p>
                <div className="flex space-x-2">
                  {theme.colors.map((color, index) => (
                    <div
                      key={index}
                      className="w-8 h-8 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: color }}
                      title={color}
                    ></div>
                  ))}
                </div>
              </div>

              {/* Features */}
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Features:</p>
                <ul className="text-xs text-gray-600 space-y-1">
                  {theme.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-1">
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Apply Button */}
              <button
                onClick={() => applyTheme(theme.id)}
                disabled={applying || currentTheme === theme.id}
                className={`w-full py-2 px-4 rounded transition-colors ${
                  currentTheme === theme.id
                    ? 'bg-green-100 text-green-800 cursor-not-allowed'
                    : applying
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {applying ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Applying...</span>
                  </div>
                ) : currentTheme === theme.id ? (
                  'Currently Active'
                ) : (
                  `Apply ${theme.name}`
                )}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Theme Info */}
      <div className="mt-6 bg-blue-50 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <Smartphone className="w-5 h-5 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">Theme Management</span>
        </div>
        <p className="text-sm text-blue-700 mt-1">
          Themes instantly change your website's appearance, colors, and seasonal focus. Perfect for matching your store to current seasons, holidays, or special sales events.
        </p>
      </div>
    </div>
  );
}
