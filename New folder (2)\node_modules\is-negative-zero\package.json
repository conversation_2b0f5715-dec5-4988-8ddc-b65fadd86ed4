{"name": "is-negative-zero", "version": "2.0.3", "description": "Is this value negative zero? === will lie to you", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-negative-zero.git"}, "bugs": {"url": "https://github.com/inspect-js/is-negative-zero/issues"}, "homepage": "https://github.com/inspect-js/is-negative-zero", "keywords": ["is", "negative", "zero", "negative zero", "number", "positive", "0", "-0"], "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}