'use client';

import { createContext, useContext, useReducer, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';
import axios from 'axios';
import { wooCommerceApi } from '../lib/woocommerce';
import { socialAuth } from '../lib/socialAuth';

const AuthContext = createContext();

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
        error: null
      };
    
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: action.payload
      };
    
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    default:
      return state;
  }
};

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: true,
  error: null
};

export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const token = Cookies.get('auth_token');
      const userData = Cookies.get('user_data');

      if (token && userData) {
        const user = JSON.parse(userData);
        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user, token } 
        });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Login with WooCommerce authentication (supports both regular and social login)
  const login = async (email, password, socialLoginData = null) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      let userData, token;
      
      // Check if this is a social login with data already provided
      if (socialLoginData && socialLoginData.socialLogin === true) {
        // For social login, we already have the user data and token
        userData = socialLoginData.userData;
        token = socialLoginData.token;
      } else {
        // Regular WooCommerce authentication
        const authResult = await wooCommerceApi.validateCredentials(email, password);
        
        if (!authResult.success) {
          // Authentication failed
          const errorMessage = authResult.error || 'Login failed';
          dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
        
        // Format user data for our application
        userData = {
          id: authResult.user.id,
          email: authResult.user.email,
          firstName: authResult.user.first_name,
          lastName: authResult.user.last_name,
          name: `${authResult.user.first_name} ${authResult.user.last_name}`,
          avatar: authResult.user.avatar_url || `https://ui-avatars.com/api/?name=${authResult.user.first_name}+${authResult.user.last_name}&background=3b82f6&color=fff`,
          phone: authResult.user.billing?.phone || '',
          address: {
            line1: authResult.user.billing?.address_1 || '',
            line2: authResult.user.billing?.address_2 || '',
            city: authResult.user.billing?.city || '',
            state: authResult.user.billing?.state || '',
            zipCode: authResult.user.billing?.postcode || '',
            country: authResult.user.billing?.country || 'US'
          },
          preferences: {
            newsletter: true,
            notifications: true
          },
          // Store original WooCommerce data for reference
          woocommerce: {
            id: authResult.user.id,
            username: authResult.user.username,
            billing: authResult.user.billing,
            shipping: authResult.user.shipping
          }
        };
        
        token = authResult.token;
      }

      // Store authentication data in cookies
      Cookies.set('auth_token', token, { 
        expires: 7, // 7 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
      
      Cookies.set('user_data', JSON.stringify(userData), { 
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });

      dispatch({ 
        type: 'LOGIN_SUCCESS', 
        payload: { user: userData, token: token } 
      });

      const loginMethod = socialLoginData?.socialLogin ? 
        `Successfully logged in with ${socialLoginData.userData.socialProvider || 'social login'}!` : 
        'Successfully logged in!';
      
      toast.success(loginMethod);
      return { success: true };
    } catch (error) {
      const errorMessage = error.message || 'Login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Register with WooCommerce
  const register = async (userData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Format the user data for WooCommerce API
      const customerData = {
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        username: userData.username || userData.email.split('@')[0],
        password: userData.password,
        phone: userData.phone || ''
      };

      // Create customer in WooCommerce
      const createdCustomer = await wooCommerceApi.createCustomer(customerData);
      
      if (!createdCustomer) {
        throw new Error('Failed to create customer account');
      }

      // Now authenticate the new user
      const authResult = await wooCommerceApi.validateCredentials(
        userData.email, 
        userData.password
      );

      if (authResult.success) {
        // Format user data for our application
        const newUser = {
          id: createdCustomer.id,
          email: createdCustomer.email,
          firstName: createdCustomer.first_name,
          lastName: createdCustomer.last_name,
          name: `${createdCustomer.first_name} ${createdCustomer.last_name}`,
          avatar: `https://ui-avatars.com/api/?name=${createdCustomer.first_name}+${createdCustomer.last_name}&background=3b82f6&color=fff`,
          phone: createdCustomer.billing?.phone || '',
          address: {
            line1: createdCustomer.billing?.address_1 || '',
            line2: createdCustomer.billing?.address_2 || '',
            city: createdCustomer.billing?.city || '',
            state: createdCustomer.billing?.state || '',
            zipCode: createdCustomer.billing?.postcode || '',
            country: createdCustomer.billing?.country || 'US'
          },
          preferences: {
            newsletter: true,
            notifications: true
          },
          // Store original WooCommerce data
          woocommerce: {
            id: createdCustomer.id,
            username: createdCustomer.username,
            billing: createdCustomer.billing,
            shipping: createdCustomer.shipping
          }
        };

        // Store in cookies
        Cookies.set('auth_token', authResult.token, { 
          expires: 7,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });
        
        Cookies.set('user_data', JSON.stringify(newUser), { 
          expires: 7,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });

        dispatch({ 
          type: 'LOGIN_SUCCESS', 
          payload: { user: newUser, token: authResult.token } 
        });

        toast.success('Account created successfully!');
        return { success: true };
      } else {
        // Account created but login failed
        dispatch({ type: 'SET_LOADING', payload: false });
        toast.success('Account created! Please log in.');
        return { success: true, requireLogin: true };
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = () => {
    try {
      // Remove cookies
      Cookies.remove('auth_token');
      Cookies.remove('user_data');

      dispatch({ type: 'LOGOUT' });
      toast.success('Successfully logged out!');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Error logging out');
    }
  };

  // Update user profile using WooCommerce API
  const updateProfile = async (updatedData) => {
    try {
      if (!state.user || !state.user.id) {
        throw new Error('User not authenticated');
      }

      dispatch({ type: 'SET_LOADING', payload: true });

      // Format data for WooCommerce API
      const customerUpdateData = {
        id: state.user.woocommerce?.id || state.user.id,
        first_name: updatedData.firstName || state.user.firstName,
        last_name: updatedData.lastName || state.user.lastName,
        billing: {
          first_name: updatedData.firstName || state.user.firstName,
          last_name: updatedData.lastName || state.user.lastName,
          phone: updatedData.phone || state.user.phone,
          email: state.user.email,
          address_1: updatedData.address?.line1 || state.user.address?.line1,
          address_2: updatedData.address?.line2 || state.user.address?.line2,
          city: updatedData.address?.city || state.user.address?.city,
          state: updatedData.address?.state || state.user.address?.state,
          postcode: updatedData.address?.zipCode || state.user.address?.zipCode,
          country: updatedData.address?.country || state.user.address?.country
        },
        shipping: {
          first_name: updatedData.firstName || state.user.firstName,
          last_name: updatedData.lastName || state.user.lastName,
          address_1: updatedData.address?.line1 || state.user.address?.line1,
          address_2: updatedData.address?.line2 || state.user.address?.line2,
          city: updatedData.address?.city || state.user.address?.city,
          state: updatedData.address?.state || state.user.address?.state,
          postcode: updatedData.address?.zipCode || state.user.address?.zipCode,
          country: updatedData.address?.country || state.user.address?.country
        }
      };

      // Update customer in WooCommerce
      // Using the wooCommerceApi instance instead of direct api reference
      let response;
      if (wooCommerceApi.isConfigured()) {
        try {
          response = await axios.put(`${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/wc/v3/customers/${customerUpdateData.id}`, 
            customerUpdateData, 
            {
              auth: {
                username: process.env.WOOCOMMERCE_CONSUMER_KEY,
                password: process.env.WOOCOMMERCE_CONSUMER_SECRET
              },
              headers: {
                'Content-Type': 'application/json'
              }
            }
          );
        } catch (apiError) {
          console.error('Error updating customer in WooCommerce:', apiError);
          // Continue with local update even if API fails
        }
      }

      // Format the response data for our application
      const updatedUser = { ...state.user, ...updatedData };
      
      if (response && response.data) {
        // Update with any additional data from WooCommerce
        updatedUser.woocommerce = {
          ...state.user.woocommerce,
          billing: response.data.billing,
          shipping: response.data.shipping
        };
      }

      // Update cookie
      Cookies.set('user_data', JSON.stringify(updatedUser), { 
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });

      dispatch({ type: 'UPDATE_USER', payload: updatedData });
      dispatch({ type: 'SET_LOADING', payload: false });

      toast.success('Profile updated successfully!');
      return { success: true };

    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      const errorMessage = error.response?.data?.message || error.message || 'Update failed';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Change password function
  const changePassword = async (currentPassword, newPassword) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Mock API call - replace with real password change
      await new Promise(resolve => setTimeout(resolve, 1000));

      dispatch({ type: 'SET_LOADING', payload: false });
      toast.success('Password changed successfully!');
      return { success: true };

    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      const errorMessage = error.message || 'Password change failed';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Forgot password function
  const forgotPassword = async (email) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Mock API call - replace with real forgot password
      await new Promise(resolve => setTimeout(resolve, 1000));

      dispatch({ type: 'SET_LOADING', payload: false });
      toast.success('Password reset link sent to your email!');
      return { success: true };

    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      const errorMessage = error.message || 'Request failed';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    // State
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    loading: state.loading,
    error: state.error,

    // Actions
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    forgotPassword,
    clearError,
    checkAuthState
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;