{"name": "follow-redirects", "version": "1.5.10", "description": "HTTP and HTTPS modules that follow redirects.", "main": "index.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "npm run lint && npm run mocha", "lint": "eslint *.js test", "mocha": "nyc mocha"}, "repository": {"type": "git", "url": "**************:follow-redirects/follow-redirects.git"}, "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": "<PERSON><PERSON> <<EMAIL>> (https://ruben.verborgh.org/)", "contributors": ["<PERSON> <<EMAIL>> (http://www.syskall.com)", "<PERSON> <<EMAIL>>"], "files": ["index.js", "create.js", "http.js", "https.js"], "dependencies": {"debug": "=3.1.0"}, "devDependencies": {"concat-stream": "^1.6.0", "coveralls": "^3.0.2", "eslint": "^4.19.1", "express": "^4.16.2", "mocha": "^5.0.0", "nyc": "^11.8.0"}, "license": "MIT", "nyc": {"reporter": ["lcov", "text"]}}