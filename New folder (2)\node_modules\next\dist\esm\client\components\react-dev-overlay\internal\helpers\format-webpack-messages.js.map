{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.ts"], "names": ["stripAnsi", "friendlySyntaxErrorLabel", "WEBPACK_BREAKING_CHANGE_POLYFILLS", "isLikelyASyntaxError", "message", "includes", "hadMissingSassError", "formatMessage", "verbose", "importTraceNote", "filteredModuleTrace", "moduleTrace", "filter", "trace", "test", "originName", "body", "breakingChangeIndex", "indexOf", "slice", "moduleName", "file", "details", "length", "map", "join", "stack", "lines", "split", "line", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "replace", "trim", "splice", "match", "firstLine", "index", "arr", "formatWebpackMessages", "json", "formattedErrors", "errors", "isUnknownNextFontError", "formattedWarnings", "warnings", "reactServerComponentsError", "i", "error", "unshift", "result", "some"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,OAAOA,eAAe,gCAA+B;AACrD,qKAAqK;AACrK,0DAA0D;AAE1D,MAAMC,2BAA2B;AAEjC,MAAMC,oCACJ;AAEF,SAASC,qBAAqBC,OAAe;IAC3C,OAAOJ,UAAUI,SAASC,QAAQ,CAACJ;AACrC;AAEA,IAAIK,sBAAsB;AAE1B,oCAAoC;AACpC,SAASC,cACPH,OAAY,EACZI,OAAiB,EACjBC,eAAyB;IAEzB,8CAA8C;IAC9C,IAAI,OAAOL,YAAY,YAAYA,QAAQA,OAAO,EAAE;QAClD,MAAMM,sBACJN,QAAQO,WAAW,IACnBP,QAAQO,WAAW,CAACC,MAAM,CACxB,CAACC,QACC,CAAC,gEAAgEC,IAAI,CACnED,MAAME,UAAU;QAIxB,IAAIC,OAAOZ,QAAQA,OAAO;QAC1B,MAAMa,sBAAsBD,KAAKE,OAAO,CAAChB;QACzC,IAAIe,uBAAuB,GAAG;YAC5BD,OAAOA,KAAKG,KAAK,CAAC,GAAGF;QACvB;QAEAb,UACE,AAACA,CAAAA,QAAQgB,UAAU,GAAGpB,UAAUI,QAAQgB,UAAU,IAAI,OAAO,EAAC,IAC7DhB,CAAAA,QAAQiB,IAAI,GAAGrB,UAAUI,QAAQiB,IAAI,IAAI,OAAO,EAAC,IAClDL,OACCZ,CAAAA,QAAQkB,OAAO,IAAId,UAAU,OAAOJ,QAAQkB,OAAO,GAAG,EAAC,IACvDZ,CAAAA,uBAAuBA,oBAAoBa,MAAM,GAC9C,AAACd,CAAAA,mBAAmB,wCAAuC,IAC3DC,oBACGc,GAAG,CAAC,CAACX,QAAe,AAAC,OAAIA,MAAMO,UAAU,EACzCK,IAAI,CAAC,MACR,EAAC,IACJrB,CAAAA,QAAQsB,KAAK,IAAIlB,UAAU,OAAOJ,QAAQsB,KAAK,GAAG,EAAC;IACxD;IACA,IAAIC,QAAQvB,QAAQwB,KAAK,CAAC;IAE1B,kDAAkD;IAClD,oEAAoE;IACpED,QAAQA,MAAMf,MAAM,CAAC,CAACiB,OAAiB,CAAC,uBAAuBf,IAAI,CAACe;IAEpE,4CAA4C;IAC5C,2CAA2C;IAC3CF,QAAQA,MAAMH,GAAG,CAAC,CAACK;QACjB,MAAMC,eAAe,gDAAgDC,IAAI,CACvEF;QAEF,IAAI,CAACC,cAAc;YACjB,OAAOD;QACT;QACA,MAAM,GAAGG,WAAWC,aAAaC,aAAa,GAAGJ;QACjD,OAAO,AAAG7B,2BAAyB,MAAGiC,eAAa,OAAIF,YAAU,MAAGC,cAAY;IAClF;IAEA7B,UAAUuB,MAAMF,IAAI,CAAC;IACrB,+CAA+C;IAC/CrB,UAAUA,QAAQ+B,OAAO,CACvB,4CACA,AAAC,KAAElC,2BAAyB;IAE9B,yBAAyB;IACzBG,UAAUA,QAAQ+B,OAAO,CACvB,mDACC;IAEH/B,UAAUA,QAAQ+B,OAAO,CACvB,6EACC;IAEH/B,UAAUA,QAAQ+B,OAAO,CACvB,2EACC;IAEHR,QAAQvB,QAAQwB,KAAK,CAAC;IAEtB,yBAAyB;IACzB,IAAID,MAAMJ,MAAM,GAAG,KAAKI,KAAK,CAAC,EAAE,CAACS,IAAI,OAAO,IAAI;QAC9CT,MAAMU,MAAM,CAAC,GAAG;IAClB;IAEA,wEAAwE;IACxE,IAAIV,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACT,OAAO,CAAC,0BAA0B,GAAG;QAC5DS,QAAQ;YACNA,KAAK,CAAC,EAAE;YACRA,KAAK,CAAC,EAAE,CACLQ,OAAO,CAAC,WAAW,IACnBA,OAAO,CAAC,uCAAuC;eAC/CR,MAAMR,KAAK,CAAC;SAChB;IACH;IAEA,sEAAsE;IACtE,IAAIQ,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACW,KAAK,CAAC,6BAA6B;QAC1D,6DAA6D;QAC7D,MAAMC,YAAYZ,KAAK,CAAC,EAAE,CAACC,KAAK,CAAC;QACjCD,KAAK,CAAC,EAAE,GAAGY,SAAS,CAACA,UAAUhB,MAAM,GAAG,EAAE;QAE1CI,KAAK,CAAC,EAAE,GACN;QACFA,KAAK,CAAC,EAAE,IAAI;QACZA,KAAK,CAAC,EAAE,IAAI;QAEZ,mCAAmC;QACnCA,QAAQA,MAAMR,KAAK,CAAC,GAAG;QACvBb,sBAAsB;IACxB,OAAO,IACLA,uBACAF,QAAQkC,KAAK,CAAC,gDACd;QACA,iEAAiE;QACjEX,QAAQ,EAAE;IACZ;IAEA,IAAI,CAACnB,SAAS;QACZJ,UAAUuB,MAAMF,IAAI,CAAC;QACrB,qEAAqE;QACrE,qEAAqE;QACrE,gEAAgE;QAChE,yDAAyD;QACzDrB,UAAUA,QAAQ+B,OAAO,CACvB,kDACA,IACA,iBAAiB;;QACnB/B,UAAUA,QAAQ+B,OAAO,CAAC,+BAA+B,IAAI,iBAAiB;;QAE9E/B,UAAUA,QAAQ+B,OAAO,CACvB,sMACA;QAGFR,QAAQvB,QAAQwB,KAAK,CAAC;IACxB;IAEA,6BAA6B;IAC7BD,QAAQ,AAACA,MAAmBf,MAAM,CAChC,CAACiB,MAAMW,OAAOC,MACZD,UAAU,KAAKX,KAAKO,IAAI,OAAO,MAAMP,KAAKO,IAAI,OAAOK,GAAG,CAACD,QAAQ,EAAE,CAACJ,IAAI;IAG5E,yBAAyB;IACzBhC,UAAUuB,MAAMF,IAAI,CAAC;IACrB,OAAOrB,QAAQgC,IAAI;AACrB;AAEA,eAAe,SAASM,sBAAsBC,IAAS,EAAEnC,OAAiB;IACxE,MAAMoC,kBAAkBD,KAAKE,MAAM,CAACrB,GAAG,CAAC,CAACpB;QACvC,MAAM0C,yBAAyB1C,QAAQA,OAAO,CAACC,QAAQ,CACrD;QAEF,OAAOE,cAAcH,SAAS0C,0BAA0BtC;IAC1D;IACA,MAAMuC,oBAAoBJ,KAAKK,QAAQ,CAACxB,GAAG,CAAC,CAACpB;QAC3C,OAAOG,cAAcH,SAASI;IAChC;IAEA,sDAAsD;IACtD,IAAIyC,6BAA6B,CAAC;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIN,gBAAgBrB,MAAM,EAAE2B,IAAK;QAC/C,MAAMC,QAAQP,eAAe,CAACM,EAAE;QAChC,IAAIC,MAAM9C,QAAQ,CAAC,+BAA+B;YAChD4C,6BAA6BC;YAC7B;QACF;IACF;IAEA,8DAA8D;IAC9D,IAAID,+BAA+B,CAAC,GAAG;QACrC,MAAME,QAAQP,gBAAgBP,MAAM,CAACY,4BAA4B;QACjEL,gBAAgBQ,OAAO,CAACD,KAAK,CAAC,EAAE;IAClC;IAEA,MAAME,SAAS;QACb,GAAGV,IAAI;QACPE,QAAQD;QACRI,UAAUD;IACZ;IACA,IAAI,CAACvC,WAAW6C,OAAOR,MAAM,CAACS,IAAI,CAACnD,uBAAuB;QACxD,kDAAkD;QAClDkD,OAAOR,MAAM,GAAGQ,OAAOR,MAAM,CAACjC,MAAM,CAACT;QACrCkD,OAAOL,QAAQ,GAAG,EAAE;IACtB;IACA,OAAOK;AACT"}