import { NextResponse } from 'next/server';

// Mock WooCommerce products data for testing
const mockProducts = [
  {
    id: 1,
    name: "Premium Wireless Headphones",
    slug: "premium-wireless-headphones",
    permalink: "https://deal4u.co/product/premium-wireless-headphones",
    date_created: "2024-01-15T10:00:00",
    date_modified: "2024-01-15T10:00:00",
    type: "simple",
    status: "publish",
    featured: true,
    catalog_visibility: "visible",
    description: "<p>Experience crystal-clear audio with our premium wireless headphones. Features active noise cancellation, 30-hour battery life, and premium comfort padding.</p>",
    short_description: "<p>Premium wireless headphones with noise cancellation and long battery life.</p>",
    sku: "PWH-001",
    price: "199.99",
    regular_price: "249.99",
    sale_price: "199.99",
    date_on_sale_from: null,
    date_on_sale_to: null,
    price_html: "<del><span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>249.99</span></del> <ins><span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>199.99</span></ins>",
    on_sale: true,
    purchasable: true,
    total_sales: 156,
    virtual: false,
    downloadable: false,
    downloads: [],
    download_limit: -1,
    download_expiry: -1,
    external_url: "",
    button_text: "",
    tax_status: "taxable",
    tax_class: "",
    manage_stock: true,
    stock_quantity: 50,
    stock_status: "instock",
    backorders: "no",
    backorders_allowed: false,
    backordered: false,
    sold_individually: false,
    weight: "0.5",
    dimensions: {
      length: "20",
      width: "15",
      height: "8"
    },
    shipping_required: true,
    shipping_taxable: true,
    shipping_class: "",
    shipping_class_id: 0,
    reviews_allowed: true,
    average_rating: "4.8",
    rating_count: 24,
    related_ids: [2, 3, 4],
    upsell_ids: [],
    cross_sell_ids: [],
    parent_id: 0,
    purchase_note: "",
    categories: [
      {
        id: 15,
        name: "Electronics",
        slug: "electronics"
      },
      {
        id: 16,
        name: "Audio",
        slug: "audio"
      }
    ],
    tags: [
      {
        id: 37,
        name: "wireless",
        slug: "wireless"
      },
      {
        id: 38,
        name: "premium",
        slug: "premium"
      }
    ],
    images: [
      {
        id: 123,
        date_created: "2024-01-15T10:00:00",
        date_modified: "2024-01-15T10:00:00",
        src: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop",
        name: "headphones-main",
        alt: "Premium Wireless Headphones"
      },
      {
        id: 124,
        date_created: "2024-01-15T10:00:00",
        date_modified: "2024-01-15T10:00:00",
        src: "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=600&h=600&fit=crop",
        name: "headphones-side",
        alt: "Premium Wireless Headphones Side View"
      }
    ],
    attributes: [
      {
        id: 1,
        name: "Color",
        position: 0,
        visible: true,
        variation: false,
        options: ["Black", "White", "Silver"]
      },
      {
        id: 2,
        name: "Connectivity",
        position: 1,
        visible: true,
        variation: false,
        options: ["Bluetooth 5.0", "USB-C", "3.5mm Jack"]
      }
    ],
    default_attributes: [],
    variations: [],
    grouped_products: [],
    menu_order: 0,
    meta_data: []
  },
  {
    id: 2,
    name: "Smart Fitness Watch",
    slug: "smart-fitness-watch",
    permalink: "https://deal4u.co/product/smart-fitness-watch",
    date_created: "2024-01-14T09:00:00",
    date_modified: "2024-01-14T09:00:00",
    type: "simple",
    status: "publish",
    featured: false,
    catalog_visibility: "visible",
    description: "<p>Track your fitness goals with our advanced smart watch. Features heart rate monitoring, GPS tracking, and 7-day battery life.</p>",
    short_description: "<p>Advanced fitness tracking with heart rate monitoring and GPS.</p>",
    sku: "SFW-002",
    price: "299.99",
    regular_price: "299.99",
    sale_price: "",
    date_on_sale_from: null,
    date_on_sale_to: null,
    price_html: "<span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>299.99</span>",
    on_sale: false,
    purchasable: true,
    total_sales: 89,
    virtual: false,
    downloadable: false,
    downloads: [],
    download_limit: -1,
    download_expiry: -1,
    external_url: "",
    button_text: "",
    tax_status: "taxable",
    tax_class: "",
    manage_stock: true,
    stock_quantity: 25,
    stock_status: "instock",
    backorders: "no",
    backorders_allowed: false,
    backordered: false,
    sold_individually: false,
    weight: "0.2",
    dimensions: {
      length: "5",
      width: "4",
      height: "1"
    },
    shipping_required: true,
    shipping_taxable: true,
    shipping_class: "",
    shipping_class_id: 0,
    reviews_allowed: true,
    average_rating: "4.6",
    rating_count: 18,
    related_ids: [1, 3, 5],
    upsell_ids: [],
    cross_sell_ids: [],
    parent_id: 0,
    purchase_note: "",
    categories: [
      {
        id: 15,
        name: "Electronics",
        slug: "electronics"
      },
      {
        id: 17,
        name: "Wearables",
        slug: "wearables"
      }
    ],
    tags: [
      {
        id: 39,
        name: "fitness",
        slug: "fitness"
      },
      {
        id: 40,
        name: "smartwatch",
        slug: "smartwatch"
      }
    ],
    images: [
      {
        id: 125,
        date_created: "2024-01-14T09:00:00",
        date_modified: "2024-01-14T09:00:00",
        src: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop",
        name: "smartwatch-main",
        alt: "Smart Fitness Watch"
      }
    ],
    attributes: [
      {
        id: 3,
        name: "Band Color",
        position: 0,
        visible: true,
        variation: false,
        options: ["Black", "Blue", "Red", "White"]
      },
      {
        id: 4,
        name: "Size",
        position: 1,
        visible: true,
        variation: false,
        options: ["38mm", "42mm"]
      }
    ],
    default_attributes: [],
    variations: [],
    grouped_products: [],
    menu_order: 0,
    meta_data: []
  },
  {
    id: 3,
    name: "Portable Bluetooth Speaker",
    slug: "portable-bluetooth-speaker",
    permalink: "https://deal4u.co/product/portable-bluetooth-speaker",
    date_created: "2024-01-13T08:00:00",
    date_modified: "2024-01-13T08:00:00",
    type: "simple",
    status: "publish",
    featured: true,
    catalog_visibility: "visible",
    description: "<p>Take your music anywhere with our waterproof portable speaker. 360-degree sound, 12-hour battery, and rugged design.</p>",
    short_description: "<p>Waterproof portable speaker with 360-degree sound and long battery life.</p>",
    sku: "PBS-003",
    price: "79.99",
    regular_price: "99.99",
    sale_price: "79.99",
    date_on_sale_from: null,
    date_on_sale_to: null,
    price_html: "<del><span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>99.99</span></del> <ins><span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>79.99</span></ins>",
    on_sale: true,
    purchasable: true,
    total_sales: 203,
    virtual: false,
    downloadable: false,
    downloads: [],
    download_limit: -1,
    download_expiry: -1,
    external_url: "",
    button_text: "",
    tax_status: "taxable",
    tax_class: "",
    manage_stock: true,
    stock_quantity: 75,
    stock_status: "instock",
    backorders: "no",
    backorders_allowed: false,
    backordered: false,
    sold_individually: false,
    weight: "0.8",
    dimensions: {
      length: "15",
      width: "8",
      height: "8"
    },
    shipping_required: true,
    shipping_taxable: true,
    shipping_class: "",
    shipping_class_id: 0,
    reviews_allowed: true,
    average_rating: "4.7",
    rating_count: 31,
    related_ids: [1, 2, 4],
    upsell_ids: [],
    cross_sell_ids: [],
    parent_id: 0,
    purchase_note: "",
    categories: [
      {
        id: 15,
        name: "Electronics",
        slug: "electronics"
      },
      {
        id: 16,
        name: "Audio",
        slug: "audio"
      }
    ],
    tags: [
      {
        id: 37,
        name: "wireless",
        slug: "wireless"
      },
      {
        id: 41,
        name: "portable",
        slug: "portable"
      },
      {
        id: 42,
        name: "waterproof",
        slug: "waterproof"
      }
    ],
    images: [
      {
        id: 126,
        date_created: "2024-01-13T08:00:00",
        date_modified: "2024-01-13T08:00:00",
        src: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&h=600&fit=crop",
        name: "speaker-main",
        alt: "Portable Bluetooth Speaker"
      }
    ],
    attributes: [
      {
        id: 5,
        name: "Color",
        position: 0,
        visible: true,
        variation: false,
        options: ["Black", "Blue", "Red", "Green"]
      }
    ],
    default_attributes: [],
    variations: [],
    grouped_products: [],
    menu_order: 0,
    meta_data: []
  }
];

/**
 * GET /api/mock-woocommerce/products
 * Mock WooCommerce products endpoint
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const perPage = parseInt(searchParams.get('per_page')) || 10;
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const onSale = searchParams.get('on_sale');

    let filteredProducts = [...mockProducts];

    // Apply search filter
    if (search) {
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply category filter
    if (category) {
      filteredProducts = filteredProducts.filter(product =>
        product.categories.some(cat => cat.slug === category)
      );
    }

    // Apply featured filter
    if (featured === 'true') {
      filteredProducts = filteredProducts.filter(product => product.featured);
    }

    // Apply on sale filter
    if (onSale === 'true') {
      filteredProducts = filteredProducts.filter(product => product.on_sale);
    }

    // Apply pagination
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    // Add pagination headers
    const totalProducts = filteredProducts.length;
    const totalPages = Math.ceil(totalProducts / perPage);

    const response = NextResponse.json(paginatedProducts);
    response.headers.set('X-WP-Total', totalProducts.toString());
    response.headers.set('X-WP-TotalPages', totalPages.toString());

    return response;

  } catch (error) {
    console.error('Error in mock WooCommerce products API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
