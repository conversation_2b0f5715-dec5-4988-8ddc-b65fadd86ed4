"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    createDynamicallyTrackedSearchParams: null,
    createUntrackedSearchParams: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    createDynamicallyTrackedSearchParams: function() {
        return createDynamicallyTrackedSearchParams;
    },
    createUntrackedSearchParams: function() {
        return createUntrackedSearchParams;
    }
});
const _staticgenerationasyncstorageexternal = require("./static-generation-async-storage.external");
const _dynamicrendering = require("../../server/app-render/dynamic-rendering");
const _reflect = require("../../server/web/spec-extension/adapters/reflect");
function createUntrackedSearchParams(searchParams) {
    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();
    if (store && store.forceStatic) {
        return {};
    } else {
        return searchParams;
    }
}
function createDynamicallyTrackedSearchParams(searchParams) {
    const store = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();
    if (!store) {
        // we assume we are in a route handler or page render. just return the searchParams
        return searchParams;
    } else if (store.forceStatic) {
        // If we forced static we omit searchParams entirely. This is true both during SSR
        // and browser render because we need there to be parity between these environments
        return {};
    } else if (!store.isStaticGeneration && !store.dynamicShouldError) {
        // during dynamic renders we don't actually have to track anything so we just return
        // the searchParams directly. However if dynamic data access should error then we
        // still want to track access. This covers the case in Dev where all renders are dynamic
        // but we still want to error if you use a dynamic data source because it will fail the build
        // or revalidate if you do.
        return searchParams;
    } else {
        // We need to track dynamic access with a Proxy. We implement get, has, and ownKeys because
        // these can all be used to exfiltrate information about searchParams.
        return new Proxy({}, {
            get (target, prop, receiver) {
                if (typeof prop === "string") {
                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, "searchParams." + prop);
                }
                return _reflect.ReflectAdapter.get(target, prop, receiver);
            },
            has (target, prop) {
                if (typeof prop === "string") {
                    (0, _dynamicrendering.trackDynamicDataAccessed)(store, "searchParams." + prop);
                }
                return Reflect.has(target, prop);
            },
            ownKeys (target) {
                (0, _dynamicrendering.trackDynamicDataAccessed)(store, "searchParams");
                return Reflect.ownKeys(target);
            }
        });
    }
}

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=search-params.js.map