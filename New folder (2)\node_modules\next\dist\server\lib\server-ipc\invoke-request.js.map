{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/invoke-request.ts"], "names": ["invokeRequest", "targetUrl", "requestInit", "readableBody", "invokeHeaders", "filterReqHeaders", "headers", "ipcForbiddenHeaders", "fetch", "method", "redirect", "signal", "body", "duplex", "next", "internal"], "mappings": ";;;;+BAIaA;;;eAAAA;;;uBAFyC;AAE/C,MAAMA,gBAAgB,OAC3BC,WACAC,aAKAC;IAEA,MAAMC,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,iBAAiB;QACjB,GAAGH,YAAYI,OAAO;IACxB,GACAC,0BAAmB;IAGrB,OAAO,MAAMC,MAAMP,WAAW;QAC5BK,SAASF;QACTK,QAAQP,YAAYO,MAAM;QAC1BC,UAAU;QACVC,QAAQT,YAAYS,MAAM;QAE1B,GAAIT,YAAYO,MAAM,KAAK,SAC3BP,YAAYO,MAAM,KAAK,UACvBN,eACI;YACES,MAAMT;YACNU,QAAQ;QACV,IACA,CAAC,CAAC;QAENC,MAAM;YACJ,aAAa;YACbC,UAAU;QACZ;IACF;AACF"}