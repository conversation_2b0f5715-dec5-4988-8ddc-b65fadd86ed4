#!/usr/bin/env node

/**
 * PWA Testing Script for Deal4u
 * Tests all PWA features and functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 PWA Feature Testing - Deal4u');
console.log('================================\n');

let passedTests = 0;
let totalTests = 0;

function test(description, condition) {
  totalTests++;
  if (condition) {
    console.log(`✅ ${description}`);
    passedTests++;
  } else {
    console.log(`❌ ${description}`);
  }
}

// Test 1: Manifest file
console.log('📋 Testing PWA Manifest...');
const manifestPath = path.join(__dirname, 'public', 'manifest.json');
test('Manifest file exists', fs.existsSync(manifestPath));

if (fs.existsSync(manifestPath)) {
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    test('Manifest has name', !!manifest.name);
    test('Manifest has short_name', !!manifest.short_name);
    test('Manifest has start_url', !!manifest.start_url);
    test('Manifest has display mode', !!manifest.display);
    test('Manifest has theme_color', !!manifest.theme_color);
    test('Manifest has background_color', !!manifest.background_color);
    test('Manifest has icons array', Array.isArray(manifest.icons));
    test('Manifest has at least 2 icons', manifest.icons && manifest.icons.length >= 2);
    test('Manifest has 192x192 icon', manifest.icons && manifest.icons.some(icon => icon.sizes === '192x192'));
    test('Manifest has 512x512 icon', manifest.icons && manifest.icons.some(icon => icon.sizes === '512x512'));
    test('Manifest has shortcuts', Array.isArray(manifest.shortcuts));
  } catch (error) {
    test('Manifest is valid JSON', false);
  }
}

console.log('');

// Test 2: Service Worker
console.log('🔧 Testing Service Worker...');
const swPath = path.join(__dirname, 'public', 'sw.js');
test('Service Worker file exists', fs.existsSync(swPath));

if (fs.existsSync(swPath)) {
  const swContent = fs.readFileSync(swPath, 'utf8');
  test('Service Worker has install event', swContent.includes('addEventListener(\'install\''));
  test('Service Worker has activate event', swContent.includes('addEventListener(\'activate\''));
  test('Service Worker has fetch event', swContent.includes('addEventListener(\'fetch\''));
  test('Service Worker has caching logic', swContent.includes('caches.open'));
  test('Service Worker has offline handling', swContent.includes('offline') || swContent.includes('cache'));
  test('Service Worker has push notification handler', swContent.includes('addEventListener(\'push\''));
  test('Service Worker has background sync', swContent.includes('addEventListener(\'sync\''));
}

console.log('');

// Test 3: PWA Components
console.log('🎨 Testing PWA Components...');
const installPromptPath = path.join(__dirname, 'components', 'PWAInstallPrompt.jsx');
test('PWA Install Prompt component exists', fs.existsSync(installPromptPath));

const pwaLibPath = path.join(__dirname, 'lib', 'pwa.js');
test('PWA utilities library exists', fs.existsSync(pwaLibPath));

if (fs.existsSync(pwaLibPath)) {
  const pwaLibContent = fs.readFileSync(pwaLibPath, 'utf8');
  test('PWA lib has service worker registration', pwaLibContent.includes('registerServiceWorker'));
  test('PWA lib has notification functions', pwaLibContent.includes('requestNotificationPermission'));
  test('PWA lib has offline detection', pwaLibContent.includes('isOnline'));
  test('PWA lib has background sync', pwaLibContent.includes('requestBackgroundSync'));
}

console.log('');

// Test 4: Layout Integration
console.log('🏗️ Testing Layout Integration...');
const layoutPath = path.join(__dirname, 'app', 'layout.js');
test('Layout file exists', fs.existsSync(layoutPath));

if (fs.existsSync(layoutPath)) {
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  test('Layout links to manifest', layoutContent.includes('manifest.json'));
  test('Layout has PWA meta tags', layoutContent.includes('mobile-web-app-capable'));
  test('Layout has theme color', layoutContent.includes('theme-color'));
  test('Layout imports PWA components', layoutContent.includes('PWAInstallPrompt'));
  test('Layout has apple touch icon', layoutContent.includes('apple-touch-icon'));
}

console.log('');

// Test 5: Icons
console.log('🎯 Testing PWA Icons...');
const iconsDir = path.join(__dirname, 'public', 'icons');
test('Icons directory exists', fs.existsSync(iconsDir));

if (fs.existsSync(iconsDir)) {
  const iconFiles = fs.readdirSync(iconsDir);
  test('Has icon files', iconFiles.length > 0);
  test('Has 192x192 icon', iconFiles.some(file => file.includes('192x192')));
  test('Has 512x512 icon', iconFiles.some(file => file.includes('512x512')));
  test('Has shortcut icons', iconFiles.some(file => file.includes('shortcut')));
}

const faviconPath = path.join(__dirname, 'public', 'icon.svg');
test('Favicon exists', fs.existsSync(faviconPath));

const appleTouchIconPath = path.join(__dirname, 'public', 'apple-touch-icon.svg');
test('Apple touch icon exists', fs.existsSync(appleTouchIconPath));

console.log('');

// Test 6: API Endpoints
console.log('🔌 Testing API Endpoints...');
const pushSubscribePath = path.join(__dirname, 'app', 'api', 'push-subscribe', 'route.js');
test('Push subscribe API exists', fs.existsSync(pushSubscribePath));

const pushUnsubscribePath = path.join(__dirname, 'app', 'api', 'push-unsubscribe', 'route.js');
test('Push unsubscribe API exists', fs.existsSync(pushUnsubscribePath));

console.log('');

// Test 7: Offline Page
console.log('📱 Testing Offline Support...');
const offlinePage = path.join(__dirname, 'app', 'offline', 'page.js');
test('Offline page exists', fs.existsSync(offlinePage));

if (fs.existsSync(offlinePage)) {
  const offlineContent = fs.readFileSync(offlinePage, 'utf8');
  test('Offline page has proper content', offlineContent.includes('offline') || offlineContent.includes('Offline'));
  test('Offline page has navigation options', offlineContent.includes('cart') || offlineContent.includes('wishlist'));
}

console.log('');

// Test 8: Client Scripts Integration
console.log('⚙️ Testing Client Scripts...');
const clientScriptsPath = path.join(__dirname, 'components', 'ClientScripts.jsx');
test('Client scripts component exists', fs.existsSync(clientScriptsPath));

if (fs.existsSync(clientScriptsPath)) {
  const clientScriptsContent = fs.readFileSync(clientScriptsPath, 'utf8');
  test('Client scripts register service worker', clientScriptsContent.includes('registerServiceWorker'));
}

console.log('');

// Test Results
console.log('📊 Test Results:');
console.log('================');
console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);

const successRate = (passedTests / totalTests * 100).toFixed(1);
console.log(`📈 Success Rate: ${successRate}%`);

console.log('');

if (passedTests === totalTests) {
  console.log('🎉 All PWA tests passed! Your app is ready for installation.');
  console.log('');
  console.log('🚀 Next Steps:');
  console.log('1. Generate PNG icons from SVG files');
  console.log('2. Test on mobile devices');
  console.log('3. Deploy to production');
  console.log('4. Test installation on different browsers');
  console.log('5. Set up push notification server (optional)');
} else {
  console.log('⚠️ Some PWA features need attention.');
  console.log('');
  console.log('🔧 Recommendations:');
  
  if (!fs.existsSync(manifestPath)) {
    console.log('- Create manifest.json file');
  }
  if (!fs.existsSync(swPath)) {
    console.log('- Create service worker (sw.js)');
  }
  if (!fs.existsSync(iconsDir)) {
    console.log('- Generate PWA icons');
  }
  if (!fs.existsSync(offlinePage)) {
    console.log('- Create offline page');
  }
}

console.log('');
console.log('📱 Testing Instructions:');
console.log('========================');
console.log('1. Start your development server: npm run dev');
console.log('2. Open Chrome DevTools > Application > Manifest');
console.log('3. Check "Service Workers" tab for registration');
console.log('4. Test offline functionality in Network tab');
console.log('5. Look for "Install App" prompt in address bar');
console.log('6. Test on mobile devices for full PWA experience');

console.log('');
console.log('🔍 PWA Audit Tools:');
console.log('===================');
console.log('- Chrome DevTools Lighthouse');
console.log('- PWA Builder (https://www.pwabuilder.com/)');
console.log('- Web.dev PWA Checklist');
console.log('- Chrome DevTools Application tab');

console.log('');
console.log('📞 Support:');
console.log('===========');
console.log('Email: <EMAIL>');
console.log('Phone: +44 7447 186806');
