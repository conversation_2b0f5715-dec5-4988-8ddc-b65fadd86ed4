{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/ReactDevOverlay.tsx"], "names": ["React", "ACTION_UNHANDLED_ERROR", "ShadowPort<PERSON>", "BuildError", "Errors", "parseStack", "Base", "ComponentStyles", "CssReset", "RootLayoutMissingTagsError", "ReactDevOverlay", "PureComponent", "getDerivedStateFromError", "error", "stack", "reactError", "id", "event", "type", "reason", "frames", "componentDidCatch", "componentErr", "props", "onReactError", "render", "state", "children", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "hasMissingTags", "rootLayoutMissingTags", "isMounted", "html", "head", "body", "missingTags", "message", "versionInfo", "isAppDir", "initialDisplayState", "undefined"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,sBAAsB,QAA2B,YAAW;AAErE,SAASC,YAAY,QAAQ,sCAAqC;AAClE,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,MAAM,QAAQ,+BAA8B;AAErD,SAASC,UAAU,QAAQ,iCAAgC;AAC3D,SAASC,IAAI,QAAQ,0BAAyB;AAC9C,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,QAAQ,QAAQ,8BAA6B;AACtD,SAASC,0BAA0B,QAAQ,uDAAsD;AAKlF,MAAMC,wBAAwBV,MAAMW,aAAa;IAU9D,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,IAAI,CAACA,MAAMC,KAAK,EAAE,OAAO;YAAEC,YAAY;QAAK;QAC5C,OAAO;YACLA,YAAY;gBACVC,IAAI;gBACJC,OAAO;oBACLC,MAAMjB;oBACNkB,QAAQN;oBACRO,QAAQf,WAAWQ,MAAMC,KAAK;gBAChC;YACF;QACF;IACF;IAEAO,kBAAkBC,YAAmB,EAAE;QACrC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACF;IAC1B;IAEAG,SAAS;YAMwBC,8BAmBtBA;QAxBT,MAAM,EAAEA,KAAK,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACJ,KAAK;QACtC,MAAM,EAAER,UAAU,EAAE,GAAG,IAAI,CAACW,KAAK;QAEjC,MAAME,gBAAgBF,MAAMG,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQL,MAAMM,MAAM,CAACC,MAAM;QACpD,MAAMC,iBAAiBH,SAAQL,+BAAAA,MAAMS,qBAAqB,qBAA3BT,6BAA6BO,MAAM;QAClE,MAAMG,YACJR,iBAAiBE,oBAAoBf,cAAcmB;QAErD,qBACE;;gBACGnB,2BACC,MAACsB;;sCACC,KAACC;sCACD,KAACC;;qBAGHZ;gBAEDS,0BACC,MAAClC;;sCACC,KAACM;sCACD,KAACF;sCACD,KAACC;wBACAmB,EAAAA,gCAAAA,MAAMS,qBAAqB,qBAA3BT,8BAA6BO,MAAM,kBAClC,KAACxB;4BACC+B,aAAad,MAAMS,qBAAqB;6BAExCP,8BACF,KAACzB;4BACCsC,SAASf,MAAMG,UAAU;4BACzBa,aAAahB,MAAMgB,WAAW;6BAE9B3B,2BACF,KAACX;4BACCuC,UAAU;4BACVD,aAAahB,MAAMgB,WAAW;4BAC9BE,qBAAoB;4BACpBZ,QAAQ;gCAACjB;6BAAW;6BAEpBe,iCACF,KAAC1B;4BACCuC,UAAU;4BACVC,qBAAoB;4BACpBZ,QAAQN,MAAMM,MAAM;4BACpBU,aAAahB,MAAMgB,WAAW;6BAE9BG;;qBAEJA;;;IAGV;;;aAzEAnB,QAAQ;YAAEX,YAAY;QAAK;;AA0E7B;AAlFA,SAAqBL,6BAkFpB"}