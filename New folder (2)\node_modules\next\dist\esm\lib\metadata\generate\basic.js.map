{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "names": ["React", "Meta", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiMeta", "ViewportMetaKeys", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "viewportKey", "value", "ViewportMeta", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "BasicMeta", "metadata", "meta", "charSet", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "manifest", "crossOrigin", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "category", "classification", "other", "Object", "entries", "Array", "isArray", "contentItem", "ItunesMeta", "itunes", "appId", "appArgument", "FacebookMeta", "facebook", "admins", "property", "admin", "formatDetectionKeys", "FormatDetectionMeta", "formatDetection", "key", "AppleWebAppMeta", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "VerificationMeta", "verification", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": ";AAOA,OAAOA,WAAW,QAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,QAAQ,SAAQ;AACpD,SAASC,gBAAgB,QAAQ,eAAc;AAE/C,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBJ,iBAAkB;YAC3C,MAAMK,cAAcD;YACpB,IAAIC,eAAeH,UAAU;gBAC3B,IAAII,QAAQJ,QAAQ,CAACG,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIH,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEH,gBAAgB,CAACK,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACzD;QACF;IACF;IACA,OAAOH;AACT;AAEA,OAAO,SAASI,aAAa,EAAEL,QAAQ,EAAkC;IACvE,OAAOJ,WAAW;QAChBD,KAAK;YAAEW,MAAM;YAAYC,SAASR,sBAAsBC;QAAU;WAC9DA,SAASQ,UAAU,GACnBR,SAASQ,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBb,KAAK;gBACHW,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNhB,KAAK;YAAEW,MAAM;YAAgBC,SAASP,SAASY,WAAW;QAAC;KAC5D;AACH;AAEA,OAAO,SAASC,UAAU,EAAEC,QAAQ,EAAkC;QAwBhCA,oBAIFA,kBACGA;IA5BrC,OAAOlB,WAAW;sBAChB,KAACmB;YAAKC,SAAQ;;QACdF,SAASG,KAAK,KAAK,QAAQH,SAASG,KAAK,CAACC,QAAQ,iBAChD,KAACD;sBAAOH,SAASG,KAAK,CAACC,QAAQ;aAC7B;QACJvB,KAAK;YAAEW,MAAM;YAAeC,SAASO,SAASK,WAAW;QAAC;QAC1DxB,KAAK;YAAEW,MAAM;YAAoBC,SAASO,SAASM,eAAe;QAAC;WAC/DN,SAASO,OAAO,GAChBP,SAASO,OAAO,CAACZ,GAAG,CAAC,CAACa,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,KAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJhC,KAAK;oBAAEW,MAAM;oBAAUC,SAASe,OAAOhB,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNQ,SAASc,QAAQ,iBACf,KAACJ;YACCC,KAAI;YACJC,MAAMZ,SAASc,QAAQ,CAACD,QAAQ;YAChCE,aAAY;aAEZ;QACJlC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASgB,SAAS;QAAC;QACtDnC,KAAK;YAAEW,MAAM;YAAYC,OAAO,GAAEO,qBAAAA,SAASiB,QAAQ,qBAAjBjB,mBAAmBkB,IAAI,CAAC;QAAK;QAC/DrC,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASmB,QAAQ;QAAC;QACpDtC,KAAK;YAAEW,MAAM;YAAWC,SAASO,SAASoB,OAAO;QAAC;QAClDvC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASqB,SAAS;QAAC;QACtDxC,KAAK;YAAEW,MAAM;YAAUC,OAAO,GAAEO,mBAAAA,SAASsB,MAAM,qBAAftB,iBAAiBuB,KAAK;QAAC;QACvD1C,KAAK;YAAEW,MAAM;YAAaC,OAAO,GAAEO,oBAAAA,SAASsB,MAAM,qBAAftB,kBAAiBwB,SAAS;QAAC;QAC9D3C,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASyB,QAAQ;QAAC;WAChDzB,SAAS0B,QAAQ,GACjB1B,SAAS0B,QAAQ,CAAC/B,GAAG,CAAC,CAACgC,wBACrB,KAACjB;gBAAKC,KAAI;gBAAWC,MAAMe;kBAE7B,EAAE;WACF3B,SAAS4B,MAAM,GACf5B,SAAS4B,MAAM,CAACjC,GAAG,CAAC,CAACkC,sBAAU,KAACnB;gBAAKC,KAAI;gBAASC,MAAMiB;kBACxD,EAAE;WACF7B,SAAS8B,SAAS,GAClB9B,SAAS8B,SAAS,CAACnC,GAAG,CAAC,CAACoC,yBACtB,KAACrB;gBAAKC,KAAI;gBAAYC,MAAMmB;kBAE9B,EAAE;QACNlD,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASgC,QAAQ;QAAC;QACpDnD,KAAK;YAAEW,MAAM;YAAkBC,SAASO,SAASiC,cAAc;QAAC;WAC5DjC,SAASkC,KAAK,GACdC,OAAOC,OAAO,CAACpC,SAASkC,KAAK,EAAEvC,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ;YACjD,IAAI4C,MAAMC,OAAO,CAAC7C,UAAU;gBAC1B,OAAOA,QAAQE,GAAG,CAAC,CAAC4C,cAClB1D,KAAK;wBAAEW;wBAAMC,SAAS8C;oBAAY;YAEtC,OAAO;gBACL,OAAO1D,KAAK;oBAAEW;oBAAMC;gBAAQ;YAC9B;QACF,KACA,EAAE;KACP;AACH;AAEA,OAAO,SAAS+C,WAAW,EAAEC,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAIhD,UAAU,CAAC,OAAO,EAAEiD,MAAM,CAAC;IAC/B,IAAIC,aAAa;QACflD,WAAW,CAAC,eAAe,EAAEkD,YAAY,CAAC;IAC5C;IACA,qBAAO,KAAC1C;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAASmD,aAAa,EAC3BC,QAAQ,EAGT;IACC,IAAI,CAACA,UAAU,OAAO;IAEtB,MAAM,EAAEH,KAAK,EAAEI,MAAM,EAAE,GAAGD;IAE1B,OAAO/D,WAAW;QAChB4D,sBAAQ,KAACzC;YAAK8C,UAAS;YAAYtD,SAASiD;aAAY;WACpDI,SACAA,OAAOnD,GAAG,CAAC,CAACqD,sBAAU,KAAC/C;gBAAK8C,UAAS;gBAAYtD,SAASuD;kBAC1D,EAAE;KACP;AACH;AAEA,MAAMC,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACD,OAAO,SAASC,oBAAoB,EAClCC,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAI1D,UAAU;IACd,KAAK,MAAM2D,OAAOH,oBAAqB;QACrC,IAAIG,OAAOD,iBAAiB;YAC1B,IAAI1D,SAASA,WAAW;YACxBA,WAAW,CAAC,EAAE2D,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,KAACnD;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAAS4D,gBAAgB,EAC9BC,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAEpD,KAAK,EAAEqD,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOxE,WAAW;QAChByE,UACI1E,KAAK;YAAEW,MAAM;YAAgCC,SAAS;QAAM,KAC5D;QACJZ,KAAK;YAAEW,MAAM;YAA8BC,SAASU;QAAM;QAC1DqD,eACIA,aAAa7D,GAAG,CAAC,CAAC+D,sBAChB,KAAChD;gBACCE,MAAM8C,MAAMjD,GAAG;gBACfZ,OAAO6D,MAAM7D,KAAK;gBAClBc,KAAI;kBAGR;QACJ8C,iBACI5E,KAAK;YACHW,MAAM;YACNC,SAASgE;QACX,KACA;KACL;AACH;AAEA,OAAO,SAASE,iBAAiB,EAC/BC,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAO9E,WAAW;QAChBC,UAAU;YACR8E,YAAY;YACZC,UAAUF,aAAaG,MAAM;QAC/B;QACAhF,UAAU;YAAE8E,YAAY;YAASC,UAAUF,aAAaI,KAAK;QAAC;QAC9DjF,UAAU;YACR8E,YAAY;YACZC,UAAUF,aAAaK,MAAM;QAC/B;QACAlF,UAAU;YAAE8E,YAAY;YAAMC,UAAUF,aAAaM,EAAE;QAAC;WACpDN,aAAa1B,KAAK,GAClBC,OAAOC,OAAO,CAACwB,aAAa1B,KAAK,EAAEvC,GAAG,CAAC,CAAC,CAACyD,KAAK9D,MAAM,GAClDP,UAAU;gBAAE8E,YAAYT;gBAAKU,UAAUxE;YAAM,MAE/C,EAAE;KACP;AACH"}