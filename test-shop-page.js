const axios = require('axios');

async function testShopPage() {
  console.log('🛍️ Testing Shop Page...\n');
  
  try {
    console.log('1. Loading shop page...');
    const response = await axios.get('http://localhost:3000/shop', { 
      timeout: 15000 
    });
    
    const content = response.data;
    console.log(`✅ Shop page loaded (${content.length} characters)`);
    
    // Check if it's showing loading state or actual products
    const isLoading = content.includes('Loading amazing deals') || 
                     content.includes('animate-spin');
    
    const hasProducts = content.includes('product-card') || 
                       content.includes('Add to Cart') ||
                       content.includes('$') && content.includes('price');
    
    const hasError = content.includes('error') || 
                    content.includes('Error') ||
                    content.includes('404') ||
                    content.includes('not found');
    
    console.log('\n📊 Shop Page Analysis:');
    console.log(`   Loading state: ${isLoading ? 'Yes' : 'No'}`);
    console.log(`   Has products: ${hasProducts ? 'Yes' : 'No'}`);
    console.log(`   Has errors: ${hasError ? 'Yes' : 'No'}`);
    
    // Look for specific product elements
    const productElements = {
      'product cards': content.includes('product-card') || content.includes('ProductCard'),
      'prices': content.match(/\$\d+/g)?.length || 0,
      'add to cart': content.includes('Add to Cart'),
      'product images': content.includes('product') && content.includes('image'),
      'filters': content.includes('filter') || content.includes('Filter'),
      'pagination': content.includes('page') && content.includes('next')
    };
    
    console.log('\n🔍 Product Elements:');
    Object.entries(productElements).forEach(([element, found]) => {
      if (typeof found === 'number') {
        console.log(`   ${element}: ${found} found`);
      } else {
        console.log(`   ${element}: ${found ? 'Present' : 'Missing'}`);
      }
    });
    
    // Check if it's using demo data or real data
    const demoIndicators = [
      'demo',
      'sample',
      'placeholder',
      'example',
      'test product'
    ];
    
    const hasDemoData = demoIndicators.some(indicator => 
      content.toLowerCase().includes(indicator)
    );
    
    console.log(`\n📦 Data Type: ${hasDemoData ? 'Demo/Placeholder' : 'Real/API'}`);
    
    if (isLoading) {
      console.log('\n⚠️  Shop page is stuck in loading state');
      console.log('   This is likely due to API authentication issues');
    } else if (!hasProducts) {
      console.log('\n⚠️  Shop page loaded but no products found');
      console.log('   This could be due to API errors or empty response');
    } else {
      console.log('\n✅ Shop page appears to be working correctly');
    }
    
  } catch (error) {
    console.log('❌ Error testing shop page:');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Make sure the development server is running');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   The page is taking too long to load');
    }
  }
}

testShopPage();
