import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function GET() {
  try {
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    console.log('🔍 Starting comprehensive product diagnosis...');
    
    // Test different status queries
    const statusTests = [
      { status: 'any', name: 'All Products' },
      { status: 'publish', name: 'Published' },
      { status: 'draft', name: 'Draft' },
      { status: 'private', name: 'Private' },
      { status: 'pending', name: 'Pending' },
      { status: 'trash', name: 'Trash' }
    ];

    const results = {};
    
    for (const test of statusTests) {
      try {
        console.log(`🔍 Testing status: ${test.status}`);
        
        let allProducts = [];
        let page = 1;
        let hasMore = true;
        
        while (hasMore && page <= 10) { // Limit to 10 pages for safety
          const response = await api.get('products', {
            per_page: 100,
            page: page,
            status: test.status
          });
          
          const products = response.data;
          allProducts = [...allProducts, ...products];
          
          hasMore = products.length === 100;
          page++;
          
          console.log(`📦 ${test.name} - Page ${page - 1}: ${products.length} products`);
        }
        
        // Analyze the products
        const statusBreakdown = {};
        const sampleProducts = [];
        
        allProducts.forEach((product, index) => {
          const status = product.status || 'unknown';
          statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
          
          // Keep first 5 products as samples
          if (index < 5) {
            sampleProducts.push({
              id: product.id,
              name: product.name,
              status: product.status,
              date_created: product.date_created,
              images: product.images ? product.images.length : 0
            });
          }
        });
        
        results[test.status] = {
          name: test.name,
          total: allProducts.length,
          statusBreakdown: statusBreakdown,
          sampleProducts: sampleProducts
        };
        
        console.log(`✅ ${test.name}: Found ${allProducts.length} products`);
        
      } catch (error) {
        console.error(`❌ Error testing ${test.status}:`, error.message);
        results[test.status] = {
          name: test.name,
          error: error.message
        };
      }
    }

    // Additional API info
    const apiInfo = {
      baseURL: WC_CONFIG.baseURL,
      hasConsumerKey: !!WC_CONFIG.consumerKey,
      hasConsumerSecret: !!WC_CONFIG.consumerSecret,
      consumerKeyLength: WC_CONFIG.consumerKey ? WC_CONFIG.consumerKey.length : 0
    };

    return NextResponse.json({
      success: true,
      message: 'Product diagnosis completed',
      apiInfo: apiInfo,
      results: results,
      summary: {
        totalByAny: results.any?.total || 0,
        totalPublished: results.publish?.total || 0,
        totalDraft: results.draft?.total || 0,
        totalPrivate: results.private?.total || 0,
        totalPending: results.pending?.total || 0
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Diagnosis API error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
