"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reviews/route";
exports.ids = ["app/api/reviews/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_reviews_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/reviews/route.js */ \"(rsc)/./app/api/reviews/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reviews/route\",\n        pathname: \"/api/reviews\",\n        filename: \"route\",\n        bundlePath: \"app/api/reviews/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Last1\\\\New folder (2)\\\\app\\\\api\\\\reviews\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_reviews_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/reviews/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/reviews/route.js":
/*!**********************************!*\
  !*** ./app/api/reviews/route.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_reviews__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/reviews */ \"(rsc)/./lib/reviews.js\");\n\n\n/**\n * GET /api/reviews - Get reviews with various filters\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const productId = searchParams.get(\"productId\");\n        const page = parseInt(searchParams.get(\"page\")) || 1;\n        const limit = parseInt(searchParams.get(\"limit\")) || 10;\n        const sortBy = searchParams.get(\"sortBy\") || \"newest\";\n        const filterRating = searchParams.get(\"rating\");\n        const query = searchParams.get(\"q\");\n        const recent = searchParams.get(\"recent\");\n        // Get recent reviews for homepage\n        if (recent === \"true\") {\n            const recentReviews = await (0,_lib_reviews__WEBPACK_IMPORTED_MODULE_1__.getRecentReviews)(parseInt(recent) || 5);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                reviews: recentReviews\n            });\n        }\n        // Search reviews\n        if (query) {\n            const searchResults = await (0,_lib_reviews__WEBPACK_IMPORTED_MODULE_1__.searchReviews)(query, {\n                productId,\n                minRating: searchParams.get(\"minRating\"),\n                maxRating: searchParams.get(\"maxRating\")\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                reviews: searchResults,\n                total: searchResults.length\n            });\n        }\n        // Get product reviews\n        if (productId) {\n            const result = await (0,_lib_reviews__WEBPACK_IMPORTED_MODULE_1__.getProductReviews)(productId, {\n                page,\n                limit,\n                sortBy,\n                filterRating\n            });\n            // Also get review statistics\n            const stats = await (0,_lib_reviews__WEBPACK_IMPORTED_MODULE_1__.getProductReviewStats)(productId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                ...result,\n                stats\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Product ID is required\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/reviews:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch reviews\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/reviews - Submit a new review\n */ async function POST(request) {\n    try {\n        const reviewData = await request.json();\n        // Validate required fields\n        const { productId, userId, userName, rating, comment } = reviewData;\n        if (!productId || !userId || !userName || !rating || !comment) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields: productId, userId, userName, rating, comment\"\n            }, {\n                status: 400\n            });\n        }\n        if (rating < 1 || rating > 5) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Rating must be between 1 and 5\"\n            }, {\n                status: 400\n            });\n        }\n        if (comment.length < 10) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Comment must be at least 10 characters long\"\n            }, {\n                status: 400\n            });\n        }\n        if (comment.length > 1000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Comment must be less than 1000 characters\"\n            }, {\n                status: 400\n            });\n        }\n        // Submit the review\n        const newReview = await (0,_lib_reviews__WEBPACK_IMPORTED_MODULE_1__.submitReview)(reviewData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Review submitted successfully\",\n            review: newReview\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/reviews:\", error);\n        if (error.message === \"You have already reviewed this product\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to submit review\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/reviews/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/reviews.js":
/*!************************!*\
  !*** ./lib/reviews.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteReview: () => (/* binding */ deleteReview),\n/* harmony export */   getProductReviewStats: () => (/* binding */ getProductReviewStats),\n/* harmony export */   getProductReviews: () => (/* binding */ getProductReviews),\n/* harmony export */   getRecentReviews: () => (/* binding */ getRecentReviews),\n/* harmony export */   getUserReviews: () => (/* binding */ getUserReviews),\n/* harmony export */   searchReviews: () => (/* binding */ searchReviews),\n/* harmony export */   submitReview: () => (/* binding */ submitReview),\n/* harmony export */   updateReviewHelpfulness: () => (/* binding */ updateReviewHelpfulness)\n/* harmony export */ });\n// Reviews Management Library for Deal4u\n/**\n * Mock reviews database (in production, use a real database)\n */ let reviewsDatabase = [\n    {\n        id: \"1\",\n        productId: \"1\",\n        userId: \"user1\",\n        userName: \"Sarah Johnson\",\n        userEmail: \"<EMAIL>\",\n        userAvatar: \"https://ui-avatars.com/api/?name=Sarah+Johnson&background=3b82f6&color=fff\",\n        rating: 5,\n        title: \"Excellent quality!\",\n        comment: \"This product exceeded my expectations. Great build quality and fast shipping. Highly recommended!\",\n        images: [\n            \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop\",\n            \"https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?w=300&h=300&fit=crop\"\n        ],\n        verified: true,\n        helpful: 12,\n        notHelpful: 1,\n        createdAt: \"2024-01-15T10:30:00Z\",\n        updatedAt: \"2024-01-15T10:30:00Z\"\n    },\n    {\n        id: \"2\",\n        productId: \"1\",\n        userId: \"user2\",\n        userName: \"Mike Chen\",\n        userEmail: \"<EMAIL>\",\n        userAvatar: \"https://ui-avatars.com/api/?name=Mike+Chen&background=10b981&color=fff\",\n        rating: 4,\n        title: \"Good value for money\",\n        comment: \"Solid product with good features. Delivery was quick and packaging was secure.\",\n        images: [],\n        verified: true,\n        helpful: 8,\n        notHelpful: 0,\n        createdAt: \"2024-01-10T14:20:00Z\",\n        updatedAt: \"2024-01-10T14:20:00Z\"\n    },\n    {\n        id: \"3\",\n        productId: \"2\",\n        userId: \"user3\",\n        userName: \"Emma Wilson\",\n        userEmail: \"<EMAIL>\",\n        userAvatar: \"https://ui-avatars.com/api/?name=Emma+Wilson&background=f59e0b&color=fff\",\n        rating: 5,\n        title: \"Perfect!\",\n        comment: \"Exactly what I was looking for. Works perfectly and looks great.\",\n        images: [\n            \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=300&fit=crop\"\n        ],\n        verified: false,\n        helpful: 5,\n        notHelpful: 0,\n        createdAt: \"2024-01-08T09:15:00Z\",\n        updatedAt: \"2024-01-08T09:15:00Z\"\n    }\n];\n/**\n * Get reviews for a specific product\n */ const getProductReviews = async (productId, options = {})=>{\n    try {\n        const { page = 1, limit = 10, sortBy = \"newest\", filterRating = null } = options;\n        let reviews = reviewsDatabase.filter((review)=>review.productId === productId.toString());\n        // Filter by rating if specified\n        if (filterRating) {\n            reviews = reviews.filter((review)=>review.rating === parseInt(filterRating));\n        }\n        // Sort reviews\n        switch(sortBy){\n            case \"oldest\":\n                reviews.sort((a, b)=>new Date(a.createdAt) - new Date(b.createdAt));\n                break;\n            case \"highest\":\n                reviews.sort((a, b)=>b.rating - a.rating);\n                break;\n            case \"lowest\":\n                reviews.sort((a, b)=>a.rating - b.rating);\n                break;\n            case \"helpful\":\n                reviews.sort((a, b)=>b.helpful - a.helpful);\n                break;\n            case \"newest\":\n            default:\n                reviews.sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n                break;\n        }\n        // Pagination\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedReviews = reviews.slice(startIndex, endIndex);\n        return {\n            reviews: paginatedReviews,\n            pagination: {\n                currentPage: page,\n                totalPages: Math.ceil(reviews.length / limit),\n                totalReviews: reviews.length,\n                hasNextPage: endIndex < reviews.length,\n                hasPrevPage: page > 1\n            }\n        };\n    } catch (error) {\n        console.error(\"Error getting product reviews:\", error);\n        throw error;\n    }\n};\n/**\n * Get review statistics for a product\n */ const getProductReviewStats = async (productId)=>{\n    try {\n        const reviews = reviewsDatabase.filter((review)=>review.productId === productId.toString());\n        if (reviews.length === 0) {\n            return {\n                totalReviews: 0,\n                averageRating: 0,\n                ratingDistribution: {\n                    5: 0,\n                    4: 0,\n                    3: 0,\n                    2: 0,\n                    1: 0\n                },\n                verifiedPurchases: 0\n            };\n        }\n        const totalReviews = reviews.length;\n        const averageRating = reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews;\n        const verifiedPurchases = reviews.filter((review)=>review.verified).length;\n        const ratingDistribution = {\n            5: 0,\n            4: 0,\n            3: 0,\n            2: 0,\n            1: 0\n        };\n        reviews.forEach((review)=>{\n            ratingDistribution[review.rating]++;\n        });\n        return {\n            totalReviews,\n            averageRating: Math.round(averageRating * 10) / 10,\n            ratingDistribution,\n            verifiedPurchases\n        };\n    } catch (error) {\n        console.error(\"Error getting review stats:\", error);\n        throw error;\n    }\n};\n/**\n * Submit a new review\n */ const submitReview = async (reviewData)=>{\n    try {\n        const { productId, userId, userName, userEmail, rating, title, comment, images = [] } = reviewData;\n        // Validate required fields\n        if (!productId || !userId || !rating || !comment) {\n            throw new Error(\"Missing required fields\");\n        }\n        if (rating < 1 || rating > 5) {\n            throw new Error(\"Rating must be between 1 and 5\");\n        }\n        // Check if user already reviewed this product\n        const existingReview = reviewsDatabase.find((review)=>review.productId === productId.toString() && review.userId === userId);\n        if (existingReview) {\n            throw new Error(\"You have already reviewed this product\");\n        }\n        // Create new review\n        const newReview = {\n            id: Date.now().toString(),\n            productId: productId.toString(),\n            userId,\n            userName,\n            userEmail,\n            userAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=3b82f6&color=fff`,\n            rating: parseInt(rating),\n            title: title || \"\",\n            comment,\n            images: images || [],\n            verified: false,\n            helpful: 0,\n            notHelpful: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        reviewsDatabase.push(newReview);\n        return newReview;\n    } catch (error) {\n        console.error(\"Error submitting review:\", error);\n        throw error;\n    }\n};\n/**\n * Update review helpfulness\n */ const updateReviewHelpfulness = async (reviewId, isHelpful)=>{\n    try {\n        const review = reviewsDatabase.find((r)=>r.id === reviewId);\n        if (!review) {\n            throw new Error(\"Review not found\");\n        }\n        if (isHelpful) {\n            review.helpful++;\n        } else {\n            review.notHelpful++;\n        }\n        review.updatedAt = new Date().toISOString();\n        return review;\n    } catch (error) {\n        console.error(\"Error updating review helpfulness:\", error);\n        throw error;\n    }\n};\n/**\n * Get user's reviews\n */ const getUserReviews = async (userId)=>{\n    try {\n        const userReviews = reviewsDatabase.filter((review)=>review.userId === userId);\n        return userReviews.sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n    } catch (error) {\n        console.error(\"Error getting user reviews:\", error);\n        throw error;\n    }\n};\n/**\n * Delete a review (user or admin)\n */ const deleteReview = async (reviewId, userId, isAdmin = false)=>{\n    try {\n        const reviewIndex = reviewsDatabase.findIndex((r)=>r.id === reviewId);\n        if (reviewIndex === -1) {\n            throw new Error(\"Review not found\");\n        }\n        const review = reviewsDatabase[reviewIndex];\n        // Check permissions\n        if (!isAdmin && review.userId !== userId) {\n            throw new Error(\"Unauthorized to delete this review\");\n        }\n        reviewsDatabase.splice(reviewIndex, 1);\n        return true;\n    } catch (error) {\n        console.error(\"Error deleting review:\", error);\n        throw error;\n    }\n};\n/**\n * Get recent reviews (for homepage/dashboard)\n */ const getRecentReviews = async (limit = 5)=>{\n    try {\n        const recentReviews = reviewsDatabase.sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt)).slice(0, limit);\n        return recentReviews;\n    } catch (error) {\n        console.error(\"Error getting recent reviews:\", error);\n        throw error;\n    }\n};\n/**\n * Search reviews\n */ const searchReviews = async (query, options = {})=>{\n    try {\n        const { productId, minRating, maxRating } = options;\n        let reviews = reviewsDatabase;\n        // Filter by product if specified\n        if (productId) {\n            reviews = reviews.filter((review)=>review.productId === productId.toString());\n        }\n        // Filter by rating range\n        if (minRating) {\n            reviews = reviews.filter((review)=>review.rating >= minRating);\n        }\n        if (maxRating) {\n            reviews = reviews.filter((review)=>review.rating <= maxRating);\n        }\n        // Search in title and comment\n        if (query) {\n            const searchTerm = query.toLowerCase();\n            reviews = reviews.filter((review)=>review.title.toLowerCase().includes(searchTerm) || review.comment.toLowerCase().includes(searchTerm) || review.userName.toLowerCase().includes(searchTerm));\n        }\n        return reviews.sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n    } catch (error) {\n        console.error(\"Error searching reviews:\", error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/reviews.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();