{"version": 3, "sources": ["../../../src/lib/metadata/get-metadata-route.ts"], "names": ["isMetadataRoute", "isStaticMetadataRoute", "path", "interpolateDynamicPath", "getNamedRouteRegex", "djb2Hash", "normalizeAppPath", "normalizePathSep", "getMetadataRouteSuffix", "page", "suffix", "includes", "toString", "slice", "fillMetadataSegment", "segment", "params", "imageSegment", "pathname", "routeRegex", "route", "routeSuffix", "name", "ext", "parse", "join", "normalizeMetadataRoute", "endsWith", "pathnamePrefix", "basename", "length", "dir", "baseName", "isStaticRoute", "posix"], "mappings": "AAAA,SAASA,eAAe,EAAEC,qBAAqB,QAAQ,sBAAqB;AAC5E,OAAOC,UAAU,mCAAkC;AACnD,SAASC,sBAAsB,QAAQ,4BAA2B;AAClE,SAASC,kBAAkB,QAAQ,4CAA2C;AAC9E,SAASC,QAAQ,QAAQ,wBAAuB;AAChD,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,gBAAgB,QAAQ,gDAA+C;AAEhF;;;;;;;CAOC,GACD,SAASC,uBAAuBC,IAAY;IAC1C,IAAIC,SAAS;IAEb,IAAI,AAACD,KAAKE,QAAQ,CAAC,QAAQF,KAAKE,QAAQ,CAAC,QAASF,KAAKE,QAAQ,CAAC,MAAM;QACpED,SAASL,SAASI,MAAMG,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;IAChD;IACA,OAAOH;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASI,oBACdC,OAAe,EACfC,MAAW,EACXC,YAAoB;IAEpB,MAAMC,WAAWZ,iBAAiBS;IAClC,MAAMI,aAAaf,mBAAmBc,UAAU;IAChD,MAAME,QAAQjB,uBAAuBe,UAAUF,QAAQG;IACvD,MAAMT,SAASF,uBAAuBO;IACtC,MAAMM,cAAcX,SAAS,CAAC,CAAC,EAAEA,OAAO,CAAC,GAAG;IAE5C,MAAM,EAAEY,IAAI,EAAEC,GAAG,EAAE,GAAGrB,KAAKsB,KAAK,CAACP;IAEjC,OAAOV,iBAAiBL,KAAKuB,IAAI,CAACL,OAAO,CAAC,EAAEE,KAAK,EAAED,YAAY,EAAEE,IAAI,CAAC;AACxE;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASG,uBAAuBjB,IAAY;IACjD,IAAI,CAACT,gBAAgBS,OAAO;QAC1B,OAAOA;IACT;IACA,IAAIW,QAAQX;IACZ,IAAIC,SAAS;IACb,IAAID,SAAS,WAAW;QACtBW,SAAS;IACX,OAAO,IAAIX,SAAS,aAAa;QAC/BW,SAAS;IACX,OAAO,IAAIX,KAAKkB,QAAQ,CAAC,aAAa;QACpCP,SAAS;IACX,OAAO;QACL,wEAAwE;QACxE,MAAMQ,iBAAiBnB,KAAKI,KAAK,CAAC,GAAG,CAAEX,CAAAA,KAAK2B,QAAQ,CAACpB,MAAMqB,MAAM,GAAG,CAAA;QACpEpB,SAASF,uBAAuBoB;IAClC;IACA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAI,CAACR,MAAMO,QAAQ,CAAC,WAAW;QAC7B,MAAM,EAAEI,GAAG,EAAET,MAAMU,QAAQ,EAAET,GAAG,EAAE,GAAGrB,KAAKsB,KAAK,CAACJ;QAChD,MAAMa,gBAAgBhC,sBAAsBQ;QAE5CW,QAAQlB,KAAKgC,KAAK,CAACT,IAAI,CACrBM,KACA,CAAC,EAAEC,SAAS,EAAEtB,SAAS,CAAC,CAAC,EAAEA,OAAO,CAAC,GAAG,GAAG,EAAEa,IAAI,CAAC,EAChDU,gBAAgB,KAAK,0BACrB;IAEJ;IAEA,OAAOb;AACT"}