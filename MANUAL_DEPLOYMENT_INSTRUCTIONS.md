# 📦 Deal4u cPanel Deployment - Manual Instructions

## 🎯 What You Need to Do

Since the automated script had issues, here's how to manually create your deployment package:

### **Step 1: Create Deployment Folder**
1. Go to your Desktop
2. Create a new folder called `Deal4u_cpanel`

### **Step 2: Copy These Files to Deal4u_cpanel Folder**

**📄 Individual Files (copy these):**
- `server.js`
- `package.json`
- `next.config.js`
- `tailwind.config.js`
- `postcss.config.js`
- `jsconfig.json`

**📁 Entire Folders (copy these complete folders):**
- `.next` folder (CRITICAL - contains your compiled app)
- `app` folder (your pages)
- `components` folder (React components)
- `context` folder (React contexts)
- `lib` folder (WooCommerce API)
- `public` folder (images and static files)

### **Step 3: Your Deal4u_cpanel Folder Should Look Like This:**
```
📁 Deal4u_cpanel/
├── server.js
├── package.json
├── next.config.js
├── tailwind.config.js
├── postcss.config.js
├── jsconfig.json
├── 📁 .next/
├── 📁 app/
├── 📁 components/
├── 📁 context/
├── 📁 lib/
└── 📁 public/
```

### **Step 4: Create ZIP File (Optional)**
1. Right-click on the `Deal4u_cpanel` folder
2. Select "Send to" → "Compressed (zipped) folder"
3. Name it `Deal4u_cpanel.zip`

---

## 🚀 cPanel Deployment Steps

### **Step 1: Create Node.js App in cPanel**
1. Login to your cPanel
2. Find "Node.js Apps" (or "Node.js Selector")
3. Click "Create Application"
4. Fill in these settings:
   - **Node.js Version**: `18.x` (or latest available)
   - **Application Root**: `/public_html/deal4u`
   - **Application URL**: `yourdomain.com/deal4u`
   - **Startup File**: `server.js`
5. Click "Create"

### **Step 2: Upload Files**
1. Go to cPanel File Manager
2. Navigate to `/public_html/deal4u`
3. Upload ALL files and folders from your `Deal4u_cpanel` folder
4. If you created a ZIP, upload and extract it
5. Make sure ALL folders are uploaded completely

### **Step 3: Install Dependencies**
1. Go back to cPanel → Node.js Apps
2. Click on your Deal4u application
3. Go to "Package.json" tab
4. Click "Run NPM Install"
5. Wait 2-3 minutes for installation to complete

### **Step 4: Set Environment Variables**
1. Go to "Environment Variables" tab
2. Add:
   - **Variable**: `NODE_ENV`
   - **Value**: `production`

### **Step 5: Start Your App**
1. Click "Start App" button
2. Wait for status to show "Running"
3. If there are errors, check the logs

### **Step 6: Test Your Dynamic Sync**
1. Visit your application URL
2. Check if the site loads properly
3. **Test the sync**: Add a new product to your WooCommerce store
4. Refresh your website → The new product should appear automatically!

---

## ✅ Success Indicators

When everything works correctly:
- ✅ App shows "Running" status in cPanel
- ✅ You can visit your URL and see your website
- ✅ New products added to WooCommerce appear on your site immediately
- ✅ No build errors in the logs

## 🔧 Troubleshooting

**If app fails to start:**
- Check Node.js version (18.x recommended)
- Ensure all folders (.next, app, components, etc.) are uploaded
- Check cPanel logs for specific error messages
- Verify the startup file is set to `server.js`

**If products don't sync:**
- Check your WooCommerce API credentials in the code
- Verify your WordPress site is accessible
- Check browser console for API errors

---

## 🎉 What Your App Will Do

Once deployed successfully:
- 🔄 **Real-time WooCommerce sync** - Products update automatically
- ⚡ **Instant updates** - New products appear without rebuilding
- 📱 **Live inventory** - Stock levels update in real-time
- 🚀 **Dynamic pricing** - Price changes reflect immediately

Your e-commerce site will stay perfectly synchronized with your WooCommerce store! 🎉
