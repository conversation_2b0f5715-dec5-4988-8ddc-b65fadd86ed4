"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_woocommerceFeatures_js"],{

/***/ "(app-pages-browser)/./lib/woocommerceFeatures.js":
/*!************************************!*\
  !*** ./lib/woocommerceFeatures.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./woocommerce.js */ \"(app-pages-browser)/./lib/woocommerce.js\");\n// WooCommerce Features Integration\n\nclass WooCommerceFeatures {\n    async initialize() {\n        try {\n            console.log(\"\\uD83D\\uDD17 Initializing WooCommerce Features Integration...\");\n            // Test WooCommerce connection\n            const testConnection = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProducts({\n                per_page: 1\n            });\n            if (testConnection) {\n                this.initialized = true;\n                console.log(\"✅ WooCommerce Features Integration ready\");\n                return true;\n            }\n            throw new Error(\"WooCommerce connection failed\");\n        } catch (error) {\n            console.error(\"❌ WooCommerce Features Integration failed:\", error);\n            return false;\n        }\n    }\n    // Cache management\n    setCache(key, data) {\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now()\n        });\n    }\n    getCache(key) {\n        const cached = this.cache.get(key);\n        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\n            return cached.data;\n        }\n        this.cache.delete(key);\n        return null;\n    }\n    // 1. PRODUCT RECOMMENDATIONS - Real WooCommerce Integration\n    async getProductRecommendations(productId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4;\n        try {\n            var _currentProduct_categories;\n            const cacheKey = \"recommendations_\".concat(productId, \"_\").concat(limit);\n            const cached = this.getCache(cacheKey);\n            if (cached) return cached;\n            console.log(\"\\uD83C\\uDFAF Getting recommendations for product \".concat(productId, \"...\"));\n            // Get current product details\n            const currentProduct = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProduct(productId);\n            if (!currentProduct) {\n                throw new Error(\"Product not found\");\n            }\n            // Get products from same categories\n            const categoryIds = ((_currentProduct_categories = currentProduct.categories) === null || _currentProduct_categories === void 0 ? void 0 : _currentProduct_categories.map((cat)=>cat.id)) || [];\n            let recommendations = [];\n            if (categoryIds.length > 0) {\n                // Get products from same categories\n                const categoryProducts = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProducts({\n                    category: categoryIds.join(\",\"),\n                    per_page: limit * 2,\n                    exclude: [\n                        productId\n                    ],\n                    status: \"publish\",\n                    orderby: \"popularity\"\n                });\n                recommendations = categoryProducts || [];\n            }\n            // If not enough recommendations, get popular products\n            if (recommendations.length < limit) {\n                const popularProducts = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProducts({\n                    per_page: limit * 2,\n                    exclude: [\n                        productId\n                    ],\n                    status: \"publish\",\n                    orderby: \"popularity\"\n                });\n                // Merge and deduplicate\n                const existingIds = recommendations.map((p)=>p.id);\n                const additionalProducts = (popularProducts || []).filter((p)=>!existingIds.includes(p.id));\n                recommendations = [\n                    ...recommendations,\n                    ...additionalProducts\n                ];\n            }\n            // Format recommendations\n            const formattedRecommendations = recommendations.slice(0, limit).map((product)=>{\n                var _product_images_, _product_images, _product_categories;\n                return {\n                    id: product.id,\n                    name: product.name,\n                    price: product.price,\n                    regular_price: product.regular_price,\n                    sale_price: product.sale_price,\n                    image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.src) || \"/placeholder.jpg\",\n                    permalink: product.permalink,\n                    categories: ((_product_categories = product.categories) === null || _product_categories === void 0 ? void 0 : _product_categories.map((cat)=>cat.name)) || [],\n                    rating: product.average_rating || 0,\n                    rating_count: product.rating_count || 0\n                };\n            });\n            this.setCache(cacheKey, formattedRecommendations);\n            console.log(\"✅ Found \".concat(formattedRecommendations.length, \" recommendations\"));\n            return formattedRecommendations;\n        } catch (error) {\n            console.error(\"❌ Error getting product recommendations:\", error);\n            return [];\n        }\n    }\n    // 2. LOYALTY PROGRAM - WooCommerce Orders Integration\n    async getLoyaltyPoints(customerId) {\n        try {\n            const cacheKey = \"loyalty_\".concat(customerId);\n            const cached = this.getCache(cacheKey);\n            if (cached) return cached;\n            console.log(\"\\uD83C\\uDF81 Calculating loyalty points for customer \".concat(customerId, \"...\"));\n            // Get customer orders\n            const orders = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getOrders({\n                customer: customerId,\n                status: \"completed\",\n                per_page: 100\n            });\n            if (!orders || orders.length === 0) {\n                return {\n                    points: 0,\n                    tier: \"Bronze\",\n                    totalSpent: 0,\n                    orderCount: 0\n                };\n            }\n            // Calculate points (10 points per £1 spent)\n            const totalSpent = orders.reduce((sum, order)=>sum + parseFloat(order.total || 0), 0);\n            const points = Math.floor(totalSpent * 10);\n            // Determine tier\n            let tier = \"Bronze\";\n            if (totalSpent >= 1000) tier = \"Platinum\";\n            else if (totalSpent >= 500) tier = \"Gold\";\n            else if (totalSpent >= 200) tier = \"Silver\";\n            const loyaltyData = {\n                points,\n                tier,\n                totalSpent,\n                orderCount: orders.length,\n                nextTierThreshold: tier === \"Bronze\" ? 200 : tier === \"Silver\" ? 500 : tier === \"Gold\" ? 1000 : null,\n                benefits: this.getTierBenefits(tier)\n            };\n            this.setCache(cacheKey, loyaltyData);\n            console.log(\"✅ Customer has \".concat(points, \" points (\").concat(tier, \" tier)\"));\n            return loyaltyData;\n        } catch (error) {\n            console.error(\"❌ Error calculating loyalty points:\", error);\n            return {\n                points: 0,\n                tier: \"Bronze\",\n                totalSpent: 0,\n                orderCount: 0\n            };\n        }\n    }\n    getTierBenefits(tier) {\n        const benefits = {\n            Bronze: [\n                \"5% birthday discount\",\n                \"Free shipping on orders over \\xa350\"\n            ],\n            Silver: [\n                \"10% birthday discount\",\n                \"Free shipping on orders over \\xa330\",\n                \"Early access to sales\"\n            ],\n            Gold: [\n                \"15% birthday discount\",\n                \"Free shipping on all orders\",\n                \"Early access to sales\",\n                \"Priority customer support\"\n            ],\n            Platinum: [\n                \"20% birthday discount\",\n                \"Free shipping on all orders\",\n                \"Early access to sales\",\n                \"Priority customer support\",\n                \"Exclusive products access\"\n            ]\n        };\n        return benefits[tier] || benefits.Bronze;\n    }\n    // 3. ONE-CLICK REORDER - WooCommerce Order History\n    async getReorderableOrders(customerId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        try {\n            const cacheKey = \"reorder_\".concat(customerId, \"_\").concat(limit);\n            const cached = this.getCache(cacheKey);\n            if (cached) return cached;\n            console.log(\"⚡ Getting reorderable orders for customer \".concat(customerId, \"...\"));\n            // Get customer's completed orders\n            const orders = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getOrders({\n                customer: customerId,\n                status: \"completed\",\n                per_page: limit,\n                orderby: \"date\",\n                order: \"desc\"\n            });\n            if (!orders || orders.length === 0) {\n                return [];\n            }\n            // Format orders for reordering\n            const reorderableOrders = orders.map((order)=>{\n                var _order_line_items, _order_line_items1;\n                return {\n                    id: order.id,\n                    date: order.date_created,\n                    total: order.total,\n                    items: ((_order_line_items = order.line_items) === null || _order_line_items === void 0 ? void 0 : _order_line_items.map((item)=>{\n                        var _item_image;\n                        return {\n                            product_id: item.product_id,\n                            variation_id: item.variation_id || null,\n                            name: item.name,\n                            quantity: item.quantity,\n                            price: item.price,\n                            image: ((_item_image = item.image) === null || _item_image === void 0 ? void 0 : _item_image.src) || \"/placeholder.jpg\"\n                        };\n                    })) || [],\n                    itemCount: ((_order_line_items1 = order.line_items) === null || _order_line_items1 === void 0 ? void 0 : _order_line_items1.length) || 0\n                };\n            });\n            this.setCache(cacheKey, reorderableOrders);\n            console.log(\"✅ Found \".concat(reorderableOrders.length, \" reorderable orders\"));\n            return reorderableOrders;\n        } catch (error) {\n            console.error(\"❌ Error getting reorderable orders:\", error);\n            return [];\n        }\n    }\n    // 4. INVENTORY ALERTS - WooCommerce Stock Management\n    async getInventoryAlerts() {\n        try {\n            const cacheKey = \"inventory_alerts\";\n            const cached = this.getCache(cacheKey);\n            if (cached) return cached;\n            console.log(\"\\uD83D\\uDCE6 Checking inventory alerts...\");\n            // Get all products with stock management\n            const products = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProducts({\n                per_page: 100,\n                manage_stock: true,\n                status: \"publish\"\n            });\n            if (!products || products.length === 0) {\n                return {\n                    lowStock: [],\n                    outOfStock: [],\n                    total: 0\n                };\n            }\n            const lowStockThreshold = 10;\n            const lowStock = [];\n            const outOfStock = [];\n            products.forEach((product)=>{\n                const stock = parseInt(product.stock_quantity || 0);\n                if (product.stock_status === \"outofstock\" || stock === 0) {\n                    outOfStock.push({\n                        id: product.id,\n                        name: product.name,\n                        stock: stock,\n                        sku: product.sku\n                    });\n                } else if (stock <= lowStockThreshold) {\n                    lowStock.push({\n                        id: product.id,\n                        name: product.name,\n                        stock: stock,\n                        sku: product.sku\n                    });\n                }\n            });\n            const alerts = {\n                lowStock,\n                outOfStock,\n                total: lowStock.length + outOfStock.length,\n                lastChecked: new Date().toISOString()\n            };\n            this.setCache(cacheKey, alerts);\n            console.log(\"✅ Found \".concat(alerts.total, \" inventory alerts\"));\n            return alerts;\n        } catch (error) {\n            console.error(\"❌ Error checking inventory alerts:\", error);\n            return {\n                lowStock: [],\n                outOfStock: [],\n                total: 0\n            };\n        }\n    }\n    // 5. CUSTOMER ANALYTICS - WooCommerce Customer Data\n    async getCustomerAnalytics(customerId) {\n        try {\n            const cacheKey = \"analytics_\".concat(customerId);\n            const cached = this.getCache(cacheKey);\n            if (cached) return cached;\n            console.log(\"\\uD83D\\uDCCA Getting customer analytics for \".concat(customerId, \"...\"));\n            // Get customer data\n            const customer = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getCustomer(customerId);\n            const orders = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getOrders({\n                customer: customerId,\n                per_page: 100\n            });\n            if (!customer || !orders) {\n                throw new Error(\"Customer data not found\");\n            }\n            // Calculate analytics\n            const totalSpent = orders.reduce((sum, order)=>sum + parseFloat(order.total || 0), 0);\n            const avgOrderValue = orders.length > 0 ? totalSpent / orders.length : 0;\n            const lastOrderDate = orders.length > 0 ? orders[0].date_created : null;\n            // Get favorite categories\n            const categoryCount = {};\n            orders.forEach((order)=>{\n                var _order_line_items;\n                (_order_line_items = order.line_items) === null || _order_line_items === void 0 ? void 0 : _order_line_items.forEach((item)=>{\n                // You'd need to get product details to get categories\n                // This is a simplified version\n                });\n            });\n            const analytics = {\n                customerId,\n                email: customer.email,\n                firstName: customer.first_name,\n                lastName: customer.last_name,\n                totalOrders: orders.length,\n                totalSpent,\n                avgOrderValue,\n                lastOrderDate,\n                registrationDate: customer.date_created,\n                favoriteCategories: Object.keys(categoryCount).slice(0, 3)\n            };\n            this.setCache(cacheKey, analytics);\n            console.log(\"✅ Customer analytics calculated\");\n            return analytics;\n        } catch (error) {\n            console.error(\"❌ Error getting customer analytics:\", error);\n            return null;\n        }\n    }\n    // 6. PUSH NOTIFICATIONS - WooCommerce Order Updates\n    async getOrderUpdates(customerId) {\n        let lastCheck = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDD14 Checking order updates for customer \".concat(customerId, \"...\"));\n            const params = {\n                customer: customerId,\n                per_page: 10,\n                orderby: \"date\",\n                order: \"desc\"\n            };\n            if (lastCheck) {\n                params.modified_after = lastCheck;\n            }\n            const orders = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getOrders(params);\n            if (!orders || orders.length === 0) {\n                return [];\n            }\n            // Format notifications\n            const notifications = orders.map((order)=>({\n                    id: order.id,\n                    type: \"order_update\",\n                    title: \"Order #\".concat(order.number, \" \").concat(order.status),\n                    message: \"Your order is now \".concat(order.status),\n                    status: order.status,\n                    total: order.total,\n                    date: order.date_modified || order.date_created,\n                    actionUrl: \"/account/orders/\".concat(order.id)\n                }));\n            console.log(\"✅ Found \".concat(notifications.length, \" order updates\"));\n            return notifications;\n        } catch (error) {\n            console.error(\"❌ Error getting order updates:\", error);\n            return [];\n        }\n    }\n    // 7. PRODUCT SEARCH - Enhanced WooCommerce Search\n    async searchProducts(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            console.log('\\uD83D\\uDD0D Searching products: \"'.concat(query, '\"...'));\n            const searchParams = {\n                search: query,\n                per_page: filters.limit || 20,\n                status: \"publish\"\n            };\n            // Add filters\n            if (filters.category) searchParams.category = filters.category;\n            if (filters.min_price) searchParams.min_price = filters.min_price;\n            if (filters.max_price) searchParams.max_price = filters.max_price;\n            if (filters.on_sale) searchParams.on_sale = filters.on_sale;\n            const products = await _woocommerce_js__WEBPACK_IMPORTED_MODULE_0__.wooCommerceApi.getProducts(searchParams);\n            if (!products || products.length === 0) {\n                return {\n                    products: [],\n                    total: 0,\n                    query\n                };\n            }\n            // Format search results\n            const formattedProducts = products.map((product)=>{\n                var _product_images_, _product_images, _product_categories;\n                return {\n                    id: product.id,\n                    name: product.name,\n                    price: product.price,\n                    regular_price: product.regular_price,\n                    sale_price: product.sale_price,\n                    image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.src) || \"/placeholder.jpg\",\n                    permalink: product.permalink,\n                    categories: ((_product_categories = product.categories) === null || _product_categories === void 0 ? void 0 : _product_categories.map((cat)=>cat.name)) || [],\n                    rating: product.average_rating || 0,\n                    stock_status: product.stock_status,\n                    on_sale: product.on_sale\n                };\n            });\n            console.log(\"✅ Found \".concat(formattedProducts.length, \" products\"));\n            return {\n                products: formattedProducts,\n                total: formattedProducts.length,\n                query,\n                filters\n            };\n        } catch (error) {\n            console.error(\"❌ Error searching products:\", error);\n            return {\n                products: [],\n                total: 0,\n                query\n            };\n        }\n    }\n    // 8. CART INTEGRATION - WooCommerce Cart Management\n    async addToCart(productId) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, variationId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null, customerId = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n        try {\n            console.log(\"\\uD83D\\uDED2 Adding product \".concat(productId, \" to cart...\"));\n            const response = await fetch(\"/api/woocommerce-cart\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"add_to_cart\",\n                    productId,\n                    quantity,\n                    variationId,\n                    customerId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                var _data_product;\n                // Trigger cart update event\n                window.dispatchEvent(new CustomEvent(\"cartUpdated\", {\n                    detail: {\n                        action: \"add\",\n                        product: data.product,\n                        quantity\n                    }\n                }));\n                console.log(\"✅ Product added to cart: \".concat((_data_product = data.product) === null || _data_product === void 0 ? void 0 : _data_product.name));\n                return data;\n            } else {\n                throw new Error(data.error);\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding to cart:\", error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    }\n    // Bulk add to cart (for reorder functionality)\n    async bulkAddToCart(items) {\n        let customerId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDED2 Bulk adding \".concat(items.length, \" items to cart...\"));\n            const response = await fetch(\"/api/woocommerce-cart\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"bulk_add_to_cart\",\n                    items,\n                    customerId\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Trigger cart update event\n                window.dispatchEvent(new CustomEvent(\"cartUpdated\", {\n                    detail: {\n                        action: \"bulk_add\",\n                        results: data.results\n                    }\n                }));\n                console.log(\"✅ Bulk add complete: \".concat(data.results.total_added, \" items added\"));\n                return data;\n            } else {\n                throw new Error(data.error);\n            }\n        } catch (error) {\n            console.error(\"❌ Error bulk adding to cart:\", error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    }\n    // Get cart contents\n    async getCart() {\n        let customerId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        try {\n            const params = customerId ? \"?customer_id=\".concat(customerId) : \"\";\n            const response = await fetch(\"/api/woocommerce-cart\".concat(params));\n            const data = await response.json();\n            if (data.success) {\n                return data.cart;\n            } else {\n                throw new Error(data.error);\n            }\n        } catch (error) {\n            console.error(\"❌ Error getting cart:\", error);\n            return null;\n        }\n    }\n    constructor(){\n        this.initialized = false;\n        this.cache = new Map();\n        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes\n    }\n}\n// Create global instance\nconst wooCommerceFeatures = new WooCommerceFeatures();\n// Auto-initialize\nif (true) {\n    wooCommerceFeatures.initialize();\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (wooCommerceFeatures);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/woocommerceFeatures.js\n"));

/***/ })

}]);