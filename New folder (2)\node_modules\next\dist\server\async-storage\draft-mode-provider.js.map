{"version": 3, "sources": ["../../../src/server/async-storage/draft-mode-provider.ts"], "names": ["DraftModeProvider", "constructor", "previewProps", "req", "cookies", "mutableCookies", "isOnDemandRevalidate", "checkIsOnDemandRevalidate", "cookieValue", "get", "COOKIE_NAME_PRERENDER_BYPASS", "value", "isEnabled", "Boolean", "previewModeId", "process", "env", "NODE_ENV", "_previewModeId", "_mutableCookies", "enable", "Error", "set", "name", "httpOnly", "sameSite", "secure", "path", "disable", "expires", "Date"], "mappings": ";;;;+BAYaA;;;eAAAA;;;0BAHN;AAGA,MAAMA;IAaXC,YACEC,YAA2C,EAC3CC,GAA6D,EAC7DC,OAA+B,EAC/BC,cAA+B,CAC/B;YAOoBD;QANpB,mEAAmE;QACnE,4DAA4D;QAC5D,MAAME,uBACJJ,gBACAK,IAAAA,mCAAyB,EAACJ,KAAKD,cAAcI,oBAAoB;QAEnE,MAAME,eAAcJ,eAAAA,QAAQK,GAAG,CAACC,sCAA4B,sBAAxCN,aAA2CO,KAAK;QAEpE,IAAI,CAACC,SAAS,GAAGC,QACf,CAACP,wBACCE,eACAN,gBACCM,CAAAA,gBAAgBN,aAAaY,aAAa,IACzC,mHAAmH;QAClHC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACxBf,aAAaY,aAAa,KAAK,gBAAgB;QAGvD,IAAI,CAACI,cAAc,GAAGhB,gCAAAA,aAAcY,aAAa;QACjD,IAAI,CAACK,eAAe,GAAGd;IACzB;IAEAe,SAAS;QACP,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;YACxB,MAAM,IAAIG,MACR;QAEJ;QAEA,IAAI,CAACF,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMb,sCAA4B;YAClCC,OAAO,IAAI,CAACO,cAAc;YAC1BM,UAAU;YACVC,UAAUV,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DS,QAAQX,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCU,MAAM;QACR;IACF;IAEAC,UAAU;QACR,2DAA2D;QAC3D,oDAAoD;QACpD,wEAAwE;QACxE,IAAI,CAACT,eAAe,CAACG,GAAG,CAAC;YACvBC,MAAMb,sCAA4B;YAClCC,OAAO;YACPa,UAAU;YACVC,UAAUV,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DS,QAAQX,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCU,MAAM;YACNE,SAAS,IAAIC,KAAK;QACpB;IACF;AACF"}