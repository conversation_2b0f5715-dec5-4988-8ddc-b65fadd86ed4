{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/parseStack.ts"], "names": ["parseStack", "regexNextStatic", "stack", "frames", "parse", "map", "frame", "url", "URL", "file", "res", "exec", "pathname", "process", "distDir", "env", "__NEXT_DIST_DIR", "replace", "concat", "pop", "search"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;kCALM;AAGtB,MAAMC,kBAAkB;AAEjB,SAASD,WAAWE,KAAa;IACtC,MAAMC,SAASC,IAAAA,uBAAK,EAACF;IACrB,OAAOC,OAAOE,GAAG,CAAC,CAACC;QACjB,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIF,MAAMG,IAAI;YAC9B,MAAMC,MAAMT,gBAAgBU,IAAI,CAACJ,IAAIK,QAAQ;YAC7C,IAAIF,KAAK;oBACSG,sCAAAA;gBAAhB,MAAMC,WAAUD,+BAAAA,QAAQE,GAAG,CAACC,eAAe,sBAA3BH,uCAAAA,6BACZI,OAAO,CAAC,OAAO,yBADHJ,qCAEZI,OAAO,CAAC,OAAO;gBACnB,IAAIH,SAAS;oBACXR,MAAMG,IAAI,GAAG,YAAYK,QAAQI,MAAM,CAACR,IAAIS,GAAG,MAAOZ,IAAIa,MAAM;gBAClE;YACF;QACF,EAAE,UAAM,CAAC;QACT,OAAOd;IACT;AACF"}