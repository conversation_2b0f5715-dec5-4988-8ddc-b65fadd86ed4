{"version": 3, "sources": ["../../../../src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts"], "names": ["createRouterCache<PERSON>ey", "clearCacheNodeDataForSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "loading", "slice"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,iCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IACpC,MAAMK,WAAWT,qBAAqBQ;IAEtC,MAAME,0BACJP,cAAcQ,cAAc,CAACC,GAAG,CAACL;IAEnC,IAAIM,kBAAkBX,SAASS,cAAc,CAACC,GAAG,CAACL;IAElD,IAAI,CAACM,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BR,SAASS,cAAc,CAACI,GAAG,CAACR,kBAAkBM;IAChD;IAEA,MAAMG,yBAAyBN,2CAAAA,wBAAyBE,GAAG,CAACH;IAC5D,IAAIQ,iBAAiBJ,gBAAgBD,GAAG,CAACH;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACY,kBACD,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACN,UAAU;gBAC5BS,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,kBAAkB;gBAClBC,SAAS;YACX;QACF;QACA;IACF;IAEA,IAAI,CAACP,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACN,UAAU;gBAC5BS,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,kBAAkB;gBAClBC,SAAS;YACX;QACF;QACA;IACF;IAEA,IAAIP,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCX,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;YACrDY,kBAAkBN,eAAeM,gBAAgB;YACjDC,SAASP,eAAeO,OAAO;QACjC;QACAX,gBAAgBE,GAAG,CAACN,UAAUQ;IAChC;IAEA,OAAOhB,iCACLgB,gBACAD,wBACAZ,kBAAkBqB,KAAK,CAAC;AAE5B"}