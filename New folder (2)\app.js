// Simple startup file for cPanel Node.js compatibility
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Deal4u Next.js application...');
console.log('📁 Working directory:', __dirname);
console.log('🔧 Node.js version:', process.version);

// Start the server.js file
const serverPath = path.join(__dirname, 'server.js');
console.log('📄 Starting server from:', serverPath);

const server = spawn('node', [serverPath], {
  stdio: 'inherit',
  cwd: __dirname
});

server.on('error', (err) => {
  console.error('❌ Failed to start server:', err);
  process.exit(1);
});

server.on('close', (code) => {
  console.log(`🛑 Server process exited with code ${code}`);
  if (code !== 0) {
    process.exit(code);
  }
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.kill('SIGTERM');
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.kill('SIGINT');
});
