{"version": 3, "sources": ["../../../../src/build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "names": ["clearModuleContext", "realpathSync", "path", "isError", "clearManifestCache", "originModules", "require", "resolve", "RUNTIME_NAMES", "deleteFromRequireCache", "filePath", "e", "code", "mod", "cache", "originModule", "parent", "idx", "children", "indexOf", "splice", "child", "deleteAppClientCache", "deleteCache", "PLUGIN_NAME", "NextJsRequireCacheHotReloader", "constructor", "opts", "prevAssets", "serverComponents", "apply", "compiler", "hooks", "assetEmitted", "tap", "_file", "targetPath", "afterEmit", "tapPromise", "compilation", "name", "runtimeChunk<PERSON><PERSON>", "join", "outputOptions", "hasAppEntry", "entries", "keys", "filter", "entry", "isAppPath", "toString", "startsWith", "page", "outputPath"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,YAAY,QAAQ,wBAAuB;AACpD,OAAOC,UAAU,OAAM;AACvB,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,kBAAkB,QAAQ,gCAA+B;AAKlE,MAAMC,gBAAgB;IACpBC,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;CACjB;AAED,MAAMC,gBAAgB;IAAC;IAAmB;CAAsB;AAEhE,SAASC,uBAAuBC,QAAgB;IAC9C,IAAI;QACFA,WAAWT,aAAaS;IAC1B,EAAE,OAAOC,GAAG;QACV,IAAIR,QAAQQ,MAAMA,EAAEC,IAAI,KAAK,UAAU,MAAMD;IAC/C;IACA,MAAME,MAAMP,QAAQQ,KAAK,CAACJ,SAAS;IACnC,IAAIG,KAAK;QACP,oDAAoD;QACpD,KAAK,MAAME,gBAAgBV,cAAe;YACxC,MAAMW,SAASV,QAAQQ,KAAK,CAACC,aAAa;YAC1C,IAAIC,QAAQ;gBACV,MAAMC,MAAMD,OAAOE,QAAQ,CAACC,OAAO,CAACN;gBACpC,IAAII,OAAO,GAAGD,OAAOE,QAAQ,CAACE,MAAM,CAACH,KAAK;YAC5C;QACF;QACA,iDAAiD;QACjD,KAAK,MAAMI,SAASR,IAAIK,QAAQ,CAAE;YAChCG,MAAML,MAAM,GAAG;QACjB;QACA,OAAOV,QAAQQ,KAAK,CAACJ,SAAS;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AAEA,OAAO,SAASY;IACdb,uBACEH,QAAQC,OAAO,CAAC;IAElBE,uBACEH,QAAQC,OAAO,CACb;AAGN;AAEA,OAAO,SAASgB,YAAYb,QAAgB;IAC1C,oCAAoC;IACpCN,mBAAmBM;IAEnBD,uBAAuBC;AACzB;AAEA,MAAMc,cAAc;AAEpB,yGAAyG;AACzG,OAAO,MAAMC;IAIXC,YAAYC,IAAmC,CAAE;aAHjDC,aAAkB;QAIhB,IAAI,CAACC,gBAAgB,GAAGF,KAAKE,gBAAgB;IAC/C;IAEAC,MAAMC,QAAkB,EAAE;QACxBA,SAASC,KAAK,CAACC,YAAY,CAACC,GAAG,CAACV,aAAa,CAACW,OAAO,EAAEC,UAAU,EAAE;YACjE,uCAAuC;YACvCpC,mBAAmBoC;YACnBb,YAAYa;QACd;QAEAL,SAASC,KAAK,CAACK,SAAS,CAACC,UAAU,CAACd,aAAa,OAAOe;YACtD,KAAK,MAAMC,QAAQhC,cAAe;gBAChC,MAAMiC,mBAAmBvC,KAAKwC,IAAI,CAChCH,YAAYI,aAAa,CAACzC,IAAI,EAC9B,CAAC,EAAEsC,KAAK,GAAG,CAAC;gBAEdjB,YAAYkB;YACd;YAEA,8DAA8D;YAC9D,oDAAoD;YACpD,mCAAmC;YACnC,IAAIG,cAAc;YAClB,MAAMC,UAAU;mBAAIN,YAAYM,OAAO,CAACC,IAAI;aAAG,CAACC,MAAM,CAAC,CAACC;gBACtD,MAAMC,YAAYD,MAAME,QAAQ,GAAGC,UAAU,CAAC;gBAC9C,IAAIF,WAAWL,cAAc;gBAC7B,OAAOI,MAAME,QAAQ,GAAGC,UAAU,CAAC,aAAaF;YAClD;YAEA,IAAIL,aAAa;gBACftB;YACF;YAEA,KAAK,MAAM8B,QAAQP,QAAS;gBAC1B,MAAMQ,aAAanD,KAAKwC,IAAI,CAC1BH,YAAYI,aAAa,CAACzC,IAAI,EAC9BkD,OAAO;gBAET7B,YAAY8B;YACd;QACF;IACF;AACF"}