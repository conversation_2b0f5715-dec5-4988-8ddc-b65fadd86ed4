{"version": 3, "sources": ["../../../src/server/app-render/dynamic-rendering.ts"], "names": ["React", "DynamicServerError", "StaticGenBailoutError", "getPathname", "hasPostpone", "unstable_postpone", "createPrerenderState", "isDebugSkeleton", "dynamicAccesses", "markCurrentScopeAsDynamic", "store", "expression", "pathname", "urlPathname", "isUnstableCacheCallback", "dynamicShouldError", "prerenderState", "postponeWithTracking", "revalidate", "isStaticGeneration", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "trackDynamicDataAccessed", "Error", "Postpone", "reason", "trackDynamicFetch", "assertPostpone", "push", "undefined", "usedDynamicAPIs", "length", "formatDynamicAPIAccesses", "filter", "access", "map", "split", "slice", "line", "includes", "join", "createPostponedAbortSignal", "controller", "AbortController", "x", "abort", "signal"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAED,wFAAwF;AACxF,OAAOA,WAAW,QAAO;AAGzB,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,WAAW,QAAQ,gBAAe;AAE3C,MAAMC,cAAc,OAAOJ,MAAMK,iBAAiB,KAAK;AA6BvD,OAAO,SAASC,qBACdC,eAAoC;IAEpC,OAAO;QACLA;QACAC,iBAAiB,EAAE;IACrB;AACF;AAEA;;;;;CAKC,GACD,OAAO,SAASC,0BACdC,KAA4B,EAC5BC,UAAkB;IAElB,MAAMC,WAAWT,YAAYO,MAAMG,WAAW;IAC9C,IAAIH,MAAMI,uBAAuB,EAAE;QACjC,6FAA6F;QAC7F,iGAAiG;QACjG,kCAAkC;QAClC;IACF,OAAO,IAAIJ,MAAMK,kBAAkB,EAAE;QACnC,MAAM,IAAIb,sBACR,CAAC,MAAM,EAAEU,SAAS,8EAA8E,EAAED,WAAW,4HAA4H,CAAC;IAE9O,OAAO,IACL,oDAAoD;IACpDD,MAAMM,cAAc,EACpB;QACA,uDAAuD;QACvD,sDAAsD;QACtD,kCAAkC;QAClCC,qBAAqBP,MAAMM,cAAc,EAAEL,YAAYC;IACzD,OAAO;QACLF,MAAMQ,UAAU,GAAG;QAEnB,IAAIR,MAAMS,kBAAkB,EAAE;YAC5B,uGAAuG;YACvG,MAAMC,MAAM,IAAInB,mBACd,CAAC,MAAM,EAAEW,SAAS,iDAAiD,EAAED,WAAW,2EAA2E,CAAC;YAE9JD,MAAMW,uBAAuB,GAAGV;YAChCD,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;YAEnC,MAAMH;QACR;IACF;AACF;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASI,yBACdd,KAA4B,EAC5BC,UAAkB;IAElB,MAAMC,WAAWT,YAAYO,MAAMG,WAAW;IAC9C,IAAIH,MAAMI,uBAAuB,EAAE;QACjC,MAAM,IAAIW,MACR,CAAC,MAAM,EAAEb,SAAS,OAAO,EAAED,WAAW,iLAAiL,EAAEA,WAAW,6KAA6K,CAAC;IAEtZ,OAAO,IAAID,MAAMK,kBAAkB,EAAE;QACnC,MAAM,IAAIb,sBACR,CAAC,MAAM,EAAEU,SAAS,8EAA8E,EAAED,WAAW,4HAA4H,CAAC;IAE9O,OAAO,IACL,oDAAoD;IACpDD,MAAMM,cAAc,EACpB;QACA,uDAAuD;QACvD,sDAAsD;QACtD,kCAAkC;QAClCC,qBAAqBP,MAAMM,cAAc,EAAEL,YAAYC;IACzD,OAAO;QACLF,MAAMQ,UAAU,GAAG;QAEnB,IAAIR,MAAMS,kBAAkB,EAAE;YAC5B,uGAAuG;YACvG,MAAMC,MAAM,IAAInB,mBACd,CAAC,MAAM,EAAEW,SAAS,mDAAmD,EAAED,WAAW,6EAA6E,CAAC;YAElKD,MAAMW,uBAAuB,GAAGV;YAChCD,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;YAEnC,MAAMH;QACR;IACF;AACF;AAUA,OAAO,SAASM,SAAS,EACvBC,MAAM,EACNX,cAAc,EACdJ,QAAQ,EACM;IACdK,qBAAqBD,gBAAgBW,QAAQf;AAC/C;AAEA,gHAAgH;AAChH,oHAAoH;AACpH,mHAAmH;AACnH,6BAA6B;AAC7B,OAAO,SAASgB,kBACdlB,KAA4B,EAC5BC,UAAkB;IAElB,IAAID,MAAMM,cAAc,EAAE;QACxBC,qBAAqBP,MAAMM,cAAc,EAAEL,YAAYD,MAAMG,WAAW;IAC1E;AACF;AAEA,SAASI,qBACPD,cAA8B,EAC9BL,UAAkB,EAClBC,QAAgB;IAEhBiB;IACA,MAAMF,SACJ,CAAC,MAAM,EAAEf,SAAS,iEAAiE,EAAED,WAAW,EAAE,CAAC,GACnG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;IAErFK,eAAeR,eAAe,CAACsB,IAAI,CAAC;QAClC,0EAA0E;QAC1E,eAAe;QACfP,OAAOP,eAAeT,eAAe,GAAG,IAAIkB,QAAQF,KAAK,GAAGQ;QAC5DpB;IACF;IAEAX,MAAMK,iBAAiB,CAACsB;AAC1B;AAEA,OAAO,SAASK,gBAAgBhB,cAA8B;IAC5D,OAAOA,eAAeR,eAAe,CAACyB,MAAM,GAAG;AACjD;AAEA,OAAO,SAASC,yBACdlB,cAA8B;IAE9B,OAAOA,eAAeR,eAAe,CAClC2B,MAAM,CACL,CAACC,SACC,OAAOA,OAAOb,KAAK,KAAK,YAAYa,OAAOb,KAAK,CAACU,MAAM,GAAG,GAE7DI,GAAG,CAAC,CAAC,EAAE1B,UAAU,EAAEY,KAAK,EAAE;QACzBA,QAAQA,MACLe,KAAK,CAAC,KACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKC,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAID,KAAKC,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAID,KAAKC,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCC,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAE/B,WAAW,GAAG,EAAEY,MAAM,CAAC;IAC7D;AACJ;AAEA,SAASM;IACP,IAAI,CAACzB,aAAa;QAChB,MAAM,IAAIqB,MACR,CAAC,gIAAgI,CAAC;IAEtI;AACF;AAEA;;;CAGC,GACD,OAAO,SAASkB,2BAA2BhB,MAAc;IACvDE;IACA,MAAMe,aAAa,IAAIC;IACvB,qFAAqF;IACrF,IAAI;QACF7C,MAAMK,iBAAiB,CAACsB;IAC1B,EAAE,OAAOmB,GAAY;QACnBF,WAAWG,KAAK,CAACD;IACnB;IACA,OAAOF,WAAWI,MAAM;AAC1B"}