{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "names": ["InvalidArgumentError", "printAndExit", "message", "code", "console", "log", "error", "process", "exit", "getDebugPort", "debugPortStr", "execArgv", "find", "localArg", "startsWith", "split", "env", "NODE_OPTIONS", "match", "parseInt", "NODE_INSPECT_RE", "getNodeOptionsWithoutInspect", "replace", "myParseInt", "value", "parsedValue", "isNaN", "isFinite", "RESTART_EXIT_CODE", "checkNodeDebugType", "nodeDebugType", "undefined", "some", "getMaxOldSpaceSize", "maxOldSpaceSize"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,+BAA8B;AAEnE,OAAO,SAASC,aAAaC,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEAK,QAAQC,IAAI,CAACL;AACf;AAEA,OAAO,MAAMM,eAAe;QAExBF,wBAOAA,iCAAAA,kCAAAA;IARF,MAAMG,eACJH,EAAAA,yBAAAA,QAAQI,QAAQ,CACbC,IAAI,CACH,CAACC,WACCA,SAASC,UAAU,CAAC,gBACpBD,SAASC,UAAU,CAAC,sCAJ1BP,uBAMIQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACpBR,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,mCAAAA,0BAA0BW,KAAK,sBAA/BX,kCAAAA,sCAAAA,2BAAkC,sDAAlCA,+BAAqE,CAAC,EAAE;IAC1E,OAAOG,eAAeS,SAAST,cAAc,MAAM;AACrD,EAAC;AAED,MAAMU,kBAAkB;AACxB,OAAO,SAASC;IACd,OAAO,AAACd,CAAAA,QAAQS,GAAG,CAACC,YAAY,IAAI,EAAC,EAAGK,OAAO,CAACF,iBAAiB;AACnE;AAEA,OAAO,SAASG,WAAWC,KAAa;IACtC,sCAAsC;IACtC,MAAMC,cAAcN,SAASK,OAAO;IAEpC,IAAIE,MAAMD,gBAAgB,CAACE,SAASF,gBAAgBA,cAAc,GAAG;QACnE,MAAM,IAAIzB,qBAAqB,CAAC,CAAC,EAAEwB,MAAM,+BAA+B,CAAC;IAC3E;IACA,OAAOC;AACT;AAEA,OAAO,MAAMG,oBAAoB,GAAE;AAEnC,OAAO,SAASC;QAKZtB,iCAAAA,2BAOAA,kCAAAA;IAXF,IAAIuB,gBAAgBC;IAEpB,IACExB,QAAQI,QAAQ,CAACqB,IAAI,CAAC,CAACnB,WAAaA,SAASC,UAAU,CAAC,mBACxDP,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,kCAAAA,0BAA0BW,KAAK,qBAA/BX,qCAAAA,2BAAkC,2BAClC;QACAuB,gBAAgB;IAClB;IAEA,IACEvB,QAAQI,QAAQ,CAACqB,IAAI,CAAC,CAACnB,WAAaA,SAASC,UAAU,CAAC,uBACxDP,6BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,mCAAAA,2BAA0BW,KAAK,qBAA/BX,sCAAAA,4BAAkC,+BAClC;QACAuB,gBAAgB;IAClB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASG;QACU1B,iCAAAA;IAAxB,MAAM2B,mBAAkB3B,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,kCAAAA,0BAA0BW,KAAK,CACrD,2DADsBX,+BAErB,CAAC,EAAE;IAEN,OAAO2B,kBAAkBf,SAASe,iBAAiB,MAAMH;AAC3D"}