{"version": 3, "sources": ["../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts"], "names": ["applyFlightData", "fetchServerResponse", "PAGE_SEGMENT_KEY", "refreshInactiveParallelSegments", "options", "fetchedSegments", "Set", "refreshInactiveParallelSegmentsImpl", "rootTree", "updatedTree", "state", "updatedCache", "includeNextUrl", "canonicalUrl", "parallelRoutes", "refetch<PERSON>ath", "refetch<PERSON><PERSON><PERSON>", "fetchPromises", "has", "add", "fetchPromise", "URL", "location", "origin", "nextUrl", "buildId", "then", "fetchResponse", "flightData", "flightDataPath", "push", "key", "parallelFetchPromise", "Promise", "all", "addRefreshMarkerToActiveParallelSegments", "tree", "path", "segment", "includes"], "mappings": "AAGA,SAASA,eAAe,QAAQ,sBAAqB;AACrD,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,gBAAgB,QAAQ,8BAA6B;AAU9D;;;;;;;;;;CAUC,GACD,OAAO,eAAeC,gCACpBC,OAAwC;IAExC,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,oCAAoC;QACxC,GAAGH,OAAO;QACVI,UAAUJ,QAAQK,WAAW;QAC7BJ;IACF;AACF;AAEA,eAAeE,oCAAoC,KAWlD;IAXkD,IAAA,EACjDG,KAAK,EACLD,WAAW,EACXE,YAAY,EACZC,cAAc,EACdP,eAAe,EACfG,WAAWC,WAAW,EACtBI,YAAY,EAIb,GAXkD;IAYjD,MAAM,GAAGC,gBAAgBC,aAAaC,cAAc,GAAGP;IACvD,MAAMQ,gBAAgB,EAAE;IAExB,IACEF,eACAA,gBAAgBF,gBAChBG,kBAAkB,aAClB,4FAA4F;IAC5F,sDAAsD;IACtD,CAACX,gBAAgBa,GAAG,CAACH,cACrB;QACAV,gBAAgBc,GAAG,CAACJ,aAAa,2BAA2B;;QAE5D,wHAAwH;QACxH,kIAAkI;QAClI,MAAMK,eAAenB,oBACnB,IAAIoB,IAAIN,aAAaO,SAASC,MAAM,GACpC,gGAAgG;QAChG,8HAA8H;QAC9H;YAACf,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAE;SAAU,EAClDI,iBAAiBF,MAAMc,OAAO,GAAG,MACjCd,MAAMe,OAAO,EACbC,IAAI,CAAC,CAACC;YACN,MAAMC,aAAaD,aAAa,CAAC,EAAE;YACnC,IAAI,OAAOC,eAAe,UAAU;gBAClC,KAAK,MAAMC,kBAAkBD,WAAY;oBACvC,wFAAwF;oBACxF,4GAA4G;oBAC5G,4EAA4E;oBAC5E5B,gBAAgBW,cAAcA,cAAckB;gBAC9C;YACF,OAAO;YACL,4GAA4G;YAC5G,+GAA+G;YAC/G,sEAAsE;YACxE;QACF;QAEAZ,cAAca,IAAI,CAACV;IACrB;IAEA,IAAK,MAAMW,OAAOjB,eAAgB;QAChC,MAAMkB,uBAAuBzB,oCAAoC;YAC/DG;YACAD,aAAaK,cAAc,CAACiB,IAAI;YAChCpB;YACAC;YACAP;YACAG;YACAK;QACF;QAEAI,cAAca,IAAI,CAACE;IACrB;IAEA,MAAMC,QAAQC,GAAG,CAACjB;AACpB;AAEA;;;;;CAKC,GACD,OAAO,SAASkB,yCACdC,IAAuB,EACvBC,IAAY;IAEZ,MAAM,CAACC,SAASxB,kBAAkBE,cAAc,GAAGoB;IACnD,oGAAoG;IACpG,IAAIE,QAAQC,QAAQ,CAACrC,qBAAqBc,kBAAkB,WAAW;QACrEoB,IAAI,CAAC,EAAE,GAAGC;QACVD,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,IAAK,MAAML,OAAOjB,eAAgB;QAChCqB,yCAAyCrB,cAAc,CAACiB,IAAI,EAAEM;IAChE;AACF"}