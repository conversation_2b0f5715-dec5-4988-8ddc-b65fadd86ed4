'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Star, Quote, ArrowRight } from 'lucide-react';
import { StarRatingDisplay } from './StarRating';

/**
 * RecentReviews Component
 * Displays recent customer reviews on homepage
 */
export default function RecentReviews({ limit = 6, className = '' }) {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecentReviews();
  }, [limit]);

  const fetchRecentReviews = async () => {
    try {
      const response = await fetch(`/api/reviews?recent=true&limit=${limit}`);
      const data = await response.json();

      if (data.success) {
        setReviews(data.reviews);
      }
    } catch (error) {
      console.error('Error fetching recent reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <section className={`py-16 bg-gray-50 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-600">
              Real reviews from real customers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-6 shadow-sm animate-pulse">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full mr-4"></div>
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (reviews.length === 0) {
    return null; // Don't show section if no reviews
  }

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Don't just take our word for it. Here's what real customers have to say about their experience with Deal4u.
          </p>
        </div>

        {/* Reviews Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {reviews.map((review) => (
            <ReviewCard key={review.id} review={review} />
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Link
            href="/reviews"
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            View All Reviews
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}

/**
 * Individual Review Card Component
 */
function ReviewCard({ review }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
      {/* Quote Icon */}
      <div className="flex items-start justify-between mb-4">
        <Quote className="w-8 h-8 text-blue-600 opacity-20" />
        <span className="text-xs text-gray-500">{formatDate(review.createdAt)}</span>
      </div>

      {/* Review Content */}
      <div className="mb-4">
        {review.title && (
          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">
            {review.title}
          </h3>
        )}
        <p className="text-gray-700 text-sm line-clamp-3 leading-relaxed">
          {review.comment}
        </p>
      </div>

      {/* Rating */}
      <div className="mb-4">
        <StarRatingDisplay
          rating={review.rating}
          size="sm"
          showCount={false}
        />
      </div>

      {/* User Info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <img
            src={review.userAvatar}
            alt={review.userName}
            className="w-10 h-10 rounded-full mr-3"
          />
          <div>
            <p className="font-medium text-gray-900 text-sm">{review.userName}</p>
            {review.verified && (
              <p className="text-xs text-green-600">Verified Purchase</p>
            )}
          </div>
        </div>

        {/* Product Link */}
        <Link
          href={`/shop/product/${review.productId}`}
          className="text-blue-600 hover:text-blue-700 text-xs font-medium"
        >
          View Product
        </Link>
      </div>
    </div>
  );
}

/**
 * Review Statistics Component
 * Shows overall review stats for homepage
 */
export function ReviewStats({ className = '' }) {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOverallStats();
  }, []);

  const fetchOverallStats = async () => {
    try {
      // In a real app, you'd have an endpoint for overall stats
      // For now, we'll simulate it
      setStats({
        totalReviews: 1247,
        averageRating: 4.6,
        fiveStarPercentage: 78,
        recommendationRate: 94
      });
    } catch (error) {
      console.error('Error fetching review stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !stats) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg p-6 shadow-sm ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Customer Satisfaction
      </h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {stats.averageRating}
          </div>
          <div className="flex justify-center mb-1">
            <StarRatingDisplay
              rating={stats.averageRating}
              size="sm"
              showCount={false}
            />
          </div>
          <p className="text-xs text-gray-600">Average Rating</p>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 mb-1">
            {stats.totalReviews.toLocaleString()}
          </div>
          <p className="text-xs text-gray-600">Total Reviews</p>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600 mb-1">
            {stats.fiveStarPercentage}%
          </div>
          <p className="text-xs text-gray-600">5-Star Reviews</p>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">
            {stats.recommendationRate}%
          </div>
          <p className="text-xs text-gray-600">Would Recommend</p>
        </div>
      </div>
    </div>
  );
}
