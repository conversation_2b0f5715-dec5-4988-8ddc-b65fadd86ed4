'use client';

import { useState } from 'react';
import { Star } from 'lucide-react';

/**
 * StarRating Component
 * Displays and allows interaction with star ratings
 */
export default function StarRating({ 
  rating = 0, 
  maxRating = 5, 
  size = 'md',
  interactive = false,
  onChange,
  showValue = false,
  className = ''
}) {
  const [hoverRating, setHoverRating] = useState(0);
  const [selectedRating, setSelectedRating] = useState(rating);

  // Size configurations
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const handleStarClick = (starValue) => {
    if (!interactive) return;
    
    setSelectedRating(starValue);
    if (onChange) {
      onChange(starValue);
    }
  };

  const handleStarHover = (starValue) => {
    if (!interactive) return;
    setHoverRating(starValue);
  };

  const handleMouseLeave = () => {
    if (!interactive) return;
    setHoverRating(0);
  };

  const displayRating = interactive ? (hoverRating || selectedRating) : rating;

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* Stars */}
      <div 
        className="flex items-center gap-0.5"
        onMouseLeave={handleMouseLeave}
      >
        {[...Array(maxRating)].map((_, index) => {
          const starValue = index + 1;
          const isFilled = starValue <= displayRating;
          const isPartiallyFilled = !Number.isInteger(displayRating) && 
                                   starValue === Math.ceil(displayRating);

          return (
            <button
              key={index}
              type="button"
              className={`
                relative transition-all duration-200 
                ${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'}
                ${interactive && hoverRating >= starValue ? 'transform scale-110' : ''}
              `}
              onClick={() => handleStarClick(starValue)}
              onMouseEnter={() => handleStarHover(starValue)}
              disabled={!interactive}
            >
              {/* Background star (empty) */}
              <Star 
                className={`
                  ${sizeClasses[size]} 
                  text-gray-300 
                  ${interactive ? 'hover:text-gray-400' : ''}
                `}
                fill="currentColor"
              />
              
              {/* Filled star overlay */}
              {(isFilled || isPartiallyFilled) && (
                <Star 
                  className={`
                    ${sizeClasses[size]} 
                    text-yellow-400 
                    absolute top-0 left-0
                    ${interactive && hoverRating >= starValue ? 'text-yellow-500' : ''}
                  `}
                  fill="currentColor"
                  style={{
                    clipPath: isPartiallyFilled 
                      ? `inset(0 ${100 - ((displayRating % 1) * 100)}% 0 0)`
                      : 'none'
                  }}
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Rating value */}
      {showValue && (
        <span className={`
          text-gray-600 font-medium ml-1
          ${textSizeClasses[size]}
        `}>
          {displayRating.toFixed(1)}
        </span>
      )}
    </div>
  );
}

/**
 * StarRatingDisplay Component
 * Read-only star rating with additional info
 */
export function StarRatingDisplay({ 
  rating = 0, 
  totalReviews = 0,
  size = 'md',
  showCount = true,
  className = ''
}) {
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <StarRating 
        rating={rating} 
        size={size} 
        interactive={false}
        showValue={true}
      />
      
      {showCount && totalReviews > 0 && (
        <span className={`
          text-gray-500 
          ${textSizeClasses[size]}
        `}>
          ({totalReviews} {totalReviews === 1 ? 'review' : 'reviews'})
        </span>
      )}
    </div>
  );
}

/**
 * RatingDistribution Component
 * Shows rating breakdown with bars
 */
export function RatingDistribution({ 
  distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  totalReviews = 0,
  onFilterByRating,
  className = ''
}) {
  const maxCount = Math.max(...Object.values(distribution));

  return (
    <div className={`space-y-2 ${className}`}>
      {[5, 4, 3, 2, 1].map(rating => {
        const count = distribution[rating] || 0;
        const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
        const barWidth = maxCount > 0 ? (count / maxCount) * 100 : 0;

        return (
          <button
            key={rating}
            className={`
              flex items-center gap-3 w-full text-left p-2 rounded-lg
              transition-colors duration-200
              ${onFilterByRating ? 'hover:bg-gray-50 cursor-pointer' : 'cursor-default'}
            `}
            onClick={() => onFilterByRating && onFilterByRating(rating)}
          >
            {/* Star rating */}
            <div className="flex items-center gap-1 min-w-0">
              <span className="text-sm font-medium w-3">{rating}</span>
              <Star className="w-4 h-4 text-yellow-400" fill="currentColor" />
            </div>

            {/* Progress bar */}
            <div className="flex-1 bg-gray-200 rounded-full h-2 min-w-0">
              <div 
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${barWidth}%` }}
              />
            </div>

            {/* Count and percentage */}
            <div className="text-sm text-gray-600 min-w-0">
              <span className="font-medium">{count}</span>
              <span className="text-gray-400 ml-1">({percentage.toFixed(0)}%)</span>
            </div>
          </button>
        );
      })}
    </div>
  );
}
