{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.tsx"], "names": ["getFrameSource", "useOpenInEditor", "HotlinkedText", "CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "file", "lineNumber", "column", "undefined", "div", "data-nextjs-call-stack-frame", "h3", "data-nextjs-frame-expanded", "expanded", "text", "methodName", "data-has-source", "tabIndex", "role", "onClick", "title", "span", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": ";AACA,SACEA,cAAc,QAET,4BAA2B;AAClC,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,aAAa,QAAQ,mCAAkC;AAEhE,OAAO,MAAMC,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAILA;IAHtB,0CAA0C;IAC1C,2CAA2C;IAE3C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,YAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,OAAOV,gBACXO,YACI;QACEI,MAAMP,EAAEO,IAAI;QACZC,YAAYR,EAAEQ,UAAU;QACxBC,QAAQT,EAAES,MAAM;IAClB,IACAC;IAGN,qBACE,MAACC;QAAIC,8BAA4B;;0BAC/B,KAACC;gBAAGC,8BAA4BV,QAAQL,MAAMgB,QAAQ;0BACpD,cAAA,KAAClB;oBAAcmB,MAAMhB,EAAEiB,UAAU;;;0BAEnC,MAACN;gBACCO,mBAAiBf,YAAY,SAASO;gBACtCS,UAAUhB,YAAY,KAAKO;gBAC3BU,MAAMjB,YAAY,SAASO;gBAC3BW,SAASf;gBACTgB,OAAOnB,YAAY,iCAAiCO;;kCAEpD,KAACa;kCAAM5B,eAAeK;;kCACtB,MAACwB;wBACCC,OAAM;wBACNC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0CAEf,KAACC;gCAAKC,GAAE;;0CACR,KAACC;gCAASC,QAAO;;0CACjB,KAACC;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;;;;;;;;AAK3C,EAAC"}