<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal4u API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Deal4u API Connection Test</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">API Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>Domain:</strong> https://deal4u.co
                </div>
                <div>
                    <strong>Consumer Key:</strong> ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193
                </div>
                <div class="md:col-span-2">
                    <strong>Consumer Secret:</strong> cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Connection Test</h2>
            <button onclick="testConnection()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                Test WooCommerce Connection
            </button>
            <div id="test-results" class="mt-4"></div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Sample Products</h2>
            <div id="products-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Products will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Environment configuration
        window.ENV = {
            NEXT_PUBLIC_APP_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WORDPRESS_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WOOCOMMERCE_URL: 'https://deal4u.co',
            WOOCOMMERCE_CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            WOOCOMMERCE_CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        // Simple API test
        async function testConnection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="text-blue-600">Testing connection...</div>';

            try {
                const baseURL = window.ENV.NEXT_PUBLIC_WOOCOMMERCE_URL;
                const consumerKey = window.ENV.WOOCOMMERCE_CONSUMER_KEY;
                const consumerSecret = window.ENV.WOOCOMMERCE_CONSUMER_SECRET;
                
                const credentials = btoa(`${consumerKey}:${consumerSecret}`);
                const url = `${baseURL}/wp-json/wc/v3/products?per_page=5`;

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Basic ${credentials}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const products = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="text-green-600 font-semibold">✅ Connection Successful!</div>
                        <div class="text-sm text-gray-600 mt-2">
                            Found ${products.length} products. API is working correctly.
                        </div>
                    `;
                    
                    // Display sample products
                    displayProducts(products);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="text-red-600 font-semibold">❌ Connection Failed</div>
                        <div class="text-sm text-gray-600 mt-2">
                            HTTP ${response.status}: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="text-red-600 font-semibold">❌ Connection Error</div>
                    <div class="text-sm text-gray-600 mt-2">
                        ${error.message}
                    </div>
                `;
            }
        }

        function displayProducts(products) {
            const container = document.getElementById('products-container');
            
            if (products.length === 0) {
                container.innerHTML = '<div class="col-span-full text-center text-gray-500">No products found</div>';
                return;
            }

            container.innerHTML = products.map(product => `
                <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <img src="${product.images[0]?.src || '/placeholder.jpg'}" 
                         alt="${product.name}" 
                         class="w-full h-32 object-cover rounded mb-3">
                    <h3 class="font-semibold text-sm mb-2 line-clamp-2">${product.name}</h3>
                    <div class="text-blue-600 font-bold">
                        £${parseFloat(product.price).toFixed(2)}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        Status: ${product.status} | Stock: ${product.stock_status}
                    </div>
                </div>
            `).join('');
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testConnection, 1000);
        });
    </script>

    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</body>
</html>
