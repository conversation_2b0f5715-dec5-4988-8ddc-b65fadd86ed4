// Shopping Cart Management
class ShoppingCart {
    constructor() {
        this.items = this.loadCart();
        this.isOpen = false;
        this.init();
    }

    init() {
        this.updateCartDisplay();
        this.updateCartCount();
    }

    // Load cart from localStorage
    loadCart() {
        try {
            const saved = localStorage.getItem('deal4u_cart');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error loading cart:', error);
            return [];
        }
    }

    // Save cart to localStorage
    saveCart() {
        try {
            localStorage.setItem('deal4u_cart', JSON.stringify(this.items));
        } catch (error) {
            console.error('Error saving cart:', error);
        }
    }

    // Add item to cart
    addItem(product, quantity = 1) {
        const existingItem = this.items.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                id: product.id,
                name: product.name,
                price: parseFloat(product.price),
                image: product.image,
                quantity: quantity,
                permalink: product.permalink
            });
        }

        this.saveCart();
        this.updateCartDisplay();
        this.updateCartCount();
        
        // Show success toast
        showToast(`${product.name} added to cart!`, 'success');
        
        // Animate cart icon
        this.animateCartIcon();
    }

    // Remove item from cart
    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartDisplay();
        this.updateCartCount();
        showToast('Item removed from cart', 'info');
    }

    // Update item quantity
    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartDisplay();
                this.updateCartCount();
            }
        }
    }

    // Get cart total
    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Get cart item count
    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    // Update cart count display
    updateCartCount() {
        const countElements = document.querySelectorAll('.cart-count');
        const count = this.getItemCount();
        
        countElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'block' : 'none';
        });
    }

    // Update cart display
    updateCartDisplay() {
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');
        
        if (!cartItems || !cartTotal) return;

        if (this.items.length === 0) {
            cartItems.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i data-lucide="shopping-cart" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                    <p>Your cart is empty</p>
                    <button onclick="toggleCart()" class="mt-4 text-blue-600 hover:text-blue-700">
                        Continue Shopping
                    </button>
                </div>
            `;
        } else {
            cartItems.innerHTML = this.items.map(item => `
                <div class="flex items-center space-x-4 p-4 border-b">
                    <img src="${item.image}" alt="${item.name}" class="w-16 h-16 object-cover rounded">
                    <div class="flex-1">
                        <h4 class="font-medium text-sm line-clamp-2">${item.name}</h4>
                        <p class="text-blue-600 font-semibold">${this.formatPrice(item.price)}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="cart.updateQuantity(${item.id}, ${item.quantity - 1})" 
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-100">
                            <i data-lucide="minus" class="w-4 h-4"></i>
                        </button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button onclick="cart.updateQuantity(${item.id}, ${item.quantity + 1})" 
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-100">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                        </button>
                        <button onclick="cart.removeItem(${item.id})" 
                                class="w-8 h-8 flex items-center justify-center text-red-500 hover:bg-red-50 rounded">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        cartTotal.textContent = this.formatPrice(this.getTotal());
        
        // Re-initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Format price
    formatPrice(price) {
        return new Intl.NumberFormat('en-GB', {
            style: 'currency',
            currency: 'GBP'
        }).format(price);
    }

    // Animate cart icon
    animateCartIcon() {
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon) {
            cartIcon.classList.add('animate-bounce');
            setTimeout(() => {
                cartIcon.classList.remove('animate-bounce');
            }, 1000);
        }
    }

    // Clear cart
    clear() {
        this.items = [];
        this.saveCart();
        this.updateCartDisplay();
        this.updateCartCount();
        showToast('Cart cleared', 'info');
    }

    // Get cart data for checkout
    getCheckoutData() {
        return {
            line_items: this.items.map(item => ({
                product_id: item.id,
                quantity: item.quantity
            })),
            total: this.getTotal().toFixed(2)
        };
    }
}

// Toggle cart sidebar
function toggleCart() {
    const sidebar = document.getElementById('cart-sidebar');
    const isOpen = !sidebar.classList.contains('hidden');
    
    if (isOpen) {
        sidebar.classList.add('translate-x-full');
        setTimeout(() => {
            sidebar.classList.add('hidden');
        }, 300);
    } else {
        sidebar.classList.remove('hidden');
        setTimeout(() => {
            sidebar.classList.remove('translate-x-full');
        }, 10);
    }
}

// Proceed to checkout
function proceedToCheckout() {
    if (cart.items.length === 0) {
        showToast('Your cart is empty', 'error');
        return;
    }

    // Check if user is logged in (you can implement this)
    const isLoggedIn = localStorage.getItem('deal4u_user');
    
    if (!isLoggedIn) {
        showToast('Please login to continue', 'info');
        // Redirect to login page
        window.location.href = '/login.html';
        return;
    }

    // Redirect to checkout page
    window.location.href = '/checkout.html';
}

// Add to cart function (global)
function addToCart(product) {
    cart.addItem(product);
}

// Initialize cart
const cart = new ShoppingCart();
