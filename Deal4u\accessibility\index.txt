1:HL["/_next/static/css/0ebc520152eede79.css","style",{"crossOrigin":""}]
0:["qBGG3i9NaXjuV_NA48--5",[[["",{"children":["accessibility",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L2",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0ebc520152eede79.css","precedence":"next","crossOrigin":""}]],"$L3"]]]]
4:I[3843,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
5:I[9768,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"AuthProvider"]
6:I[8365,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"CartProvider"]
7:I[2194,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
8:I[6954,[],""]
9:I[5822,["326","static/chunks/326-80cc90e4cc9eac2a.js","601","static/chunks/app/error-253db9213a1fa457.js"],""]
a:I[7264,[],""]
b:I[1253,["326","static/chunks/326-80cc90e4cc9eac2a.js","160","static/chunks/app/not-found-6dd0f2e974eb5fd4.js"],""]
d:I[8326,["326","static/chunks/326-80cc90e4cc9eac2a.js","41","static/chunks/app/accessibility/page-50d4b853f0d544e1.js"],""]
e:I[6547,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
f:I[5925,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],"Toaster"]
10:I[1045,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
11:I[6873,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
12:I[4527,["326","static/chunks/326-80cc90e4cc9eac2a.js","358","static/chunks/358-12459c71a13d30e1.js","413","static/chunks/413-f4d1f1c534704df9.js","768","static/chunks/768-0f7cb735eb6f5f0c.js","185","static/chunks/app/layout-3f8d37c3fdd0f837.js"],""]
2:[null,["$","html",null,{"lang":"en","className":"scroll-smooth","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preconnect","href":"https://images.unsplash.com"}],["$","link",null,{"rel":"icon","href":"/favicon.ico","sizes":"any"}],["$","link",null,{"rel":"icon","href":"/icon.svg","type":"image/svg+xml"}],["$","link",null,{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}],["$","$L4",null,{}]]}],["$","body",null,{"className":"antialiased expansion-alids-init","children":["$undefined",["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","div",null,{"className":"min-h-screen flex flex-col bg-gray-50","children":[["$","$L7",null,{}],["$","main",null,{"className":"flex-1 relative","children":["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":["$","div",null,{"className":"min-h-screen bg-gray-50 flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-bold text-2xl mb-8 inline-block","children":"Deal4u"}],["$","div",null,{"className":"relative mb-6","children":[["$","div",null,{"className":"animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"w-8 h-8 bg-blue-100 rounded-full animate-pulse"}]}]]}],["$","p",null,{"className":"text-gray-600 text-lg font-medium animate-pulse","children":"Loading amazing deals..."}],["$","div",null,{"className":"mt-6 w-64 mx-auto bg-gray-200 rounded-full h-2 overflow-hidden","children":["$","div",null,{"className":"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"}]}],["$","div",null,{"className":"flex justify-center space-x-2 mt-6","children":[["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce"}],["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce","style":{"animationDelay":"0.1s"}}],["$","div",null,{"className":"w-2 h-2 bg-blue-500 rounded-full animate-bounce","style":{"animationDelay":"0.2s"}}]]}]]}]}],"loadingStyles":[],"loadingScripts":[],"hasLoading":true,"error":"$9","errorStyles":[],"errorScripts":[],"template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Lb",null,{}],"notFoundStyles":[],"childProp":{"current":["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children","accessibility","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","childProp":{"current":["$Lc",["$","div",null,{"className":"min-h-screen bg-gray-50 py-16","children":["$","div",null,{"className":"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"bg-white rounded-2xl shadow-xl p-8 mb-8","children":[["$","div",null,{"className":"mb-10 pb-6 border-b border-gray-200","children":[["$","h1",null,{"className":"text-3xl md:text-4xl font-bold text-gray-900 mb-2","children":"Accessibility Statement"}],["$","p",null,{"className":"text-gray-500","children":"Last updated: June 3, 2025"}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Our Commitment to Accessibility"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"At Deal4u, we are committed to ensuring digital accessibility for people with disabilities. We are continuously improving the user experience for everyone, and applying the relevant accessibility standards."}],["$","p",null,{"className":"text-gray-600 mb-4","children":"We strive to ensure that our website and applications follow the Web Content Accessibility Guidelines (WCAG 2.1), levels A and AA. These guidelines provide recommendations to make content more accessible to a wider range of people with disabilities, including blindness and low vision, deafness and hearing loss, learning disabilities, cognitive limitations, limited movement, speech disabilities, photosensitivity and combinations of these."}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Measures We've Taken for Accessibility"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"We take the following measures to ensure accessibility of our website:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-2","children":[["$","li",null,{"children":"Include accessibility as part of our mission statement"}],["$","li",null,{"children":"Provide clear navigation and semantic structure"}],["$","li",null,{"children":"Include alternative text for all images"}],["$","li",null,{"children":"Ensure sufficient color contrast for text and important graphics"}],["$","li",null,{"children":"Design our forms to be accessible with properly labeled form fields"}],["$","li",null,{"children":"Make all functionality available from a keyboard"}],["$","li",null,{"children":"Provide visible focus indicators for keyboard users"}],["$","li",null,{"children":"Allow users to resize text without breaking functionality"}],["$","li",null,{"children":"Support modern screen readers and other assistive technologies"}],["$","li",null,{"children":"Regularly test with automated tools and real users"}]]}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Accessibility Features on Our Website"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Our website includes the following accessibility features:"}],["$","div",null,{"className":"mb-6","children":[["$","h3",null,{"className":"text-xl font-medium text-gray-700 mb-3","children":"Navigation"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"We've designed our website with a consistent navigation structure, making it easier for users with screen readers and keyboard-only users to navigate our site. We include:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-1","children":[["$","li",null,{"children":"Skip to main content links"}],["$","li",null,{"children":"Consistent navigation across all pages"}],["$","li",null,{"children":"Breadcrumb navigation on appropriate pages"}],["$","li",null,{"children":"Descriptive page titles"}]]}]]}],["$","div",null,{"className":"mb-6","children":[["$","h3",null,{"className":"text-xl font-medium text-gray-700 mb-3","children":"Text and Typography"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"We've paid special attention to text readability:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-1","children":[["$","li",null,{"children":"Sufficient color contrast between text and background"}],["$","li",null,{"children":"Resizable text that doesn't break the layout"}],["$","li",null,{"children":"Clear headings and content structure"}],["$","li",null,{"children":"Avoiding justified text which can be hard for some users to read"}]]}]]}],["$","div",null,{"className":"mb-6","children":[["$","h3",null,{"className":"text-xl font-medium text-gray-700 mb-3","children":"Images and Media"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"For users with visual impairments:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-1","children":[["$","li",null,{"children":"All images have appropriate alternative text"}],["$","li",null,{"children":"Complex images have longer descriptions available"}],["$","li",null,{"children":"Videos include captions and transcripts when possible"}],["$","li",null,{"children":"No content relies solely on color to convey information"}]]}]]}],["$","div",null,{"className":"mb-6","children":[["$","h3",null,{"className":"text-xl font-medium text-gray-700 mb-3","children":"Forms and Interactive Elements"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Our forms and interactive elements are designed for accessibility:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-1","children":[["$","li",null,{"children":"All form fields have associated labels"}],["$","li",null,{"children":"Error messages are clearly identified and described"}],["$","li",null,{"children":"Forms can be navigated and completed using keyboard only"}],["$","li",null,{"children":"Interactive elements have appropriate focus states"}]]}]]}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Compatibility with Assistive Technologies"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Our website is designed to be compatible with a variety of assistive technologies, including:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-2","children":[["$","li",null,{"children":"Screen readers (such as JAWS, NVDA, VoiceOver, and TalkBack)"}],["$","li",null,{"children":"Screen magnification software"}],["$","li",null,{"children":"Speech recognition software"}],["$","li",null,{"children":"Keyboard-only navigation"}],["$","li",null,{"children":"Switch controls and other adaptive devices"}]]}],["$","p",null,{"className":"text-gray-600","children":"We regularly test with these technologies to ensure a good user experience."}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Known Limitations"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Despite our efforts to ensure accessibility of Deal4u, there may be some limitations. Below is a list of known limitations and current workarounds:"}],["$","ul",null,{"className":"list-disc pl-5 mb-4 text-gray-600 space-y-2","children":[["$","li",null,{"children":"Some older content may not yet meet all our accessibility standards. We are working to update these pages."}],["$","li",null,{"children":"Some third-party content or functionality may not be fully accessible. We are working with our partners to address these issues."}],["$","li",null,{"children":"Some complex product images may not have fully detailed descriptions. Please contact us if you need assistance with specific product details."}]]}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Feedback and Contact Information"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"We welcome your feedback on the accessibility of Deal4u. Please let us know if you encounter any accessibility barriers on our website:"}],["$","div",null,{"className":"bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4","children":[["$","p",null,{"className":"text-gray-700 font-medium","children":"Deal4u Accessibility Team"}],["$","p",null,{"className":"text-gray-600","children":"Email: <EMAIL>"}],["$","p",null,{"className":"text-gray-600","children":"Phone: (*************"}]]}],["$","p",null,{"className":"text-gray-600","children":"We try to respond to feedback within 3 business days."}]]}],["$","section",null,{"className":"mb-10","children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-800 mb-4","children":"Assessment and Compliance Status"}],["$","p",null,{"className":"text-gray-600 mb-4","children":"The Web Content Accessibility Guidelines (WCAG) defines requirements for designers and developers to improve accessibility for people with disabilities. It defines three levels of conformance: Level A, Level AA, and Level AAA."}],["$","p",null,{"className":"text-gray-600 mb-4","children":"Deal4u is partially conformant with WCAG 2.1 level AA. Partially conformant means that some parts of the content do not fully conform to the accessibility standard."}],["$","p",null,{"className":"text-gray-600","children":"This statement was prepared on June 3, 2025. It was last reviewed on June 3, 2025."}]]}],["$","div",null,{"className":"mt-12 text-center","children":["$","$Ld",null,{"href":"/","className":"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-colors","children":"Return to Homepage"}]}]]}]}]}],null],"segment":"__PAGE__"},"styles":null}],"segment":"accessibility"},"styles":null}]}],["$","$Le",null,{}]]}],["$","$Lf",null,{"position":"top-right","toastOptions":{"duration":4000,"style":{"background":"#fff","color":"#374151","border":"1px solid #e5e7eb","borderRadius":"8px","fontSize":"14px","fontWeight":"500"},"success":{"iconTheme":{"primary":"#10b981","secondary":"#fff"}},"error":{"iconTheme":{"primary":"#ef4444","secondary":"#fff"}}}}],["$","div",null,{"id":"loading-indicator","className":"hidden fixed top-0 left-0 w-full h-1 bg-blue-600 z-50","children":["$","div",null,{"className":"h-full bg-blue-400 animate-pulse"}]}]]}]}],["$","a",null,{"href":"#main-content","className":"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50","children":"Skip to main content"}],["$","$L10",null,{}],["$","$L11",null,{}],["$","$L12",null,{}]]}]]}],null]
3:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"}],["$","meta","1",{"name":"theme-color","media":"(prefers-color-scheme: light)","content":"#3b82f6"}],["$","meta","2",{"name":"theme-color","media":"(prefers-color-scheme: dark)","content":"#1e40af"}],["$","meta","3",{"charSet":"utf-8"}],["$","title","4",{"children":"Accessibility | Deal4u | Deal4u"}],["$","meta","5",{"name":"description","content":"Learn about Deal4u's commitment to accessibility and how we make our website accessible to all users."}],["$","meta","6",{"name":"author","content":"Deal4u Team"}],["$","meta","7",{"name":"keywords","content":"ecommerce,deals,shopping,products,online store"}],["$","meta","8",{"name":"creator","content":"Deal4u"}],["$","meta","9",{"name":"publisher","content":"Deal4u"}],["$","meta","10",{"name":"robots","content":"index, follow"}],["$","meta","11",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","12",{"rel":"canonical","href":"http://localhost:3000/"}],["$","meta","13",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","14",{"property":"og:title","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","15",{"property":"og:description","content":"Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service."}],["$","meta","16",{"property":"og:url","content":"http://localhost:3000/"}],["$","meta","17",{"property":"og:site_name","content":"Deal4u"}],["$","meta","18",{"property":"og:locale","content":"en_US"}],["$","meta","19",{"property":"og:image","content":"http://localhost:3000/og-image.jpg"}],["$","meta","20",{"property":"og:image:width","content":"1200"}],["$","meta","21",{"property":"og:image:height","content":"630"}],["$","meta","22",{"property":"og:image:alt","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","23",{"property":"og:type","content":"website"}],["$","meta","24",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","25",{"name":"twitter:site","content":"@deal4u"}],["$","meta","26",{"name":"twitter:creator","content":"@deal4u"}],["$","meta","27",{"name":"twitter:title","content":"Deal4u - Amazing Deals on Premium Products"}],["$","meta","28",{"name":"twitter:description","content":"Discover amazing products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service."}],["$","meta","29",{"name":"twitter:image","content":"http://localhost:3000/og-image.jpg"}]]
c:null
