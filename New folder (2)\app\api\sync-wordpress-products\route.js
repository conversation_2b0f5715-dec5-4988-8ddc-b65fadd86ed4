import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WC_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_WOOCOMMERCE_URL,
  consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
  consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
  version: 'wc/v3'
};

export async function POST(request) {
  try {
    console.log('🔄 Starting WordPress products sync...');
    
    const api = new WooCommerceRestApi({
      url: WC_CONFIG.baseURL,
      consumerKey: WC_CONFIG.consumerKey,
      consumerSecret: WC_CONFIG.consumerSecret,
      version: WC_CONFIG.version
    });

    // Get ALL products from WordPress (any status)
    let allProducts = [];
    let page = 1;
    let hasMore = true;
    
    while (hasMore) {
      console.log(`📦 Fetching page ${page} from WordPress...`);
      const response = await api.get('products', {
        per_page: 100,
        page: page,
        status: 'any' // Get all products regardless of status
      });
      
      const products = response.data.filter(p => p.status !== 'trash');
      allProducts = [...allProducts, ...products];
      
      hasMore = products.length === 100;
      page++;
    }

    console.log(`📊 Found ${allProducts.length} products in WordPress`);

    // Process each product
    const processedProducts = [];
    const errors = [];

    for (const product of allProducts) {
      try {
        // Clean up product data
        const cleanProduct = {
          id: product.id,
          name: product.name?.replace(/AliExpress/gi, 'Deal4U') || 'Untitled Product',
          description: product.description?.replace(/AliExpress/gi, 'Deal4U') || '',
          short_description: product.short_description?.replace(/AliExpress/gi, 'Deal4U') || '',
          price: product.regular_price || product.price || '0',
          sale_price: product.sale_price || '',
          images: product.images || [],
          categories: product.categories || [],
          status: product.status,
          stock_status: product.stock_status || 'instock',
          permalink: product.permalink || '',
          date_created: product.date_created,
          date_modified: product.date_modified
        };

        // Filter out size chart images
        if (cleanProduct.images.length > 0) {
          cleanProduct.images = cleanProduct.images.filter(img => {
            const imgSrc = img.src || '';
            const imgName = img.name || '';
            const imgAlt = img.alt || '';
            
            const isSizeChart = [imgSrc, imgName, imgAlt].some(text => {
              const lower = text.toLowerCase();
              return ['size', 'chart', 'table', 'measurement', 'guide', 'cm', 'inch'].some(term => 
                lower.includes(term)
              );
            });
            
            return !isSizeChart;
          });
        }

        processedProducts.push(cleanProduct);
        console.log(`✅ Processed: ${cleanProduct.name}`);

      } catch (error) {
        console.error(`❌ Error processing product ${product.id}:`, error.message);
        errors.push({
          productId: product.id,
          productName: product.name,
          error: error.message
        });
      }
    }

    // Group products by status
    const statusGroups = {
      published: processedProducts.filter(p => p.status === 'publish').length,
      draft: processedProducts.filter(p => p.status === 'draft').length,
      private: processedProducts.filter(p => p.status === 'private').length,
      pending: processedProducts.filter(p => p.status === 'pending').length
    };

    // PUBLISH ALL DRAFT/PRIVATE PRODUCTS
    console.log('📢 Publishing draft/private products...');
    let publishedCount = 0;

    for (const product of allProducts) {
      if (product.status !== 'publish' && product.status !== 'trash') {
        try {
          await api.put(`products/${product.id}`, {
            status: 'publish'
          });
          publishedCount++;
          console.log(`✅ Published: ${product.name}`);
        } catch (error) {
          console.error(`❌ Failed to publish product ${product.id}:`, error.message);
        }
      }
    }

    console.log(`🎉 Sync completed: ${processedProducts.length} products processed`);
    console.log(`📢 Published: ${publishedCount} products`);
    console.log(`📊 Status breakdown:`, statusGroups);

    return NextResponse.json({
      success: true,
      message: `Successfully synced ${processedProducts.length} products from WordPress and published ${publishedCount} products`,
      totalProducts: allProducts.length,
      processedProducts: processedProducts.length,
      publishedCount: publishedCount,
      statusBreakdown: statusGroups,
      errors: errors,
      products: processedProducts.slice(0, 10) // Return first 10 for preview
    });

  } catch (error) {
    console.error('❌ WordPress sync error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      details: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'WordPress Products Sync API',
    usage: {
      'POST /api/sync-wordpress-products': 'Sync all products from WordPress to website'
    }
  });
}
