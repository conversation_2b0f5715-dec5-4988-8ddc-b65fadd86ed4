{"version": 3, "sources": ["../../../src/client/dev/hot-middleware-client.ts"], "names": ["connect", "sendMessage", "reloading", "mode", "devClient", "subscribeToHmrEvent", "obj", "isOnErrorPage", "window", "next", "router", "pathname", "action", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "components", "Error"], "mappings": "AAAA,OAAOA,aAAa,4DAA2D;AAC/E,SAASC,WAAW,QAAQ,kDAAiD;AAE7E,IAAIC,YAAY;AAEhB,eAAe,CAAA,CAACC;IACd,MAAMC,YAAYJ,QAAQG;IAE1BC,UAAUC,mBAAmB,CAAC,CAACC;QAC7B,IAAIJ,WAAW;QACf,wFAAwF;QACxF,uHAAuH;QACvH,MAAMK,gBACJC,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK,UAChCH,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK;QAElC,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBX,YACEY,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUR,OAAOS,iBAAiB;oBACpC;oBAEFf,YAAY;oBACZ,OAAOM,OAAOU,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGd,IAAIe,IAAI;oBACvB,IAAID,SAASZ,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IAAIJ,eAAe;wBACzDN,YACEY,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUR,OAAOS,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOZ,OAAOU,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChB,MAAM,CAACC,KAAK,GAAGd,IAAIe,IAAI;oBACvB,IACE,AAACD,SAASZ,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IACnC,OAAOH,OAAOC,IAAI,CAACC,MAAM,CAACY,UAAU,CAACF,KAAK,KAAK,eACjDb,eACA;wBACAN,YACEY,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUR,OAAOS,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOZ,OAAOU,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAkB;oBACrB;gBACF;YACA;gBAAS;oBACP,MAAM,IAAII,MAAM,uBAAuBjB,IAAIM,MAAM;gBACnD;QACF;IACF;IAEA,OAAOR;AACT,CAAA,EAAC"}