{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-relative-url.ts"], "names": ["parseRelativeUrl", "url", "base", "globalBase", "URL", "window", "getLocationOrigin", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "searchParamsToUrlQuery", "slice", "length"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;uBAjBkB;6BACK;AAgBhC,SAASA,iBACdC,GAAW,EACXC,IAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,cAAc,aAAaC,IAAAA,wBAAiB;IAGhE,MAAMC,eAAeL,OACjB,IAAIE,IAAIF,MAAMC,cACdF,IAAIO,UAAU,CAAC,OACf,IAAIJ,IAAI,OAAOC,WAAW,cAAc,aAAaA,OAAOI,QAAQ,CAACC,IAAI,IACzEP;IAEJ,MAAM,EAAEQ,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIX,IACjEH,KACAM;IAEF,IAAIQ,WAAWZ,WAAWY,MAAM,EAAE;QAChC,MAAM,IAAIC,MAAM,AAAC,sDAAmDf;IACtE;IACA,OAAO;QACLU;QACAM,OAAOC,IAAAA,mCAAsB,EAACN;QAC9BC;QACAC;QACAJ,MAAMA,KAAKS,KAAK,CAAChB,WAAWY,MAAM,CAACK,MAAM;IAC3C;AACF"}