import { NextResponse } from 'next/server';
import { wooCommerceApi } from '@/lib/woocommerce';

export async function GET() {
  try {
    console.log('🔍 Testing WooCommerce API connection...');
    
    // Test basic connection
    const connectionTest = await wooCommerceApi.testConnection();
    console.log('Connection test result:', connectionTest);
    
    // Test getting products with different statuses
    const publishedProducts = await wooCommerceApi.getProducts({
      per_page: 5,
      status: 'publish'
    });
    
    const draftProducts = await wooCommerceApi.getProducts({
      per_page: 5,
      status: 'draft'
    });
    
    const privateProducts = await wooCommerceApi.getProducts({
      per_page: 5,
      status: 'private'
    });
    
    const pendingProducts = await wooCommerceApi.getProducts({
      per_page: 5,
      status: 'pending'
    });
    
    // Test getting unpublished products
    const unpublishedProducts = await wooCommerceApi.getUnpublishedProducts();
    
    // Test categories
    const categories = await wooCommerceApi.getCategories();
    
    const results = {
      success: true,
      connection: connectionTest,
      productCounts: {
        published: Array.isArray(publishedProducts) ? publishedProducts.length : 0,
        draft: Array.isArray(draftProducts) ? draftProducts.length : 0,
        private: Array.isArray(privateProducts) ? privateProducts.length : 0,
        pending: Array.isArray(pendingProducts) ? pendingProducts.length : 0,
        unpublished: Array.isArray(unpublishedProducts) ? unpublishedProducts.length : 0
      },
      categoriesCount: Array.isArray(categories) ? categories.length : 0,
      sampleProducts: {
        published: publishedProducts?.slice(0, 2)?.map(p => ({ id: p.id, name: p.name, status: p.status })) || [],
        unpublished: unpublishedProducts?.slice(0, 2)?.map(p => ({ id: p.id, name: p.name, status: p.status })) || []
      },
      timestamp: new Date().toISOString()
    };
    
    console.log('✅ API Test Results:', results);
    
    return NextResponse.json(results);
    
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
