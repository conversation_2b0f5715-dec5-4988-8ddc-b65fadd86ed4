/**
 * lucide-react v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const UserCheck2 = createLucideIcon("UserCheck2", [
  ["path", { d: "M14 19a6 6 0 0 0-12 0", key: "vej9p1" }],
  ["circle", { cx: "8", cy: "9", r: "4", key: "143rtg" }],
  ["polyline", { points: "16 11 18 13 22 9", key: "1pwet4" }]
]);

export { UserCheck2 as default };
//# sourceMappingURL=user-check-2.js.map
