{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["format", "formatUrl", "parse", "parseUrl", "normalizeLocalePath", "getPathMatch", "getNamedRouteRegex", "getRouteMatcher", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeRscURL", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "normalizeVercelUrl", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "url", "search", "key", "Object", "keys", "query", "isNextQueryPrefix", "startsWith", "isNextInterceptionMarkerPrefix", "groups", "includes", "interpolateDynamicPath", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "normalizeDynamicRouteParams", "ignoreOptional", "defaultRouteMatches", "hasValidParams", "reduce", "prev", "val", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "undefined", "split", "getUtils", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "checkRewrite", "rewrite", "matcher", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "isCatchAll", "_val", "item", "toLowerCase", "locale", "splice", "every", "name", "keyName", "paramName", "pos", "parseInt", "headers"], "mappings": "AAOA,SAASA,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,QAAQ,EACRC,kBAAkB,QACb,iDAAgD;AACvD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SACEC,+BAA+B,EAC/BC,uBAAuB,QAClB,mBAAkB;AAEzB,OAAO,SAASC,mBACdC,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAajB,SAASY,IAAIM,GAAG,EAAG;QACtC,OAAO,AAACD,WAAmBE,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,WAAWM,KAAK,EAAG;YAC/C,MAAMC,oBACJJ,QAAQV,2BACRU,IAAIK,UAAU,CAACf;YAEjB,MAAMgB,iCACJN,QAAQX,mCACRW,IAAIK,UAAU,CAAChB;YAEjB,IACEe,qBACAE,kCACA,AAACZ,CAAAA,aAAaO,OAAOC,IAAI,CAACN,kBAAkBW,MAAM,CAAA,EAAGC,QAAQ,CAACR,MAC9D;gBACA,OAAOH,WAAWM,KAAK,CAACH,IAAI;YAC9B;QACF;QACAR,IAAIM,GAAG,GAAGpB,UAAUmB;IACtB;AACF;AAEA,OAAO,SAASY,uBACdC,QAAgB,EAChBC,MAAsB,EACtBf,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOc;IAE/B,KAAK,MAAME,SAASX,OAAOC,IAAI,CAACN,kBAAkBW,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGlB,kBAAkBW,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEA,OAAO,SAASkB,4BACdjB,MAAsB,EACtBkB,cAAwB,EACxBjC,iBAAqE,EACrEkC,mBAAgD;IAEhD,IAAIC,iBAAiB;IACrB,IAAI,CAACnC,mBAAmB,OAAO;QAAEe;QAAQoB,gBAAgB;IAAM;IAE/DpB,SAASV,OAAOC,IAAI,CAACN,kBAAkBW,MAAM,EAAEyB,MAAM,CAAC,CAACC,MAAMjC;QAC3D,IAAImB,QAAuCR,MAAM,CAACX,IAAI;QAEtD,IAAI,OAAOmB,UAAU,UAAU;YAC7BA,QAAQ/B,gBAAgB+B;QAC1B;QACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACY;gBACjB,IAAI,OAAOA,QAAQ,UAAU;oBAC3BA,MAAM9C,gBAAgB8C;gBACxB;gBACA,OAAOA;YACT;QACF;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeL,mBAAoB,CAAC9B,IAAI;QAC9C,MAAMoC,aAAaxC,kBAAmBW,MAAM,CAACP,IAAI,CAACa,QAAQ;QAE1D,MAAMwB,iBAAiBjB,MAAMC,OAAO,CAACc,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOnB,MAAMC,OAAO,CAACF,SACjBA,MAAMmB,IAAI,CAAC,CAACJ,MAAQA,IAAI1B,QAAQ,CAAC+B,eACjCpB,yBAAAA,MAAOX,QAAQ,CAAC+B;QACtB,KACApB,yBAAAA,MAAOX,QAAQ,CAAC2B;QAEpB,IACEE,kBACC,OAAOlB,UAAU,eAAe,CAAEiB,CAAAA,cAAcP,cAAa,GAC9D;YACAE,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEK,cACC,CAAA,CAACjB,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEnB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAmB,QAAQqB;YACR,OAAO7B,MAAM,CAACX,IAAI;QACpB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEmB,SACA,OAAOA,UAAU,YACjBvB,kBAAmBW,MAAM,CAACP,IAAI,CAACc,MAAM,EACrC;YACAK,QAAQA,MAAMsB,KAAK,CAAC;QACtB;QAEA,IAAItB,OAAO;YACTc,IAAI,CAACjC,IAAI,GAAGmB;QACd;QACA,OAAOc;IACT,GAAG,CAAC;IAEJ,OAAO;QACLtB;QACAoB;IACF;AACF;AAEA,OAAO,SAASW,SAAS,EACvBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRnD,aAAa,EACboD,aAAa,EACbC,aAAa,EAad;IACC,IAAIpD;IACJ,IAAIqD;IACJ,IAAInB;IAEJ,IAAInC,eAAe;QACjBC,oBAAoBb,mBAAmB4D,MAAM;QAC7CM,sBAAsBjE,gBAAgBY;QACtCkC,sBAAsBmB,oBAAoBN;IAC5C;IAEA,SAASO,eAAe1D,GAAoB,EAAE2D,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAUzC,QAAQ;QAEnC,MAAM4C,cAAc;YAClB,MAAMC,oBAAoBpE,oBAAoBkE,cAAc;YAC5D,OACEE,sBAAsBpE,oBAAoBwD,UAC1CM,uCAAAA,oBAAsBM;QAE1B;QAEA,MAAMC,eAAe,CAACC;YACpB,MAAMC,UAAU5E,aACd2E,QAAQE,MAAM,GAAIZ,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEa,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACd;YACf;YAEF,IAAIrC,SAAS+C,QAAQP,UAAUzC,QAAQ;YAEvC,IAAI,AAAC+C,CAAAA,QAAQM,GAAG,IAAIN,QAAQO,OAAO,AAAD,KAAMrD,QAAQ;gBAC9C,MAAMsD,YAAYhF,SAChBO,KACA2D,UAAUhD,KAAK,EACfsD,QAAQM,GAAG,EACXN,QAAQO,OAAO;gBAGjB,IAAIC,WAAW;oBACbhE,OAAOiE,MAAM,CAACvD,QAAQsD;gBACxB,OAAO;oBACLtD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAEwD,iBAAiB,EAAEC,SAAS,EAAE,GAAGlF,mBAAmB;oBAC1DmF,qBAAqB;oBACrBC,aAAab,QAAQa,WAAW;oBAChC3D,QAAQA;oBACRR,OAAOgD,UAAUhD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIgE,kBAAkBI,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEAtE,OAAOiE,MAAM,CAACd,eAAegB,WAAWzD;gBACxCV,OAAOiE,MAAM,CAACf,UAAUhD,KAAK,EAAEgE,kBAAkBhE,KAAK;gBACtD,OAAO,AAACgE,kBAA0BhE,KAAK;gBAEvCF,OAAOiE,MAAM,CAACf,WAAWgB;gBAEzBd,aAAaF,UAAUzC,QAAQ;gBAE/B,IAAImC,UAAU;oBACZQ,aACEA,WAAYmB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE5B,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAM8B,uBAAuB7F,oBAC3BwE,YACAT,KAAK+B,OAAO;oBAEdtB,aAAaqB,qBAAqBhE,QAAQ;oBAC1CyC,UAAUhD,KAAK,CAACyE,kBAAkB,GAChCF,qBAAqBG,cAAc,IAAIlE,OAAOiE,kBAAkB;gBACpE;gBAEA,IAAIvB,eAAeV,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAIhD,iBAAiBsD,qBAAqB;oBACxC,MAAM6B,gBAAgB7B,oBAAoBI;oBAC1C,IAAIyB,eAAe;wBACjB3B,UAAUhD,KAAK,GAAG;4BAChB,GAAGgD,UAAUhD,KAAK;4BAClB,GAAG2E,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrB,WAAWX,SAASiC,WAAW,IAAI,EAAE,CAAE;YAChDvB,aAAaC;QACf;QAEA,IAAIJ,eAAeV,MAAM;YACvB,IAAIqC,WAAW;YAEf,KAAK,MAAMvB,WAAWX,SAASmC,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxB,aAAaC;gBACxB,IAAIuB,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC1B,eAAe;gBAC/B,KAAK,MAAMG,WAAWX,SAASoC,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxB,aAAaC;oBACxB,IAAIuB,UAAU;gBAChB;YACF;QACF;QACA,OAAO5B;IACT;IAEA,SAAS+B,0BACP3F,GAAoB,EACpB4F,UAAgB,EAChBP,cAAuB;QAEvB,OAAO7F,gBACL,AAAC;YACC,MAAM,EAAEuB,MAAM,EAAE8E,SAAS,EAAE,GAAGzF;YAE9B,OAAO;gBACL0F,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAMxF,OAAOyF,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJhD,QAAQiC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAM7E,OAAOC,OAAOC,IAAI,CAACuF,KAAM;4BAClC,MAAMtE,QAAQsE,GAAG,CAACzF,IAAI;4BAEtB,IACEA,QAAQV,2BACRU,IAAIK,UAAU,CAACf,0BACf;gCACA,MAAMuG,gBAAgB7F,IAAI8F,SAAS,CACjCxG,wBAAwBqC,MAAM;gCAEhC8D,GAAG,CAACI,cAAc,GAAG1E;gCACrB,OAAOsE,GAAG,CAACzF,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAM+F,gBAAgB9F,OAAOC,IAAI,CAACmF,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAAC9D;4BACxB,IAAIU,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAMqD,aAAa7E,MAAMC,OAAO,CAACa;gCACjC,MAAMgE,OAAOD,aAAa/D,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOgE,SAAS,YAChBtD,KAAK+B,OAAO,CAACrC,IAAI,CAAC,CAAC6D;oCACjB,IAAIA,KAAKC,WAAW,OAAOF,KAAKE,WAAW,IAAI;wCAC7CvB,iBAAiBsB;wCACjBf,WAAWiB,MAAM,GAAGxB;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIoB,YAAY;wCACZ/D,IAAiBoE,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAOL,aAAa/D,IAAIP,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAIoE,cAAcQ,KAAK,CAAC,CAACC,OAASf,GAAG,CAACe,KAAK,GAAG;4BAC5C,OAAOT,cAAc/D,MAAM,CAAC,CAACC,MAAMwE;gCACjC,MAAMC,YAAYrB,6BAAAA,SAAW,CAACoB,QAAQ;gCAEtC,IAAIC,aAAa,CAACV,iBAAiBP,GAAG,CAACgB,QAAQ,GAAG;oCAChDxE,IAAI,CAAC1B,MAAM,CAACmG,UAAU,CAACC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,QAAQ;gCAC5C;gCACA,OAAOxE;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAOhC,OAAOC,IAAI,CAACuF,KAAKzD,MAAM,CAAC,CAACC,MAAMjC;4BACpC,IAAI,CAACgG,iBAAiBP,GAAG,CAACzF,IAAI,GAAG;gCAC/B,IAAI6F,gBAAgB7F;gCAEpB,IAAI4F,kBAAkB;oCACpBC,gBAAgBe,SAAS5G,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOiE,MAAM,CAACjC,MAAM;oCACzB,CAAC4D,cAAc,EAAEJ,GAAG,CAACzF,IAAI;gCAC3B;4BACF;4BACA,OAAOiC;wBACT,GAAG,CAAC;oBACN;gBACF;gBACA1B;YACF;QACF,KACAf,IAAIqH,OAAO,CAAC,sBAAsB;IACtC;IAEA,OAAO;QACL3D;QACAtD;QACAqD;QACAnB;QACAqD;QACAvD,6BAA6B,CAC3BjB,QACAkB,iBAEAD,4BACEjB,QACAkB,gBACAjC,mBACAkC;QAEJvC,oBAAoB,CAClBC,KACAC,YACAC,YAEAH,mBACEC,KACAC,YACAC,WACAC,eACAC;QAEJa,wBAAwB,CACtBC,UACAC,SACGF,uBAAuBC,UAAUC,QAAQf;IAChD;AACF"}