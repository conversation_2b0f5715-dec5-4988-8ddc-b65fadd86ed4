{"version": 3, "sources": ["../../../src/lib/metadata/get-metadata-route.ts"], "names": ["fillMetadataSegment", "normalizeMetadataRoute", "getMetadataRouteSuffix", "page", "suffix", "includes", "djb2Hash", "toString", "slice", "segment", "params", "imageSegment", "pathname", "normalizeAppPath", "routeRegex", "getNamedRouteRegex", "route", "interpolateDynamicPath", "routeSuffix", "name", "ext", "path", "parse", "normalizePathSep", "join", "isMetadataRoute", "endsWith", "pathnamePrefix", "basename", "length", "dir", "baseName", "isStaticRoute", "isStaticMetadataRoute", "posix"], "mappings": ";;;;;;;;;;;;;;;IAgCgBA,mBAAmB;eAAnBA;;IAyBAC,sBAAsB;eAAtBA;;;iCAzDuC;6DACtC;6BACsB;4BACJ;sBACV;0BACQ;kCACA;;;;;;AAEjC;;;;;;;CAOC,GACD,SAASC,uBAAuBC,IAAY;IAC1C,IAAIC,SAAS;IAEb,IAAI,AAACD,KAAKE,QAAQ,CAAC,QAAQF,KAAKE,QAAQ,CAAC,QAASF,KAAKE,QAAQ,CAAC,MAAM;QACpED,SAASE,IAAAA,cAAQ,EAACH,MAAMI,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;IAChD;IACA,OAAOJ;AACT;AASO,SAASJ,oBACdS,OAAe,EACfC,MAAW,EACXC,YAAoB;IAEpB,MAAMC,WAAWC,IAAAA,0BAAgB,EAACJ;IAClC,MAAMK,aAAaC,IAAAA,8BAAkB,EAACH,UAAU;IAChD,MAAMI,QAAQC,IAAAA,mCAAsB,EAACL,UAAUF,QAAQI;IACvD,MAAMV,SAASF,uBAAuBO;IACtC,MAAMS,cAAcd,SAAS,CAAC,CAAC,EAAEA,OAAO,CAAC,GAAG;IAE5C,MAAM,EAAEe,IAAI,EAAEC,GAAG,EAAE,GAAGC,aAAI,CAACC,KAAK,CAACX;IAEjC,OAAOY,IAAAA,kCAAgB,EAACF,aAAI,CAACG,IAAI,CAACR,OAAO,CAAC,EAAEG,KAAK,EAAED,YAAY,EAAEE,IAAI,CAAC;AACxE;AAWO,SAASnB,uBAAuBE,IAAY;IACjD,IAAI,CAACsB,IAAAA,gCAAe,EAACtB,OAAO;QAC1B,OAAOA;IACT;IACA,IAAIa,QAAQb;IACZ,IAAIC,SAAS;IACb,IAAID,SAAS,WAAW;QACtBa,SAAS;IACX,OAAO,IAAIb,SAAS,aAAa;QAC/Ba,SAAS;IACX,OAAO,IAAIb,KAAKuB,QAAQ,CAAC,aAAa;QACpCV,SAAS;IACX,OAAO;QACL,wEAAwE;QACxE,MAAMW,iBAAiBxB,KAAKK,KAAK,CAAC,GAAG,CAAEa,CAAAA,aAAI,CAACO,QAAQ,CAACzB,MAAM0B,MAAM,GAAG,CAAA;QACpEzB,SAASF,uBAAuByB;IAClC;IACA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAI,CAACX,MAAMU,QAAQ,CAAC,WAAW;QAC7B,MAAM,EAAEI,GAAG,EAAEX,MAAMY,QAAQ,EAAEX,GAAG,EAAE,GAAGC,aAAI,CAACC,KAAK,CAACN;QAChD,MAAMgB,gBAAgBC,IAAAA,sCAAqB,EAAC9B;QAE5Ca,QAAQK,aAAI,CAACa,KAAK,CAACV,IAAI,CACrBM,KACA,CAAC,EAAEC,SAAS,EAAE3B,SAAS,CAAC,CAAC,EAAEA,OAAO,CAAC,GAAG,GAAG,EAAEgB,IAAI,CAAC,EAChDY,gBAAgB,KAAK,0BACrB;IAEJ;IAEA,OAAOhB;AACT"}