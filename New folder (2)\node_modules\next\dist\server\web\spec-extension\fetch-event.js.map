{"version": 3, "sources": ["../../../../src/server/web/spec-extension/fetch-event.ts"], "names": ["NextFetchEvent", "waitUntilSymbol", "responseSymbol", "Symbol", "passThroughSymbol", "FetchEvent", "constructor", "_request", "respondWith", "response", "Promise", "resolve", "passThroughOnException", "waitUntil", "promise", "push", "params", "request", "sourcePage", "page", "PageSignatureError"], "mappings": ";;;;;;;;;;;;;;;IA8BaA,cAAc;eAAdA;;IAzBAC,eAAe;eAAfA;;;uBALsB;AAGnC,MAAMC,iBAAiBC,OAAO;AAC9B,MAAMC,oBAAoBD,OAAO;AAC1B,MAAMF,kBAAkBE,OAAO;AAEtC,MAAME;IAKJ,qEAAqE;IACrEC,YAAYC,QAAiB,CAAE;YALtB,CAACN,gBAAgB,GAAmB,EAAE;YAE/C,CAACG,kBAAkB,GAAG;IAGU;IAEhCI,YAAYC,QAAsC,EAAQ;QACxD,IAAI,CAAC,IAAI,CAACP,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGQ,QAAQC,OAAO,CAACF;QACzC;IACF;IAEAG,yBAA+B;QAC7B,IAAI,CAACR,kBAAkB,GAAG;IAC5B;IAEAS,UAAUC,OAAqB,EAAQ;QACrC,IAAI,CAACb,gBAAgB,CAACc,IAAI,CAACD;IAC7B;AACF;AAEO,MAAMd,uBAAuBK;IAGlCC,YAAYU,MAA8C,CAAE;QAC1D,KAAK,CAACA,OAAOC,OAAO;QACpB,IAAI,CAACC,UAAU,GAAGF,OAAOG,IAAI;IAC/B;IAEA;;;;GAIC,GACD,IAAIF,UAAU;QACZ,MAAM,IAAIG,yBAAkB,CAAC;YAC3BD,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;IAEA;;;;GAIC,GACDV,cAAc;QACZ,MAAM,IAAIY,yBAAkB,CAAC;YAC3BD,MAAM,IAAI,CAACD,UAAU;QACvB;IACF;AACF"}