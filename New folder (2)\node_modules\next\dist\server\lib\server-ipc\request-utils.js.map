{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/request-utils.ts"], "names": ["deserializeErr", "invokeIpcMethod", "serializedErr", "stack", "ErrorType", "Error", "name", "PageNotFoundError", "err", "message", "digest", "process", "env", "NODE_ENV", "NEXT_RUNTIME", "decorateServerError", "source", "fetchHostname", "method", "args", "ipcPort", "ipcKey", "res", "invokeRequest", "encodeURIComponent", "JSON", "stringify", "headers", "body", "text", "startsWith", "endsWith", "parsedBody", "parse"], "mappings": ";;;;;;;;;;;;;;;IAIaA,cAAc;eAAdA;;IA4BSC,eAAe;eAAfA;;;6BAhCc;uBACF;+BACJ;AAEvB,MAAMD,iBAAiB,CAACE;IAC7B,IACE,CAACA,iBACD,OAAOA,kBAAkB,YACzB,CAACA,cAAcC,KAAK,EACpB;QACA,OAAOD;IACT;IACA,IAAIE,YAAiBC;IAErB,IAAIH,cAAcI,IAAI,KAAK,qBAAqB;QAC9CF,YAAYG,wBAAiB;IAC/B;IAEA,MAAMC,MAAM,IAAIJ,UAAUF,cAAcO,OAAO;IAC/CD,IAAIL,KAAK,GAAGD,cAAcC,KAAK;IAC/BK,IAAIF,IAAI,GAAGJ,cAAcI,IAAI;IAC3BE,IAAYE,MAAM,GAAGR,cAAcQ,MAAM;IAE3C,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACE,YAAY,KAAK,QAC7B;QACAC,IAAAA,gCAAmB,EAACP,KAAKN,cAAcc,MAAM,IAAI;IACnD;IACA,OAAOR;AACT;AAEO,eAAeP,gBAAgB,EACpCgB,gBAAgB,WAAW,EAC3BC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,MAAM,EAOP;IACC,IAAID,SAAS;QACX,MAAME,MAAM,MAAMC,IAAAA,4BAAa,EAC7B,CAAC,OAAO,EAAEN,cAAc,CAAC,EAAEG,QAAQ,KAAK,EAAEC,OAAO,QAAQ,EACvDH,OACD,MAAM,EAAEM,mBAAmBC,KAAKC,SAAS,CAACP,OAAO,CAAC,EACnD;YACED,QAAQ;YACRS,SAAS,CAAC;QACZ;QAEF,MAAMC,OAAO,MAAMN,IAAIO,IAAI;QAE3B,IAAID,KAAKE,UAAU,CAAC,QAAQF,KAAKG,QAAQ,CAAC,MAAM;YAC9C,MAAMC,aAAaP,KAAKQ,KAAK,CAACL;YAE9B,IACEI,cACA,OAAOA,eAAe,YACtB,SAASA,cACT,WAAWA,WAAWxB,GAAG,EACzB;gBACA,MAAMR,eAAegC,WAAWxB,GAAG;YACrC;YACA,OAAOwB;QACT;IACF;AACF"}