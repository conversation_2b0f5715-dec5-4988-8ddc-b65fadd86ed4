'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowRight, Package, Check, Truck, Clock, Calendar, AlertCircle, Box, MapPin, ShoppingBag } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TrackOrderPage() {
  const [orderId, setOrderId] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [orderData, setOrderData] = useState(null);
  const [error, setError] = useState('');
  const router = useRouter();

  // Mock order data - in a real app, this would come from your API
  const mockOrders = {
    'ORD-001': {
      id: 'ORD-001',
      email: '<EMAIL>',
      date: '2025-06-01',
      status: 'delivered',
      total: 129.99,
      items: [
        { id: 1, name: 'Wireless Headphones', price: 79.99, quantity: 1 },
        { id: 2, name: 'Phone Case', price: 49.99, quantity: 1 },
      ],
      timeline: [
        { status: 'order_placed', date: '2025-06-01', time: '09:23 AM', completed: true },
        { status: 'payment_confirmed', date: '2025-06-01', time: '09:25 AM', completed: true },
        { status: 'processing', date: '2025-06-01', time: '11:30 AM', completed: true },
        { status: 'shipped', date: '2025-06-02', time: '10:15 AM', completed: true },
        { status: 'out_for_delivery', date: '2025-06-03', time: '08:45 AM', completed: true },
        { status: 'delivered', date: '2025-06-03', time: '02:30 PM', completed: true }
      ],
      trackingNumber: 'TRK123456789',
      carrier: 'FedEx',
      shippingAddress: {
        name: 'John Doe',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'USA'
      },
      estimatedDelivery: '2025-06-03'
    },
    'ORD-002': {
      id: 'ORD-002',
      email: '<EMAIL>',
      date: '2025-06-02',
      status: 'shipped',
      total: 89.50,
      items: [
        { id: 3, name: 'Smart Watch', price: 89.50, quantity: 1 }
      ],
      timeline: [
        { status: 'order_placed', date: '2025-06-02', time: '14:05 PM', completed: true },
        { status: 'payment_confirmed', date: '2025-06-02', time: '14:07 PM', completed: true },
        { status: 'processing', date: '2025-06-02', time: '16:30 PM', completed: true },
        { status: 'shipped', date: '2025-06-03', time: '11:20 AM', completed: true },
        { status: 'out_for_delivery', date: '2025-06-04', time: '', completed: false },
        { status: 'delivered', date: '', time: '', completed: false }
      ],
      trackingNumber: 'TRK987654321',
      carrier: 'DHL',
      shippingAddress: {
        name: 'John Doe',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'USA'
      },
      estimatedDelivery: '2025-06-04'
    },
    'ORD-003': {
      id: 'ORD-003',
      email: '<EMAIL>',
      date: '2025-06-03',
      status: 'processing',
      total: 245.00,
      items: [
        { id: 4, name: 'Bluetooth Speaker', price: 120.00, quantity: 1 },
        { id: 5, name: 'Wireless Charger', price: 45.00, quantity: 1 },
        { id: 6, name: 'USB-C Cable', price: 25.00, quantity: 2 }
      ],
      timeline: [
        { status: 'order_placed', date: '2025-06-03', time: '10:15 AM', completed: true },
        { status: 'payment_confirmed', date: '2025-06-03', time: '10:17 AM', completed: true },
        { status: 'processing', date: '2025-06-03', time: '11:30 AM', completed: true },
        { status: 'shipped', date: '', time: '', completed: false },
        { status: 'out_for_delivery', date: '', time: '', completed: false },
        { status: 'delivered', date: '', time: '', completed: false }
      ],
      trackingNumber: null,
      carrier: null,
      shippingAddress: {
        name: 'John Doe',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'USA'
      },
      estimatedDelivery: '2025-06-05'
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setOrderData(null);

    // Simulate API call
    setTimeout(() => {
      const foundOrder = mockOrders[orderId];
      
      if (foundOrder && foundOrder.email.toLowerCase() === email.toLowerCase()) {
        setOrderData(foundOrder);
        toast.success('Order found!');
      } else {
        setError('No order found with the provided ID and email combination.');
        toast.error('Order not found');
      }
      
      setLoading(false);
    }, 1200);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'order_placed':
        return <ShoppingBag className="w-6 h-6" />;
      case 'payment_confirmed':
        return <Check className="w-6 h-6" />;
      case 'processing':
        return <Box className="w-6 h-6" />;
      case 'shipped':
        return <Package className="w-6 h-6" />;
      case 'out_for_delivery':
        return <Truck className="w-6 h-6" />;
      case 'delivered':
        return <MapPin className="w-6 h-6" />;
      default:
        return <Clock className="w-6 h-6" />;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'order_placed':
        return 'Order Placed';
      case 'payment_confirmed':
        return 'Payment Confirmed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      default:
        return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
      case 'out_for_delivery':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'order_placed':
      case 'payment_confirmed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-8 md:py-12">
      <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
        {/* Promotional Banner */}
        <div className="mb-6 sm:mb-8 md:mb-10 relative overflow-hidden">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg shadow-lg p-6 relative overflow-hidden">
            {/* Spinning dashed border */}
            <div className="absolute -inset-1 rounded-lg border-2 border-dashed border-yellow-400 animate-spin-slow z-10"></div>
            
            <div className="relative z-20 flex flex-col md:flex-row items-center justify-between">
              <div>
                <h2 className="text-xl md:text-2xl font-bold text-white">
                  <span className="bg-yellow-400 text-purple-900 px-2 py-1 rounded mr-2 animate-pulse">HOT</span>
                  Easy Order Tracking
                </h2>
                <p className="text-white/80 mt-2">
                  Track any order with your order ID and email address!
                </p>
              </div>
              
              {/* Animated badge */}
              <div className="relative">
                <div className="absolute -top-3 -right-3 bg-yellow-400 text-xs font-bold uppercase rounded-full px-2 py-1 z-30 animate-bounce text-purple-900">
                  New Feature
                </div>
                <Link 
                  href="/account" 
                  className="bg-white text-purple-700 hover:bg-yellow-400 hover:text-purple-900 transition-all transform hover:scale-105 px-4 sm:px-6 py-2 rounded-full font-bold shadow-md flex items-center space-x-2 whitespace-nowrap text-sm sm:text-base"
                >
                  <span>View All Orders</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold text-gray-900">
            Track Your Order
          </h1>
          <p className="mt-2 sm:mt-3 max-w-2xl mx-auto text-base sm:text-lg md:text-xl text-gray-500">
            Enter your order ID and email address to check the status of your purchase.
          </p>
        </div>

        {/* Tracking Form */}
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-5 md:p-6 mb-6 sm:mb-8 md:mb-10">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="orderId" className="block text-sm font-medium text-gray-700">
                Order ID
              </label>
              <input
                type="text"
                id="orderId"
                name="orderId"
                placeholder="e.g. ORD-001"
                required
                value={orderId}
                onChange={(e) => setOrderId(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
              />
              <p className="mt-1 text-xs text-gray-500">
                Your order ID was sent to you in your order confirmation email.
              </p>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter the email used for your order"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
              />
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={loading}
                className={`w-full flex justify-center py-2.5 sm:py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Searching...
                  </>
                ) : (
                  'Track Order'
                )}
              </button>
            </div>
          </form>

          {error && (
            <div className="mt-4 p-4 bg-red-50 rounded-md border border-red-200">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Order Details */}
        {orderData && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden text-sm sm:text-base">
            {/* Order Header */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <h2 className="text-xl font-bold">Order #{orderData.id}</h2>
                  <p className="text-white/80 mt-1">Placed on {orderData.date}</p>
                </div>
                <div className="mt-4 md:mt-0">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium capitalize ${
                    orderData.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    orderData.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {orderData.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="p-4 sm:p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Delivery Status</h3>
              
              <div className="relative pl-1">
                {/* Vertical line */}
                <div className="absolute h-full w-0.5 bg-gray-200 left-3 sm:left-3.5 top-0 z-0"></div>
                
                <ul className="relative z-10 space-y-4 sm:space-y-6">
                  {orderData.timeline.map((event, index) => (
                    <li key={index} className="flex items-start">
                      <div className={`rounded-full p-1 sm:p-1.5 ${
                        event.completed 
                          ? 'bg-green-500 text-white' 
                          : 'bg-gray-300 text-gray-500'
                      } flex-shrink-0 z-10`}>
                        <span className="hidden sm:inline">{getStatusIcon(event.status)}</span>
                        <span className="inline sm:hidden">{getStatusIcon(event.status)}</span>
                      </div>
                      <div className="ml-4">
                        <h4 className="text-sm font-semibold text-gray-900">{getStatusLabel(event.status)}</h4>
                        <div className="flex items-center mt-1">
                          {event.completed ? (
                            <>
                              <Calendar className="w-4 h-4 text-gray-500 mr-1" />
                              <span className="text-sm text-gray-600">{event.date}</span>
                              <span className="mx-2 text-gray-400">•</span>
                              <Clock className="w-4 h-4 text-gray-500 mr-1" />
                              <span className="text-sm text-gray-600">{event.time}</span>
                            </>
                          ) : (
                            <span className="text-sm text-gray-500">Pending</span>
                          )}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Tracking Info */}
            {orderData.trackingNumber && (
              <div className="p-4 sm:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tracking Information</h3>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                    <div>
                      <p className="text-sm text-gray-500">Tracking Number:</p>
                      <p className="text-base font-medium text-gray-900">{orderData.trackingNumber}</p>
                    </div>
                    <div className="mt-2 md:mt-0">
                      <p className="text-sm text-gray-500">Carrier:</p>
                      <p className="text-base font-medium text-gray-900">{orderData.carrier}</p>
                    </div>
                    {orderData.status !== 'delivered' && (
                      <div className="mt-2 md:mt-0">
                        <p className="text-sm text-gray-500">Estimated Delivery:</p>
                        <p className="text-base font-medium text-gray-900">{orderData.estimatedDelivery}</p>
                      </div>
                    )}
                  </div>
                  <div className="mt-4">
                    <a 
                      href="#" 
                      className="inline-flex items-center text-blue-600 hover:text-blue-800"
                      onClick={(e) => {
                        e.preventDefault();
                        toast.success('This would redirect to the carrier website in a real implementation');
                      }}
                    >
                      <span>View on carrier website</span>
                      <ArrowRight className="ml-1 w-4 h-4" />
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Shipping Address */}
            <div className="p-4 sm:p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
              <address className="not-italic">
                <p className="text-gray-900 font-medium">{orderData.shippingAddress.name}</p>
                <p className="text-gray-700">{orderData.shippingAddress.street}</p>
                <p className="text-gray-700">
                  {orderData.shippingAddress.city}, {orderData.shippingAddress.state} {orderData.shippingAddress.zip}
                </p>
                <p className="text-gray-700">{orderData.shippingAddress.country}</p>
              </address>
            </div>

            {/* Order Items */}
            <div className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
              <ul className="divide-y divide-gray-200">
                {orderData.items.map((item) => (
                  <li key={item.id} className="py-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-md flex items-center justify-center text-gray-500">
                        <Package className="w-6 h-6 sm:w-8 sm:h-8" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-900">{item.name}</p>
                        <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                      </div>
                    </div>
                    <p className="text-sm font-medium text-gray-900">${item.price.toFixed(2)}</p>
                  </li>
                ))}
              </ul>
              <div className="mt-6 border-t border-gray-200 pt-4">
                <div className="flex justify-between">
                  <p className="text-base font-medium text-gray-900">Total</p>
                  <p className="text-base font-medium text-gray-900">${orderData.total.toFixed(2)}</p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-gray-50 p-4 sm:p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <Link
                  href="/"
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Continue Shopping
                </Link>
                <button
                  type="button"
                  onClick={() => {
                    toast.success('Support request submitted');
                    setOrderData(null);
                    setOrderId('');
                    setEmail('');
                  }}
                  className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 w-full sm:w-auto bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Need Help with Order
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Demo Instructions */}
        {!orderData && !loading && (
          <div className="mt-6 sm:mt-8 md:mt-10 bg-blue-50 rounded-lg p-3 sm:p-4 border border-blue-100">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Demo Instructions</h3>
            <p className="text-sm text-blue-700">
              You can try these example order IDs with email <code><EMAIL></code>:
            </p>
            <ul className="mt-2 list-disc list-inside text-sm text-blue-700 space-y-1">
              <li>ORD-001 (Delivered)</li>
              <li>ORD-002 (Shipped)</li>
              <li>ORD-003 (Processing)</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
