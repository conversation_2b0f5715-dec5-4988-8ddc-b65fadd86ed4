const { wooCommerceApi } = require('./lib/woocommerce');

async function testSingleProduct() {
  console.log('🧪 Testing Single Product API Call...\n');
  
  try {
    console.log('1. Testing product ID 13619...');
    const product = await wooCommerceApi.getProduct(13619);
    
    if (product) {
      console.log('✅ Product loaded successfully!');
      console.log(`   Name: ${product.name}`);
      console.log(`   Price: ${product.price}`);
      console.log(`   Images: ${product.images?.length || 0}`);
      console.log(`   Categories: ${product.categories?.map(c => c.name).join(', ') || 'None'}`);
      console.log(`   Stock Status: ${product.stockStatus}`);
      
      // Test if all required fields are present
      const requiredFields = ['id', 'name', 'price', 'images'];
      const missingFields = requiredFields.filter(field => !product[field]);
      
      if (missingFields.length === 0) {
        console.log('✅ All required fields present');
      } else {
        console.log(`⚠️  Missing fields: ${missingFields.join(', ')}`);
      }
      
    } else {
      console.log('❌ Product not found or returned null');
    }
    
  } catch (error) {
    console.log('❌ Error testing product API:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

testSingleProduct();
