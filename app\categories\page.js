import { wooCommerceApi } from '@/lib/woocommerce';
import Link from 'next/link';
import Image from 'next/image';
import { ShoppingBag } from 'lucide-react';

export const metadata = {
  title: 'Product Categories - Deal4u',
  description: 'Browse all product categories available at Deal4u',
};

// Helper function to get a background color based on category name
const getCategoryColor = (name) => {
  const colors = [
    'bg-blue-200', 'bg-green-200', 'bg-yellow-200', 'bg-red-200',
    'bg-purple-200', 'bg-pink-200', 'bg-indigo-200', 'bg-orange-200'
  ];
  
  // Use the first character of the name to determine the color
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};

// Helper function to get a text color based on background color
const getTextColor = (bgColor) => {
  const colorMap = {
    'bg-blue-200': 'text-blue-700',
    'bg-green-200': 'text-green-700',
    'bg-yellow-200': 'text-yellow-700',
    'bg-red-200': 'text-red-700',
    'bg-purple-200': 'text-purple-700',
    'bg-pink-200': 'text-pink-700',
    'bg-indigo-200': 'text-indigo-700',
    'bg-orange-200': 'text-orange-700'
  };
  
  return colorMap[bgColor] || 'text-gray-700';
};

export default async function CategoriesPage() {
  // Fetch all categories
  const categories = await wooCommerceApi.getCategories();
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-10 text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Shop by Category
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Browse our wide selection of products by category to find exactly what you're looking for.
          </p>
        </div>
        
        {/* Categories Grid */}
        {categories.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link 
                key={category.id} 
                href={`/shop?category=${category.slug}`}
                className="group"
              >
                <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow h-full">
                  {/* Use category-specific colored background with icon */}
                  <div className={`${getCategoryColor(category.name)} h-48 flex items-center justify-center relative`}>
                    {/* Category icon */}
                    <ShoppingBag className={`w-16 h-16 ${getTextColor(getCategoryColor(category.name))}`} />
                    
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-white font-medium text-lg">View Products</span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                      {category.name}
                    </h3>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {category.count} {category.count === 1 ? 'product' : 'products'}
                      </span>
                      <span className="text-blue-600 text-sm group-hover:underline">
                        Browse →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg shadow-sm">
            <ShoppingBag className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600">
              Please check back later or browse our products directly.
            </p>
            <Link 
              href="/shop"
              className="mt-6 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Browse All Products
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
