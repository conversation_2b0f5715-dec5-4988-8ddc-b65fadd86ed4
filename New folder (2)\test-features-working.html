<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Features Test - Deal4u</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">🧪 Features Test Page</h1>
        
        <!-- Test Results -->
        <div id="test-results" class="space-y-4 mb-8">
            <div class="bg-white p-4 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🔍 Testing Features...</h2>
                <div id="test-output" class="space-y-2 text-sm font-mono">
                    <div>Starting feature tests...</div>
                </div>
            </div>
        </div>

        <!-- Feature Demonstrations -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Live Chat Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">💬 Live Chat Test</h3>
                <div id="chat-test-area" class="border-2 border-dashed border-gray-300 p-4 rounded">
                    <p class="text-gray-500">Live chat widget should appear here when enabled</p>
                </div>
                <button onclick="testLiveChat()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Live Chat
                </button>
            </div>

            <!-- Product Recommendations Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">🎯 Product Recommendations Test</h3>
                <div id="recommendations-container" class="border-2 border-dashed border-gray-300 p-4 rounded">
                    <p class="text-gray-500">Product recommendations should appear here</p>
                </div>
                <button onclick="testRecommendations()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test Recommendations
                </button>
            </div>

            <!-- Loyalty Program Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">🎁 Loyalty Program Test</h3>
                <div id="loyalty-test-area" class="border-2 border-dashed border-gray-300 p-4 rounded">
                    <p class="text-gray-500">Loyalty widget should appear here</p>
                </div>
                <button onclick="testLoyalty()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test Loyalty Program
                </button>
            </div>

            <!-- Voice Search Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">🎤 Voice Search Test</h3>
                <div class="flex space-x-2">
                    <input type="text" id="search-input" placeholder="Search products..." class="flex-1 p-2 border rounded">
                    <button id="voice-search-btn" onclick="testVoiceSearch()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                        🎤
                    </button>
                </div>
                <div id="voice-status" class="mt-2 text-sm text-gray-600"></div>
            </div>

            <!-- Social Commerce Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">📱 Social Commerce Test</h3>
                <div class="border rounded p-4">
                    <h4 class="font-medium">Sample Product</h4>
                    <p class="text-sm text-gray-600">Amazing product description</p>
                    <div id="social-buttons" class="mt-2 flex space-x-2">
                        <!-- Social buttons should appear here -->
                    </div>
                </div>
                <button onclick="testSocialCommerce()" class="mt-4 bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700">
                    Test Social Sharing
                </button>
            </div>

            <!-- One-Click Reorder Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">⚡ One-Click Reorder Test</h3>
                <div class="border rounded p-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="font-medium">Order #12345</h4>
                            <p class="text-sm text-gray-600">3 items - £45.99</p>
                        </div>
                        <div id="reorder-button-area">
                            <!-- Reorder button should appear here -->
                        </div>
                    </div>
                </div>
                <button onclick="testReorder()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Reorder
                </button>
            </div>
        </div>

        <!-- API Test Section -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">🔌 API Connection Tests</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="testAPI('dashboard-stats')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test Dashboard API
                </button>
                <button onclick="testAPI('toggle-feature')" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Feature Toggle API
                </button>
                <button onclick="testAPI('woocommerce-cart')" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test WooCommerce API
                </button>
            </div>
            <div id="api-results" class="mt-4 p-4 bg-gray-50 rounded text-sm font-mono">
                API test results will appear here...
            </div>
        </div>
    </div>

    <script>
        let testOutput = document.getElementById('test-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            div.className = color;
            div.textContent = `[${timestamp}] ${message}`;
            testOutput.appendChild(div);
            testOutput.scrollTop = testOutput.scrollHeight;
        }

        // Test Live Chat
        function testLiveChat() {
            log('Testing Live Chat feature...');
            
            // Simulate live chat widget
            const chatArea = document.getElementById('chat-test-area');
            chatArea.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded p-3">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-green-600">💬</span>
                        <span class="font-medium">Live Chat Active</span>
                    </div>
                    <a href="https://wa.me/447447186806" target="_blank" class="text-green-600 hover:underline text-sm">
                        Chat on WhatsApp
                    </a>
                </div>
            `;
            log('Live Chat widget created successfully!', 'success');
        }

        // Test Product Recommendations
        function testRecommendations() {
            log('Testing Product Recommendations...');
            
            const container = document.getElementById('recommendations-container');
            container.innerHTML = `
                <div class="grid grid-cols-2 gap-2">
                    <div class="border rounded p-2 text-center">
                        <div class="w-full h-16 bg-gray-200 rounded mb-1"></div>
                        <p class="text-xs font-medium">Product 1</p>
                        <p class="text-xs text-blue-600">£19.99</p>
                    </div>
                    <div class="border rounded p-2 text-center">
                        <div class="w-full h-16 bg-gray-200 rounded mb-1"></div>
                        <p class="text-xs font-medium">Product 2</p>
                        <p class="text-xs text-blue-600">£24.99</p>
                    </div>
                </div>
            `;
            log('Product recommendations loaded!', 'success');
        }

        // Test Loyalty Program
        function testLoyalty() {
            log('Testing Loyalty Program...');
            
            const loyaltyArea = document.getElementById('loyalty-test-area');
            loyaltyArea.innerHTML = `
                <div class="bg-purple-50 border border-purple-200 rounded p-3 text-center">
                    <div class="text-2xl font-bold text-purple-600">1,250</div>
                    <div class="text-sm text-purple-600">Loyalty Points</div>
                    <div class="text-xs text-gray-500 mt-1">£12.50 value</div>
                    <button class="mt-2 bg-purple-600 text-white px-3 py-1 rounded text-xs">
                        Redeem Points
                    </button>
                </div>
            `;
            log('Loyalty program widget created!', 'success');
        }

        // Test Voice Search
        function testVoiceSearch() {
            log('Testing Voice Search...');
            
            const statusDiv = document.getElementById('voice-status');
            const searchInput = document.getElementById('search-input');
            
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                statusDiv.textContent = '🎤 Voice search is supported! Click to start listening...';
                statusDiv.className = 'mt-2 text-sm text-green-600';
                
                // Simulate voice input
                setTimeout(() => {
                    searchInput.value = 'wireless headphones';
                    statusDiv.textContent = '✅ Voice input: "wireless headphones"';
                    log('Voice search working!', 'success');
                }, 1000);
            } else {
                statusDiv.textContent = '❌ Voice search not supported in this browser';
                statusDiv.className = 'mt-2 text-sm text-red-600';
                log('Voice search not supported', 'error');
            }
        }

        // Test Social Commerce
        function testSocialCommerce() {
            log('Testing Social Commerce...');
            
            const socialButtons = document.getElementById('social-buttons');
            socialButtons.innerHTML = `
                <button class="bg-blue-600 text-white px-2 py-1 rounded text-xs">📘 Facebook</button>
                <button class="bg-pink-600 text-white px-2 py-1 rounded text-xs">📷 Instagram</button>
                <button class="bg-blue-400 text-white px-2 py-1 rounded text-xs">🐦 Twitter</button>
            `;
            log('Social sharing buttons added!', 'success');
        }

        // Test One-Click Reorder
        function testReorder() {
            log('Testing One-Click Reorder...');
            
            const reorderArea = document.getElementById('reorder-button-area');
            reorderArea.innerHTML = `
                <button class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                    ⚡ Reorder
                </button>
            `;
            log('Reorder button added!', 'success');
        }

        // Test API Endpoints
        async function testAPI(endpoint) {
            log(`Testing ${endpoint} API...`);
            const resultsDiv = document.getElementById('api-results');
            
            try {
                const response = await fetch(`/api/admin/${endpoint}`);
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="text-green-600">✅ ${endpoint} API Response:</div>
                    <pre class="mt-2 text-xs overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
                log(`${endpoint} API working!`, 'success');
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="text-red-600">❌ ${endpoint} API Error:</div>
                    <pre class="mt-2 text-xs">${error.message}</pre>
                `;
                log(`${endpoint} API failed: ${error.message}`, 'error');
            }
        }

        // Auto-run initial tests
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 Features Test Page Loaded');
            log('Click the test buttons to verify each feature works');
            log('Check the admin dashboard at: http://localhost:3000/admin/dashboard');
            
            // Test if admin button exists
            setTimeout(() => {
                const adminBtn = document.getElementById('admin-access-btn');
                if (adminBtn) {
                    log('✅ Admin access button found on page', 'success');
                } else {
                    log('❌ Admin access button not found', 'error');
                }
            }, 2000);
        });
    </script>
</body>
</html>
