{"version": 3, "sources": ["../../../src/trace/report/to-json.ts"], "names": ["batcher", "localEndpoint", "serviceName", "ipv4", "port", "reportEvents", "events", "queue", "Set", "flushAll", "Promise", "all", "length", "report", "event", "push", "evts", "slice", "add", "then", "delete", "writeStream", "traceId", "batch", "writeStreamOptions", "flags", "encoding", "RotatingWriteStream", "constructor", "file", "sizeLimit", "size", "createWriteStream", "fs", "rotate", "end", "unlinkSync", "err", "code", "rotatePromise", "undefined", "write", "data", "drainPromise", "resolve", "_reject", "once", "reportToLocalHost", "distDir", "traceGlobals", "get", "phase", "process", "env", "TRACE_ID", "randomBytes", "toString", "promises", "mkdir", "recursive", "path", "join", "PHASE_DEVELOPMENT_SERVER", "Infinity", "eventsJson", "JSON", "stringify", "console", "log"], "mappings": ";;;;;;;;;;;;;;;IAkBgBA,OAAO;eAAPA;;IAkIhB,OAYC;eAZD;;;wBApJ4B;wBACC;2DACd;6DACE;2BACwB;;;;;;AAGzC,MAAMC,gBAAgB;IACpBC,aAAa;IACbC,MAAM;IACNC,MAAM;AACR;AAOO,SAASJ,QAAQK,YAA8C;IACpE,MAAMC,SAAkB,EAAE;IAC1B,6DAA6D;IAC7D,MAAMC,QAAQ,IAAIC;IAClB,OAAO;QACLC,UAAU;YACR,MAAMC,QAAQC,GAAG,CAACJ;YAClB,IAAID,OAAOM,MAAM,GAAG,GAAG;gBACrB,MAAMP,aAAaC;gBACnBA,OAAOM,MAAM,GAAG;YAClB;QACF;QACAC,QAAQ,CAACC;YACPR,OAAOS,IAAI,CAACD;YAEZ,IAAIR,OAAOM,MAAM,GAAG,KAAK;gBACvB,MAAMI,OAAOV,OAAOW,KAAK;gBACzBX,OAAOM,MAAM,GAAG;gBAChB,MAAMC,SAASR,aAAaW;gBAC5BT,MAAMW,GAAG,CAACL;gBACVA,OAAOM,IAAI,CAAC,IAAMZ,MAAMa,MAAM,CAACP;YACjC;QACF;IACF;AACF;AAEA,IAAIQ;AACJ,IAAIC;AACJ,IAAIC;AAEJ,MAAMC,qBAAqB;IACzBC,OAAO;IACPC,UAAU;AACZ;AACA,MAAMC;IAOJC,YAAYC,IAAY,EAAEC,SAAiB,CAAE;QAC3C,IAAI,CAACD,IAAI,GAAGA;QACZ,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACD,SAAS,GAAGA;QACjB,IAAI,CAACE,iBAAiB;IACxB;IACQA,oBAAoB;QAC1B,IAAI,CAACX,WAAW,GAAGY,WAAE,CAACD,iBAAiB,CAAC,IAAI,CAACH,IAAI,EAAEL;IACrD;IACA,oBAAoB;IACpB,MAAcU,SAAS;QACrB,MAAM,IAAI,CAACC,GAAG;QACd,IAAI;YACFF,WAAE,CAACG,UAAU,CAAC,IAAI,CAACP,IAAI;QACzB,EAAE,OAAOQ,KAAU;YACjB,2CAA2C;YAC3C,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QACA,IAAI,CAACN,IAAI,GAAG;QACZ,IAAI,CAACC,iBAAiB;QACtB,IAAI,CAACO,aAAa,GAAGC;IACvB;IACA,MAAMC,MAAMC,IAAY,EAAiB;QACvC,IAAI,IAAI,CAACH,aAAa,EAAE,MAAM,IAAI,CAACA,aAAa;QAEhD,IAAI,CAACR,IAAI,IAAIW,KAAK9B,MAAM;QACxB,IAAI,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACD,SAAS,EAAE;YAC9B,MAAO,CAAA,IAAI,CAACS,aAAa,GAAG,IAAI,CAACL,MAAM,EAAC;QAC1C;QAEA,IAAI,CAAC,IAAI,CAACb,WAAW,CAACoB,KAAK,CAACC,MAAM,SAAS;YACzC,IAAI,IAAI,CAACC,YAAY,KAAKH,WAAW;gBACnC,IAAI,CAACG,YAAY,GAAG,IAAIjC,QAAc,CAACkC,SAASC;oBAC9C,IAAI,CAACxB,WAAW,CAACyB,IAAI,CAAC,SAAS;wBAC7B,IAAI,CAACH,YAAY,GAAGH;wBACpBI;oBACF;gBACF;YACF;YACA,MAAM,IAAI,CAACD,YAAY;QACzB;IACF;IAEAR,MAAqB;QACnB,OAAO,IAAIzB,QAAQ,CAACkC;YAClB,IAAI,CAACvB,WAAW,CAACc,GAAG,CAACS;QACvB;IACF;AACF;AAEA,MAAMG,oBAAoB,CAACjC;IACzB,MAAMkC,UAAUC,oBAAY,CAACC,GAAG,CAAC;IACjC,MAAMC,QAAQF,oBAAY,CAACC,GAAG,CAAC;IAC/B,IAAI,CAACF,WAAW,CAACG,OAAO;QACtB;IACF;IAEA,IAAI,CAAC7B,SAAS;QACZA,UAAU8B,QAAQC,GAAG,CAACC,QAAQ,IAAIC,IAAAA,mBAAW,EAAC,GAAGC,QAAQ,CAAC;IAC5D;IAEA,IAAI,CAACjC,OAAO;QACVA,QAAQvB,QAAQ,OAAOM;YACrB,IAAI,CAACe,aAAa;gBAChB,MAAMY,WAAE,CAACwB,QAAQ,CAACC,KAAK,CAACV,SAAS;oBAAEW,WAAW;gBAAK;gBACnD,MAAM9B,OAAO+B,aAAI,CAACC,IAAI,CAACb,SAAS;gBAChC3B,cAAc,IAAIM,oBAChBE,MACA,0DAA0D;gBAC1DsB,UAAUW,mCAAwB,GAAG,WAAWC;YAEpD;YACA,MAAMC,aAAaC,KAAKC,SAAS,CAAC5D;YAClC,IAAI;gBACF,MAAMe,YAAYoB,KAAK,CAACuB,aAAa;YACvC,EAAE,OAAO3B,KAAK;gBACZ8B,QAAQC,GAAG,CAAC/B;YACd;QACF;IACF;IAEAd,MAAMV,MAAM,CAAC;QACX,GAAGC,KAAK;QACRQ;IACF;AACF;MAEA,WAAe;IACbb,UAAU,IACRc,QACIA,MAAMd,QAAQ,GAAGU,IAAI,CAAC;YACpB,MAAMgC,QAAQF,oBAAY,CAACC,GAAG,CAAC;YAC/B,4DAA4D;YAC5D,IAAIC,UAAUW,mCAAwB,EAAE;gBACtC,OAAOzC,YAAYc,GAAG;YACxB;QACF,KACAK;IACN3B,QAAQkC;AACV"}