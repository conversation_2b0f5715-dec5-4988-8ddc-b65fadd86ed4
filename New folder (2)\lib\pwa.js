// PWA Utilities for Deal4u

/**
 * Register Service Worker
 */
export const registerServiceWorker = () => {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    window.addEventListener('load', async () => {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });

        console.log('Service Worker registered successfully:', registration);

        // Handle updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, show update notification
              showUpdateNotification();
            }
          });
        });

        // Listen for messages from service worker
        navigator.serviceWorker.addEventListener('message', (event) => {
          console.log('Message from service worker:', event.data);
          
          if (event.data.type === 'CACHE_UPDATED') {
            showCacheUpdateNotification();
          }
        });

      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    });
  }
};

/**
 * Check if app is running in standalone mode (installed as PWA)
 */
export const isPWAInstalled = () => {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone ||
         document.referrer.includes('android-app://');
};

/**
 * Check if device supports PWA installation
 */
export const canInstallPWA = () => {
  if (typeof window === 'undefined') return false;
  
  return 'serviceWorker' in navigator && 
         'PushManager' in window &&
         'Notification' in window;
};

/**
 * Request notification permission
 */
export const requestNotificationPermission = async () => {
  if (!('Notification' in window)) {
    console.log('This browser does not support notifications');
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

/**
 * Show local notification
 */
export const showNotification = (title, options = {}) => {
  if (!('Notification' in window) || Notification.permission !== 'granted') {
    return;
  }

  const defaultOptions = {
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [200, 100, 200],
    ...options
  };

  return new Notification(title, defaultOptions);
};

/**
 * Subscribe to push notifications
 */
export const subscribeToPushNotifications = async () => {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.log('Push notifications not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || '')
    });

    console.log('Push subscription successful:', subscription);
    
    // Send subscription to server
    await sendSubscriptionToServer(subscription);
    
    return subscription;
  } catch (error) {
    console.error('Push subscription failed:', error);
    return null;
  }
};

/**
 * Unsubscribe from push notifications
 */
export const unsubscribeFromPushNotifications = async () => {
  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();
    
    if (subscription) {
      await subscription.unsubscribe();
      console.log('Push unsubscription successful');
      
      // Remove subscription from server
      await removeSubscriptionFromServer(subscription);
    }
  } catch (error) {
    console.error('Push unsubscription failed:', error);
  }
};

/**
 * Check if user is online
 */
export const isOnline = () => {
  if (typeof window === 'undefined') return true;
  return navigator.onLine;
};

/**
 * Add online/offline event listeners
 */
export const addConnectionListeners = (onOnline, onOffline) => {
  if (typeof window === 'undefined') return;

  const handleOnline = () => {
    console.log('Connection restored');
    onOnline && onOnline();
  };

  const handleOffline = () => {
    console.log('Connection lost');
    onOffline && onOffline();
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};

/**
 * Cache important data for offline use
 */
export const cacheOfflineData = async (data, key) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(`offline_${key}`, JSON.stringify({
      data,
      timestamp: Date.now()
    }));
  } catch (error) {
    console.error('Failed to cache offline data:', error);
  }
};

/**
 * Get cached offline data
 */
export const getCachedOfflineData = (key, maxAge = 24 * 60 * 60 * 1000) => {
  if (typeof window === 'undefined') return null;

  try {
    const cached = localStorage.getItem(`offline_${key}`);
    if (!cached) return null;

    const { data, timestamp } = JSON.parse(cached);
    
    // Check if data is still fresh
    if (Date.now() - timestamp > maxAge) {
      localStorage.removeItem(`offline_${key}`);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Failed to get cached offline data:', error);
    return null;
  }
};

/**
 * Background sync for cart and wishlist
 */
export const requestBackgroundSync = async (tag) => {
  if (!('serviceWorker' in navigator) || !('sync' in window.ServiceWorkerRegistration.prototype)) {
    console.log('Background sync not supported');
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    await registration.sync.register(tag);
    console.log(`Background sync registered for: ${tag}`);
    return true;
  } catch (error) {
    console.error('Background sync registration failed:', error);
    return false;
  }
};

// Helper functions

function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

async function sendSubscriptionToServer(subscription) {
  try {
    const response = await fetch('/api/push-subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }
  } catch (error) {
    console.error('Error sending subscription to server:', error);
  }
}

async function removeSubscriptionFromServer(subscription) {
  try {
    const response = await fetch('/api/push-unsubscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription),
    });

    if (!response.ok) {
      throw new Error('Failed to remove subscription from server');
    }
  } catch (error) {
    console.error('Error removing subscription from server:', error);
  }
}

function showUpdateNotification() {
  if (typeof window === 'undefined') return;

  const notification = document.createElement('div');
  notification.className = 'fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50';
  notification.innerHTML = `
    <div class="flex items-center justify-between">
      <span>New version available!</span>
      <button onclick="window.location.reload()" class="ml-4 bg-white text-blue-600 px-3 py-1 rounded text-sm">
        Update
      </button>
    </div>
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 10000);
}

function showCacheUpdateNotification() {
  console.log('Cache updated with new content');
}
