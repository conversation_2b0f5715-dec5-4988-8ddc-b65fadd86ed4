{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-ssg-transform.ts"], "names": ["SERVER_PROPS_SSG_CONFLICT", "SERVER_PROPS_ID", "STATIC_PROPS_ID", "EXPORT_NAME_GET_STATIC_PROPS", "EXPORT_NAME_GET_STATIC_PATHS", "EXPORT_NAME_GET_SERVER_PROPS", "ssgExports", "Set", "decorateSsgExport", "t", "path", "state", "gsspName", "<PERSON><PERSON><PERSON><PERSON>", "gsspId", "identifier", "addGsspExport", "exportPath", "done", "pageCompPath", "replaceWithMultiple", "exportNamedDeclaration", "variableDeclaration", "variableDeclarator", "booleanLiteral", "exportSpecifier", "node", "scope", "registerDeclaration", "traverse", "ExportDefaultDeclaration", "exportDefaultPath", "ExportNamedDeclaration", "exportNamedPath", "isDataIdentifier", "name", "has", "Error", "isServerProps", "nextTransformSsg", "types", "getIdentifier", "parentPath", "type", "pp", "get", "id", "isIdentifierReferenced", "ident", "b", "getBinding", "referenced", "constantViolations", "concat", "referencePaths", "every", "ref", "findParent", "p", "markFunction", "refs", "add", "markImport", "local", "visitor", "Program", "enter", "VariableDeclarator", "variablePath", "variableState", "pattern", "properties", "for<PERSON>ach", "elements", "e", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ImportSpecifier", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "exportNamedState", "specifiers", "length", "s", "isIdentifier", "exported", "value", "remove", "decl", "inner", "d", "count", "sweepFunction", "sweepPath", "isAssignmentExpression", "isVariableDeclarator", "sweepImport", "parent", "crawl", "beforeCount"], "mappings": "AAKA,SAASA,yBAAyB,QAAQ,yBAAwB;AAClE,SAASC,eAAe,EAAEC,eAAe,QAAQ,gCAA+B;AAEhF,OAAO,MAAMC,+BAA+B,iBAAgB;AAC5D,OAAO,MAAMC,+BAA+B,iBAAgB;AAC5D,OAAO,MAAMC,+BAA+B,qBAAoB;AAEhE,MAAMC,aAAa,IAAIC,IAAI;IACzBJ;IACAC;IACAC;IAEA,4DAA4D;IAC5D,2BAA2B;IAC3B,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,2BAA2B,CAAC;CAC9B;AASD,SAASG,kBACPC,CAAoB,EACpBC,IAAkC,EAClCC,KAAkB;IAElB,MAAMC,WAAWD,MAAME,WAAW,GAAGX,kBAAkBD;IACvD,MAAMa,SAASL,EAAEM,UAAU,CAACH;IAE5B,MAAMI,gBAAgB,CACpBC;QAIA,IAAIN,MAAMO,IAAI,EAAE;YACd;QACF;QACAP,MAAMO,IAAI,GAAG;QAEb,MAAM,CAACC,aAAa,GAAGF,WAAWG,mBAAmB,CAAC;YACpDX,EAAEY,sBAAsB,CACtBZ,EAAEa,mBAAmB,CACnB,kEAAkE;YAClE,iEAAiE;YACjE,iCAAiC;YACjC,OACA;gBAACb,EAAEc,kBAAkB,CAACT,QAAQL,EAAEe,cAAc,CAAC;aAAO,GAExD;gBAACf,EAAEgB,eAAe,CAACX,QAAQA;aAAQ;YAErCG,WAAWS,IAAI;SAChB;QACDT,WAAWU,KAAK,CAACC,mBAAmB,CAClCT;IAEJ;IAEAT,KAAKmB,QAAQ,CAAC;QACZC,0BAAyBC,iBAAiB;YACxCf,cAAce;QAChB;QACAC,wBAAuBC,eAAe;YACpCjB,cAAciB;QAChB;IACF;AACF;AAEA,MAAMC,mBAAmB,CAACC,MAAcxB;IACtC,IAAIL,WAAW8B,GAAG,CAACD,OAAO;QACxB,IAAIA,SAAS9B,8BAA8B;YACzC,IAAIM,MAAME,WAAW,EAAE;gBACrB,MAAM,IAAIwB,MAAMrC;YAClB;YACAW,MAAM2B,aAAa,GAAG;QACxB,OAAO;YACL,IAAI3B,MAAM2B,aAAa,EAAE;gBACvB,MAAM,IAAID,MAAMrC;YAClB;YACAW,MAAME,WAAW,GAAG;QACtB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,eAAe,SAAS0B,iBAAiB,EACvCC,OAAO/B,CAAC,EAGT;IACC,SAASgC,cACP/B,IAGgD;QAEhD,MAAMgC,aAAahC,KAAKgC,UAAU;QAClC,IAAIA,WAAWC,IAAI,KAAK,sBAAsB;YAC5C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAIO,WAAWC,IAAI,KAAK,wBAAwB;YAC9C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAIzB,KAAKgB,IAAI,CAACiB,IAAI,KAAK,2BAA2B;YAChD,OAAO;QACT;QAEA,OAAOjC,KAAKgB,IAAI,CAACoB,EAAE,IAAIpC,KAAKgB,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,eACxCjC,KAAKmC,GAAG,CAAC,QACV;IACN;IAEA,SAASE,uBACPC,KAAsC;QAEtC,MAAMC,IAAID,MAAMrB,KAAK,CAACuB,UAAU,CAACF,MAAMtB,IAAI,CAACS,IAAI;QAChD,IAAIc,qBAAAA,EAAGE,UAAU,EAAE;YACjB,uEAAuE;YACvE,6CAA6C;YAC7C,IAAIF,EAAEvC,IAAI,CAACiC,IAAI,KAAK,uBAAuB;gBACzC,OAAO,CAACM,EAAEG,kBAAkB,CACzBC,MAAM,CAACJ,EAAEK,cAAc,CACxB,+DAA+D;iBAC9DC,KAAK,CAAC,CAACC,MAAQA,IAAIC,UAAU,CAAC,CAACC,IAAMA,MAAMT,EAAEvC,IAAI;YACtD;YAEA,OAAO;QACT;QACA,OAAO;IACT;IAEA,SAASiD,aACPjD,IAGgD,EAChDC,KAAkB;QAElB,MAAMqC,QAAQP,cAAc/B;QAC5B,IAAIsC,CAAAA,yBAAAA,MAAOtB,IAAI,KAAIqB,uBAAuBC,QAAQ;YAChDrC,MAAMiD,IAAI,CAACC,GAAG,CAACb;QACjB;IACF;IAEA,SAASc,WACPpD,IAGiD,EACjDC,KAAkB;QAElB,MAAMoD,QAAQrD,KAAKmC,GAAG,CAAC;QACvB,IAAIE,uBAAuBgB,QAAQ;YACjCpD,MAAMiD,IAAI,CAACC,GAAG,CAACE;QACjB;IACF;IAEA,OAAO;QACLC,SAAS;YACPC,SAAS;gBACPC,OAAMxD,IAAI,EAAEC,KAAK;oBACfA,MAAMiD,IAAI,GAAG,IAAIrD;oBACjBI,MAAME,WAAW,GAAG;oBACpBF,MAAM2B,aAAa,GAAG;oBACtB3B,MAAMO,IAAI,GAAG;oBAEbR,KAAKmB,QAAQ,CACX;wBACEsC,oBAAmBC,YAAY,EAAEC,aAAa;4BAC5C,IAAID,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;gCAEF,IAAIE,uBAAuBgB,QAAQ;oCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;gCACzB;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;gCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM0B,aAAaD,QAAQzB,GAAG,CAAC;gCAC/B0B,WAAWC,OAAO,CAAC,CAACd;oCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBAChB,aACA,AAAC;wCACC,MAAM,IAAIN,MAAM;oCAClB;oCAEN,IAAIU,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;gCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM4B,WAAWH,QAAQzB,GAAG,CAAC;gCAC7B4B,SAASD,OAAO,CAAC,CAACE;wCAEZA,SAEOA;oCAHX,IAAIX;oCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;wCACjCoB,QAAQW;oCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;wCACzCoB,QAAQW,EAAE7B,GAAG,CACX;oCAEJ,OAAO;wCACL;oCACF;oCAEA,IAAIE,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF;wBACF;wBACAY,qBAAqBhB;wBACrBiB,oBAAoBjB;wBACpBkB,yBAAyBlB;wBACzBmB,iBAAiBhB;wBACjBiB,wBAAwBjB;wBACxBkB,0BAA0BlB;wBAC1B9B,wBAAuBC,eAAe,EAAEgD,gBAAgB;4BACtD,MAAMC,aAAajD,gBAAgBY,GAAG,CAAC;4BACvC,IAAIqC,WAAWC,MAAM,EAAE;gCACrBD,WAAWV,OAAO,CAAC,CAACY;oCAClB,IACElD,iBACEzB,EAAE4E,YAAY,CAACD,EAAE1D,IAAI,CAAC4D,QAAQ,IAC1BF,EAAE1D,IAAI,CAAC4D,QAAQ,CAACnD,IAAI,GACpBiD,EAAE1D,IAAI,CAAC4D,QAAQ,CAACC,KAAK,EACzBN,mBAEF;wCACAG,EAAEI,MAAM;oCACV;gCACF;gCAEA,IAAIvD,gBAAgBP,IAAI,CAACwD,UAAU,CAACC,MAAM,GAAG,GAAG;oCAC9ClD,gBAAgBuD,MAAM;gCACxB;gCACA;4BACF;4BAEA,MAAMC,OAAOxD,gBAAgBY,GAAG,CAAC;4BAIjC,IAAI4C,QAAQ,QAAQA,KAAK/D,IAAI,IAAI,MAAM;gCACrC;4BACF;4BAEA,OAAQ+D,KAAK/D,IAAI,CAACiB,IAAI;gCACpB,KAAK;oCAAuB;wCAC1B,MAAMR,OAAOsD,KAAK/D,IAAI,CAACoB,EAAE,CAAEX,IAAI;wCAC/B,IAAID,iBAAiBC,MAAM8C,mBAAmB;4CAC5ChD,gBAAgBuD,MAAM;wCACxB;wCACA;oCACF;gCACA,KAAK;oCAAuB;wCAC1B,MAAME,QAAQD,KAAK5C,GAAG,CACpB;wCAEF6C,MAAMlB,OAAO,CAAC,CAACmB;4CACb,IAAIA,EAAEjE,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gDACnC;4CACF;4CACA,MAAMR,OAAOwD,EAAEjE,IAAI,CAACoB,EAAE,CAACX,IAAI;4CAC3B,IAAID,iBAAiBC,MAAM8C,mBAAmB;gDAC5CU,EAAEH,MAAM;4CACV;wCACF;wCACA;oCACF;gCACA;oCAAS;wCACP;oCACF;4BACF;wBACF;oBACF,GACA7E;oBAGF,IAAI,CAACA,MAAME,WAAW,IAAI,CAACF,MAAM2B,aAAa,EAAE;wBAC9C;oBACF;oBAEA,MAAMsB,OAAOjD,MAAMiD,IAAI;oBACvB,IAAIgC;oBAEJ,SAASC,cACPC,SAGgD;wBAEhD,MAAM9C,QAAQP,cAAcqD;wBAC5B,IACE9C,CAAAA,yBAAAA,MAAOtB,IAAI,KACXkC,KAAKxB,GAAG,CAACY,UACT,CAACD,uBAAuBC,QACxB;4BACA,EAAE4C;4BAEF,IACEnF,EAAEsF,sBAAsB,CAACD,UAAUpD,UAAU,CAAChB,IAAI,KAClDjB,EAAEuF,oBAAoB,CAACF,UAAUpD,UAAU,CAAChB,IAAI,GAChD;gCACAoE,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B,OAAO;gCACLM,UAAUN,MAAM;4BAClB;wBACF;oBACF;oBAEA,SAASS,YACPH,SAGiD;wBAEjD,MAAM/B,QAAQ+B,UAAUjD,GAAG,CACzB;wBAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4BACrD,EAAE6B;4BACFE,UAAUN,MAAM;4BAChB,IACE,AAACM,UAAUI,MAAM,CAAkChB,UAAU,CAC1DC,MAAM,KAAK,GACd;gCACAW,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B;wBACF;oBACF;oBAEA,GAAG;wBACC9E,KAAKiB,KAAK,CAASwE,KAAK;wBAC1BP,QAAQ;wBAERlF,KAAKmB,QAAQ,CAAC;4BACZ,wCAAwC;4BACxCsC,oBAAmBC,YAAY;gCAC7B,IAAIA,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;oCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;oCAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;wCACrD,EAAE6B;wCACFxB,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;oCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMrB,aAAaD,QAAQzB,GAAG,CAAC;oCAC/B0B,WAAWC,OAAO,CAAC,CAACd;wCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBAChB,aACA,AAAC;4CACC,MAAM,IAAIN,MAAM;wCAClB;wCAGN,IAAIuB,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlC,EAAE8B,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,cAAcsC,MAAM,GAAG,GACnC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;oCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMnB,WAAWH,QAAQzB,GAAG,CAAC;oCAC7B4B,SAASD,OAAO,CAAC,CAACE;4CAEZA,SAEOA;wCAHX,IAAIX;wCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;4CACjCoB,QAAQW;wCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;4CACzCoB,QAAQW,EAAE7B,GAAG,CACX;wCAEJ,OAAO;4CACL;wCACF;wCAEA,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlB,EAAEc,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,YAAYsC,MAAM,GAAG,GACjC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF;4BACF;4BACAb,qBAAqBkB;4BACrBjB,oBAAoBiB;4BACpBhB,yBAAyBgB;4BACzBf,iBAAiBmB;4BACjBlB,wBAAwBkB;4BACxBjB,0BAA0BiB;wBAC5B;oBACF,QAASL,OAAM;oBAEfpF,kBAAkBC,GAAGC,MAAMC;gBAC7B;YACF;QACF;IACF;AACF"}