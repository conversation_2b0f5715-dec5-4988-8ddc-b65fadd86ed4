{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/utils.ts"], "names": ["actionsForbiddenHeaders", "filterInternalHeaders", "filterReqHeaders", "ipcForbiddenHeaders", "headers", "forbiddenHeaders", "key", "value", "Object", "entries", "includes", "Array", "isArray", "INTERNAL_HEADERS", "header"], "mappings": ";;;;;;;;;;;;;;;;;IAYaA,uBAAuB;eAAvBA;;IAwCAC,qBAAqB;eAArBA;;IAlCAC,gBAAgB;eAAhBA;;IAlBAC,mBAAmB;eAAnBA;;;AAAN,MAAMA,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA,+CAA+C;IAC/C;IACA,2IAA2I;IAC3I;CACD;AAEM,MAAMH,0BAA0B;OAClCG;IACH;IACA;CACD;AAEM,MAAMD,mBAAmB,CAC9BE,SACAC;IAEA,kGAAkG;IAClG,+CAA+C;IAC/C,IAAID,OAAO,CAAC,iBAAiB,IAAIA,OAAO,CAAC,iBAAiB,KAAK,KAAK;QAClE,OAAOA,OAAO,CAAC,iBAAiB;IAClC;IAEA,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,SAAU;QAClD,IACEC,iBAAiBK,QAAQ,CAACJ,QAC1B,CAAEK,CAAAA,MAAMC,OAAO,CAACL,UAAU,OAAOA,UAAU,QAAO,GAClD;YACA,OAAOH,OAAO,CAACE,IAAI;QACrB;IACF;IACA,OAAOF;AACT;AAEA,6DAA6D;AAC7D,2CAA2C;AAC3C,MAAMS,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAMZ,wBAAwB,CACnCG;IAEA,IAAK,MAAMU,UAAUV,QAAS;QAC5B,IAAIS,iBAAiBH,QAAQ,CAACI,SAAS;YACrC,OAAOV,OAAO,CAACU,OAAO;QACxB;IACF;AACF"}