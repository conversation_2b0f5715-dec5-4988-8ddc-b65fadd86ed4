'use client';

import Link from 'next/link';
import { Wifi, RefreshCw, Home, ShoppingBag, Heart } from 'lucide-react';

// Force dynamic rendering - this prevents static generation
export const dynamic = 'force-dynamic';

export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* Offline Icon */}
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
            <Wifi className="w-12 h-12 text-gray-400" />
          </div>
        </div>

        {/* Heading */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          You're Offline
        </h1>

        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          It looks like you've lost your internet connection. Don't worry - you can still browse 
          some content that we've saved for you!
        </p>

        {/* Available Actions */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            What you can do offline:
          </h2>
          
          <div className="space-y-3 text-left">
            <div className="flex items-center text-gray-700">
              <ShoppingBag className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
              <span>View your shopping cart</span>
            </div>
            <div className="flex items-center text-gray-700">
              <Heart className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <span>Browse your wishlist</span>
            </div>
            <div className="flex items-center text-gray-700">
              <Home className="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" />
              <span>View cached pages</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          {/* Retry Connection */}
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            Try Again
          </button>

          {/* Navigation Links */}
          <div className="grid grid-cols-2 gap-4">
            <Link
              href="/cart"
              className="bg-white border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              <ShoppingBag className="w-4 h-4 mr-2" />
              Cart
            </Link>
            
            <Link
              href="/wishlist"
              className="bg-white border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              <Heart className="w-4 h-4 mr-2" />
              Wishlist
            </Link>
          </div>

          {/* Home Link */}
          <Link
            href="/"
            className="block w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors"
          >
            Go to Homepage
          </Link>
        </div>

        {/* Connection Status */}
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Tip:</strong> When you're back online, all your cart items and wishlist will sync automatically.
          </p>
        </div>

        {/* Contact Information */}
        <div className="mt-6 text-sm text-gray-500">
          <p>Need help? Contact us:</p>
          <p className="font-medium">📞 +44 7447 186806</p>
          <p className="font-medium">📧 <EMAIL></p>
        </div>
      </div>
    </div>
  );
}

// Add client-side connection detection
export function OfflineDetector() {
  if (typeof window === 'undefined') return null;

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          // Connection status detection
          function updateOnlineStatus() {
            const status = navigator.onLine ? 'online' : 'offline';
            document.body.setAttribute('data-connection', status);
            
            if (status === 'online') {
              // Show online notification
              if (window.location.pathname === '/offline') {
                window.location.href = '/';
              }
            }
          }

          window.addEventListener('online', updateOnlineStatus);
          window.addEventListener('offline', updateOnlineStatus);
          updateOnlineStatus();
        `
      }}
    />
  );
}
