@echo off
echo Creating Deal4u cPanel deployment package...

REM Create deployment directory on Desktop
set DEPLOY_PATH=C:\Users\<USER>\OneDrive\Desktop\Deal4u_cpanel
if exist "%DEPLOY_PATH%" rmdir /s /q "%DEPLOY_PATH%"
mkdir "%DEPLOY_PATH%"

echo Copying essential files...

REM Copy main files
copy /Y server.js "%DEPLOY_PATH%\"
copy /Y package.json "%DEPLOY_PATH%\"
copy /Y next.config.js "%DEPLOY_PATH%\"
copy /Y tailwind.config.js "%DEPLOY_PATH%\"
copy /Y postcss.config.js "%DEPLOY_PATH%\"
copy /Y jsconfig.json "%DEPLOY_PATH%\"

echo Copying application folders...

REM Copy folders
xcopy /E /I /Y .next "%DEPLOY_PATH%\.next"
xcopy /E /I /Y app "%DEPLOY_PATH%\app"
xcopy /E /I /Y components "%DEPLOY_PATH%\components"
xcopy /E /I /Y context "%DEPLOY_PATH%\context"
xcopy /E /I /Y lib "%DEPLOY_PATH%\lib"
xcopy /E /I /Y public "%DEPLOY_PATH%\public"

echo Creating deployment instructions...

REM Create deployment instructions
echo DEAL4U CPANEL DEPLOYMENT INSTRUCTIONS > "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ======================================= >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 🔄 REAL-TIME WOOCOMMERCE SYNC ENABLED >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo Your app will fetch fresh product data from WooCommerce on every page load! >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 1: CREATE NODE.JS APP IN CPANEL >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ===================================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Login to your cPanel >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Find "Node.js Apps" (or "Node.js Selector") >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Click "Create Application" >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Fill in these settings: >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Node.js Version: 18.x (or latest available) >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Application Root: /public_html/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Application URL: yourdomain.com/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo    - Startup File: server.js >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 5. Click "Create" >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 2: UPLOAD ALL FILES >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ======================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Go to cPanel File Manager >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Navigate to /public_html/deal4u >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Upload ALL files and folders from this Deal4u_cpanel folder >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Make sure ALL folders are uploaded completely >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 3: INSTALL DEPENDENCIES >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ============================ >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Go back to cPanel Node.js Apps >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Click on your Deal4u application >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Go to "Package.json" tab >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 4. Click "Run NPM Install" >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 5. Wait 2-3 minutes for installation >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 4: SET ENVIRONMENT >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ====================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Go to "Environment Variables" tab >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Add: NODE_ENV = production >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 5: START YOUR APP >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ==================== >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Click "Start App" button >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Wait for "Running" status >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. Visit your website URL >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo. >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo STEP 6: TEST DYNAMIC SYNC >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo ========================= >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 1. Add a new product to your WooCommerce store >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 2. Refresh your website >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"
echo 3. New product should appear automatically! >> "%DEPLOY_PATH%\README_DEPLOYMENT.txt"

echo.
echo ✅ SUCCESS! Deal4u cPanel package created!
echo 📁 Location: C:\Users\<USER>\OneDrive\Desktop\Deal4u_cpanel
echo 📖 Read README_DEPLOYMENT.txt for step-by-step instructions
echo.
pause
