{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/websocket.ts"], "names": ["addMessageListener", "connectHMR", "sendMessage", "source", "eventCallbacks", "callback", "push", "data", "readyState", "OPEN", "send", "reconnections", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "location", "reload", "clearTimeout", "setTimeout", "url", "getSocketUrl", "assetPrefix", "WebSocket", "path", "onopen", "onmessage"], "mappings": ";;;;;;;;;;;;;;;;IASgBA,kBAAkB;eAAlBA;;IAWAC,UAAU;eAAVA;;IAPAC,WAAW;eAAXA;;;8BAZa;AAE7B,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEzC,SAASJ,mBAAmBK,QAAwB;IACzDD,eAAeE,IAAI,CAACD;AACtB;AAEO,SAASH,YAAYK,IAAY;IACtC,IAAI,CAACJ,UAAUA,OAAOK,UAAU,KAAKL,OAAOM,IAAI,EAAE;IAClD,OAAON,OAAOO,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AAEb,SAASV,WAAWW,OAA8C;IACvE,SAASC;QACP,IAAIV,QAAQA,OAAOW,KAAK;QAExB,SAASC;YACPJ,gBAAgB;YAChBK,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMb,IAAI;YACnD,KAAK,MAAMiB,iBAAiBpB,eAAgB;gBAC1CoB,cAAcH;YAChB;QACF;QAEA,IAAII;QACJ,SAASC;YACPvB,OAAOwB,OAAO,GAAG;YACjBxB,OAAOyB,OAAO,GAAG;YACjBzB,OAAOW,KAAK;YACZH;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBK,OAAOa,QAAQ,CAACC,MAAM;gBACtB;YACF;YAEAC,aAAaN;YACb,4BAA4B;YAC5BA,QAAQO,WAAWnB,MAAMF,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAMsB,MAAMC,IAAAA,0BAAY,EAACtB,QAAQuB,WAAW;QAE5ChC,SAAS,IAAIa,OAAOoB,SAAS,CAAC,AAAC,KAAEH,MAAMrB,QAAQyB,IAAI;QACnDlC,OAAOmC,MAAM,GAAGvB;QAChBZ,OAAOwB,OAAO,GAAGD;QACjBvB,OAAOyB,OAAO,GAAGF;QACjBvB,OAAOoC,SAAS,GAAGpB;IACrB;IAEAN;AACF"}