{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/ReactDevOverlay.tsx"], "names": ["ReactDevOverlay", "shouldPreventDisplay", "errorType", "preventType", "includes", "children", "preventDisplay", "globalOverlay", "state", "dispatch", "useErrorOverlayReducer", "React", "useEffect", "Bus", "on", "off", "onComponentError", "useCallback", "_error", "_componentStack", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "isMounted", "displayPrevented", "Error<PERSON>ou<PERSON><PERSON>", "onError", "ShadowPort<PERSON>", "CssReset", "Base", "ComponentStyles", "BuildError", "message", "versionInfo", "Errors", "isAppDir", "initialDisplayState", "undefined"], "mappings": ";;;;+BA8BA;;;eAAwBA;;;;;iEA9BD;+DAEF;8BACQ;4BACF;wBACJ;+BACO;sBACT;iCACW;0BACP;wBACc;AAIvC,MAAMC,uBAAuB,CAC3BC,WACAC;IAEA,IAAI,CAACA,eAAe,CAACD,WAAW;QAC9B,OAAO;IACT;IACA,OAAOC,YAAYC,QAAQ,CAACF;AAC9B;AAQe,SAASF,gBAAgB,KAIjB;IAJiB,IAAA,EACtCK,QAAQ,EACRC,cAAc,EACdC,aAAa,EACQ,GAJiB;IAKtC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB;IAEhDC,OAAMC,SAAS,CAAC;QACdC,KAAIC,EAAE,CAACL;QACP,OAAO;YACLI,KAAIE,GAAG,CAACN;QACV;IACF,GAAG;QAACA;KAAS;IAEb,MAAMO,mBAAmBL,OAAMM,WAAW,CACxC,CAACC,QAAeC;IACd,yBAAyB;IAC3B,GACA,EAAE;IAGJ,MAAMC,gBAAgBZ,MAAMa,UAAU,IAAI;IAC1C,MAAMC,mBAAmBC,QAAQf,MAAMgB,MAAM,CAACC,MAAM;IACpD,MAAMvB,YAAYkB,gBACd,UACAE,mBACA,YACA;IACJ,MAAMI,YAAYxB,cAAc;IAEhC,MAAMyB,mBAAmB1B,qBAAqBC,WAAWI;IAEzD,qBACE;;0BACE,qBAACsB,4BAAa;gBACZrB,eAAeA;gBACfmB,WAAWA;gBACXG,SAASb;0BAERX,mBAAAA,WAAY;;YAEdqB,0BACC,sBAACI,0BAAY;;kCACX,qBAACC,kBAAQ;kCACT,qBAACC,UAAI;kCACL,qBAACC,gCAAe;oBAEfN,mBAAmB,OAAOP,8BACzB,qBAACc,sBAAU;wBACTC,SAAS3B,MAAMa,UAAU;wBACzBe,aAAa5B,MAAM4B,WAAW;yBAE9Bd,iCACF,qBAACe,cAAM;wBACLC,UAAU;wBACVd,QAAQhB,MAAMgB,MAAM;wBACpBY,aAAa5B,MAAM4B,WAAW;wBAC9BG,qBAAqB;yBAErBC;;iBAEJA;;;AAGV"}