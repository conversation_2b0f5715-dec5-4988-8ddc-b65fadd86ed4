{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-relative-url.ts"], "names": ["getLocationOrigin", "searchParamsToUrlQuery", "parseRelativeUrl", "url", "base", "globalBase", "URL", "window", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "slice", "length"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SAASC,sBAAsB,QAAQ,gBAAe;AAUtD;;;;;CAKC,GACD,OAAO,SAASC,iBACdC,GAAW,EACXC,IAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,cAAc,aAAaP;IAG/C,MAAMQ,eAAeJ,OACjB,IAAIE,IAAIF,MAAMC,cACdF,IAAIM,UAAU,CAAC,OACf,IAAIH,IAAI,OAAOC,WAAW,cAAc,aAAaA,OAAOG,QAAQ,CAACC,IAAI,IACzEN;IAEJ,MAAM,EAAEO,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIV,IACjEH,KACAK;IAEF,IAAIQ,WAAWX,WAAWW,MAAM,EAAE;QAChC,MAAM,IAAIC,MAAM,AAAC,sDAAmDd;IACtE;IACA,OAAO;QACLS;QACAM,OAAOjB,uBAAuBY;QAC9BC;QACAC;QACAJ,MAAMA,KAAKQ,KAAK,CAACd,WAAWW,MAAM,CAACI,MAAM;IAC3C;AACF"}