// Reviews Management Library for Deal4u

/**
 * Mock reviews database (in production, use a real database)
 */
let reviewsDatabase = [
  {
    id: '1',
    productId: '1',
    userId: 'user1',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    userAvatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=3b82f6&color=fff',
    rating: 5,
    title: 'Excellent quality!',
    comment: 'This product exceeded my expectations. Great build quality and fast shipping. Highly recommended!',
    images: [
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop',
      'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?w=300&h=300&fit=crop'
    ],
    verified: true,
    helpful: 12,
    notHelpful: 1,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    productId: '1',
    userId: 'user2',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    userAvatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=10b981&color=fff',
    rating: 4,
    title: 'Good value for money',
    comment: 'Solid product with good features. Delivery was quick and packaging was secure.',
    images: [],
    verified: true,
    helpful: 8,
    notHelpful: 0,
    createdAt: '2024-01-10T14:20:00Z',
    updatedAt: '2024-01-10T14:20:00Z'
  },
  {
    id: '3',
    productId: '2',
    userId: 'user3',
    userName: 'Emma Wilson',
    userEmail: '<EMAIL>',
    userAvatar: 'https://ui-avatars.com/api/?name=Emma+Wilson&background=f59e0b&color=fff',
    rating: 5,
    title: 'Perfect!',
    comment: 'Exactly what I was looking for. Works perfectly and looks great.',
    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=300&fit=crop'],
    verified: false,
    helpful: 5,
    notHelpful: 0,
    createdAt: '2024-01-08T09:15:00Z',
    updatedAt: '2024-01-08T09:15:00Z'
  }
];

/**
 * Get reviews for a specific product
 */
export const getProductReviews = async (productId, options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'newest', // newest, oldest, highest, lowest, helpful
      filterRating = null
    } = options;

    let reviews = reviewsDatabase.filter(review => review.productId === productId.toString());

    // Filter by rating if specified
    if (filterRating) {
      reviews = reviews.filter(review => review.rating === parseInt(filterRating));
    }

    // Sort reviews
    switch (sortBy) {
      case 'oldest':
        reviews.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        break;
      case 'highest':
        reviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        reviews.sort((a, b) => a.rating - b.rating);
        break;
      case 'helpful':
        reviews.sort((a, b) => b.helpful - a.helpful);
        break;
      case 'newest':
      default:
        reviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReviews = reviews.slice(startIndex, endIndex);

    return {
      reviews: paginatedReviews,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(reviews.length / limit),
        totalReviews: reviews.length,
        hasNextPage: endIndex < reviews.length,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    console.error('Error getting product reviews:', error);
    throw error;
  }
};

/**
 * Get review statistics for a product
 */
export const getProductReviewStats = async (productId) => {
  try {
    const reviews = reviewsDatabase.filter(review => review.productId === productId.toString());
    
    if (reviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
        verifiedPurchases: 0
      };
    }

    const totalReviews = reviews.length;
    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
    const verifiedPurchases = reviews.filter(review => review.verified).length;

    const ratingDistribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    reviews.forEach(review => {
      ratingDistribution[review.rating]++;
    });

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution,
      verifiedPurchases
    };
  } catch (error) {
    console.error('Error getting review stats:', error);
    throw error;
  }
};

/**
 * Submit a new review
 */
export const submitReview = async (reviewData) => {
  try {
    const {
      productId,
      userId,
      userName,
      userEmail,
      rating,
      title,
      comment,
      images = []
    } = reviewData;

    // Validate required fields
    if (!productId || !userId || !rating || !comment) {
      throw new Error('Missing required fields');
    }

    if (rating < 1 || rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }

    // Check if user already reviewed this product
    const existingReview = reviewsDatabase.find(
      review => review.productId === productId.toString() && review.userId === userId
    );

    if (existingReview) {
      throw new Error('You have already reviewed this product');
    }

    // Create new review
    const newReview = {
      id: Date.now().toString(),
      productId: productId.toString(),
      userId,
      userName,
      userEmail,
      userAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=3b82f6&color=fff`,
      rating: parseInt(rating),
      title: title || '',
      comment,
      images: images || [],
      verified: false, // Would be set based on purchase history
      helpful: 0,
      notHelpful: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    reviewsDatabase.push(newReview);

    return newReview;
  } catch (error) {
    console.error('Error submitting review:', error);
    throw error;
  }
};

/**
 * Update review helpfulness
 */
export const updateReviewHelpfulness = async (reviewId, isHelpful) => {
  try {
    const review = reviewsDatabase.find(r => r.id === reviewId);
    
    if (!review) {
      throw new Error('Review not found');
    }

    if (isHelpful) {
      review.helpful++;
    } else {
      review.notHelpful++;
    }

    review.updatedAt = new Date().toISOString();

    return review;
  } catch (error) {
    console.error('Error updating review helpfulness:', error);
    throw error;
  }
};

/**
 * Get user's reviews
 */
export const getUserReviews = async (userId) => {
  try {
    const userReviews = reviewsDatabase.filter(review => review.userId === userId);
    return userReviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  } catch (error) {
    console.error('Error getting user reviews:', error);
    throw error;
  }
};

/**
 * Delete a review (user or admin)
 */
export const deleteReview = async (reviewId, userId, isAdmin = false) => {
  try {
    const reviewIndex = reviewsDatabase.findIndex(r => r.id === reviewId);
    
    if (reviewIndex === -1) {
      throw new Error('Review not found');
    }

    const review = reviewsDatabase[reviewIndex];

    // Check permissions
    if (!isAdmin && review.userId !== userId) {
      throw new Error('Unauthorized to delete this review');
    }

    reviewsDatabase.splice(reviewIndex, 1);
    return true;
  } catch (error) {
    console.error('Error deleting review:', error);
    throw error;
  }
};

/**
 * Get recent reviews (for homepage/dashboard)
 */
export const getRecentReviews = async (limit = 5) => {
  try {
    const recentReviews = reviewsDatabase
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);

    return recentReviews;
  } catch (error) {
    console.error('Error getting recent reviews:', error);
    throw error;
  }
};

/**
 * Search reviews
 */
export const searchReviews = async (query, options = {}) => {
  try {
    const { productId, minRating, maxRating } = options;
    
    let reviews = reviewsDatabase;

    // Filter by product if specified
    if (productId) {
      reviews = reviews.filter(review => review.productId === productId.toString());
    }

    // Filter by rating range
    if (minRating) {
      reviews = reviews.filter(review => review.rating >= minRating);
    }
    if (maxRating) {
      reviews = reviews.filter(review => review.rating <= maxRating);
    }

    // Search in title and comment
    if (query) {
      const searchTerm = query.toLowerCase();
      reviews = reviews.filter(review => 
        review.title.toLowerCase().includes(searchTerm) ||
        review.comment.toLowerCase().includes(searchTerm) ||
        review.userName.toLowerCase().includes(searchTerm)
      );
    }

    return reviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  } catch (error) {
    console.error('Error searching reviews:', error);
    throw error;
  }
};
