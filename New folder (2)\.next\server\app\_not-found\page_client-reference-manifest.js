globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product/ProductGrid.js":{"*":{"id":"(ssr)/./components/product/ProductGrid.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/reviews/RecentReviews.jsx":{"*":{"id":"(ssr)/./components/reviews/RecentReviews.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ClientScripts.js":{"*":{"id":"(ssr)/./components/ClientScripts.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Footer.js":{"*":{"id":"(ssr)/./components/layout/Footer.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Header.js":{"*":{"id":"(ssr)/./components/layout/Header.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/PWAInstallPrompt.jsx":{"*":{"id":"(ssr)/./components/PWAInstallPrompt.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/BackToTopButton.js":{"*":{"id":"(ssr)/./components/ui/BackToTopButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/WhatsAppButton.js":{"*":{"id":"(ssr)/./components/ui/WhatsAppButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/AuthContext.js":{"*":{"id":"(ssr)/./context/AuthContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/CartContext.js":{"*":{"id":"(ssr)/./context/CartContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.js":{"*":{"id":"(ssr)/./app/error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.js":{"*":{"id":"(ssr)/./app/not-found.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product/ProductDetail.js":{"*":{"id":"(ssr)/./components/product/ProductDetail.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product/RelatedProducts.js":{"*":{"id":"(ssr)/./components/product/RelatedProducts.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\FeaturesSection.js":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\HeroSection.js":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\SimpleProductCard.js":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\home\\StatsSection.js":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductGrid.js":{"id":"(app-pages-browser)/./components/product/ProductGrid.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\reviews\\RecentReviews.jsx":{"id":"(app-pages-browser)/./components/reviews/RecentReviews.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ClientScripts.js":{"id":"(app-pages-browser)/./components/ClientScripts.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Footer.js":{"id":"(app-pages-browser)/./components/layout/Footer.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\layout\\Header.js":{"id":"(app-pages-browser)/./components/layout/Header.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\PWAInstallPrompt.jsx":{"id":"(app-pages-browser)/./components/PWAInstallPrompt.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\BackToTopButton.js":{"id":"(app-pages-browser)/./components/ui/BackToTopButton.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\ui\\WhatsAppButton.js":{"id":"(app-pages-browser)/./components/ui/WhatsAppButton.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\context\\AuthContext.js":{"id":"(app-pages-browser)/./context/AuthContext.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\context\\CartContext.js":{"id":"(app-pages-browser)/./context/CartContext.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\error.js":{"id":"(app-pages-browser)/./app/error.js","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\not-found.js":{"id":"(app-pages-browser)/./app/not-found.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\ProductDetail.js":{"id":"(app-pages-browser)/./components/product/ProductDetail.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\components\\product\\RelatedProducts.js":{"id":"(app-pages-browser)/./components/product/RelatedProducts.js","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\page":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\error":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\loading":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\not-found":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Last1\\New folder (2)\\app\\_not-found\\page":[]}}