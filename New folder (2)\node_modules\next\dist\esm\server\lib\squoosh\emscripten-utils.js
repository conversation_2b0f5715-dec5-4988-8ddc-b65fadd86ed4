import { fileURLToPath } from "url";
export function pathify(path) {
    if (path.startsWith("file://")) {
        path = fileURLToPath(path);
    }
    return path;
}
export function instantiateEmscriptenWasm(factory, path, workerJS = "") {
    return factory({
        locateFile (requestPath) {
            // The glue code generated by emscripten uses the original
            // file names of the worker file and the wasm binary.
            // These will have changed in the bundling process and
            // we need to inject them here.
            if (requestPath.endsWith(".wasm")) return pathify(path);
            if (requestPath.endsWith(".worker.js")) return pathify(workerJS);
            return requestPath;
        }
    });
}

//# sourceMappingURL=emscripten-utils.js.map