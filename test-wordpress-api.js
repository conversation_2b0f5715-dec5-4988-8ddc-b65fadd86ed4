const axios = require('axios');

async function testWordPressAPI() {
  console.log('🔍 Testing WordPress API Status...\n');
  
  try {
    // Test 1: WordPress REST API (no auth needed)
    console.log('1. Testing WordPress REST API...');
    const wpResponse = await axios.get('https://deal4u.co/wp-json/wp/v2', { 
      timeout: 10000 
    });
    console.log(`✅ WordPress REST API: ${wpResponse.status} OK`);
    
    // Test 2: WooCommerce API endpoint (no auth)
    console.log('\n2. Testing WooCommerce API endpoint...');
    const wcResponse = await axios.get('https://deal4u.co/wp-json/wc/v3', { 
      timeout: 10000 
    });
    console.log(`✅ WooCommerce API endpoint: ${wcResponse.status} OK`);
    
    // Test 3: Check if API keys are being sent correctly
    console.log('\n3. Testing API key format...');
    const consumerKey = 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193';
    const consumerSecret = 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3';
    
    console.log(`   Consumer Key: ${consumerKey.substring(0, 10)}...${consumerKey.substring(consumerKey.length - 10)}`);
    console.log(`   Consumer Secret: ${consumerSecret.substring(0, 10)}...${consumerSecret.substring(consumerSecret.length - 10)}`);
    
    // Test 4: Try a simple authenticated request
    console.log('\n4. Testing authenticated request...');
    try {
      const authResponse = await axios.get('https://deal4u.co/wp-json/wc/v3/products', {
        auth: {
          username: consumerKey,
          password: consumerSecret
        },
        params: { per_page: 1 },
        timeout: 10000
      });
      console.log(`✅ Authenticated request: ${authResponse.status} OK`);
      console.log(`   Products found: ${authResponse.data.length}`);
    } catch (authError) {
      console.log(`❌ Authenticated request failed: ${authError.response?.status || 'No response'}`);
      console.log(`   Error: ${authError.response?.data?.message || authError.message}`);
      
      // Check if it's a redirect to login
      if (authError.response?.headers?.location?.includes('wp-login.php')) {
        console.log('   🔍 WordPress is redirecting to login page');
        console.log('   This suggests API keys are not being recognized');
      }
    }
    
    console.log('\n📋 DIAGNOSIS:');
    if (wpResponse.status === 200 && wcResponse.status === 200) {
      console.log('✅ WordPress and WooCommerce are running properly');
      console.log('❌ Issue is with API key authentication');
      console.log('\n💡 LIKELY CAUSES:');
      console.log('   1. API keys were deleted/disabled in WordPress admin');
      console.log('   2. User permissions changed for the API key owner');
      console.log('   3. WooCommerce settings changed to disable REST API');
      console.log('   4. WordPress security plugin blocking API access');
      console.log('   5. Server-level security changes');
    }
    
  } catch (error) {
    console.log('❌ Error testing WordPress API:');
    console.log(`   ${error.message}`);
  }
}

testWordPressAPI();
