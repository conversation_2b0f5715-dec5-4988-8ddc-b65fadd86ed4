'use client';

import { useState, useEffect } from 'react';
import { 
  ThumbsUp, 
  ThumbsDown, 
  MoreVertical, 
  Flag, 
  Trash2,
  Calendar,
  CheckCircle,
  Filter,
  SortAsc
} from 'lucide-react';
import StarRating, { StarRatingDisplay, RatingDistribution } from './StarRating';
import { toast } from 'react-hot-toast';

/**
 * ReviewList Component
 * Displays list of reviews with filtering and sorting
 */
export default function ReviewList({ 
  productId, 
  currentUser,
  onWriteReview,
  className = ''
}) {
  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    sortBy: 'newest',
    rating: null,
    page: 1
  });
  const [pagination, setPagination] = useState(null);

  // Fetch reviews
  const fetchReviews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        productId,
        page: filters.page.toString(),
        sortBy: filters.sortBy,
        ...(filters.rating && { rating: filters.rating.toString() })
      });

      const response = await fetch(`/api/reviews?${params}`);
      const data = await response.json();

      if (data.success) {
        setReviews(data.reviews);
        setStats(data.stats);
        setPagination(data.pagination);
      } else {
        toast.error('Failed to load reviews');
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (productId) {
      fetchReviews();
    }
  }, [productId, filters]);

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handleHelpfulClick = async (reviewId, isHelpful) => {
    try {
      const response = await fetch(`/api/reviews/${reviewId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: isHelpful ? 'helpful' : 'not-helpful',
          userId: currentUser?.id
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Update the review in the list
        setReviews(prev => prev.map(review => 
          review.id === reviewId ? result.review : review
        ));
        toast.success(`Review marked as ${isHelpful ? 'helpful' : 'not helpful'}`);
      } else {
        toast.error(result.error || 'Failed to update review');
      }
    } catch (error) {
      console.error('Error updating review helpfulness:', error);
      toast.error('Failed to update review');
    }
  };

  const handleDeleteReview = async (reviewId) => {
    if (!confirm('Are you sure you want to delete this review?')) {
      return;
    }

    try {
      const response = await fetch(`/api/reviews/${reviewId}?userId=${currentUser?.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setReviews(prev => prev.filter(review => review.id !== reviewId));
        toast.success('Review deleted successfully');
        // Refresh stats
        fetchReviews();
      } else {
        toast.error(result.error || 'Failed to delete review');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border p-6 animate-pulse">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-3">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Review Summary */}
      {stats && (
        <div className="bg-white rounded-lg border p-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Overall Rating */}
            <div className="text-center md:text-left">
              <div className="flex items-center justify-center md:justify-start gap-4 mb-4">
                <div className="text-4xl font-bold text-gray-900">
                  {stats.averageRating.toFixed(1)}
                </div>
                <div>
                  <StarRating rating={stats.averageRating} size="lg" />
                  <p className="text-sm text-gray-600 mt-1">
                    Based on {stats.totalReviews} {stats.totalReviews === 1 ? 'review' : 'reviews'}
                  </p>
                </div>
              </div>
              
              {currentUser && (
                <button
                  onClick={onWriteReview}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Write a Review
                </button>
              )}
            </div>

            {/* Rating Distribution */}
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Rating Breakdown</h3>
              <RatingDistribution
                distribution={stats.ratingDistribution}
                totalReviews={stats.totalReviews}
                onFilterByRating={(rating) => handleFilterChange({ rating })}
              />
            </div>
          </div>
        </div>
      )}

      {/* Filters and Sorting */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Sort Options */}
          <div className="flex items-center gap-2">
            <SortAsc className="w-4 h-4 text-gray-500" />
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange({ sortBy: e.target.value })}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="highest">Highest Rating</option>
              <option value="lowest">Lowest Rating</option>
              <option value="helpful">Most Helpful</option>
            </select>
          </div>

          {/* Rating Filter */}
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filters.rating || ''}
              onChange={(e) => handleFilterChange({ 
                rating: e.target.value ? parseInt(e.target.value) : null 
              })}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          {/* Clear Filters */}
          {(filters.rating || filters.sortBy !== 'newest') && (
            <button
              onClick={() => setFilters({ sortBy: 'newest', rating: null, page: 1 })}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.length === 0 ? (
          <div className="bg-white rounded-lg border p-8 text-center">
            <div className="text-gray-400 mb-4">
              <StarRating rating={0} size="xl" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No reviews yet
            </h3>
            <p className="text-gray-600 mb-4">
              Be the first to share your experience with this product.
            </p>
            {currentUser && (
              <button
                onClick={onWriteReview}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Write the First Review
              </button>
            )}
          </div>
        ) : (
          reviews.map((review) => (
            <ReviewItem
              key={review.id}
              review={review}
              currentUser={currentUser}
              onHelpfulClick={handleHelpfulClick}
              onDeleteReview={handleDeleteReview}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4">
          <button
            onClick={() => handleFilterChange({ page: filters.page - 1 })}
            disabled={!pagination.hasPrevPage}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>
          
          <span className="text-sm text-gray-600">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          
          <button
            onClick={() => handleFilterChange({ page: filters.page + 1 })}
            disabled={!pagination.hasNextPage}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}

/**
 * Individual Review Item Component
 */
function ReviewItem({ review, currentUser, onHelpfulClick, onDeleteReview }) {
  const [showMenu, setShowMenu] = useState(false);
  const isOwnReview = currentUser?.id === review.userId;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      <div className="flex items-start gap-4">
        {/* User Avatar */}
        <img
          src={review.userAvatar}
          alt={review.userName}
          className="w-12 h-12 rounded-full"
        />

        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-start justify-between mb-2">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-gray-900">{review.userName}</h4>
                {review.verified && (
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-xs">Verified Purchase</span>
                  </div>
                )}
              </div>
              <StarRating rating={review.rating} size="sm" />
            </div>

            {/* Menu */}
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <MoreVertical className="w-4 h-4" />
              </button>

              {showMenu && (
                <div className="absolute right-0 top-8 bg-white border rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
                  {isOwnReview ? (
                    <button
                      onClick={() => {
                        onDeleteReview(review.id);
                        setShowMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  ) : (
                    <button
                      onClick={() => setShowMenu(false)}
                      className="w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Flag className="w-4 h-4" />
                      Report
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Review Title */}
          {review.title && (
            <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
          )}

          {/* Review Content */}
          <p className="text-gray-700 mb-3 leading-relaxed">{review.comment}</p>

          {/* Review Images */}
          {review.images && review.images.length > 0 && (
            <div className="flex gap-2 mb-3 overflow-x-auto">
              {review.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`Review image ${index + 1}`}
                  className="w-20 h-20 object-cover rounded-lg border flex-shrink-0"
                />
              ))}
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(review.createdAt)}</span>
            </div>

            {/* Helpful Buttons */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => onHelpfulClick(review.id, true)}
                className="flex items-center gap-1 hover:text-green-600 transition-colors"
              >
                <ThumbsUp className="w-4 h-4" />
                <span>Helpful ({review.helpful})</span>
              </button>
              
              <button
                onClick={() => onHelpfulClick(review.id, false)}
                className="flex items-center gap-1 hover:text-red-600 transition-colors"
              >
                <ThumbsDown className="w-4 h-4" />
                <span>({review.notHelpful})</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
