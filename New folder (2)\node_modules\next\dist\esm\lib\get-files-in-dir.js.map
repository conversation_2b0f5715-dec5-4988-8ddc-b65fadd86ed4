{"version": 3, "sources": ["../../src/lib/get-files-in-dir.ts"], "names": ["join", "fs", "getFilesInDir", "path", "dir", "opendir", "results", "file", "resolvedFile", "isSymbolicLink", "stat", "name", "isFile", "push"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAM;AAC3B,OAAOC,QAAQ,cAAa;AAG5B,OAAO,eAAeC,cAAcC,IAAY;IAC9C,MAAMC,MAAM,MAAMH,GAAGI,OAAO,CAACF;IAC7B,MAAMG,UAAU,EAAE;IAElB,WAAW,MAAMC,QAAQH,IAAK;QAC5B,IAAII,eAA2CD;QAE/C,IAAIA,KAAKE,cAAc,IAAI;YACzBD,eAAe,MAAMP,GAAGS,IAAI,CAACV,KAAKG,MAAMI,KAAKI,IAAI;QACnD;QAEA,IAAIH,aAAaI,MAAM,IAAI;YACzBN,QAAQO,IAAI,CAACN,KAAKI,IAAI;QACxB;IACF;IAEA,OAAOL;AACT"}