import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'publish';
    const perPage = searchParams.get('per_page') || '100';
    
    console.log('🔍 Direct products API called with status:', status);
    
    // Use the exact same URL format that worked in the direct test
    const directUrl = `https://deal4u.co/wp-json/wc/v3/products?consumer_key=ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193&consumer_secret=cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3&per_page=${perPage}&status=${status}`;
    
    console.log('📡 Calling URL:', directUrl);
    
    const response = await fetch(directUrl);
    console.log('📊 Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return NextResponse.json({
        success: false,
        error: `API returned ${response.status}: ${errorText}`,
        url: directUrl
      }, { status: response.status });
    }
    
    const products = await response.json();
    console.log('✅ Products received:', products.length);

    // Debug first product to see FULL raw data structure
    if (products.length > 0) {
      console.log('🔍 FULL RAW PRODUCT DATA:', JSON.stringify(products[0], null, 2));

      // Check if ANY products have real images
      const productsWithImages = products.filter(p => p.images && p.images.length > 0);
      console.log('🖼️ Products with images:', productsWithImages.length, 'out of', products.length);
      if (productsWithImages.length > 0) {
        console.log('📸 Sample product with images:', {
          id: productsWithImages[0].id,
          name: productsWithImages[0].name?.substring(0, 30),
          imageCount: productsWithImages[0].images.length,
          firstImage: productsWithImages[0].images[0]
        });
      } else {
        console.log('⚠️ NO PRODUCTS HAVE REAL IMAGES - All using placeholders');
      }
    }
    
    // Transform products to match our expected format with proper image and stock handling
    const transformedProducts = products.map(product => {
      // Handle images properly - extract src from WooCommerce image objects
      let productImages = product.images && Array.isArray(product.images)
        ? product.images.map(img => {
            // If img is already a string URL, use it directly
            if (typeof img === 'string') return img;
            // If img is a WooCommerce image object, extract the src
            if (img && typeof img === 'object' && img.src) return img.src;
            return null;
          }).filter(Boolean)
        : [];

      // If no gallery images, extract from description with SMART filtering
      if (productImages.length === 0 && product.description) {
        productImages = extractSmartImages(product.description);
        console.log(`📸 Product ${product.id}: Found ${productImages.length} smart-filtered images`);
      }

      // Use real images if available, otherwise use a single placeholder
      const finalImages = productImages.length > 0
        ? productImages
        : ['/placeholder.jpg'];

      // Handle stock status properly
      const stockStatus = product.stock_status || 'instock';
      const stockQuantity = parseInt(product.stock_quantity) || 0;
      const isInStock = stockStatus === 'instock' && stockQuantity > 0;

      return {
        id: product.id,
        name: product.name,
        slug: product.slug,
        status: product.status,
        price: product.price || '0',
        regular_price: product.regular_price || product.price || '0',
        sale_price: product.sale_price || '',
        on_sale: product.on_sale || false,
        stock_status: stockStatus,
        stock_quantity: stockQuantity,
        inStock: isInStock,
        images: finalImages,
        categories: product.categories || [],
        description: product.description || '',
        short_description: product.short_description || '',
        rating: 4.5, // Default rating
        rating_count: 25, // Default rating count
        featured: false
      };
    });
    
    // Status breakdown
    const statusCounts = {};
    transformedProducts.forEach(product => {
      const productStatus = product.status || 'unknown';
      statusCounts[productStatus] = (statusCounts[productStatus] || 0) + 1;
    });
    
    console.log('📋 Status breakdown:', statusCounts);
    
    return NextResponse.json({
      success: true,
      products: transformedProducts,
      count: transformedProducts.length,
      statusCounts: statusCounts,
      requestedStatus: status,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Direct products API error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Smart image extraction function
function extractSmartImages(description) {
  const imageRegex = /<img[^>]+src="([^">]+)"[^>]*>/gi;
  const matches = [...description.matchAll(imageRegex)];

  if (matches.length === 0) return [];

  // Strategy 1: Look for images in the FIRST HALF of the description
  // These are more likely to be product photos rather than size charts
  const firstHalf = description.substring(0, Math.floor(description.length / 2));
  const firstHalfMatches = [...firstHalf.matchAll(imageRegex)];

  if (firstHalfMatches.length > 0) {
    const firstHalfImages = firstHalfMatches.slice(0, 3).map(match => {
      let src = match[1];
      if (src.startsWith('//')) {
        src = 'https:' + src;
      }
      return src;
    });

    // Filter out obvious size charts even from first half
    const cleanImages = firstHalfImages.filter(src => {
      const lowerSrc = src.toLowerCase();
      return !['size', 'chart', 'table', 'cm', 'inch', 'waist', 'hip'].some(term =>
        lowerSrc.includes(term)
      );
    });

    if (cleanImages.length > 0) {
      return cleanImages;
    }
  }

  // Strategy 2: Look for images with product-like characteristics
  const productImages = [];
  for (const match of matches) {
    let src = match[1];
    if (src.startsWith('//')) {
      src = 'https:' + src;
    }

    const lowerSrc = src.toLowerCase();
    const fullMatch = match[0].toLowerCase();

    // Skip obvious size charts
    const isSizeChart = ['size', 'chart', 'table', 'cm', 'inch', 'waist', 'hip', 'measurement'].some(term =>
      lowerSrc.includes(term) || fullMatch.includes(term)
    );

    // Look for product-like indicators
    const isProductImage = ['product', 'item', 'main', 'front', 'back', 'side', 'detail'].some(term =>
      lowerSrc.includes(term) || fullMatch.includes(term)
    );

    if (!isSizeChart && (isProductImage || productImages.length < 2)) {
      productImages.push(src);
      if (productImages.length >= 3) break;
    }
  }

  return productImages;
}
