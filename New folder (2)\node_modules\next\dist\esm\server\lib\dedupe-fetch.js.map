{"version": 3, "sources": ["../../../src/server/lib/dedupe-fetch.ts"], "names": ["React", "cloneResponse", "simple<PERSON><PERSON><PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "request", "JSON", "stringify", "method", "Array", "from", "headers", "entries", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "createDedupeFetch", "originalFetch", "getCacheEntries", "cache", "url", "dedupe<PERSON><PERSON>ch", "resource", "options", "signal", "cache<PERSON>ey", "URL", "Request", "keepalive", "cacheEntries", "i", "j", "length", "key", "promise", "then", "response", "Error", "cloned1", "cloned2", "controller", "AbortController", "entry", "push"], "mappings": "AAAA;;CAEC,GACD,YAAYA,WAAW,QAAO;AAC9B,SAASC,aAAa,QAAQ,mBAAkB;AAEhD,MAAMC,iBAAiB,+CAA+C,kDAAkD;;AAExH,SAASC,iBAAiBC,OAAgB;IACxC,qEAAqE;IACrE,uEAAuE;IACvE,2CAA2C;IAC3C,wEAAwE;IACxE,4EAA4E;IAC5E,sDAAsD;IACtD,OAAOC,KAAKC,SAAS,CAAC;QACpBF,QAAQG,MAAM;QACdC,MAAMC,IAAI,CAACL,QAAQM,OAAO,CAACC,OAAO;QAClCP,QAAQQ,IAAI;QACZR,QAAQS,QAAQ;QAChBT,QAAQU,WAAW;QACnBV,QAAQW,QAAQ;QAChBX,QAAQY,cAAc;QACtBZ,QAAQa,SAAS;KAClB;AACH;AAQA,OAAO,SAASC,kBAAkBC,aAA2B;IAC3D,MAAMC,kBAAkBpB,MAAMqB,KAAK,CACjC,qFAAqF;IACrF,CAACC,MAA8B,EAAE;IAGnC,OAAO,SAASC,YACdC,QAA2B,EAC3BC,OAAqB;QAErB,IAAIA,WAAWA,QAAQC,MAAM,EAAE;YAC7B,gDAAgD;YAChD,oEAAoE;YACpE,mDAAmD;YACnD,6DAA6D;YAC7D,6DAA6D;YAC7D,kEAAkE;YAClE,uBAAuB;YACvB,OAAOP,cAAcK,UAAUC;QACjC;QACA,wBAAwB;QACxB,IAAIH;QACJ,IAAIK;QACJ,IAAI,OAAOH,aAAa,YAAY,CAACC,SAAS;YAC5C,aAAa;YACbE,WAAWzB;YACXoB,MAAME;QACR,OAAO;YACL,yBAAyB;YACzB,oEAAoE;YACpE,oDAAoD;YACpD,mFAAmF;YACnF,MAAMpB,UACJ,OAAOoB,aAAa,YAAYA,oBAAoBI,MAChD,IAAIC,QAAQL,UAAUC,WACtBD;YACN,IACE,AAACpB,QAAQG,MAAM,KAAK,SAASH,QAAQG,MAAM,KAAK,UAChDH,QAAQ0B,SAAS,EACjB;gBACA,yEAAyE;gBACzE,0EAA0E;gBAC1E,4BAA4B;gBAC5B,0EAA0E;gBAC1E,OAAOX,cAAcK,UAAUC;YACjC;YACAE,WAAWxB,iBAAiBC;YAC5BkB,MAAMlB,QAAQkB,GAAG;QACnB;QAEA,MAAMS,eAAeX,gBAAgBE;QACrC,IAAK,IAAIU,IAAI,GAAGC,IAAIF,aAAaG,MAAM,EAAEF,IAAIC,GAAGD,KAAK,EAAG;YACtD,MAAM,CAACG,KAAKC,QAAQ,GAAGL,YAAY,CAACC,EAAE;YACtC,IAAIG,QAAQR,UAAU;gBACpB,OAAOS,QAAQC,IAAI,CAAC;oBAClB,MAAMC,WAAWP,YAAY,CAACC,EAAE,CAAC,EAAE;oBACnC,IAAI,CAACM,UAAU,MAAM,IAAIC,MAAM;oBAE/B,qEAAqE;oBACrE,+DAA+D;oBAC/D,2CAA2C;oBAC3C,+CAA+C;oBAC/C,MAAM,CAACC,SAASC,QAAQ,GAAGxC,cAAcqC;oBACzCP,YAAY,CAACC,EAAE,CAAC,EAAE,GAAGS;oBACrB,OAAOD;gBACT;YACF;QACF;QAEA,sEAAsE;QACtE,sEAAsE;QACtE,4EAA4E;QAC5E,SAAS;QACT,MAAME,aAAa,IAAIC;QACvB,MAAMP,UAAUjB,cAAcK,UAAU;YACtC,GAAGC,OAAO;YACVC,QAAQgB,WAAWhB,MAAM;QAC3B;QACA,MAAMkB,QAAoB;YAACjB;YAAUS;YAAS;SAAK;QACnDL,aAAac,IAAI,CAACD;QAElB,OAAOR,QAAQC,IAAI,CAAC,CAACC;YACnB,qEAAqE;YACrE,+DAA+D;YAC/D,2CAA2C;YAC3C,+CAA+C;YAC/C,MAAM,CAACE,SAASC,QAAQ,GAAGxC,cAAcqC;YACzCM,KAAK,CAAC,EAAE,GAAGH;YACX,OAAOD;QACT;IACF;AACF"}