"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/toggle-feature/route";
exports.ids = ["app/api/admin/toggle-feature/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-feature%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-feature%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_admin_toggle_feature_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/toggle-feature/route.js */ \"(rsc)/./app/api/admin/toggle-feature/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/toggle-feature/route\",\n        pathname: \"/api/admin/toggle-feature\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/toggle-feature/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Last1\\\\New folder (2)\\\\app\\\\api\\\\admin\\\\toggle-feature\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_mikes_OneDrive_Desktop_Last1_New_folder_2_app_api_admin_toggle_feature_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/toggle-feature/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-feature%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/toggle-feature/route.js":
/*!***********************************************!*\
  !*** ./app/api/admin/toggle-feature/route.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Feature configuration file path\nconst FEATURES_CONFIG_PATH = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"config\", \"features.json\");\n// Default feature configuration\nconst DEFAULT_FEATURES = {\n    liveChat: {\n        enabled: true,\n        name: \"Live Chat Support\",\n        description: \"Real-time customer support with WhatsApp integration\",\n        category: \"Customer Support\",\n        settings: {\n            whatsappNumber: \"+447447186806\",\n            autoResponse: true,\n            businessHours: \"9:00-18:00\"\n        }\n    },\n    productRecommendations: {\n        enabled: true,\n        name: \"Smart Product Recommendations\",\n        description: 'AI-powered \"You might also like\" suggestions',\n        category: \"AI Features\",\n        settings: {\n            algorithm: \"collaborative_filtering\",\n            maxRecommendations: 6,\n            confidenceThreshold: 0.7\n        }\n    },\n    loyaltyProgram: {\n        enabled: false,\n        name: \"Loyalty & Rewards Program\",\n        description: \"Points, VIP tiers, and exclusive benefits\",\n        category: \"Customer Engagement\",\n        settings: {\n            pointsPerPound: 10,\n            vipThreshold: 500,\n            welcomeBonus: 100\n        }\n    },\n    arTryOn: {\n        enabled: false,\n        name: \"AR Try-On Experience\",\n        description: \"Virtual try-on for clothes and accessories\",\n        category: \"Advanced Features\",\n        settings: {\n            supportedCategories: [\n                \"clothing\",\n                \"accessories\"\n            ],\n            quality: \"high\"\n        }\n    },\n    voiceSearch: {\n        enabled: false,\n        name: \"Voice Search\",\n        description: \"Search products using voice commands\",\n        category: \"Search & Discovery\",\n        settings: {\n            language: \"en-GB\",\n            sensitivity: \"medium\"\n        }\n    },\n    socialCommerce: {\n        enabled: true,\n        name: \"Social Commerce\",\n        description: \"Instagram integration and social sharing\",\n        category: \"Social Features\",\n        settings: {\n            platforms: [\n                \"instagram\",\n                \"facebook\",\n                \"twitter\"\n            ],\n            autoPost: false\n        }\n    },\n    pushNotifications: {\n        enabled: true,\n        name: \"Push Notifications\",\n        description: \"Real-time notifications for deals and updates\",\n        category: \"Engagement\",\n        settings: {\n            orderUpdates: true,\n            promotions: true,\n            backInStock: true\n        }\n    },\n    oneClickReorder: {\n        enabled: true,\n        name: \"One-Click Reorder\",\n        description: \"Quick reorder from purchase history\",\n        category: \"User Experience\",\n        settings: {\n            maxHistoryItems: 20,\n            showInProfile: true\n        }\n    },\n    advancedSearch: {\n        enabled: true,\n        name: \"Advanced Search & Filters\",\n        description: \"Enhanced search with filters and suggestions\",\n        category: \"Search & Discovery\",\n        settings: {\n            autoComplete: true,\n            searchHistory: true,\n            visualSearch: false\n        }\n    },\n    mobilePayments: {\n        enabled: true,\n        name: \"Mobile Payment Options\",\n        description: \"Apple Pay, Google Pay, and other mobile payments\",\n        category: \"Payments\",\n        settings: {\n            applePay: true,\n            googlePay: true,\n            paypal: true\n        }\n    },\n    inventoryAlerts: {\n        enabled: true,\n        name: \"Inventory Alerts\",\n        description: \"Low stock and out of stock notifications\",\n        category: \"Inventory Management\",\n        settings: {\n            lowStockThreshold: 10,\n            emailAlerts: true,\n            customerNotifications: true\n        }\n    },\n    priceDropAlerts: {\n        enabled: false,\n        name: \"Price Drop Alerts\",\n        description: \"Notify customers when prices drop on wishlist items\",\n        category: \"Customer Engagement\",\n        settings: {\n            emailNotifications: true,\n            pushNotifications: true,\n            threshold: 10\n        }\n    }\n};\n// Ensure config directory exists\nfunction ensureConfigDirectory() {\n    const configDir = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(FEATURES_CONFIG_PATH);\n    if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(configDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().mkdirSync(configDir, {\n            recursive: true\n        });\n    }\n}\n// Load features configuration\nfunction loadFeaturesConfig() {\n    try {\n        ensureConfigDirectory();\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(FEATURES_CONFIG_PATH)) {\n            const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(FEATURES_CONFIG_PATH, \"utf8\");\n            const config = JSON.parse(configData);\n            // Merge with defaults to ensure all features exist\n            return {\n                ...DEFAULT_FEATURES,\n                ...config\n            };\n        } else {\n            // Create default config file\n            saveFeaturesConfig(DEFAULT_FEATURES);\n            return DEFAULT_FEATURES;\n        }\n    } catch (error) {\n        console.error(\"Error loading features config:\", error);\n        return DEFAULT_FEATURES;\n    }\n}\n// Save features configuration\nfunction saveFeaturesConfig(config) {\n    try {\n        ensureConfigDirectory();\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(FEATURES_CONFIG_PATH, JSON.stringify(config, null, 2));\n        return true;\n    } catch (error) {\n        console.error(\"Error saving features config:\", error);\n        return false;\n    }\n}\n// GET endpoint - Get all features\nasync function GET() {\n    try {\n        const features = loadFeaturesConfig();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            features,\n            message: \"Features configuration loaded successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error getting features:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message,\n            features: DEFAULT_FEATURES\n        }, {\n            status: 500\n        });\n    }\n}\n// POST endpoint - Toggle feature or update settings\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { feature, enabled, settings } = body;\n        if (!feature) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Feature name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Load current configuration\n        const features = loadFeaturesConfig();\n        // Check if feature exists\n        if (!features[feature]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Feature '${feature}' not found`\n            }, {\n                status: 404\n            });\n        }\n        // Update feature\n        if (enabled !== undefined) {\n            features[feature].enabled = enabled;\n            console.log(`🔧 Feature '${feature}' ${enabled ? \"enabled\" : \"disabled\"}`);\n        }\n        if (settings) {\n            features[feature].settings = {\n                ...features[feature].settings,\n                ...settings\n            };\n            console.log(`⚙️ Feature '${feature}' settings updated`);\n        }\n        // Save configuration\n        const saved = saveFeaturesConfig(features);\n        if (!saved) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Failed to save feature configuration\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            feature: features[feature],\n            message: `Feature '${feature}' updated successfully`\n        });\n    } catch (error) {\n        console.error(\"Error toggling feature:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT endpoint - Update multiple features\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { features: updatedFeatures } = body;\n        if (!updatedFeatures || typeof updatedFeatures !== \"object\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Features object is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Load current configuration\n        const features = loadFeaturesConfig();\n        // Update features\n        let updatedCount = 0;\n        for (const [featureName, featureData] of Object.entries(updatedFeatures)){\n            if (features[featureName]) {\n                features[featureName] = {\n                    ...features[featureName],\n                    ...featureData\n                };\n                updatedCount++;\n            }\n        }\n        // Save configuration\n        const saved = saveFeaturesConfig(features);\n        if (!saved) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Failed to save features configuration\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(`🔧 Updated ${updatedCount} features`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            features,\n            updatedCount,\n            message: `${updatedCount} features updated successfully`\n        });\n    } catch (error) {\n        console.error(\"Error updating features:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE endpoint - Reset to defaults\nasync function DELETE() {\n    try {\n        const saved = saveFeaturesConfig(DEFAULT_FEATURES);\n        if (!saved) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Failed to reset features configuration\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"\\uD83D\\uDD04 Features configuration reset to defaults\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            features: DEFAULT_FEATURES,\n            message: \"Features configuration reset to defaults\"\n        });\n    } catch (error) {\n        console.error(\"Error resetting features:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/toggle-feature/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-feature%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-feature%2Froute.js&appDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmikes%5COneDrive%5CDesktop%5CLast1%5CNew%20folder%20(2)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();