{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFontLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "callback", "async", "path", "relativeFilePathFromRoot", "import", "functionName", "arguments", "data", "variableName", "JSON", "parse", "resourceQuery", "slice", "test", "err", "Error", "bold", "cyan", "name", "isDev", "isServer", "assetPrefix", "fontLoaderPath", "postcss", "getPostcss", "getOptions", "emitFontFile", "content", "ext", "preload", "isUsingSizeAdjust", "opts", "context", "rootContext", "interpolatedName", "loaderUtils", "interpolateName", "outputPath", "emitFile", "fontLoader", "require", "default", "css", "fallbackFonts", "adjustFontFallback", "weight", "style", "variable", "resolve", "src", "promisify", "dirname", "join", "startsWith", "loaderContext", "exports", "fontFamilyHash", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "result", "postcssNextFontPlugin", "process", "undefined", "ast", "type", "version", "processor", "root"], "mappings": ";;;;+BAQA;;;eAA8BA;;;6DANb;4BACU;qEACH;wEACU;sBACR;;;;;;AAEX,eAAeA;IAC5B,MAAMC,qBACJ,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACnC,OAAOF,mBAAmBG,YAAY,CAAC;QACrC,MAAMC,WAAW,IAAI,CAACC,KAAK;QAE3B;;;;;;;;;KASC,GACD,MAAM,EACJC,MAAMC,wBAAwB,EAC9BC,QAAQC,YAAY,EACpBC,WAAWC,IAAI,EACfC,YAAY,EACb,GAAGC,KAAKC,KAAK,CAAC,IAAI,CAACC,aAAa,CAACC,KAAK,CAAC;QAExC,oDAAoD;QACpD,IAAI,wBAAwBC,IAAI,CAACV,2BAA2B;YAC1D,MAAMW,MAAM,IAAIC,MACd,CAAC,EAAEC,IAAAA,gBAAI,EAAC,UAAU,gBAAgB,EAAEC,IAAAA,gBAAI,EAAC,sBAAsB,CAAC,CAAC;YAEnEH,IAAII,IAAI,GAAG;YACXlB,SAASc;YACT;QACF;QAEA,MAAM,EACJK,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,cAAc,EACdC,SAASC,UAAU,EACpB,GAAG,IAAI,CAACC,UAAU;QAEnB,IAAIJ,eAAe,CAAC,kBAAkBR,IAAI,CAACQ,cAAc;YACvD,MAAMP,MAAM,IAAIC,MACd;YAEFD,IAAII,IAAI,GAAG;YACXlB,SAASc;YACT;QACF;QAEA;;;;;;;;;KASC,GACD,MAAMY,eAAe,CACnBC,SACAC,KACAC,SACAC;YAEA,MAAMC,OAAO;gBAAEC,SAAS,IAAI,CAACC,WAAW;gBAAEN;YAAQ;YAClD,MAAMO,mBAAmBC,qBAAW,CAACC,eAAe,CAClD,IAAI,EACJ,CAAC,mBAAmB,EAAEN,oBAAoB,OAAO,GAAG,EAClDD,UAAU,OAAO,GAClB,CAAC,EAAED,IAAI,CAAC,EACTG;YAEF,MAAMM,aAAa,CAAC,EAAEhB,YAAY,OAAO,EAAEa,iBAAiB,CAAC;YAC7D,sCAAsC;YACtC,IAAI,CAACd,UAAU;gBACb,IAAI,CAACkB,QAAQ,CAACJ,kBAAkBP,SAAS;YAC3C;YACA,6DAA6D;YAC7D,OAAOU;QACT;QAEA,IAAI;YACF,kFAAkF;YAClF,8FAA8F;YAC9F,MAAME,aAAyBC,QAAQlB,gBAAgBmB,OAAO;YAC9D,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GACrE,MAAMnD,mBAAmBE,UAAU,CAAC,eAAeC,YAAY,CAAC,IAC9DwC,WAAW;oBACTlC;oBACAG;oBACAD;oBACAmB;oBACAsB,SAAS,CAACC,MACRC,IAAAA,eAAS,EAAC,IAAI,CAACF,OAAO,EACpB9C,aAAI,CAACiD,OAAO,CACVjD,aAAI,CAACkD,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE9B,4BAE9B8C,IAAII,UAAU,CAAC,OAAOJ,MAAM,CAAC,EAAE,EAAEA,IAAI,CAAC;oBAE1C9B;oBACAC;oBACAkC,eAAe,IAAI;gBACrB;YAGJ,MAAM,EAAE/B,OAAO,EAAE,GAAG,MAAMC;YAE1B,gFAAgF;YAChF,MAAM+B,WAAuC,EAAE;YAE/C,sFAAsF;YACtF,MAAMC,iBAAiBrB,qBAAW,CAACsB,aAAa,CAC9CC,OAAOC,IAAI,CAACjB,MACZ,QACA,OACA;YAGF,6FAA6F;YAC7F,MAAMkB,SAAS,MAAMhE,mBAClBE,UAAU,CAAC,WACXC,YAAY,CAAC,IACZwB,QACEsC,IAAAA,wBAAqB,EAAC;oBACpBN,SAAAA;oBACAC;oBACAb;oBACAE;oBACAC;oBACAF;oBACAG;gBACF,IACAe,OAAO,CAACpB,KAAK;oBACbiB,MAAMI;gBACR;YAGJ,MAAMC,MAAM;gBACVC,MAAM;gBACNC,SAASN,OAAOO,SAAS,CAACD,OAAO;gBACjCE,MAAMR,OAAOQ,IAAI;YACnB;YAEA,uHAAuH;YACvHpE,SAAS,MAAM4D,OAAOlB,GAAG,EAAE,MAAM;gBAC/Ba,SAAAA;gBACAS;gBACAR;YACF;QACF,EAAE,OAAO1C,KAAU;YACjBd,SAASc;QACX;IACF;AACF"}