/*
 React
 react-server-dom-webpack-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),l=null,n=0;function p(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=new Uint8Array(2048),n=0),a.enqueue(b);else{var c=l.length-n;c<b.byteLength&&(0===c?a.enqueue(l):(l.set(b.subarray(0,c),n),a.enqueue(l),b=b.subarray(c)),l=new Uint8Array(2048),n=0);l.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,c){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:c}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var d=u({},a.$$id,!0),e=new Proxy(d,ka);a.status="fulfilled";a.value=e;return a.then=u(function(g){return Promise.resolve(g(e))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");d=a[b];d||(d=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(d,"name",{value:b}),d=a[b]=new Proxy(d,ia));return d}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},sa={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=w?w:null;if(b){var c=b.hints,d="D|"+a;c.has(d)||(c.add(d),x(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var c=w?w:null;if(c){var d=c.hints,e="C|"+(null==b?"null":b)+"|"+a;d.has(e)||(d.add(e),"string"===typeof b?x(c,"C",[a,b]):x(c,"C",a))}}}
function na(a,b,c){if("string"===typeof a){var d=w?w:null;if(d){var e=d.hints,g="L";if("image"===b&&c){var f=c.imageSrcSet,h=c.imageSizes,k="";"string"===typeof f&&""!==f?(k+="["+f+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;g+="[image]"+k}else g+="["+b+"]"+a;e.has(g)||(e.add(g),(c=z(c))?x(d,"L",[a,b,c]):x(d,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var c=w?w:null;if(c){var d=c.hints,e="m|"+a;if(!d.has(e))return d.add(e),(b=z(b))?x(c,"m",[a,b]):x(c,"m",a)}}}
function pa(a,b,c){if("string"===typeof a){var d=w?w:null;if(d){var e=d.hints,g="S|"+a;if(!e.has(g))return e.add(g),(c=z(c))?x(d,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?x(d,"S",[a,b]):x(d,"S",a)}}}function qa(a,b){if("string"===typeof a){var c=w?w:null;if(c){var d=c.hints,e="X|"+a;if(!d.has(e))return d.add(e),(b=z(b))?x(c,"X",[a,b]):x(c,"X",a)}}}
function ra(a,b){if("string"===typeof a){var c=w?w:null;if(c){var d=c.hints,e="M|"+a;if(!d.has(e))return d.add(e),(b=z(b))?x(c,"M",[a,b]):x(c,"M",a)}}}function z(a){if(null==a)return null;var b=!1,c={},d;for(d in a)null!=a[d]&&(b=!0,c[d]=a[d]);return b?c:null}
var ta=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A=Symbol.for("react.element"),ua=Symbol.for("react.fragment"),va=Symbol.for("react.context"),wa=Symbol.for("react.forward_ref"),xa=Symbol.for("react.suspense"),ya=Symbol.for("react.suspense_list"),za=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),Aa=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");var Ba=Symbol.iterator,Ca=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Da(){}function Ea(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Da,Da),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}D=b;throw Ca;}}var D=null;
function Fa(){if(null===D)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=D;D=null;return a}var E=null,Ga=0,F=null;function Ha(){var a=F||[];F=null;return a}
var Ma={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:G,useTransition:G,readContext:Ia,useContext:Ia,useReducer:G,useRef:G,useState:G,useInsertionEffect:G,useLayoutEffect:G,useImperativeHandle:G,useEffect:G,useId:Ja,useSyncExternalStore:G,useCacheRefresh:function(){return Ka},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Aa;return b},use:La};
function G(){throw Error("This Hook is not supported in Server Components.");}function Ka(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ia(){throw Error("Cannot read a Client Context from a Server Component.");}function Ja(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function La(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ga;Ga+=1;null===F&&(F=[]);return Ea(F,a,b)}a.$$typeof===va&&Ia()}if(a.$$typeof===r){if(null!=a.value&&a.value.$$typeof===va)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Na(){return(new AbortController).signal}
function Oa(){var a=w?w:null;return a?a.cache:new Map}var Pa={getCacheSignal:function(){var a=Oa(),b=a.get(Na);void 0===b&&(b=Na(),a.set(Na,b));return b},getCacheForType:function(a){var b=Oa(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Qa=Array.isArray,Ra=Object.getPrototypeOf;function Sa(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Ta(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Qa(a))return"[...]";if(null!==a&&a.$$typeof===Ua)return"client";a=Sa(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Ua?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function H(a){if("string"===typeof a)return a;switch(a){case xa:return"Suspense";case ya:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case wa:return H(a.render);case za:return H(a.type);case C:var b=a._payload;a=a._init;try{return H(a(b))}catch(c){}}return""}var Ua=Symbol.for("react.client.reference");
function I(a,b){var c=Sa(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var d=0;if(Qa(a)){var e="[";for(var g=0;g<a.length;g++){0<g&&(e+=", ");var f=a[g];f="object"===typeof f&&null!==f?I(f):Ta(f);""+g===b?(c=e.length,d=f.length,e+=f):e=10>f.length&&40>e.length+f.length?e+f:e+"..."}e+="]"}else if(a.$$typeof===A)e="<"+H(a.type)+"/>";else{if(a.$$typeof===Ua)return"client";e="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(e+=", ");var h=g[f],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k=
"object"===typeof k&&null!==k?I(k):Ta(k);h===b?(c=e.length,d=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<c&&0<d?(a=" ".repeat(c)+"^".repeat(d),"\n  "+e+"\n  "+a):"\n  "+e}var Va=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Wa=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!Wa)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var Xa=Object.prototype,J=JSON.stringify,Ya=Wa.ReactCurrentCache,Za=Va.ReactCurrentDispatcher;function $a(a){console.error(a)}function ab(){}
function bb(a,b,c,d,e){if(null!==Ya.current&&Ya.current!==Pa)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=sa;Ya.current=Pa;var g=new Set,f=[],h=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:h,abortableTasks:g,pingedTasks:f,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:d||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===c?$a:c,onPostpone:void 0===e?ab:e};a=K(b,a,null,!1,g);f.push(a);return b}var w=null;
function cb(a,b,c){var d=K(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return d.model=c.value,db(a,d),d.id;case "rejected":return b=L(a,c.reason),M(a,d.id,b),d.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(e){"pending"===c.status&&(c.status="fulfilled",c.value=e)},function(e){"pending"===c.status&&(c.status="rejected",c.reason=e)}))}c.then(function(e){d.model=e;db(a,d)},function(e){d.status=4;e=L(a,e);M(a,d.id,e);a.abortableTasks.delete(d);
null!==a.destination&&N(a,a.destination)});return d.id}function x(a,b,c){c=J(c);var d=a.nextChunkId++;b="H"+b;b=d.toString(16)+":"+b;c=q.encode(b+c+"\n");a.completedHintChunks.push(c);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(c=a.destination,a.flushScheduled=!0,N(a,c))}function eb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function fb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:C,_payload:a,_init:eb}}
function gb(a,b,c,d,e){var g=b.thenableState;b.thenableState=null;Ga=0;F=g;d=d(e,void 0);if("object"===typeof d&&null!==d&&"function"===typeof d.then){e=d;if("fulfilled"===e.status)return e.value;d=fb(d)}e=b.keyPath;g=b.implicitSlot;null!==c?b.keyPath=null===e?c:e+","+c:null===e&&(b.implicitSlot=!0);a=O(a,b,P,"",d);b.keyPath=e;b.implicitSlot=g;return a}
function hb(a,b,c,d,e,g){if(null!==e&&void 0!==e)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===r?[A,c,d,g]:gb(a,b,d,c,g);if("string"===typeof c)return[A,c,d,g];if("symbol"===typeof c)return c===ua&&null===d?(d=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=O(a,b,P,"",g.children),b.implicitSlot=d,a):[A,c,d,g];if(null!=c&&"object"===typeof c){if(c.$$typeof===r)return[A,c,d,g];switch(c.$$typeof){case C:var f=
c._init;c=f(c._payload);return hb(a,b,c,d,e,g);case wa:return gb(a,b,d,c.render,g);case za:return hb(a,b,c.type,d,e,g)}}throw Error("Unsupported Server Component type: "+Ta(c));}function db(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,ib(a))}
function K(a,b,c,d,e){a.pendingChunks++;var g=a.nextChunkId++;"object"===typeof b&&null!==b&&a.writtenObjects.set(b,g);var f={id:g,status:0,model:b,keyPath:c,implicitSlot:d,ping:function(){return db(a,f)},toJSON:function(h,k){var m=f.keyPath,y=f.implicitSlot;try{var v=O(a,f,this,h,k)}catch(Y){if(h=Y===Ca?Fa():Y,k=f.model,k="object"===typeof k&&null!==k&&(k.$$typeof===A||k.$$typeof===C),"object"===typeof h&&null!==h&&"function"===typeof h.then){v=K(a,f.model,f.keyPath,f.implicitSlot,a.abortableTasks);
var B=v.ping;h.then(B,B);v.thenableState=Ha();f.keyPath=m;f.implicitSlot=y;v=k?"$L"+v.id.toString(16):Q(v.id)}else if(f.keyPath=m,f.implicitSlot=y,k)a.pendingChunks++,m=a.nextChunkId++,y=L(a,h),M(a,m,y),v="$L"+m.toString(16);else throw h;}return v},thenableState:null};e.add(f);return f}function Q(a){return"$"+a.toString(16)}function jb(a,b,c){a=J(c);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function kb(a,b,c,d){var e=d.$$async?d.$$id+"#async":d.$$id,g=a.writtenClientReferences,f=g.get(e);if(void 0!==f)return b[0]===A&&"1"===c?"$L"+f.toString(16):Q(f);try{var h=a.bundlerConfig,k=d.$$id;f="";var m=h[k];if(m)f=m.name;else{var y=k.lastIndexOf("#");-1!==y&&(f=k.slice(y+1),m=h[k.slice(0,y)]);if(!m)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var v=!0===d.$$async?[m.id,m.chunks,f,1]:[m.id,m.chunks,
f];a.pendingChunks++;var B=a.nextChunkId++,Y=J(v),Cb=B.toString(16)+":I"+Y+"\n",Db=q.encode(Cb);a.completedImportChunks.push(Db);g.set(e,B);return b[0]===A&&"1"===c?"$L"+B.toString(16):Q(B)}catch(Eb){return a.pendingChunks++,b=a.nextChunkId++,c=L(a,Eb),M(a,b,c),Q(b)}}function R(a,b){b=K(a,b,null,!1,a.abortableTasks);lb(a,b);return b.id}var S=!1;
function O(a,b,c,d,e){b.model=e;if(e===A)return"$";if(null===e)return null;if("object"===typeof e){switch(e.$$typeof){case A:c=a.writtenObjects;d=c.get(e);if(void 0!==d)if(S===e)S=null;else return-1===d?(a=R(a,e),Q(a)):Q(d);else c.set(e,-1);return hb(a,b,e.type,e.key,e.ref,e.props);case C:return b.thenableState=null,c=e._init,e=c(e._payload),O(a,b,P,"",e)}if(e.$$typeof===r)return kb(a,c,d,e);c=a.writtenObjects;d=c.get(e);if("function"===typeof e.then){if(void 0!==d)if(S===e)S=null;else return"$@"+
d.toString(16);a=cb(a,b,e);c.set(e,a);return"$@"+a.toString(16)}if(void 0!==d)if(S===e)S=null;else return-1===d?(a=R(a,e),Q(a)):Q(d);else c.set(e,-1);if(Qa(e))return e;if(e instanceof Map){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b][0],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));return"$Q"+R(a,e).toString(16)}if(e instanceof Set){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));
return"$W"+R(a,e).toString(16)}null===e||"object"!==typeof e?a=null:(a=Ba&&e[Ba]||e["@@iterator"],a="function"===typeof a?a:null);if(a)return a=Array.from(e),a;a=Ra(e);if(a!==Xa&&(null===a||null!==Ra(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return e}if("string"===typeof e){if("Z"===e[e.length-1]&&c[d]instanceof Date)return"$D"+e;if(1024<=e.length)return a.pendingChunks+=2,b=
a.nextChunkId++,e=q.encode(e),c=e.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=q.encode(c),a.completedRegularChunks.push(c,e),Q(b);a="$"===e[0]?"$"+e:e;return a}if("boolean"===typeof e)return e;if("number"===typeof e)return Number.isFinite(e)?0===e&&-Infinity===1/e?"$-0":e:Infinity===e?"$Infinity":-Infinity===e?"$-Infinity":"$NaN";if("undefined"===typeof e)return"$undefined";if("function"===typeof e){if(e.$$typeof===r)return kb(a,c,d,e);if(e.$$typeof===t)return b=a.writtenServerReferences,
c=b.get(e),void 0!==c?a="$F"+c.toString(16):(c=e.$$bound,c={id:e.$$id,bound:c?Promise.resolve(c):null},a=R(a,c),b.set(e,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+I(c,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+
I(c,d));}if("symbol"===typeof e){b=a.writtenSymbols;var g=b.get(e);if(void 0!==g)return Q(g);g=e.description;if(Symbol.for(g)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(e.description+") cannot be found among global symbols.")+I(c,d));a.pendingChunks++;c=a.nextChunkId++;d=jb(a,c,"$S"+g);a.completedImportChunks.push(d);b.set(e,c);return Q(c)}if("bigint"===typeof e)return"$n"+e.toString(10);throw Error("Type "+typeof e+
" is not supported in Client Component props."+I(c,d));}function L(a,b){var c=w;w=null;try{var d=a.onError;var e=d(b)}finally{w=c}if(null!=e&&"string"!==typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e||""}
function mb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function M(a,b,c){c={digest:c};b=b.toString(16)+":E"+J(c)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}var P={};
function lb(a,b){if(0===b.status)try{S=b.model;var c=O(a,b,P,"",b.model);S=c;b.keyPath=null;b.implicitSlot=!1;var d="object"===typeof c&&null!==c?J(c,b.toJSON):J(c),e=b.id.toString(16)+":"+d+"\n",g=q.encode(e);a.completedRegularChunks.push(g);a.abortableTasks.delete(b);b.status=1}catch(m){var f=m===Ca?Fa():m;if("object"===typeof f&&null!==f&&"function"===typeof f.then){var h=b.ping;f.then(h,h);b.thenableState=Ha()}else{a.abortableTasks.delete(b);b.status=4;var k=L(a,f);M(a,b.id,k)}}finally{}}
function ib(a){var b=Za.current;Za.current=Ma;var c=w;E=w=a;try{var d=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<d.length;e++)lb(a,d[e]);null!==a.destination&&N(a,a.destination)}catch(g){L(a,g),mb(a,g)}finally{Za.current=b,E=null,w=c}}
function N(a,b){l=new Uint8Array(2048);n=0;try{for(var c=a.completedImportChunks,d=0;d<c.length;d++)a.pendingChunks--,p(b,c[d]);c.splice(0,d);var e=a.completedHintChunks;for(d=0;d<e.length;d++)p(b,e[d]);e.splice(0,d);var g=a.completedRegularChunks;for(d=0;d<g.length;d++)a.pendingChunks--,p(b,g[d]);g.splice(0,d);var f=a.completedErrorChunks;for(d=0;d<f.length;d++)a.pendingChunks--,p(b,f[d]);f.splice(0,d)}finally{a.flushScheduled=!1,l&&0<n&&(b.enqueue(new Uint8Array(l.buffer,0,n)),l=null,n=0)}0===a.pendingChunks&&
b.close()}function nb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var d=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,g=L(a,e);M(a,d,g,e);c.forEach(function(f){f.status=3;var h=Q(d);f=jb(a,f.id,h);a.completedErrorChunks.push(f)});c.clear()}null!==a.destination&&N(a,a.destination)}catch(f){L(a,f),mb(a,f)}}
function ob(a,b){var c="",d=a[b];if(d)c=d.name;else{var e=b.lastIndexOf("#");-1!==e&&(c=b.slice(e+1),d=a[b.slice(0,e)]);if(!d)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[d.id,d.chunks,c]}var T=new Map;
function pb(a){var b=__webpack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function qb(){}
function rb(a){for(var b=a[1],c=[],d=0;d<b.length;){var e=b[d++],g=b[d++],f=T.get(e);void 0===f?(sb.set(e,g),g=__webpack_chunk_load__(e),c.push(g),f=T.set.bind(T,e,null),g.then(f,qb),T.set(e,g)):null!==f&&c.push(f)}return 4===a.length?0===c.length?pb(a[0]):Promise.all(c).then(function(){return pb(a[0])}):0<c.length?Promise.all(c):null}
function U(a){var b=__webpack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}var sb=new Map,tb=__webpack_require__.u;__webpack_require__.u=function(a){var b=sb.get(a);return void 0!==b?b:tb(a)};function V(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}V.prototype=Object.create(Promise.prototype);
V.prototype.then=function(a,b){switch(this.status){case "resolved_model":ub(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function vb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function wb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&vb(c,b)}}function xb(a,b,c,d,e,g){var f=ob(a._bundlerConfig,b);a=rb(f);if(c)c=Promise.all([c,a]).then(function(h){h=h[0];var k=U(f);return k.bind.apply(k,[null].concat(h))});else if(a)c=Promise.resolve(a).then(function(){return U(f)});else return U(f);c.then(yb(d,e,g),zb(d));return null}var W=null,X=null;
function ub(a){var b=W,c=X;W=a;X=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==X&&0<X.deps?(X.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{W=b,X=c}}function Ab(a,b){a._closed=!0;a._closedReason=b;a._chunks.forEach(function(c){"pending"===c.status&&wb(c,b)})}
function Z(a,b){var c=a._chunks,d=c.get(b);d||(d=a._formData.get(a._prefix+b),d=null!=d?new V("resolved_model",d,null,a):a._closed?new V("rejected",null,a._closedReason,a):new V("pending",null,null,a),c.set(b,d));return d}function yb(a,b,c){if(X){var d=X;d.deps++}else d=X={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&vb(e,d.value))}}function zb(a){return function(b){return wb(a,b)}}
function Bb(a,b){a=Z(a,b);"resolved_model"===a.status&&ub(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Fb(a,b,c,d){if("$"===d[0])switch(d[1]){case "$":return d.slice(1);case "@":return b=parseInt(d.slice(2),16),Z(a,b);case "S":return Symbol.for(d.slice(2));case "F":return d=parseInt(d.slice(2),16),d=Bb(a,d),xb(a,d.id,d.bound,W,b,c);case "Q":return b=parseInt(d.slice(2),16),a=Bb(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=Bb(a,b),new Set(a);case "K":b=d.slice(2);var e=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,h){h.startsWith(e)&&g.append(h.slice(e.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=Z(a,d);switch(a.status){case "resolved_model":ub(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=W,a.then(yb(d,b,c),zb(d)),null;default:throw a.reason;}}return d}
function Gb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,d=new Map,e={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:d,_fromJSON:function(g,f){return"string"===typeof f?Fb(e,this,g,f):f},_closed:!1,_closedReason:null};return e}function Hb(a){Ab(a,Error("Connection closed."))}
function Ib(a,b,c){var d=ob(a,b);a=rb(d);return c?Promise.all([c,a]).then(function(e){e=e[0];var g=U(d);return g.bind.apply(g,[null].concat(e))}):a?Promise.resolve(a).then(function(){return U(d)}):Promise.resolve(U(d))}function Jb(a,b,c){a=Gb(b,c,a);Hb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var c=new FormData,d=null;a.forEach(function(e,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(e="$ACTION_"+g.slice(12)+":",e=Jb(a,b,e),d=Ib(b,e.id,e.bound)):g.startsWith("$ACTION_ID_")&&(e=g.slice(11),d=Ib(b,e,null)):c.append(g,e)});return null===d?null:d.then(function(e){return e.bind(null,c)})};
exports.decodeFormState=function(a,b,c){var d=b.get("$ACTION_KEY");if("string"!==typeof d)return Promise.resolve(null);var e=null;b.forEach(function(f,h){h.startsWith("$ACTION_REF_")&&(f="$ACTION_"+h.slice(12)+":",e=Jb(b,c,f))});if(null===e)return Promise.resolve(null);var g=e.id;return Promise.resolve(e.bound).then(function(f){return null===f?null:[a,d,g,f.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Gb(b,"",a);b=Z(a,0);Hb(a);return b};
exports.registerClientReference=function(a,b,c){return u(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:fa,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var d=bb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var e=c.signal;if(e.aborted)nb(d,e.reason);else{var g=function(){nb(d,e.reason);e.removeEventListener("abort",g)};e.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){d.flushScheduled=null!==d.destination;ib(d)},pull:function(f){if(1===d.status)d.status=2,ca(f,d.fatalError);else if(2!==d.status&&null===d.destination){d.destination=
f;try{N(d,f)}catch(h){L(d,h),mb(d,h)}}},cancel:function(f){d.destination=null;nb(d,f)}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-webpack-server.browser.production.min.js.map
