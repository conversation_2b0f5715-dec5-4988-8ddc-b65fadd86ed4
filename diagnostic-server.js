// Simple diagnostic server for cPanel troubleshooting
const http = require('http');
const path = require('path');
const fs = require('fs');

console.log('🔍 Starting diagnostic server...');
console.log('📁 Current directory:', __dirname);
console.log('🌍 Environment:', process.env.NODE_ENV || 'development');
console.log('🔌 Port:', process.env.PORT || 3000);

// Check critical files
const criticalFiles = [
    'package.json',
    'next.config.js',
    '.next',
    'app',
    'components',
    'lib'
];

console.log('\n📋 Checking critical files:');
criticalFiles.forEach(file => {
    const exists = fs.existsSync(path.join(__dirname, file));
    console.log(`${exists ? '✅' : '❌'} ${file}: ${exists ? 'Found' : 'Missing'}`);
});

// Check if Next.js is available
try {
    const next = require('next');
    console.log('✅ Next.js module: Available');
} catch (error) {
    console.log('❌ Next.js module: Missing or broken');
    console.log('Error:', error.message);
}

// Simple HTTP server for testing
const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
        <html>
            <head><title>Deal4u Diagnostic</title></head>
            <body>
                <h1>🎉 Deal4u Server is Running!</h1>
                <p><strong>Time:</strong> ${new Date().toISOString()}</p>
                <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
                <p><strong>Port:</strong> ${process.env.PORT || 3000}</p>
                <p><strong>Directory:</strong> ${__dirname}</p>
                <h2>Next Steps:</h2>
                <ol>
                    <li>If you see this page, basic Node.js is working</li>
                    <li>Check cPanel logs for Next.js specific errors</li>
                    <li>Verify all files were uploaded correctly</li>
                    <li>Switch back to server.js when ready</li>
                </ol>
            </body>
        </html>
    `);
});

const port = process.env.PORT || 3000;
const host = process.env.HOST || '0.0.0.0';

server.listen(port, host, () => {
    console.log(`✅ Diagnostic server running at http://${host}:${port}`);
    console.log('🔗 Visit your domain to see if this works');
    console.log('📝 Check cPanel logs for any errors');
});

server.on('error', (err) => {
    console.error('❌ Server error:', err);
    if (err.code === 'EADDRINUSE') {
        console.error(`Port ${port} is already in use`);
    }
});

// Log any unhandled errors
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
    console.error('❌ Unhandled Rejection:', err);
});
