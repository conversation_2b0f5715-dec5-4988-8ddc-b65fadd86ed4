import type { PackageJson } from './types.js';
export type * from './types.js';
export declare function isNpm(): boolean | undefined;
export declare function isPnp(): boolean;
export declare function checkAndPreparePackage(packageName: string, version?: string, checkVersion?: boolean): Promise<void>;
export declare function checkAndPreparePackage(packageJson: PackageJson, checkVersion?: boolean): Promise<void>;
