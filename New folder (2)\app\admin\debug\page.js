'use client';

import { useState } from 'react';

export default function DebugPage() {
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);

  const quickTest = async () => {
    setLoading(true);
    setResults(null);

    try {
      console.log('🔍 Quick API test...');

      // Just test the most important things quickly
      const tests = await Promise.all([
        // Test published products (what shows on website)
        fetch('/api/woocommerce/products?status=publish&per_page=5').then(r => r.json()),
        // Test any status (total products)
        fetch('/api/woocommerce/products?status=any&per_page=5').then(r => r.json()),
        // Test connection
        fetch('/api/woocommerce/test').then(r => r.json())
      ]);

      setResults({
        published: tests[0],
        anyStatus: tests[1],
        connection: tests[2],
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Quick test failed:', error);
      setResults({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🔍 API Debug Tool</h1>
          <p className="text-gray-600 mb-8">
            This tool will help us figure out why you see only 1 product instead of 159, 
            and why bulk import shows 0 processed when you have 101 unpublished products.
          </p>
          
          <button
            onClick={testAPI}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-8"
          >
            {loading ? '🔄 Testing...' : '🔍 Run Full API Test'}
          </button>
          
          {results && (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">📊 Test Results</h2>
                
                {/* Connection Test */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">🔗 Connection Test</h3>
                  <div className="bg-white rounded p-4 border">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(results.connectionTest, null, 2)}
                    </pre>
                  </div>
                </div>
                
                {/* Status Tests */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">📋 Product Status Tests</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(results.statusTests || {}).map(([status, result]) => (
                      <div key={status} className="bg-white rounded p-4 border">
                        <h4 className="font-medium text-gray-900 mb-2">
                          Status: <span className="text-blue-600">{status}</span>
                        </h4>
                        <div className="text-sm space-y-1">
                          <div>Success: <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                            {result.success ? 'Yes' : 'No'}
                          </span></div>
                          <div>Count: <span className="font-medium">{result.count || 0}</span></div>
                          {result.statusCounts && Object.keys(result.statusCounts).length > 0 && (
                            <div>
                              <div className="font-medium">Status Breakdown:</div>
                              {Object.entries(result.statusCounts).map(([s, c]) => (
                                <div key={s} className="ml-2">
                                  {s}: {c}
                                </div>
                              ))}
                            </div>
                          )}
                          {result.error && (
                            <div className="text-red-600">Error: {result.error}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Summary */}
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <h3 className="text-lg font-medium text-blue-900 mb-2">📝 Summary</h3>
                  <div className="text-sm text-blue-800 space-y-1">
                    <div>Total Published: <strong>{results.statusTests?.publish?.count || 0}</strong></div>
                    <div>Total Draft: <strong>{results.statusTests?.draft?.count || 0}</strong></div>
                    <div>Total Private: <strong>{results.statusTests?.private?.count || 0}</strong></div>
                    <div>Total Pending: <strong>{results.statusTests?.pending?.count || 0}</strong></div>
                    <div>Total Any Status: <strong>{results.statusTests?.any?.count || 0}</strong></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {results?.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-red-900 mb-2">❌ Error</h3>
              <p className="text-red-800">{results.error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
