'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Star, ShoppingCart, Heart, Eye, Zap } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useCart } from '@/context/CartContext';

const EnhancedProductCard = ({ product, viewMode = 'grid' }) => {
  const { addToCart } = useCart();
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Calculate discount percentage
  const regularPrice = parseFloat(product.regular_price) || parseFloat(product.price) || 0;
  const currentPrice = parseFloat(product.price) || 0;
  const discountPercentage = regularPrice > currentPrice 
    ? Math.round(((regularPrice - currentPrice) / regularPrice) * 100) 
    : 0;

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Get product image with fallback
  const getProductImage = () => {
    if (imageError) {
      return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';
    }
    
    if (product.images && product.images.length > 0) {
      return typeof product.images[0] === 'string' ? product.images[0] : product.images[0]?.src;
    }
    
    return product.image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';
  };

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!product.inStock) {
      toast.error('Product is out of stock');
      return;
    }

    setIsLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      await addToCart(product);
      toast.success('Added to cart!');
    } catch (error) {
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div 
      className="group relative bg-white rounded-2xl shadow-md hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-blue-200"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Product Image Container - Clickable */}
      <Link href={`/shop/product/${product.id}`}>
        <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="aspect-square relative">
            <Image
              src={getProductImage()}
              alt={product.name}
              fill
              className={`object-cover transition-all duration-700 ${
                isHovered ? 'scale-110' : 'scale-100'
              }`}
              onError={() => setImageError(true)}
            />

            {/* Overlay on hover */}
            <div className={`absolute inset-0 bg-black/20 transition-opacity duration-300 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`} />
          </div>

          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {discountPercentage > 0 && (
              <div className="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                -{discountPercentage}% OFF
              </div>
            )}
            {product.featured && (
              <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg flex items-center gap-1">
                <Zap className="w-3 h-3" />
                HOT
              </div>
            )}
          </div>
        </div>
      </Link>

      {/* Action buttons on hover - Outside Link */}
      <div className={`absolute top-3 right-3 flex flex-col gap-2 transition-all duration-300 z-10 ${
        isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'
      }`}>
        <button className="w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-lg transition-all hover:scale-110">
          <Heart className="w-4 h-4 text-gray-600 hover:text-red-500" />
        </button>
        <button className="w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-lg transition-all hover:scale-110">
          <Eye className="w-4 h-4 text-gray-600 hover:text-blue-500" />
        </button>
      </div>

      {/* Quick add to cart button - Outside Link */}
      <div className={`absolute bottom-3 left-3 right-3 transition-all duration-300 z-10 ${
        isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock || isLoading}
          className={`w-full py-2 px-4 rounded-xl flex items-center justify-center gap-2 font-semibold text-sm transition-all ${
            product.inStock
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg'
              : 'bg-gray-400 cursor-not-allowed text-white'
          }`}
        >
          <ShoppingCart className="w-4 h-4" />
          {isLoading ? 'Adding...' : product.inStock ? 'Add to Cart' : 'Out of Stock'}
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-3">
        <Link href={`/shop/product/${product.id}`}>
          <h3 className="font-semibold text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors leading-tight">
            {product.name}
          </h3>
        </Link>
        
        {/* Rating */}
        <div className="flex items-center gap-2">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.rating || 4.5) 
                    ? 'fill-yellow-400 text-yellow-400' 
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600">
            ({product.reviews || 0})
          </span>
        </div>

        {/* Price */}
        <div className="flex items-center gap-2">
          <span className="text-xl font-bold text-red-600">
            {formatPrice(currentPrice)}
          </span>
          {discountPercentage > 0 && (
            <span className="text-sm text-gray-500 line-through">
              {formatPrice(regularPrice)}
            </span>
          )}
        </div>

        {/* Stock status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${
              product.inStock ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span className={`text-xs font-medium ${
              product.inStock ? 'text-green-600' : 'text-red-600'
            }`}>
              {product.inStock ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
          {product.stock_quantity && product.stock_quantity < 10 && (
            <span className="text-xs text-orange-600 font-medium">
              Only {product.stock_quantity} left!
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedProductCard;
