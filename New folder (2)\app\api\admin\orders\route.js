import { NextResponse } from 'next/server';
import { wooCommerceApi } from '../../../../lib/woocommerce.js';

export async function GET() {
  try {
    console.log('🛒 Loading admin orders...');

    // Get orders from WooCommerce
    const orders = await wooCommerceApi.getOrders({ 
      per_page: 50,
      orderby: 'date',
      order: 'desc'
    });

    console.log(`✅ Loaded ${orders.length} orders`);

    return NextResponse.json({
      success: true,
      orders,
      message: `Loaded ${orders.length} orders successfully`
    });

  } catch (error) {
    console.error('❌ Error loading orders:', error);
    
    return NextResponse.json({
      success: false,
      orders: [],
      error: error.message,
      message: 'Failed to load orders'
    }, { status: 200 }); // Return 200 so frontend can handle gracefully
  }
}
