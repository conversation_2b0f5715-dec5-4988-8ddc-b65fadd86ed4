'use client';

import { Users, ShoppingBag, Globe, TrendingUp } from 'lucide-react';

export default function StatsSection() {
  const stats = [
    {
      icon: Users,
      label: 'Happy Customers',
      value: '50,000+',
      description: 'Satisfied customers worldwide'
    },
    {
      icon: ShoppingBag,
      label: 'Products Sold',
      value: '1M+', 
      description: 'Products delivered successfully'
    },
    {
      icon: Globe,
      label: 'Countries Served',
      value: '25+',
      description: 'Countries with fast delivery'
    },
    {
      icon: TrendingUp,
      label: 'Years Experience',
      value: '5+',
      description: 'Years of e-commerce excellence'
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Trusted by Thousands Worldwide
          </h2>
          <p className="text-lg text-gray-600">
            Join our growing community of satisfied customers
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="text-center group">
                <div className="bg-gradient-to-br from-blue-50 to-purple-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors">
                  <Icon className="w-10 h-10 text-blue-600" />
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-lg font-semibold text-gray-700 mb-1">
                  {stat.label}
                </div>
                <p className="text-sm text-gray-500">
                  {stat.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}