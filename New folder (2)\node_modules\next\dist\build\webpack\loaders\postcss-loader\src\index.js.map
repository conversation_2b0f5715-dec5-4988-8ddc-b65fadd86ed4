{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/index.ts"], "names": ["loader", "content", "sourceMap", "meta", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "callback", "async", "traceAsyncFn", "options", "getOptions", "file", "resourcePath", "useSourceMap", "processOptions", "from", "to", "map", "inline", "annotation", "prev", "traceFn", "normalizeSourceMap", "context", "root", "ast", "type", "setAttribute", "postcssWithPlugins", "postcss", "result", "process", "error", "addDependency", "name", "SyntaxError", "warning", "warnings", "emitWarning", "Warning", "message", "messages", "addBuildDependency", "addMissingDependency", "addContextDependency", "dir", "emitFile", "info", "toJSON", "undefined", "normalizeSourceMapAfterPostcss", "version", "processor", "css", "then", "err"], "mappings": ";;;;+BAIA;;;;CAIC,GACD;;;eAA8BA;;;gEATV;8DACI;uBAC2C;;;;;;AAOpD,eAAeA,OAE5B,WAAW,GACXC,OAAe,EACf,eAAe,GACfC,SAAc,EACdC,IAAS;IAET,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACpD,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3BJ,WACGK,YAAY,CAAC;QACZ,MAAMC,UAAU,IAAI,CAACC,UAAU;QAC/B,MAAMC,OAAO,IAAI,CAACC,YAAY;QAE9B,MAAMC,eACJ,OAAOJ,QAAQR,SAAS,KAAK,cACzBQ,QAAQR,SAAS,GACjB,IAAI,CAACA,SAAS;QAEpB,MAAMa,iBAAsB;YAC1BC,MAAMJ;YACNK,IAAIL;QACN;QAEA,IAAIE,cAAc;YAChBC,eAAeG,GAAG,GAAG;gBACnBC,QAAQ;gBACRC,YAAY;gBACZ,GAAGL,eAAeG,GAAG;YACvB;QACF;QAEA,IAAIhB,aAAaa,eAAeG,GAAG,EAAE;YACnCH,eAAeG,GAAG,CAACG,IAAI,GAAGjB,WACvBE,UAAU,CAAC,wBACXgB,OAAO,CAAC,IAAMC,IAAAA,yBAAkB,EAACrB,WAAW,IAAI,CAACsB,OAAO;QAC7D;QAEA,IAAIC;QAEJ,uCAAuC;QACvC,IAAItB,QAAQA,KAAKuB,GAAG,IAAIvB,KAAKuB,GAAG,CAACC,IAAI,KAAK,WAAW;YACjD,CAAA,EAAEF,IAAI,EAAE,GAAGtB,KAAKuB,GAAG,AAAD;YACpBtB,WAAWwB,YAAY,CAAC,WAAW;QACrC;QAEA,mCAAmC;QACnC,MAAM,EAAEC,kBAAkB,EAAE,GAAG,MAAMnB,QAAQoB,OAAO;QAEpD,IAAIC;QAEJ,IAAI;YACFA,SAAS,MAAM3B,WACZE,UAAU,CAAC,mBACXG,YAAY,CAAC,IACZoB,mBAAmBG,OAAO,CAACP,QAAQxB,SAASc;QAElD,EAAE,OAAOkB,OAAY;YACnB,IAAIA,MAAMrB,IAAI,EAAE;gBACd,IAAI,CAACsB,aAAa,CAACD,MAAMrB,IAAI;YAC/B;YAEA,IAAIqB,MAAME,IAAI,KAAK,kBAAkB;gBACnC,MAAM,IAAIC,cAAW,CAACH;YACxB;YAEA,MAAMA;QACR;QAEA,KAAK,MAAMI,WAAWN,OAAOO,QAAQ,GAAI;YACvC,IAAI,CAACC,WAAW,CAAC,IAAIC,gBAAO,CAACH;QAC/B;QAEA,KAAK,MAAMI,WAAWV,OAAOW,QAAQ,CAAE;YACrC,wCAAwC;YACxC,OAAQD,QAAQd,IAAI;gBAClB,KAAK;oBACH,IAAI,CAACO,aAAa,CAACO,QAAQ7B,IAAI;oBAC/B;gBACF,KAAK;oBACH,IAAI,CAAC+B,kBAAkB,CAACF,QAAQ7B,IAAI;oBACpC;gBACF,KAAK;oBACH,IAAI,CAACgC,oBAAoB,CAACH,QAAQ7B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAACiC,oBAAoB,CAACJ,QAAQ7B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAACiC,oBAAoB,CAACJ,QAAQK,GAAG;oBACrC;gBACF,KAAK;oBACH,IAAIL,QAAQxC,OAAO,IAAIwC,QAAQ7B,IAAI,EAAE;wBACnC,IAAI,CAACmC,QAAQ,CACXN,QAAQ7B,IAAI,EACZ6B,QAAQxC,OAAO,EACfwC,QAAQvC,SAAS,EACjBuC,QAAQO,IAAI;oBAEhB;YACJ;QACF;QAEA,wCAAwC;QACxC,IAAI9B,MAAMa,OAAOb,GAAG,GAAGa,OAAOb,GAAG,CAAC+B,MAAM,KAAKC;QAE7C,IAAIhC,OAAOJ,cAAc;YACvBI,MAAMiC,IAAAA,qCAA8B,EAACjC,KAAK,IAAI,CAACM,OAAO;QACxD;QAEA,MAAME,MAAM;YACVC,MAAM;YACNyB,SAASrB,OAAOsB,SAAS,CAACD,OAAO;YACjC3B,MAAMM,OAAON,IAAI;QACnB;QAEA,OAAO;YAACM,OAAOuB,GAAG;YAAEpC;YAAK;gBAAEQ;YAAI;SAAE;IACnC,GACC6B,IAAI,CACH,CAAC,CAACD,KAAKpC,KAAK,EAAEQ,GAAG,EAAE,CAAM;QACvBnB,4BAAAA,SAAW,MAAM+C,KAAKpC,KAAK;YAAEQ;QAAI;IACnC,GACA,CAAC8B;QACCjD,4BAAAA,SAAWiD;IACb;AAEN"}