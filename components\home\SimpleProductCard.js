'use client';

import Link from 'next/link';
import { Star } from 'lucide-react';

export default function SimpleProductCard({ product }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <Link href={`/shop/product/${product.id}/`}>
        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
          <img
            src={product.image}
            alt={product.name}
            className="h-48 w-full object-cover object-center group-hover:opacity-75"
          />
        </div>
      </Link>
      <div className="p-4">
        <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
          <Link href={`/shop/product/${product.id}/`}>
            {product.name}
          </Link>
        </h3>
        <div className="mt-2 flex items-center">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(product.rating) 
                    ? 'text-yellow-400 fill-current' 
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="ml-1 text-sm text-gray-600">
            ({product.reviews || 0})
          </span>
        </div>
        <div className="mt-2">
          <span className="text-lg font-bold text-gray-900">
            ${parseFloat(product.price || 0).toFixed(2)}
          </span>
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="ml-2 text-sm text-gray-500 line-through">
              ${parseFloat(product.originalPrice).toFixed(2)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
