{"version": 3, "sources": ["../../../src/server/lib/server-action-request-meta.ts"], "names": ["getIsServerAction", "getServerActionRequestMetadata", "req", "actionId", "contentType", "headers", "Headers", "get", "ACTION", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined", "isServerAction"], "mappings": ";;;;;;;;;;;;;;;IAkDgBA,iBAAiB;eAAjBA;;IA7CAC,8BAA8B;eAA9BA;;;kCAFO;AAEhB,SAASA,+BACdC,GAAoD;IAQpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,CAACC,wBAAM,CAACC,WAAW,OAAO;QACpDL,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAW,AAACD,IAAIG,OAAO,CAACG,wBAAM,CAACC,WAAW,GAAG,IAAe;QAC5DL,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMK,qBAAqBC,QACzBT,IAAIU,MAAM,KAAK,UAAUR,gBAAgB;IAE3C,MAAMS,oBAAoBF,QACxBT,IAAIU,MAAM,KAAK,WAAUR,+BAAAA,YAAaU,UAAU,CAAC;IAEnD,MAAMC,gBAAgBJ,QACpBR,aAAaa,aACX,OAAOb,aAAa,YACpBD,IAAIU,MAAM,KAAK;IAGnB,MAAMK,iBAAiBN,QACrBI,iBAAiBL,sBAAsBG;IAGzC,OAAO;QACLV;QACAO;QACAG;QACAE;QACAE;IACF;AACF;AAEO,SAASjB,kBACdE,GAAoD;IAEpD,OAAOD,+BAA+BC,KAAKe,cAAc;AAC3D"}