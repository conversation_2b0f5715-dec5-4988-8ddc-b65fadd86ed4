{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/next-worker-fixture.ts"], "names": ["applyNextWorkerFixture", "NextWorkerFixtureImpl", "setup", "server", "createProxyServer", "onFetch", "handleProxyFetch", "bind", "proxyPort", "port", "proxyServer", "teardown", "close", "cleanupTest", "testId", "proxyFetchMap", "delete", "handler", "set", "request", "get", "Map", "use", "fixture"], "mappings": ";;;;+BAmDsBA;;;eAAAA;;;uBAlDY;AAYlC,MAAMC;IAKJ,MAAMC,QAAuB;QAC3B,MAAMC,SAAS,MAAMC,IAAAA,wBAAiB,EAAC;YACrCC,SAAS,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI;QAC1C;QAEA,IAAI,CAACC,SAAS,GAAGL,OAAOM,IAAI;QAC5B,IAAI,CAACC,WAAW,GAAGP;IACrB;IAEAQ,WAAiB;QACf,IAAI,IAAI,CAACD,WAAW,EAAE;YACpB,IAAI,CAACA,WAAW,CAACE,KAAK;YACtB,IAAI,CAACF,WAAW,GAAG;QACrB;IACF;IAEAG,YAAYC,MAAc,EAAQ;QAChC,IAAI,CAACC,aAAa,CAACC,MAAM,CAACF;IAC5B;IAEAT,QAAQS,MAAc,EAAEG,OAAqB,EAAQ;QACnD,IAAI,CAACF,aAAa,CAACG,GAAG,CAACJ,QAAQG;IACjC;IAEA,MAAcX,iBACZQ,MAAc,EACdK,OAAgB,EACa;QAC7B,MAAMF,UAAU,IAAI,CAACF,aAAa,CAACK,GAAG,CAACN;QACvC,OAAOG,2BAAAA,QAAUE;IACnB;;aAlCOX,YAAoB;aACnBE,cAAkC;aAClCK,gBAAgB,IAAIM;;AAiC9B;AAEO,eAAerB,uBACpBsB,GAAkD;IAElD,MAAMC,UAAU,IAAItB;IACpB,MAAMsB,QAAQrB,KAAK;IACnB,MAAMoB,IAAIC;IACVA,QAAQZ,QAAQ;AAClB"}