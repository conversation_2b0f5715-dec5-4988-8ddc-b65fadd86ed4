{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "names": ["useCallback", "useEffect", "startTransition", "useMemo", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_REFRESH", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "useErrorOverlayReducer", "parseStack", "ReactDevOverlay", "useErrorHandler", "RuntimeError<PERSON>andler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "extractModulesFromTurbopackMessage", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "startLatency", "onBeforeFastRefresh", "dispatcher", "hasUpdates", "onBeforeRefresh", "onFastRefresh", "sendMessage", "updatedModules", "onBuildOk", "reportHmrLatency", "onRefresh", "endLatency", "latency", "console", "log", "JSON", "stringify", "event", "id", "window", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "handleAvailableHash", "hash", "isUpdateAvailable", "process", "env", "TURBOPACK", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "stackTrace", "stack", "split", "slice", "join", "message", "hadRuntimeError", "dependency<PERSON><PERSON>n", "undefined", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "warn", "Boolean", "length", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "error", "handleHotUpdate", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "webpackUpdatedModules", "action", "BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "TURBOPACK_MESSAGE", "data", "SERVER_COMPONENT_CHANGES", "fastRefresh", "RELOAD_PAGE", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "state", "dispatch", "handleOnUnhandledError", "errorDetails", "details", "componentStack", "warning", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "handleOnReactError", "webSocketRef", "websocket", "current", "addEventListener", "removeEventListener", "onReactError"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,EAAEC,OAAO,QAAQ,QAAO;AACxE,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,8CAA6C;AAC/E,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,sBAAsB,QACjB,YAAW;AAClB,SAASC,UAAU,QAAQ,iCAAgC;AAC3D,OAAOC,qBAAqB,oBAAmB;AAC/C,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,oCAAmC;AAC1C,SAASC,mBAAmB,QAAQ,4CAA2C;AAE/E,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,kCAAkC,QAAQ,gEAA+D;AAClH,SAASC,oCAAoC,QAAQ,YAAW;AAUhE,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,eAA8B;AAElC,SAASC,oBAAoBC,UAAsB,EAAEC,UAAmB;IACtE,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,cACPH,UAAsB,EACtBI,WAAsC,EACtCC,cAAqC;IAErCL,WAAWM,SAAS;IAEpBC,iBAAiBH,aAAaC;IAE9BL,WAAWQ,SAAS;AACtB;AAEA,SAASD,iBACPH,WAAsC,EACtCC,cAAqC;IAErC,IAAI,CAACP,cAAc;IACnB,IAAIW,aAAad,KAAKC,GAAG;IACzB,MAAMc,UAAUD,aAAaX;IAC7Ba,QAAQC,GAAG,CAAC,AAAC,4BAAyBF,UAAQ;IAC9CN,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAO1B,iBAAiB;QAC5B2B,WAAWpB;QACXqB,SAASV;QACTW,MAAMH,OAAOI,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;AAEJ;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCrC,4BAA4BqC;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOzC,8BAA8B0C;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEvC,WAAgB;IACnD,MAAMwC,aACJD,OACC,CAAA,AAACA,IAAIE,KAAK,IAAIF,IAAIE,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDL,IAAIM,OAAO,IACXN,MAAM,EAAC;IAEXvC,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP6B;QACAM,iBAAiB,CAAC,CAACrE,oBAAoBqE,eAAe;QACtDC,iBAAiBR,MAAMA,IAAIQ,eAAe,GAAGC;IAC/C;IAGF,IAAIvD,WAAW;IACfA,YAAY;IACZoB,OAAOI,QAAQ,CAACgC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAsD,EACtDpD,WAAgB,EAChBJ,UAAsB;IAEtB,IAAI,CAAC4B,uBAAuB,CAACK,mBAAmB;QAC9CjC,WAAWM,SAAS;QACpB;IACF;IAEA,SAASmD,mBAAmBd,GAAQ,EAAEtC,cAA+B;QACnE,IAAIsC,OAAO9D,oBAAoBqE,eAAe,IAAI,CAAC7C,gBAAgB;YACjE,IAAIsC,KAAK;gBACPhC,QAAQ+C,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAI7E,oBAAoBqE,eAAe,EAAE;gBAC9CvC,QAAQ+C,IAAI,CAACrE;YACf;YACAqD,kBAAkBC,KAAKvC;YACvB;QACF;QAEA,MAAMH,aAAa0D,QAAQtD,eAAeuD,MAAM;QAChD,IAAI,OAAOJ,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBnD;QACrB;QAEA,IAAIuB,qBAAqB;YACvB,+DAA+D;YAC/D0B,gBACErD,aAAa,KAAO,IAAIsD,gBACxBtD,aAAa,IAAMD,WAAWM,SAAS,KAAKkD,oBAC5CpD,aACAJ;QAEJ,OAAO;YACLA,WAAWM,SAAS;YACpB,IAAIuB,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;gBAChCxB,kBAAkB;oBAChB,IAAIyB,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrC7B,OAAOC,GAAG,CACP6B,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC5D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOkD,mBAAmB,YAAY;YACxC,MAAMtD,aAAa0D,QAAQtD,eAAeuD,MAAM;YAChDL,eAAetD;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOiC,OAAOC,GAAG,CAAC+B,KAAK;IACzB,GACCD,IAAI,CACH,CAAC5D;QACCoD,mBAAmB,MAAMpD;IAC3B,GACA,CAACsC;QACCc,mBAAmBd,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASwB,eACPC,GAAqB,EACrBhE,WAAsC,EACtCiE,uBAA6D,EAC7DC,MAAoC,EACpCtE,UAAsB;IAEtB,IAAI,CAAE,CAAA,YAAYoE,GAAE,GAAI;QACtB;IACF;IAEA,SAASG,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYzG,sBAAsB;YACtCwG,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B1E,WAAW2E,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACZ,MAAM,EAAEgB,IAAK;YAChDjE,QAAQkE,KAAK,CAAC9G,UAAU0G,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAI/C,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACU,UAAUD,MAAM,CAAC,EAAE;gBACtCV,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASe;QACP,IAAIjD,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB/B,WAAWM,SAAS;QACtB,OAAO;YACLgD,gBACE,SAASyB,kBAAkB9E,UAAmB;gBAC5CF,oBAAoBC,YAAYC;YAClC,GACA,SAAS+E,sBAAsBC,qBAA+B;gBAC5D,qDAAqD;gBACrD,sDAAsD;gBACtD9E,cAAcH,YAAYI,aAAa6E;YACzC,GACA7E,aACAJ;QAEJ;IACF;IAEA,OAAQoE,IAAIc,MAAM;QAChB,KAAK/F,4BAA4BgG,QAAQ;YAAE;gBACzCrF,eAAeH,KAAKC,GAAG;gBACvBe,QAAQC,GAAG,CAAC;gBACZ;YACF;QACA,KAAKzB,4BAA4BiG,KAAK;QACtC,KAAKjG,4BAA4BkG,IAAI;YAAE;gBACrC,IAAIjB,IAAIzC,IAAI,EAAE;oBACZD,oBAAoB0C,IAAIzC,IAAI;gBAC9B;gBAEA,MAAM,EAAE6C,MAAM,EAAEE,QAAQ,EAAE,GAAGN;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKpE,WAAWsF,aAAa,CAAClB,IAAImB,WAAW;gBAElE,MAAMC,YAAY7B,QAAQa,UAAUA,OAAOZ,MAAM;gBACjD,kEAAkE;gBAClE,IAAI4B,WAAW;oBACbpF,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP0E,YAAYjB,OAAOZ,MAAM;wBACzB8B,UAAUnG;oBACZ;oBAGFgF,aAAaC;oBACb;gBACF;gBAEA,MAAMmB,cAAchC,QAAQe,YAAYA,SAASd,MAAM;gBACvD,IAAI+B,aAAa;oBACfvF,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP6E,cAAclB,SAASd,MAAM;wBAC7B8B,UAAUnG;oBACZ;oBAGF,iCAAiC;oBACjC,MAAMsG,oBAAoB7H,sBAAsB;wBAC9C0G,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAIiB,kBAAkBnB,QAAQ,CAACd,MAAM,EAAEgB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXjE,QAAQ+C,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACA/C,QAAQ+C,IAAI,CAAC3F,UAAU8H,kBAAkBnB,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEAxE,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP2E,UAAUnG;gBACZ;gBAGF,IAAI6E,IAAIc,MAAM,KAAK/F,4BAA4BiG,KAAK,EAAE;oBACpD,qBAAqB;oBACrBN;gBACF;gBACA;YACF;QACA,KAAK3F,4BAA4B2G,mBAAmB;YAAE;gBACpDzB,wBAAwB;oBACtB0B,MAAM5G,4BAA4B2G,mBAAmB;gBACvD;gBACA;YACF;QACA,KAAK3G,4BAA4B6G,iBAAiB;YAAE;gBAClD,MAAM3F,iBAAiBjB,mCAAmCgF,IAAI6B,IAAI;gBAClEjG,WAAWE,eAAe;gBAC1BmE,wBAAwB;oBACtB0B,MAAM5G,4BAA4B6G,iBAAiB;oBACnDC,MAAM7B,IAAI6B,IAAI;gBAChB;gBACAjG,WAAWQ,SAAS;gBACpB,IAAI3B,oBAAoBqE,eAAe,EAAE;oBACvCvC,QAAQ+C,IAAI,CAACrE;oBACbqD,kBAAkB,MAAMtC;gBAC1B;gBACAG,iBAAiBH,aAAaC;gBAC9B;YACF;QACA,uDAAuD;QACvD,KAAKlB,4BAA4B+G,wBAAwB;YAAE;gBACzD9F,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP2E,UAAUnG;gBACZ;gBAEF,IAAIV,oBAAoBqE,eAAe,EAAE;oBACvC,IAAIrD,WAAW;oBACfA,YAAY;oBACZ,OAAOoB,OAAOI,QAAQ,CAACgC,MAAM;gBAC/B;gBACAxF,gBAAgB;oBACdyG,OAAO6B,WAAW;oBAClBnG,WAAWQ,SAAS;gBACtB;gBAEA,IAAIqB,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAK5E,4BAA4BiH,WAAW;YAAE;gBAC5ChG,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP2E,UAAUnG;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOoB,OAAOI,QAAQ,CAACgC,MAAM;YAC/B;QACA,KAAKlE,4BAA4BkH,UAAU;QAC3C,KAAKlH,4BAA4BmH,YAAY;YAAE;gBAC7C,qFAAqF;gBACrF,OAAOhC,OAAO6B,WAAW;YAC3B;QACA,KAAKhH,4BAA4BoH,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGpC;gBACtB,IAAIoC,WAAW;oBACb,MAAM,EAAEvD,OAAO,EAAEJ,KAAK,EAAE,GAAGhC,KAAK4F,KAAK,CAACD;oBACtC,MAAM3B,QAAQ,IAAI6B,MAAMzD;oBACxB4B,MAAMhC,KAAK,GAAGA;oBACd0B,aAAa;wBAACM;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK1F,4BAA4BwH,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEA,eAAe,SAASC,UAAU,KAMjC;IANiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,GAAGvI;IAE1B,MAAMuB,aAAalC,QAAoB;QACrC,OAAO;YACLwC;gBACE0G,SAAS;oBAAEjB,MAAM3H;gBAAgB;YACnC;YACAuG,cAAa1B,OAAO;gBAClB+D,SAAS;oBAAEjB,MAAM5H;oBAAoB8E;gBAAQ;YAC/C;YACA/C;gBACE8G,SAAS;oBAAEjB,MAAM7H;gBAAsB;YACzC;YACAsC;gBACEwG,SAAS;oBAAEjB,MAAM1H;gBAAe;YAClC;YACAiH,eAAcC,WAAW;gBACvByB,SAAS;oBAAEjB,MAAMvH;oBAAqB+G;gBAAY;YACpD;QACF;IACF,GAAG;QAACyB;KAAS;IAEb,MAAMC,yBAAyBtJ,YAC7B,CAACkH;QACC,MAAMqC,eAAe,AAACrC,MAAcsC,OAAO;QAG3C,kGAAkG;QAClG,MAAMC,iBAAiBF,gCAAAA,aAAcE,cAAc;QACnD,MAAMC,UAAUH,gCAAAA,aAAcG,OAAO;QACrCL,SAAS;YACPjB,MAAMzH;YACNgJ,QAAQzC;YACR0C,QAAQ7I,WAAWmG,MAAMhC,KAAK;YAC9B2E,sBAAsBJ,iBAClBlI,oBAAoBkI,kBACpBhE;YACJiE;QACF;IACF,GACA;QAACL;KAAS;IAEZ,MAAMS,6BAA6B9J,YACjC,CAAC2J;QACCN,SAAS;YACPjB,MAAMxH;YACN+I,QAAQA;YACRC,QAAQ7I,WAAW4I,OAAOzE,KAAK;QACjC;IACF,GACA;QAACmE;KAAS;IAEZ,MAAMU,qBAAqB/J,YAAY;QACrCkB,oBAAoBqE,eAAe,GAAG;IACxC,GAAG,EAAE;IACLtE,gBAAgBqI,wBAAwBQ;IAExC,MAAME,eAAe3I,aAAa6H;IAClC5H,iBAAiB0I;IACjB,MAAMvH,cAActB,eAAe6I;IACnC,MAAMtD,0BAA0BtF,aAAaqB,aAAa,CAACuC,MACzDD,kBAAkBC,KAAKvC;IAGzB,MAAMkE,SAASrG;IAEfL,UAAU;QACR,MAAMgK,YAAYD,aAAaE,OAAO;QACtC,IAAI,CAACD,WAAW;QAEhB,MAAMrF,UAAU,CAACxB;YACf,IAAI;gBACF,MAAMqD,MAAMvD,KAAK4F,KAAK,CAAC1F,MAAMkF,IAAI;gBACjC9B,eACEC,KACAhE,aACAiE,yBACAC,QACAtE;YAEJ,EAAE,OAAO2C,KAAU;oBAEkCA;gBADnDhC,QAAQ+C,IAAI,CACV,4BAA4B3C,MAAMkF,IAAI,GAAG,OAAQtD,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKE,KAAK,YAAVF,aAAc,EAAC;YAEpE;QACF;QAEAiF,UAAUE,gBAAgB,CAAC,WAAWvF;QACtC,OAAO,IAAMqF,UAAUG,mBAAmB,CAAC,WAAWxF;IACxD,GAAG;QAACnC;QAAakE;QAAQqD;QAAc3H;QAAYqE;KAAwB;IAE3E,qBACE,KAAC1F;QAAgBqJ,cAAcN;QAAoBX,OAAOA;kBACvDD;;AAGP"}