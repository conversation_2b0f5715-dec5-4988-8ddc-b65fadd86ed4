{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/codegen.ts"], "names": ["camelCase", "dashesCamelCase", "normalizeSourceMapForRuntime", "getImportCode", "imports", "options", "code", "item", "importName", "url", "icss", "esModule", "modules", "namedExport", "exportOnlyLocals", "getModuleCode", "result", "api", "replacements", "loaderContext", "sourceMapValue", "sourceMap", "map", "JSON", "stringify", "css", "beforeCode", "media", "dedupe", "replacement<PERSON>ame", "localName", "replace", "RegExp", "hash", "needQuotes", "getUrlOptions", "preparedOptions", "length", "join", "getExportCode", "exports", "localsCode", "addExportToLocalsCode", "name", "value", "exportLocalsConvention", "modifiedName"], "mappings": "AACA,OAAOA,eAAe,iCAAgC;AACtD,SACEC,eAAe,EACfC,4BAA4B,QACvB,6BAA4B;AAmCnC,OAAO,SAASC,cAAcC,OAAoB,EAAEC,OAAY;IAC9D,IAAIC,OAAO;IAEX,KAAK,MAAMC,QAAQH,QAAS;QAC1B,MAAM,EAAEI,UAAU,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGH;QAElC,IAAIF,QAAQM,QAAQ,EAAE;YACpB,IAAID,QAAQL,QAAQO,OAAO,CAACC,WAAW,EAAE;gBACvCP,QAAQ,CAAC,OAAO,EACdD,QAAQO,OAAO,CAACE,gBAAgB,GAAG,KAAK,CAAC,EAAEN,WAAW,EAAE,CAAC,CAC1D,KAAK,EAAEA,WAAW,eAAe,EAAEC,IAAI,GAAG,CAAC;YAC9C,OAAO;gBACLH,QAAQ,CAAC,OAAO,EAAEE,WAAW,MAAM,EAAEC,IAAI,GAAG,CAAC;YAC/C;QACF,OAAO;YACLH,QAAQ,CAAC,IAAI,EAAEE,WAAW,WAAW,EAAEC,IAAI,IAAI,CAAC;QAClD;IACF;IAEA,OAAOH,OAAO,CAAC,YAAY,EAAEA,KAAK,CAAC,GAAG;AACxC;AAEA,OAAO,SAASS,cACdC,MAA8B,EAC9BC,GAAe,EACfC,YAA8B,EAC9Bb,OAAY,EACZc,aAAiC;IAEjC,IAAId,QAAQO,OAAO,CAACE,gBAAgB,KAAK,MAAM;QAC7C,OAAO;IACT;IAEA,MAAMM,iBAAiBf,QAAQgB,SAAS,GACpC,CAAC,CAAC,EAAEnB,6BAA6Bc,OAAOM,GAAG,EAAEH,eAAe,CAAC,GAC7D;IAEJ,IAAIb,OAAOiB,KAAKC,SAAS,CAACR,OAAOS,GAAG;IACpC,IAAIC,aAAa,CAAC,0DAA0D,EAAErB,QAAQgB,SAAS,CAAC,IAAI,CAAC;IAErG,KAAK,MAAMd,QAAQU,IAAK;QACtB,MAAM,EAAER,GAAG,EAAEkB,KAAK,EAAEC,MAAM,EAAE,GAAGrB;QAE/BmB,cAAcjB,MACV,CAAC,yCAAyC,EAAEc,KAAKC,SAAS,CACxD,CAAC,YAAY,EAAEf,IAAI,EAAE,CAAC,EACtB,EAAEkB,QAAQ,CAAC,EAAE,EAAEJ,KAAKC,SAAS,CAACG,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,GACpD,CAAC,0BAA0B,EAAEpB,KAAKC,UAAU,CAAC,EAC3CmB,QAAQ,CAAC,EAAE,EAAEJ,KAAKC,SAAS,CAACG,OAAO,CAAC,GAAGC,SAAS,SAAS,GAC1D,EAAEA,SAAS,WAAW,GAAG,IAAI,CAAC;IACrC;IAEA,KAAK,MAAMrB,QAAQW,aAAc;QAC/B,MAAM,EAAEW,eAAe,EAAErB,UAAU,EAAEsB,SAAS,EAAE,GAAGvB;QAEnD,IAAIuB,WAAW;YACbxB,OAAOA,KAAKyB,OAAO,CAAC,IAAIC,OAAOH,iBAAiB,MAAM,IACpDxB,QAAQO,OAAO,CAACC,WAAW,GACvB,CAAC,IAAI,EAAEL,WAAW,UAAU,EAAEe,KAAKC,SAAS,CAC1CxB,UAAU8B,YACV,KAAK,CAAC,GACR,CAAC,IAAI,EAAEtB,WAAW,QAAQ,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;QAEpE,OAAO;YACL,MAAM,EAAEG,IAAI,EAAEC,UAAU,EAAE,GAAG3B;YAC7B,MAAM4B,gBAAgB;mBAChBF,OAAO;oBAAC,CAAC,MAAM,EAAEV,KAAKC,SAAS,CAACS,MAAM,CAAC;iBAAC,GAAG,EAAE;mBAC7CC,aAAa,qBAAqB,EAAE;aACzC;YACD,MAAME,kBACJD,cAAcE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAEF,cAAcG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;YAEnEZ,cAAc,CAAC,IAAI,EAAEG,gBAAgB,mCAAmC,EAAErB,WAAW,EAAE4B,gBAAgB,IAAI,CAAC;YAC5G9B,OAAOA,KAAKyB,OAAO,CACjB,IAAIC,OAAOH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,OAAO,CAAC,EAAEH,WAAW,oDAAoD,EAAEpB,KAAK,IAAI,EAAEc,eAAe,KAAK,CAAC;AAC7G;AAEA,OAAO,SAASmB,cACdC,OAAoB,EACpBtB,YAA8B,EAC9Bb,OAAY;IAEZ,IAAIC,OAAO;IACX,IAAImC,aAAa;IAEjB,MAAMC,wBAAwB,CAACC,MAAcC;QAC3C,IAAIvC,QAAQO,OAAO,CAACC,WAAW,EAAE;YAC/B4B,cAAc,CAAC,aAAa,EAAEzC,UAAU2C,MAAM,GAAG,EAAEpB,KAAKC,SAAS,CAC/DoB,OACA,GAAG,CAAC;QACR,OAAO;YACL,IAAIH,YAAY;gBACdA,cAAc,CAAC,GAAG,CAAC;YACrB;YAEAA,cAAc,CAAC,EAAE,EAAElB,KAAKC,SAAS,CAACmB,MAAM,EAAE,EAAEpB,KAAKC,SAAS,CAACoB,OAAO,CAAC;QACrE;IACF;IAEA,KAAK,MAAM,EAAED,IAAI,EAAEC,KAAK,EAAE,IAAIJ,QAAS;QACrC,OAAQnC,QAAQO,OAAO,CAACiC,sBAAsB;YAC5C,KAAK;gBAAa;oBAChBH,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAe9C,UAAU2C;oBAE/B,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpBF,sBAAsB1C,UAAU2C,OAAOC;oBACvC;gBACF;YACA,KAAK;gBAAU;oBACbF,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAe7C,gBAAgB0C;oBAErC,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAc;oBACjBF,sBAAsBzC,gBAAgB0C,OAAOC;oBAC7C;gBACF;YACA,KAAK;YACL;gBACEF,sBAAsBC,MAAMC;gBAC5B;QACJ;IACF;IAEA,KAAK,MAAMrC,QAAQW,aAAc;QAC/B,MAAM,EAAEW,eAAe,EAAEC,SAAS,EAAE,GAAGvB;QAEvC,IAAIuB,WAAW;YACb,MAAM,EAAEtB,UAAU,EAAE,GAAGD;YAEvBkC,aAAaA,WAAWV,OAAO,CAAC,IAAIC,OAAOH,iBAAiB,MAAM;gBAChE,IAAIxB,QAAQO,OAAO,CAACC,WAAW,EAAE;oBAC/B,OAAO,CAAC,IAAI,EAAEL,WAAW,UAAU,EAAEe,KAAKC,SAAS,CACjDxB,UAAU8B,YACV,KAAK,CAAC;gBACV,OAAO,IAAIzB,QAAQO,OAAO,CAACE,gBAAgB,EAAE;oBAC3C,OAAO,CAAC,IAAI,EAAEN,WAAW,CAAC,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;gBAC9D;gBAEA,OAAO,CAAC,IAAI,EAAEtB,WAAW,QAAQ,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;YACrE;QACF,OAAO;YACLW,aAAaA,WAAWV,OAAO,CAC7B,IAAIC,OAAOH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,IAAIxB,QAAQO,OAAO,CAACE,gBAAgB,EAAE;QACpCR,QAAQD,QAAQO,OAAO,CAACC,WAAW,GAC/B4B,aACA,CAAC,EACCpC,QAAQM,QAAQ,GAAG,mBAAmB,mBACvC,IAAI,EAAE8B,WAAW,MAAM,CAAC;QAE7B,OAAOnC;IACT;IAEA,IAAImC,YAAY;QACdnC,QAAQD,QAAQO,OAAO,CAACC,WAAW,GAC/B4B,aACA,CAAC,oCAAoC,EAAEA,WAAW,MAAM,CAAC;IAC/D;IAEAnC,QAAQ,CAAC,EACPD,QAAQM,QAAQ,GAAG,mBAAmB,mBACvC,2BAA2B,CAAC;IAE7B,OAAOL;AACT"}