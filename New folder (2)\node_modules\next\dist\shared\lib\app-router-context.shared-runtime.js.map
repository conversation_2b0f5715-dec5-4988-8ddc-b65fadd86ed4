{"version": 3, "sources": ["../../../src/shared/lib/app-router-context.shared-runtime.ts"], "names": ["AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "Set"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;IA0JaA,gBAAgB;eAAhBA;;IAUAC,yBAAyB;eAAzBA;;IAPAC,mBAAmB;eAAnBA;;IAwBAC,kBAAkB;eAAlBA;;IATAC,eAAe;eAAfA;;;;gEAnKK;AAiJX,MAAMJ,mBAAmBK,cAAK,CAACC,aAAa,CACjD;AAEK,MAAMJ,sBAAsBG,cAAK,CAACC,aAAa,CAK5C;AAEH,MAAML,4BAA4BI,cAAK,CAACC,aAAa,CAMzD;AAEI,MAAMF,kBAAkBC,cAAK,CAACC,aAAa,CAAkB;AAEpE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCT,iBAAiBU,WAAW,GAAG;IAC/BR,oBAAoBQ,WAAW,GAAG;IAClCT,0BAA0BS,WAAW,GAAG;IACxCN,gBAAgBM,WAAW,GAAG;AAChC;AAEO,MAAMP,qBAAqBE,cAAK,CAACC,aAAa,CAAc,IAAIK"}