import { NextResponse } from 'next/server';
import { wooCommerceApi } from '../../../lib/woocommerce.js';

// WooCommerce Cart Integration API
export async function POST(request) {
  try {
    const body = await request.json();
    const { action, productId, quantity = 1, variationId, customerId, items } = body;

    switch (action) {
      case 'add_to_cart':
        return await addToCart(productId, quantity, variationId, customerId);
      
      case 'bulk_add_to_cart':
        return await bulkAddToCart(items, customerId);
      
      case 'get_cart':
        return await getCart(customerId);
      
      case 'update_cart_item':
        return await updateCartItem(body);
      
      case 'remove_from_cart':
        return await removeFromCart(body);
      
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: add_to_cart, bulk_add_to_cart, get_cart, update_cart_item, remove_from_cart'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('WooCommerce Cart API error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Add single item to cart
async function addToCart(productId, quantity, variationId, customerId) {
  try {
    console.log(`🛒 Adding product ${productId} to cart (qty: ${quantity})...`);

    // Validate product exists and is purchasable
    const product = await wooCommerceApi.getProduct(productId);
    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    if (product.status !== 'publish') {
      return NextResponse.json({
        success: false,
        error: 'Product is not available for purchase'
      }, { status: 400 });
    }

    if (product.stock_status === 'outofstock') {
      return NextResponse.json({
        success: false,
        error: 'Product is out of stock'
      }, { status: 400 });
    }

    // Check stock quantity
    if (product.manage_stock && product.stock_quantity < quantity) {
      return NextResponse.json({
        success: false,
        error: `Only ${product.stock_quantity} items available in stock`
      }, { status: 400 });
    }

    // For WooCommerce, we'll use a session-based cart approach
    // In a real implementation, you might use WooCommerce's cart endpoints
    // or integrate with a cart service
    
    const cartItem = {
      product_id: productId,
      variation_id: variationId || null,
      quantity: quantity,
      product_name: product.name,
      product_price: product.price,
      product_image: product.images?.[0]?.src || null,
      added_at: new Date().toISOString()
    };

    console.log(`✅ Product added to cart: ${product.name}`);

    return NextResponse.json({
      success: true,
      message: 'Product added to cart successfully',
      cart_item: cartItem,
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.images?.[0]?.src
      }
    });

  } catch (error) {
    console.error('Error adding to cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Bulk add items to cart (for reorder functionality)
async function bulkAddToCart(items, customerId) {
  try {
    console.log(`🛒 Bulk adding ${items.length} items to cart...`);

    const results = {
      success: [],
      failed: [],
      total_added: 0,
      total_failed: 0
    };

    for (const item of items) {
      try {
        // Validate each product
        const product = await wooCommerceApi.getProduct(item.product_id);
        
        if (!product || product.status !== 'publish') {
          results.failed.push({
            product_id: item.product_id,
            name: item.name || 'Unknown Product',
            reason: 'Product not available'
          });
          continue;
        }

        if (product.stock_status === 'outofstock') {
          results.failed.push({
            product_id: item.product_id,
            name: product.name,
            reason: 'Out of stock'
          });
          continue;
        }

        if (product.manage_stock && product.stock_quantity < item.quantity) {
          results.failed.push({
            product_id: item.product_id,
            name: product.name,
            reason: `Only ${product.stock_quantity} available`
          });
          continue;
        }

        // Add to cart
        const cartItem = {
          product_id: item.product_id,
          variation_id: item.variation_id || null,
          quantity: item.quantity,
          product_name: product.name,
          product_price: product.price,
          product_image: product.images?.[0]?.src || null,
          added_at: new Date().toISOString()
        };

        results.success.push(cartItem);
        results.total_added += item.quantity;

      } catch (error) {
        console.error(`Error adding item ${item.product_id}:`, error);
        results.failed.push({
          product_id: item.product_id,
          name: item.name || 'Unknown Product',
          reason: error.message
        });
      }
    }

    results.total_failed = results.failed.length;

    console.log(`✅ Bulk add complete: ${results.total_added} items added, ${results.total_failed} failed`);

    return NextResponse.json({
      success: true,
      message: `Added ${results.total_added} items to cart`,
      results
    });

  } catch (error) {
    console.error('Error bulk adding to cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Get cart contents
async function getCart(customerId) {
  try {
    console.log(`🛒 Getting cart for customer ${customerId}...`);

    // In a real implementation, you'd retrieve the cart from WooCommerce
    // For now, we'll return a demo cart structure
    const cart = {
      items: [],
      total_items: 0,
      total_price: 0,
      currency: 'GBP',
      last_updated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      cart
    });

  } catch (error) {
    console.error('Error getting cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Update cart item quantity
async function updateCartItem({ cartItemId, quantity, customerId }) {
  try {
    console.log(`🛒 Updating cart item ${cartItemId} quantity to ${quantity}...`);

    // In a real implementation, you'd update the cart item in WooCommerce
    
    return NextResponse.json({
      success: true,
      message: 'Cart item updated successfully'
    });

  } catch (error) {
    console.error('Error updating cart item:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Remove item from cart
async function removeFromCart({ cartItemId, customerId }) {
  try {
    console.log(`🛒 Removing cart item ${cartItemId}...`);

    // In a real implementation, you'd remove the cart item from WooCommerce
    
    return NextResponse.json({
      success: true,
      message: 'Item removed from cart successfully'
    });

  } catch (error) {
    console.error('Error removing from cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// GET endpoint for cart status
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customer_id');

    if (!customerId) {
      return NextResponse.json({
        success: false,
        error: 'Customer ID is required'
      }, { status: 400 });
    }

    return await getCart(customerId);

  } catch (error) {
    console.error('Error getting cart:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
