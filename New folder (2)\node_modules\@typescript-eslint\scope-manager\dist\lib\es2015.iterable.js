"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_iterable = void 0;
const base_config_1 = require("./base-config");
const es2015_symbol_1 = require("./es2015.symbol");
exports.es2015_iterable = {
    ...es2015_symbol_1.es2015_symbol,
    SymbolConstructor: base_config_1.TYPE,
    IteratorYieldResult: base_config_1.TYPE,
    IteratorReturnResult: base_config_1.TYPE,
    IteratorResult: base_config_1.TYPE,
    Iterator: base_config_1.TYPE,
    Iterable: base_config_1.TYPE,
    IterableIterator: base_config_1.TYPE,
    Array: base_config_1.TYPE,
    ArrayConstructor: base_config_1.TYPE,
    ReadonlyArray: base_config_1.TYPE,
    IArguments: base_config_1.TYPE,
    Map: base_config_1.TYPE,
    ReadonlyMap: base_config_1.TYPE,
    MapConstructor: base_config_1.TYPE,
    WeakMap: base_config_1.TYPE,
    WeakMapConstructor: base_config_1.TYPE,
    Set: base_config_1.TYPE,
    ReadonlySet: base_config_1.TYPE,
    SetConstructor: base_config_1.TYPE,
    WeakSet: base_config_1.TYPE,
    WeakSetConstructor: base_config_1.TYPE,
    Promise: base_config_1.TYPE,
    PromiseConstructor: base_config_1.TYPE,
    String: base_config_1.TYPE,
    Int8Array: base_config_1.TYPE,
    Int8ArrayConstructor: base_config_1.TYPE,
    Uint8Array: base_config_1.TYPE,
    Uint8ArrayConstructor: base_config_1.TYPE,
    Uint8ClampedArray: base_config_1.TYPE,
    Uint8ClampedArrayConstructor: base_config_1.TYPE,
    Int16Array: base_config_1.TYPE,
    Int16ArrayConstructor: base_config_1.TYPE,
    Uint16Array: base_config_1.TYPE,
    Uint16ArrayConstructor: base_config_1.TYPE,
    Int32Array: base_config_1.TYPE,
    Int32ArrayConstructor: base_config_1.TYPE,
    Uint32Array: base_config_1.TYPE,
    Uint32ArrayConstructor: base_config_1.TYPE,
    Float32Array: base_config_1.TYPE,
    Float32ArrayConstructor: base_config_1.TYPE,
    Float64Array: base_config_1.TYPE,
    Float64ArrayConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2015.iterable.js.map