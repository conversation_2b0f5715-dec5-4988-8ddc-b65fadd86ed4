{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/shared.ts"], "names": ["codeFrameColumns", "reactVendoredRe", "reactNodeModulesRe", "nextInternalsRe", "nextMethodRe", "isInternal", "file", "test", "findSourcePackage", "methodName", "getOriginalCodeFrame", "frame", "source", "includes", "start", "line", "lineNumber", "column", "forceColor", "noContent", "res", "statusCode", "end", "badRequest", "internalServerError", "e", "json", "data", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,sCAAqC;AAWtE,2DAA2D,GAC3D,MAAMC,kBACJ;AAEF,mFAAmF,GACnF,MAAMC,qBAAqB;AAE3B,MAAMC,kBACJ;AAEF,MAAMC,eAAe;AAErB,SAASC,WAAWC,IAAmB;IACrC,IAAI,CAACA,MAAM,OAAO;IAElB,OACEH,gBAAgBI,IAAI,CAACD,SACrBL,gBAAgBM,IAAI,CAACD,SACrBJ,mBAAmBK,IAAI,CAACD;AAE5B;AAEA,0DAA0D,GAC1D,OAAO,SAASE,kBAAkB,KAG4B;IAH5B,IAAA,EAChCF,IAAI,EACJG,UAAU,EACkD,GAH5B;IAMhC,IAAIH,MAAM;QACR,mEAAmE;QACnE,IAAIL,gBAAgBM,IAAI,CAACD,SAASJ,mBAAmBK,IAAI,CAACD,OAAO;YAC/D,OAAO;QACT,OAAO,IAAIH,gBAAgBI,IAAI,CAACD,OAAO;YACrC,OAAO;QACT;IACF;IAEA,IAAIG,YAAY;QACd,IAAIL,aAAaG,IAAI,CAACE,aAAa;YACjC,OAAO;QACT;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASC,qBACdC,KAAiB,EACjBC,MAAqB;QAInBD;IAFF,IACE,CAACC,YACDD,cAAAA,MAAML,IAAI,qBAAVK,YAAYE,QAAQ,CAAC,oBACrBR,WAAWM,MAAML,IAAI,GACrB;QACA,OAAO;IACT;QAOYK,mBAEEA;IAPd,OAAOX,iBACLY,QACA;QACEE,OAAO;YACL,wDAAwD;YACxDC,MAAMJ,CAAAA,oBAAAA,MAAMK,UAAU,YAAhBL,oBAAoB,CAAC;YAC3B,8DAA8D;YAC9DM,QAAQN,CAAAA,gBAAAA,MAAMM,MAAM,YAAZN,gBAAgB;QAC1B;IACF,GACA;QAAEO,YAAY;IAAK;AAEvB;AAEA,OAAO,SAASC,UAAUC,GAAmB;IAC3CA,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAAC;AACV;AAEA,OAAO,SAASC,WAAWH,GAAmB;IAC5CA,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAAC;AACV;AAEA,OAAO,SAASE,oBAAoBJ,GAAmB,EAAEK,CAAO;IAC9DL,IAAIC,UAAU,GAAG;IACjBD,IAAIE,GAAG,CAACG,YAAAA,IAAK;AACf;AAEA,OAAO,SAASC,KAAKN,GAAmB,EAAEO,IAAS;IACjDP,IACGQ,SAAS,CAAC,gBAAgB,oBAC1BN,GAAG,CAACO,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACL;AACpC"}