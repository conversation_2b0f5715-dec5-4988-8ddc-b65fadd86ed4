'use client';

import Link from 'next/link';
import { useState } from 'react';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube,
  Shield,
  Truck,
  RefreshCw,
  Award,
  Send,
  ArrowUp,
  MessageCircle
} from 'lucide-react';
import { toast } from 'react-hot-toast';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleNewsletterSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsSubscribing(true);

    try {
      // Mock newsletter subscription
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Successfully subscribed to newsletter!');
      setEmail('');
    } catch (error) {
      toast.error('Failed to subscribe. Please try again.');
    } finally {
      setIsSubscribing(false);
    }
  };

  // Back to Top functionality moved to BackToTopButton client component

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated with Deal4u</h2>
          <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and be the first to know about new products, exclusive deals, and special offers.
          </p>
          <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row max-w-md mx-auto gap-4">
            <input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none text-gray-900"
              required
            />
            <button
              type="submit"
              disabled={isSubscribing}
              className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSubscribing ? (
                <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Subscribe
                </>
              )}
            </button>
          </form>
          <p className="text-blue-200 text-sm mt-4">
            Join over 50,000+ subscribers. Unsubscribe at any time.
          </p>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-bold text-2xl mb-6">
                Deal4u
              </div>
            </Link>
            <p className="text-gray-300 mb-6 text-sm leading-relaxed">
              Your ultimate destination for premium products at unbeatable prices. We bring you the best deals from around the world with fast, reliable shipping and exceptional customer service.
            </p>
            
            {/* Social Media */}
            <div className="flex space-x-4">
              <a 
                href="https://facebook.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-800 p-3 rounded-full hover:bg-blue-600 transition-colors group"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5 text-gray-400 group-hover:text-white" />
              </a>
              <a 
                href="https://twitter.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-800 p-3 rounded-full hover:bg-blue-400 transition-colors group"
                aria-label="Twitter"
              >
                <Twitter className="w-5 h-5 text-gray-400 group-hover:text-white" />
              </a>
              <a 
                href="https://instagram.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-800 p-3 rounded-full hover:bg-pink-600 transition-colors group"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5 text-gray-400 group-hover:text-white" />
              </a>
              <a 
                href="https://youtube.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-gray-800 p-3 rounded-full hover:bg-red-600 transition-colors group"
                aria-label="YouTube"
              >
                <Youtube className="w-5 h-5 text-gray-400 group-hover:text-white" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Quick Links</h3>
            <ul className="space-y-3">
              {[
                { name: 'About Us', href: '/about' },
                { name: 'Contact Us', href: '/contact' },
                { name: 'FAQ', href: '/faq' },
                { name: 'Shipping Info', href: '/shipping' },
                { name: 'Return Policy', href: '/returns' },
                { name: 'Size Guide', href: '/size-guide' },
                { name: 'Track Order', href: '/track-order' },
                { name: 'Gift Cards', href: '/gift-cards' }
              ].map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Shop Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Shop Categories</h3>
            <ul className="space-y-3">
              {[
                { name: 'Electronics & Gadgets', href: '/shop?category=electronics' },
                { name: 'Home & Living', href: '/shop?category=home' },
                { name: 'Fashion & Accessories', href: '/shop?category=fashion' },
                { name: 'Health & Beauty', href: '/shop?category=beauty' },
                { name: 'Sports & Outdoors', href: '/shop?category=sports' },
                { name: 'Automotive', href: '/shop?category=automotive' },
                { name: 'Kids & Baby', href: '/shop?category=kids' },
                { name: 'Books & Media', href: '/shop?category=books' }
              ].map((category) => (
                <li key={category.name}>
                  <Link 
                    href={category.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-white">Get in Touch</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-blue-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">Email Us</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-400 hover:text-blue-300 transition-colors text-sm"
                  >
                    <EMAIL>
                  </a>
                  <p className="text-gray-400 text-xs mt-1">24/7 Support</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">Call Us</p>
                  <a
                    href="tel:+447447186806"
                    className="text-green-400 hover:text-green-300 transition-colors text-sm"
                  >
                    +44 7447 186806
                  </a>
                  <p className="text-gray-400 text-xs mt-1">Mon-Fri, 9AM-6PM CET</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <MessageCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">WhatsApp Support</p>
                  <a 
                    href="https://wa.me/447447186806" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-green-400 hover:text-green-300 transition-colors text-sm"
                  >
                    +44 7447 186806
                  </a>
                  <p className="text-gray-400 text-xs mt-1">Available 24/7</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">Visit Us</p>
                  <p className="text-gray-400 text-sm">
                    Fröbelstraße 12<br />
                    41515 Grevenbroich<br />
                    Germany
                  </p>
                </div>
              </div>
            </div>
            
            {/* Removed duplicate contact information */}
            
            {/* Trust Badges */}
            <div className="mt-8 space-y-3">
              <h4 className="text-white font-semibold text-sm">Why Choose Deal4u?</h4>
              <div className="space-y-4 text-sm">
                <div className="flex items-center">
                  <Award className="w-4 h-4 text-orange-400" />
                  <span className="text-gray-300 text-xs">Quality Guaranteed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-green-400" />
                  <span className="text-gray-300 text-xs">SSL Secured</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Truck className="w-4 h-4 text-blue-400" />
                  <span className="text-gray-300 text-xs">Fast Delivery</span>
                </div>
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4 text-purple-400" />
                  <span className="text-gray-300 text-xs">Easy Returns</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods & Bottom */}
      <div className="border-t border-gray-800 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
            
            {/* Payment Methods */}
            <div>
              <h4 className="text-white font-semibold mb-3 text-sm">We Accept</h4>
              <div className="flex gap-3">
                <div className="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                  <span className="text-blue-600 font-bold text-xs">VISA</span>
                </div>
                <div className="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                  <span className="text-red-600 font-bold text-xs">MC</span>
                </div>
                <div className="bg-white rounded-md p-2 flex items-center justify-center w-12 h-8">
                  <span className="text-blue-500 font-bold text-xs">AMEX</span>
                </div>
                <div className="bg-blue-600 rounded-md p-2 flex items-center justify-center w-12 h-8">
                  <span className="text-white font-bold text-xs">PP</span>
                </div>
                <div className="bg-black rounded-md p-2 flex items-center justify-center w-12 h-8">
                  <span className="text-white font-bold text-xs">A</span>
                </div>
              </div>
            </div>

            {/* Back to Top Button is now in layout.js as a client component */}

            {/* Copyright & Legal */}
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-sm mb-2">
                © {currentYear} Deal4u. All rights reserved.
              </p>
              <div className="flex flex-wrap justify-center md:justify-end gap-4 text-sm">
                <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                  Cookie Policy
                </Link>
                <Link href="/accessibility" className="text-gray-400 hover:text-white transition-colors">
                  Accessibility
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security & Certifications */}
      <div className="bg-gray-900 border-t border-gray-800 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-8 text-center">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-green-400" />
              <span className="text-gray-400 text-xs">SSL Secured</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-4 h-4 text-blue-400" />
              <span className="text-gray-400 text-xs">Verified Business</span>
            </div>
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4 text-purple-400" />
              <span className="text-gray-400 text-xs">30-Day Returns</span>
            </div>
            <div className="flex items-center space-x-2">
              <Truck className="w-4 h-4 text-orange-400" />
              <span className="text-gray-400 text-xs">Free Shipping Over $50</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;