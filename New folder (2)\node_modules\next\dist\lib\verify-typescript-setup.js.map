{"version": 3, "sources": ["../../src/lib/verify-typescript-setup.ts"], "names": ["verifyTypeScriptSetup", "requiredPackages", "file", "pkg", "exportsRestrict", "dir", "distDir", "cacheDir", "intentDirs", "tsconfigPath", "typeCheckPreflight", "disableStaticImages", "hasAppDir", "hasPagesDir", "resolvedTsConfigPath", "path", "join", "deps", "intent", "getTypeScriptIntent", "version", "hasNecessaryDependencies", "missing", "length", "isCI", "missingDepsError", "console", "log", "bold", "yellow", "cyan", "installDependencies", "catch", "err", "error", "command", "tsPath", "resolved", "get", "ts", "Promise", "resolve", "require", "semver", "lt", "warn", "writeConfigurationDefaults", "firstTimeSetup", "writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "result", "runTypeCheck", "CompileError", "red", "message", "process", "exit", "env", "IS_NEXT_WORKER", "Error"], "mappings": ";;;;+BAmCsBA;;;eAAAA;;;4BAnCkB;6DACvB;0CAEwB;+DAEtB;8BACU;6DACR;qCAEe;0CAEK;4CACE;qCACP;wBACf;wCACY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,mBAAmB;IACvB;QACEC,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAEM,eAAeJ,sBAAsB,EAC1CK,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EAWZ;IACC,MAAMC,uBAAuBC,aAAI,CAACC,IAAI,CAACX,KAAKI;IAE5C,IAAI;YAaEQ;QAZJ,wCAAwC;QACxC,MAAMC,SAAS,MAAMC,IAAAA,wCAAmB,EAACd,KAAKG,YAAYC;QAC1D,IAAI,CAACS,QAAQ;YACX,OAAO;gBAAEE,SAAS;YAAK;QACzB;QAEA,4DAA4D;QAC5D,IAAIH,OAA8B,MAAMI,IAAAA,kDAAwB,EAC9DhB,KACAJ;QAGF,IAAIgB,EAAAA,gBAAAA,KAAKK,OAAO,qBAAZL,cAAcM,MAAM,IAAG,GAAG;YAC5B,IAAIC,YAAI,EAAE;gBACR,4DAA4D;gBAC5D,2DAA2D;gBAC3DC,IAAAA,wCAAgB,EAACpB,KAAKY,KAAKK,OAAO;YACpC;YACAI,QAAQC,GAAG,CACTC,IAAAA,gBAAI,EACFC,IAAAA,kBAAM,EACJ,CAAC,gGAAgG,CAAC,KAGpG,OACA,4BACA,SACAD,IAAAA,gBAAI,EACF,gEACEE,IAAAA,gBAAI,EAAC,mBACL,sFAEJ;YAEJ,MAAMC,IAAAA,wCAAmB,EAAC1B,KAAKY,KAAKK,OAAO,EAAE,MAAMU,KAAK,CAAC,CAACC;gBACxD,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDP,QAAQQ,KAAK,CACX,CAAC,+FAA+F,CAAC,GAC/F,AAACD,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;YACAhB,OAAO,MAAMI,IAAAA,kDAAwB,EAAChB,KAAKJ;QAC7C;QAEA,8CAA8C;QAC9C,MAAMmC,SAASnB,KAAKoB,QAAQ,CAACC,GAAG,CAAC;QACjC,MAAMC,KAAM,MAAMC,QAAQC,OAAO,CAC/BC,QAAQN;QAGV,IAAIO,eAAM,CAACC,EAAE,CAACL,GAAGnB,OAAO,EAAE,UAAU;YAClCO,KAAIkB,IAAI,CACN,CAAC,yHAAyH,EAAEN,GAAGnB,OAAO,CAAC,CAAC;QAE5I;QAEA,+DAA+D;QAC/D,MAAM0B,IAAAA,sDAA0B,EAC9BP,IACAzB,sBACAI,OAAO6B,cAAc,EACrBnC,WACAN,SACAO;QAEF,qEAAqE;QACrE,kBAAkB;QAClB,MAAMmC,IAAAA,kDAAwB,EAAC;YAC7BC,SAAS5C;YACT6C,qBAAqB,CAACvC;YACtBE;YACAD;QACF;QAEA,IAAIuC;QACJ,IAAIzC,oBAAoB;YACtB,MAAM,EAAE0C,YAAY,EAAE,GAAGV,QAAQ;YAEjC,yEAAyE;YACzES,SAAS,MAAMC,aACbb,IACAlC,KACAC,SACAQ,sBACAP,UACAK;QAEJ;QACA,OAAO;YAAEuC;YAAQ/B,SAASmB,GAAGnB,OAAO;QAAC;IACvC,EAAE,OAAOa,KAAK;QACZ,+DAA+D;QAC/D,IAAIA,eAAeoB,0BAAY,EAAE;YAC/B3B,QAAQQ,KAAK,CAACoB,IAAAA,eAAG,EAAC;YAClB5B,QAAQQ,KAAK,CAACD,IAAIsB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QAEA;;;;KAIC,GAED,mEAAmE;QACnE,IAAID,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC9B,IAAI1B,eAAe2B,OAAO;gBACxBlC,QAAQQ,KAAK,CAACD,IAAIsB,OAAO;YAC3B,OAAO;gBACL7B,QAAQQ,KAAK,CAACD;YAChB;YACAuB,QAAQC,IAAI,CAAC;QACf;QACA,kFAAkF;QAClF,MAAMxB;IACR;AACF"}