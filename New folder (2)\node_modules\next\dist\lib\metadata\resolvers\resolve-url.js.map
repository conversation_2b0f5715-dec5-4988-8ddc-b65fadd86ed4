{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "names": ["getSocialImageFallbackMetadataBase", "isStringOrURL", "resolveAbsoluteUrlWithPathname", "resolveRelativeUrl", "resolveUrl", "icon", "URL", "createLocalMetadataBase", "process", "env", "PORT", "getPreviewDeploymentUrl", "origin", "VERCEL_BRANCH_URL", "VERCEL_URL", "undefined", "getProductionDeploymentUrl", "VERCEL_PROJECT_PRODUCTION_URL", "metadataBase", "isMetadataBaseMissing", "defaultMetadataBase", "previewDeploymentUrl", "productionDeploymentUrl", "fallbackMetadataBase", "NODE_ENV", "VERCEL_ENV", "url", "parsedUrl", "basePath", "pathname", "joinedPath", "path", "posix", "join", "startsWith", "resolve", "FILE_REGEX", "isFilePattern", "test", "trailingSlash", "resolvedUrl", "result", "href", "endsWith", "isRelative", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isExternal", "isFileUrl"], "mappings": ";;;;;;;;;;;;;;;;;;IAuBgBA,kCAAkC;eAAlCA;;IA4HdC,aAAa;eAAbA;;IAGAC,8BAA8B;eAA9BA;;IADAC,kBAAkB;eAAlBA;;IADAC,UAAU;eAAVA;;;6DApJe;;;;;;AAGjB,SAASH,cAAcI,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,OAAO,IAAID,IAAI,CAAC,iBAAiB,EAAEE,QAAQC,GAAG,CAACC,IAAI,IAAI,KAAK,CAAC;AAC/D;AAEA,SAASC;IACP,MAAMC,SAASJ,QAAQC,GAAG,CAACI,iBAAiB,IAAIL,QAAQC,GAAG,CAACK,UAAU;IACtE,OAAOF,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,OAAO,CAAC,IAAIG;AACjD;AAEA,SAASC;IACP,MAAMJ,SAASJ,QAAQC,GAAG,CAACQ,6BAA6B;IACxD,OAAOL,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,OAAO,CAAC,IAAIG;AACjD;AAIO,SAASf,mCAAmCkB,YAAwB;IAIzE,MAAMC,wBAAwB,CAACD;IAC/B,MAAME,sBAAsBb;IAC5B,MAAMc,uBAAuBV;IAC7B,MAAMW,0BAA0BN;IAEhC,IAAIO;IACJ,IAAIf,QAAQC,GAAG,CAACe,QAAQ,KAAK,eAAe;QAC1CD,uBAAuBH;IACzB,OAAO;QACLG,uBACEf,QAAQC,GAAG,CAACe,QAAQ,KAAK,gBACzBH,wBACAb,QAAQC,GAAG,CAACgB,UAAU,KAAK,YACvBJ,uBACAH,gBAAgBI,2BAA2BF;IACnD;IAEA,OAAO;QACLG;QACAJ;IACF;AACF;AAQA,SAASf,WACPsB,GAAoC,EACpCR,YAAwB;IAExB,IAAIQ,eAAepB,KAAK,OAAOoB;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAIrB,IAAIoB;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACT,cAAc;QACjBA,eAAeX;IACjB;IAEA,oCAAoC;IACpC,MAAMqB,WAAWV,aAAaW,QAAQ,IAAI;IAC1C,MAAMC,aAAaC,aAAI,CAACC,KAAK,CAACC,IAAI,CAACL,UAAUF;IAE7C,OAAO,IAAIpB,IAAIwB,YAAYZ;AAC7B;AAEA,uDAAuD;AACvD,SAASf,mBAAmBuB,GAAiB,EAAEG,QAAgB;IAC7D,IAAI,OAAOH,QAAQ,YAAYA,IAAIQ,UAAU,CAAC,OAAO;QACnD,OAAOH,aAAI,CAACC,KAAK,CAACG,OAAO,CAACN,UAAUH;IACtC;IACA,OAAOA;AACT;AAEA,+EAA+E;AAC/E,MAAMU,aACJ;AACF,SAASC,cAAcR,QAAgB;IACrC,OAAOO,WAAWE,IAAI,CAACT;AACzB;AAEA,kFAAkF;AAClF,SAAS3B,+BACPwB,GAAiB,EACjBR,YAAwB,EACxB,EAAEqB,aAAa,EAAEV,QAAQ,EAAmB;IAE5C,wDAAwD;IACxDH,MAAMvB,mBAAmBuB,KAAKG;IAE9B,6DAA6D;IAC7D,yDAAyD;IACzD,IAAIW,cAAc;IAClB,MAAMC,SAASvB,eAAed,WAAWsB,KAAKR,gBAAgBQ;IAC9D,IAAI,OAAOe,WAAW,UAAU;QAC9BD,cAAcC;IAChB,OAAO;QACLD,cAAcC,OAAOZ,QAAQ,KAAK,MAAMY,OAAO7B,MAAM,GAAG6B,OAAOC,IAAI;IACrE;IAEA,oEAAoE;IACpE,gDAAgD;IAChD,uBAAuB;IACvB,IAAIH,iBAAiB,CAACC,YAAYG,QAAQ,CAAC,MAAM;QAC/C,IAAIC,aAAaJ,YAAYN,UAAU,CAAC;QACxC,IAAIW,WAAWL,YAAYM,QAAQ,CAAC;QACpC,IAAIC,aAAa;QACjB,IAAIC,YAAY;QAEhB,IAAI,CAACJ,YAAY;YACf,IAAI;gBACF,MAAMjB,YAAY,IAAIrB,IAAIkC;gBAC1BO,aACE7B,gBAAgB,QAAQS,UAAUf,MAAM,KAAKM,aAAaN,MAAM;gBAClEoC,YAAYX,cAAcV,UAAUE,QAAQ;YAC9C,EAAE,OAAM;gBACN,gDAAgD;gBAChDkB,aAAa;YACf;YACA,IACE,kGAAkG;YAClG,CAACC,aACD,CAACD,cACD,CAACF,UAED,OAAO,CAAC,EAAEL,YAAY,CAAC,CAAC;QAC5B;IACF;IAEA,OAAOA;AACT"}