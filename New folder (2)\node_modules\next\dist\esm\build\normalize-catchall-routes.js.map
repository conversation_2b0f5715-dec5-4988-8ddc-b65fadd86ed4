{"version": 3, "sources": ["../../src/build/normalize-catchall-routes.ts"], "names": ["isInterceptionRouteAppPath", "AppPathnameNormalizer", "normalizeCatchAllRoutes", "appPaths", "normalizer", "catchAllRoutes", "Set", "Object", "values", "flat", "filter", "isCatchAllRoute", "sort", "a", "b", "split", "length", "filteredAppPaths", "keys", "route", "appPath", "catchAllRoute", "normalizedCatchAllRoute", "normalize", "normalizedCatchAllRouteBasePath", "slice", "search", "catchAllRouteRegex", "startsWith", "some", "path", "hasMatchedSlots", "isOptionalCatchAll", "push", "isCatchAll", "path1", "path2", "slots1", "isMatchableSlot", "slots2", "i", "segment", "pathname", "includes"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,qBAAqB,QAAQ,iEAAgE;AAEtG;;;;;;CAMC,GACD,OAAO,SAASC,wBACdC,QAAkC,EAClCC,aAAa,IAAIH,uBAAuB;IAExC,MAAMI,iBAAiB;WAClB,IAAIC,IACLC,OAAOC,MAAM,CAACL,UACXM,IAAI,GACJC,MAAM,CAACC,gBACR,wEAAwE;SACvEC,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEC,KAAK,CAAC,KAAKC,MAAM,GAAGH,EAAEE,KAAK,CAAC,KAAKC,MAAM;KAE9D;IAED,oEAAoE;IACpE,mEAAmE;IACnE,sFAAsF;IACtF,MAAMC,mBAAmBV,OAAOW,IAAI,CAACf,UAAUO,MAAM,CACnD,CAACS,QAAU,CAACnB,2BAA2BmB;IAGzC,KAAK,MAAMC,WAAWH,iBAAkB;QACtC,KAAK,MAAMI,iBAAiBhB,eAAgB;YAC1C,MAAMiB,0BAA0BlB,WAAWmB,SAAS,CAACF;YACrD,MAAMG,kCAAkCF,wBAAwBG,KAAK,CACnE,GACAH,wBAAwBI,MAAM,CAACC;YAGjC,IACE,iDAAiD;YACjDP,QAAQQ,UAAU,CAACJ,oCACnB,2EAA2E;YAC3E,CAACrB,QAAQ,CAACiB,QAAQ,CAACS,IAAI,CAAC,CAACC,OAASC,gBAAgBD,MAAMT,iBACxD;gBACA,yFAAyF;gBACzF,0CAA0C;gBAC1C,IAAIW,mBAAmBX,gBAAgB;oBACrC,wFAAwF;oBACxF,yEAAyE;oBACzElB,QAAQ,CAACiB,QAAQ,CAACa,IAAI,CAACZ;gBACzB,OAAO,IAAIa,WAAWb,gBAAgB;oBACpC,yEAAyE;oBACzE,2EAA2E;oBAC3E,IAAIG,oCAAoCJ,SAAS;wBAC/CjB,QAAQ,CAACiB,QAAQ,CAACa,IAAI,CAACZ;oBACzB;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASU,gBAAgBI,KAAa,EAAEC,KAAa;IACnD,MAAMC,SAASF,MAAMpB,KAAK,CAAC,KAAKL,MAAM,CAAC4B;IACvC,MAAMC,SAASH,MAAMrB,KAAK,CAAC,KAAKL,MAAM,CAAC4B;IAEvC,gGAAgG;IAChG,IAAID,OAAOrB,MAAM,KAAKuB,OAAOvB,MAAM,EAAE,OAAO;IAE5C,uFAAuF;IACvF,IAAK,IAAIwB,IAAI,GAAGA,IAAIH,OAAOrB,MAAM,EAAEwB,IAAK;QACtC,IAAIH,MAAM,CAACG,EAAE,KAAKD,MAAM,CAACC,EAAE,EAAE,OAAO;IACtC;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAASF,gBAAgBG,OAAe;IACtC,OAAOA,QAAQb,UAAU,CAAC,QAAQa,YAAY;AAChD;AAEA,MAAMd,qBAAqB;AAE3B,SAAShB,gBAAgB+B,QAAgB;IACvC,mIAAmI;IACnI,OAAO,CAACV,mBAAmBU,aAAaR,WAAWQ;AACrD;AAEA,SAASV,mBAAmBU,QAAgB;IAC1C,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,SAAST,WAAWQ,QAAgB;IAClC,OAAOA,SAASC,QAAQ,CAAC;AAC3B"}