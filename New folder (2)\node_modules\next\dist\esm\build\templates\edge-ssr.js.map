{"version": 3, "sources": ["../../../src/build/templates/edge-ssr.ts"], "names": ["adapter", "getRender", "IncrementalCache", "Document", "appMod", "userlandPage", "userlandErrorPage", "renderToHTML", "RouteModule", "pageMod", "routeModule", "pageRouteModuleOptions", "components", "App", "default", "userland", "errorMod", "errorRouteModuleOptions", "error500Mod", "userland500Page", "user500RouteModuleOptions", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "render", "pagesType", "dev", "page", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": "AAAA,OAAO,2BAA0B;AACjC,SAASA,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,OAAOC,cAAc,sBAAqB;AAC1C,YAAYC,YAAY,iBAAgB;AACxC,YAAYC,kBAAkB,eAAc;AAC5C,YAAYC,uBAAuB,0BAAyB;AAI5D,uCAAuC;AACvC,0CAA0C;AAE1C,qEAAqE;AACrE,iCAAiC;AAEjC,SAASC,YAAY,QAAQ,sBAAqB;AAClD,OAAOC,iBAAiB,iDAAgD;AAexE,mBAAmB;AACnB,oBAAoB;AACpB,aAAa;AACb,oBAAoB;AACpB,gCAAgC;AAChC,iCAAiC;AACjC,mCAAmC;AAEnC,MAAMC,UAAU;IACd,GAAGJ,YAAY;IACfK,aAAa,IAAIF,YAAY;QAC3B,GAAGG,sBAAsB;QACzBC,YAAY;YACVC,KAAKT,OAAOU,OAAO;YACnBX;QACF;QACAY,UAAUV;IACZ;AACF;AAEA,MAAMW,WAAW;IACf,GAAGV,iBAAiB;IACpBI,aAAa,IAAIF,YAAY;QAC3B,GAAGS,uBAAuB;QAC1BL,YAAY;YACVC,KAAKT,OAAOU,OAAO;YACnBX;QACF;QACAY,UAAUT;IACZ;AACF;AAEA,4DAA4D;AAC5D,MAAMY,cAAcC,kBAChB;IACE,GAAGA,eAAe;IAClBT,aAAa,IAAIF,YAAY;QAC3B,GAAGY,yBAAyB;QAC5BR,YAAY;YACVC,KAAKT,OAAOU,OAAO;YACnBX;QACF;QACAY,UAAUI;IACZ;AACF,IACA;AAEJ,MAAME,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BC,KAAKC,gBAAgB;AAC1D,MAAMC,wBAAwBR,eAAeM,KAAKG,yBAAyB;AAC3E,MAAMC,+BAA+BC,aACjCX,eAAeM,KAAKM,gCAAgC,IACpDR;AACJ,MAAMS,mBAAmBb,eAAeM,KAAKQ,oBAAoB;AAEjE,MAAMC,SAASnC,UAAU;IACvBoC;IACAC;IACAC,MAAM;IACNnC;IACAK;IACAO;IACAE;IACAf;IACAuB;IACAnB;IACAsB;IACAE;IACAS,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpCX;IACAY;AACF;AAEA,OAAO,MAAMC,eAAetC,QAAO;AAEnC,eAAe,SAASuC,SAASC,IAA4C;IAC3E,OAAOjD,QAAQ;QACb,GAAGiD,IAAI;QACP/C;QACAgD,SAASd;IACX;AACF"}