'use client';

import { useState } from 'react';

export default function SimpleSyncPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [debugInfo, setDebugInfo] = useState(null);

  const checkProductStatuses = async () => {
    setLoading(true);
    try {
      console.log('🔍 Starting product status check...');

      // Get all products using the new direct API that works (max 100 per page)
      const response = await fetch('/api/direct-products?status=publish&per_page=100');
      console.log('📡 API Response status:', response.status);

      const data = await response.json();
      console.log('📊 API Response data:', data);

      if (data.success) {
        console.log('✅ API call successful');
        console.log('📦 Products received:', data.products?.length || 0);

        const statusBreakdown = data.products.reduce((acc, product) => {
          acc[product.status] = (acc[product.status] || 0) + 1;
          return acc;
        }, {});

        console.log('📋 Status breakdown:', statusBreakdown);

        setDebugInfo({
          total: data.products.length,
          statusBreakdown,
          sampleProducts: data.products.slice(0, 10).map(p => ({
            id: p.id,
            name: p.name?.substring(0, 40),
            status: p.status
          })),
          rawResponse: data
        });

        alert(`Found ${data.products.length} total products!\nStatus breakdown: ${JSON.stringify(statusBreakdown, null, 2)}\n\nCheck browser console (F12) for detailed logs.`);
      } else {
        console.error('❌ API call failed:', data.error);
        alert('API call failed: ' + data.error);
      }
    } catch (error) {
      console.error('❌ Error in checkProductStatuses:', error);
      alert('Error checking products: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPI = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing DIRECT WooCommerce API...');

      // Test direct API call to your WooCommerce
      const directUrl = 'https://deal4u.co/wp-json/wc/v3/products?consumer_key=ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193&consumer_secret=cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3&per_page=100&status=any';

      console.log('📡 Direct API URL:', directUrl);

      const response = await fetch(directUrl);
      console.log('📊 Direct API Response status:', response.status);

      if (response.ok) {
        const products = await response.json();
        console.log('✅ Direct API Success! Products:', products.length);
        console.log('📦 Sample products:', products.slice(0, 3).map(p => ({ id: p.id, name: p.name, status: p.status })));

        const statusBreakdown = products.reduce((acc, product) => {
          acc[product.status] = (acc[product.status] || 0) + 1;
          return acc;
        }, {});

        alert(`DIRECT API SUCCESS!\nFound ${products.length} products\nStatus: ${JSON.stringify(statusBreakdown, null, 2)}`);
      } else {
        const errorText = await response.text();
        console.error('❌ Direct API failed:', response.status, errorText);
        alert(`Direct API failed: ${response.status}\n${errorText}`);
      }
    } catch (error) {
      console.error('❌ Direct API error:', error);
      alert('Direct API error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const runBulkPublish = async () => {
    if (!confirm('This will publish ALL 100 unpublished products. Continue?')) {
      return;
    }

    setLoading(true);
    setResults(null);
    
    try {
      console.log('🚀 Starting bulk publish...');
      
      const response = await fetch('/api/alidrop-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'bulk-import-publish',
          autoCorrect: true,
          minConfidence: 6,
          fixImages: true,
          cleanBranding: true
        })
      });

      const data = await response.json();
      console.log('Bulk publish response:', data);
      
      if (data.success) {
        setResults(data.data);
        alert(`SUCCESS!\n- Processed: ${data.data.processed}\n- Published: ${data.data.published}\n- Total: ${data.data.total}`);
      } else {
        console.error('Bulk publish error:', data.error);
        alert('Error: ' + data.error);
      }
    } catch (error) {
      console.error('Error running bulk publish:', error);
      alert('Error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🚀 Simple Bulk Publisher</h1>
          
          <div className="bg-blue-50 rounded-lg p-6 mb-8 border border-blue-200">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">📊 Current Situation</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-white rounded p-3">
                <div className="font-medium text-gray-900">Published Products</div>
                <div className="text-2xl font-bold text-green-600">5</div>
                <div className="text-gray-600">Showing on website</div>
              </div>
              <div className="bg-white rounded p-3">
                <div className="font-medium text-gray-900">Unpublished Products</div>
                <div className="text-2xl font-bold text-orange-600">100</div>
                <div className="text-gray-600">Hidden from website</div>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-6 mb-8 border border-green-200">
            <h2 className="text-xl font-semibold text-green-900 mb-4">🎯 Solution</h2>
            <p className="text-green-800 mb-4">
              Click the button below to publish all 100 unpublished products. 
              After this, your website will show all 105 products instead of just 5!
            </p>
            
            <div className="flex flex-wrap gap-3">
              <button
                onClick={checkProductStatuses}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-sm"
              >
                {loading ? '🔄 Checking...' : '🔍 Check via API'}
              </button>

              <button
                onClick={testDirectAPI}
                disabled={loading}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-sm"
              >
                {loading ? '🔄 Testing...' : '⚡ Direct WooCommerce'}
              </button>

              <button
                onClick={runBulkPublish}
                disabled={loading}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg"
              >
                {loading ? '🔄 Publishing Products...' : '🚀 Publish All Products'}
              </button>
            </div>
          </div>

          {debugInfo && (
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200 mb-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">🔍 Product Status Debug Info</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                {Object.entries(debugInfo.statusBreakdown).map(([status, count]) => (
                  <div key={status} className="bg-white rounded p-3 text-center">
                    <div className="text-xl font-bold text-blue-600">{count}</div>
                    <div className="text-gray-600 capitalize">{status}</div>
                  </div>
                ))}
              </div>
              <div className="text-sm text-blue-800">
                <strong>Total Products Found: {debugInfo.total}</strong>
              </div>
            </div>
          )}

          {results && (
            <div className="bg-gray-50 rounded-lg p-6 border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Results</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="bg-white rounded p-3 text-center">
                  <div className="text-2xl font-bold text-blue-600">{results.processed || 0}</div>
                  <div className="text-gray-600">Processed</div>
                </div>
                <div className="bg-white rounded p-3 text-center">
                  <div className="text-2xl font-bold text-green-600">{results.published || 0}</div>
                  <div className="text-gray-600">Published</div>
                </div>
                <div className="bg-white rounded p-3 text-center">
                  <div className="text-2xl font-bold text-purple-600">{results.imagesFixed || 0}</div>
                  <div className="text-gray-600">Images Fixed</div>
                </div>
                <div className="bg-white rounded p-3 text-center">
                  <div className="text-2xl font-bold text-orange-600">{results.brandingFixed || 0}</div>
                  <div className="text-gray-600">Branding Fixed</div>
                </div>
              </div>
              
              {results.published > 0 && (
                <div className="mt-6 p-4 bg-green-100 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-900 mb-2">🎉 Success!</h4>
                  <p className="text-green-800">
                    {results.published} products have been published! 
                    Go check your website - you should now see all your products.
                  </p>
                  <div className="mt-3">
                    <a 
                      href="/shop" 
                      target="_blank"
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                    >
                      🛒 View Shop Page
                    </a>
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <h4 className="font-semibold text-yellow-900 mb-2">💡 What This Does</h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>✅ Finds all unpublished products (draft, private, pending)</li>
              <li>✅ Fixes missing product images</li>
              <li>✅ Replaces "AliExpress" branding with "Deal4U"</li>
              <li>✅ Automatically categorizes products</li>
              <li>✅ Publishes all products to make them visible on website</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
