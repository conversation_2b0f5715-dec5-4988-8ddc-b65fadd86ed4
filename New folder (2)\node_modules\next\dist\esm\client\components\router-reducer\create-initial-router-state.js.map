{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createHrefFromUrl", "fillLazyItemsTillLeafWithHead", "extractPathFromFlightRouterState", "createPrefetchCacheEntryForInitialLoad", "PrefetchKind", "addRefreshMarkerToActiveParallelSegments", "createInitialRouterState", "buildId", "initialTree", "initialSeedData", "urlParts", "initialParallelRoutes", "location", "initialHead", "couldBeIntercepted", "initialCanonicalUrl", "join", "isServer", "rsc", "cache", "lazyData", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "canonicalUrl", "prefetchCache", "size", "undefined", "initialState", "tree", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "url", "URL", "search", "origin", "initialFlightData", "kind", "AUTO", "data"], "mappings": "AAQA,SAASA,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,gCAAgC,QAAQ,yBAAwB;AACzE,SAASC,sCAAsC,QAAQ,yBAAwB;AAC/E,SAASC,YAAY,QAAiC,yBAAwB;AAC9E,SAASC,wCAAwC,QAAQ,uCAAsC;AAa/F,OAAO,SAASC,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,QAAQ,EACRC,qBAAqB,EACrBC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EACW,GATU;IAUvC,sFAAsF;IACtF,kGAAkG;IAClG,mCAAmC;IACnC,MAAMC,sBAAsBL,SAASM,IAAI,CAAC;IAC1C,MAAMC,WAAW,CAACL;IAClB,MAAMM,MAAMT,eAAe,CAAC,EAAE;IAE9B,MAAMU,QAAmB;QACvBC,UAAU;QACVF,KAAKA;QACLG,aAAa;QACbC,MAAM;QACNC,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBP,WAAW,IAAIQ,QAAQd;QACvCe,kBAAkB;QAClBC,SAASlB,eAAe,CAAC,EAAE;IAC7B;IAEA,MAAMmB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJhB,WAEIZ,kBAAkBY,YAClBG;IAENV,yCAAyCG,aAAaoB;IAEtD,MAAMC,gBAAgB,IAAIJ;IAE1B,yEAAyE;IACzE,IAAId,0BAA0B,QAAQA,sBAAsBmB,IAAI,KAAK,GAAG;QACtE7B,8BACEkB,OACAY,WACAvB,aACAC,iBACAI;IAEJ;QAsBI,sEAAsE;IACrEX;IArBL,MAAM8B,eAAe;QACnBzB;QACA0B,MAAMzB;QACNW;QACAU;QACAK,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAd;QACAe,SAEE,CAACzC,OAAAA,iCAAiCM,iBAAgBI,4BAAAA,SAAUgC,QAAQ,aAAnE1C,OACD;IACJ;IAEA,IAAIU,UAAU;QACZ,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,MAAMiC,MAAM,IAAIC,IACd,AAAC,KAAElC,SAASgC,QAAQ,GAAGhC,SAASmC,MAAM,EACtCnC,SAASoC,MAAM;QAGjB,MAAMC,oBAAgC;YAAC;gBAAC;gBAAIzC;gBAAa;gBAAM;aAAK;SAAC;QACrEL,uCAAuC;YACrC0C;YACAK,MAAM9C,aAAa+C,IAAI;YACvBC,MAAM;gBAACH;gBAAmBlB;gBAAW;gBAAOjB;aAAmB;YAC/DmB,MAAMD,aAAaC,IAAI;YACvBJ,eAAeG,aAAaH,aAAa;YACzCc,SAASX,aAAaW,OAAO;QAC/B;IACF;IAEA,OAAOX;AACT"}