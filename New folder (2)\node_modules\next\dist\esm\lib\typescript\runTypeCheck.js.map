{"version": 3, "sources": ["../../../src/lib/typescript/runTypeCheck.ts"], "names": ["path", "DiagnosticCategory", "getFormattedDiagnostic", "getTypeScriptConfiguration", "getRequiredConfiguration", "CompileError", "warn", "runTypeCheck", "ts", "baseDir", "distDir", "tsConfigPath", "cacheDir", "isAppDirEnabled", "effectiveConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "options", "declarationMap", "emitDeclarationOnly", "noEmit", "program", "composite", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "join", "createProgram", "result", "emit", "regexIgnoredFile", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "Error", "Boolean", "process", "env", "__NEXT_TEST_MODE", "allErrors", "map", "console", "error", "Promise", "resolve", "setTimeout", "warnings", "Warning", "getSourceFiles"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,wBAAuB;AAC9B,SAASC,0BAA0B,QAAQ,+BAA8B;AACzE,SAASC,wBAAwB,QAAQ,+BAA8B;AAEvE,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,IAAI,QAAQ,yBAAwB;AAU7C,OAAO,eAAeC,aACpBC,EAA+B,EAC/BC,OAAe,EACfC,OAAe,EACfC,YAAoB,EACpBC,QAAiB,EACjBC,eAAyB;IAEzB,MAAMC,yBAAyB,MAAMX,2BACnCK,IACAG;IAGF,IAAIG,uBAAuBC,SAAS,CAACC,MAAM,GAAG,GAAG;QAC/C,OAAO;YACLC,aAAa;YACbC,iBAAiB;YACjBC,iBAAiB;YACjBC,aAAa;QACf;IACF;IACA,MAAMC,iBAAiBjB,yBAAyBI;IAEhD,MAAMc,UAAU;QACd,GAAGD,cAAc;QACjB,GAAGP,uBAAuBQ,OAAO;QACjCC,gBAAgB;QAChBC,qBAAqB;QACrBC,QAAQ;IACV;IAEA,IAAIC;IAGJ,IAAIN,cAAc;IAClB,IAAI,AAACE,CAAAA,QAAQF,WAAW,IAAIE,QAAQK,SAAS,AAAD,KAAMf,UAAU;QAC1D,IAAIU,QAAQK,SAAS,EAAE;YACrBrB,KACE;QAEJ;QACAc,cAAc;QACdM,UAAUlB,GAAGoB,wBAAwB,CAAC;YACpCC,WAAWf,uBAAuBC,SAAS;YAC3CO,SAAS;gBACP,GAAGA,OAAO;gBACVK,WAAW;gBACXP,aAAa;gBACbU,iBAAiB9B,KAAK+B,IAAI,CAACnB,UAAU;YACvC;QACF;IACF,OAAO;QACLc,UAAUlB,GAAGwB,aAAa,CAAClB,uBAAuBC,SAAS,EAAEO;IAC/D;IAEA,MAAMW,SAASP,QAAQQ,IAAI;IAE3B,qBAAqB;IACrB,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,EAAE;IACF,WAAW;IACX,qBAAqB;IACrB,mBAAmB;IACnB,oBAAoB;IACpB,EAAE;IACF,MAAMC,mBACJ;IACF,MAAMC,iBAAiB5B,GACpB6B,qBAAqB,CAACX,SACtBY,MAAM,CAACL,OAAOM,WAAW,EACzBC,MAAM,CAAC,CAACC,IAAM,CAAEA,CAAAA,EAAEC,IAAI,IAAIP,iBAAiBQ,IAAI,CAACF,EAAEC,IAAI,CAACE,QAAQ,CAAA;IAElE,MAAMC,aACJT,eAAeU,IAAI,CACjB,CAACL,IAAMA,EAAEM,QAAQ,KAAK9C,mBAAmB+C,KAAK,IAAIC,QAAQR,EAAEC,IAAI,MAC7DN,eAAeU,IAAI,CAAC,CAACL,IAAMA,EAAEM,QAAQ,KAAK9C,mBAAmB+C,KAAK;IAEzE,0EAA0E;IAC1E,IAAIE,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIP,YAAY;YACd,MAAMQ,YAAYjB,eACfI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAK9C,mBAAmB+C,KAAK,EACrDM,GAAG,CACF,CAACb,IACC,iBACAvC,uBAAuBM,IAAIC,SAASC,SAAS+B,GAAG5B;YAGtD0C,QAAQC,KAAK,CACX,kCACEH,UAAUtB,IAAI,CAAC,UACf;YAGJ,kDAAkD;YAClD,MAAM,IAAI0B,QAAQ,CAACC,UAAYC,WAAWD,SAAS;QACrD;IACF;IAEA,IAAIb,YAAY;QACd,MAAM,IAAIxC,aACRH,uBAAuBM,IAAIC,SAASC,SAASmC,YAAYhC;IAE7D;IAEA,MAAM+C,WAAWxB,eACdI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAK9C,mBAAmB4D,OAAO,EACvDP,GAAG,CAAC,CAACb,IACJvC,uBAAuBM,IAAIC,SAASC,SAAS+B,GAAG5B;IAGpD,OAAO;QACLI,aAAa;QACb2C;QACA1C,iBAAiBJ,uBAAuBC,SAAS,CAACC,MAAM;QACxDG,iBAAiBO,QAAQoC,cAAc,GAAG9C,MAAM;QAChDI;IACF;AACF"}